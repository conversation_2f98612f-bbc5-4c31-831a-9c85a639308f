<?php

namespace Database\Seeders;

use App\Models\Client;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default client if not exists
        $client = Client::firstOrCreate(
            ['name' => 'Default Company'],
            [
                'name' => 'Default Company',
                'email' => '<EMAIL>',
                'phone' => '021-1234567',
                'address' => 'Jakarta, Indonesia',
                'is_active' => true,
            ]
        );

        // Create super admin user if not exists
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'user_type' => 'super_admin',
                'client_id' => $client->id,
                'email_verified_at' => now(),
            ]
        );

        // Create demo user if not exists
        $demoUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'password' => Hash::make('demo123'),
                'user_type' => 'client',
                'client_id' => $client->id,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Super Admin created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password');
        $this->command->info('');
        $this->command->info('Demo User created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: demo123');
    }
}
