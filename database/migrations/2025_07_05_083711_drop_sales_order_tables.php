<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop sales order tables as they are now redundant
        Schema::dropIfExists('sales_order_items');
        Schema::dropIfExists('sales_orders');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: This migration is irreversible as we're simplifying the structure
        // If you need to rollback, restore from backup
    }
};
