<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('user_type')->default('client')->after('password');
            $table->foreignId('client_id')->nullable()->after('user_type');
            $table->foreignId('created_by')->nullable()->after('client_id');
            $table->softDeletes()->after('updated_at');

            // Indexes
            $table->index('user_type');
            $table->index('client_id');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['user_type']);
            $table->dropIndex(['client_id']);
            $table->dropIndex(['created_by']);
            $table->dropColumn(['user_type', 'client_id', 'created_by', 'deleted_at']);
        });
    }
};
