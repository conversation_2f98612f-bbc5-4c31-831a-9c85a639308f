<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->after('status');
            $table->decimal('shipping_cost', 15, 2)->default(0)->after('discount_amount');
            $table->decimal('other_charges', 15, 2)->default(0)->after('shipping_cost');
            $table->string('payment_method', 50)->default('transfer')->after('payment_terms');
            $table->string('delivery_method', 50)->default('delivery')->after('delivery_address');
            $table->text('terms_conditions')->nullable()->after('delivery_method');
            $table->text('internal_notes')->nullable()->after('terms_conditions');
            $table->text('supplier_notes')->nullable()->after('internal_notes');
            $table->foreignId('received_by')->nullable()->after('approved_at')->constrained('users')->onDelete('set null');
            $table->timestamp('received_at')->nullable()->after('received_by');
            $table->string('invoice_number', 100)->nullable()->after('received_at');
            $table->date('invoice_date')->nullable()->after('invoice_number');
            $table->decimal('invoice_amount', 15, 2)->default(0)->after('invoice_date');
            $table->decimal('paid_amount', 15, 2)->default(0)->after('invoice_amount');
            $table->enum('payment_status', ['unpaid', 'partial', 'paid', 'overpaid'])->default('unpaid')->after('paid_amount');

            $table->index(['priority']);
            $table->index(['payment_status']);
            $table->index(['invoice_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->dropForeign(['received_by']);

            $table->dropIndex(['priority']);
            $table->dropIndex(['payment_status']);
            $table->dropIndex(['invoice_date']);

            $table->dropColumn([
                'priority',
                'shipping_cost',
                'other_charges',
                'payment_method',
                'delivery_method',
                'terms_conditions',
                'internal_notes',
                'supplier_notes',
                'received_by',
                'received_at',
                'invoice_number',
                'invoice_date',
                'invoice_amount',
                'paid_amount',
                'payment_status',
            ]);
        });
    }
};
