<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_transfers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('fixed_asset_id')->constrained()->onDelete('cascade');
            $table->string('transfer_number', 50)->unique();
            $table->date('transfer_date');
            $table->foreignId('from_location_id')->constrained('locations')->onDelete('restrict');
            $table->foreignId('to_location_id')->constrained('locations')->onDelete('restrict');
            $table->foreignId('from_department_id')->constrained('departments')->onDelete('restrict');
            $table->foreignId('to_department_id')->constrained('departments')->onDelete('restrict');
            $table->foreignId('from_cost_center_id')->nullable()->constrained('cost_centers')->onDelete('set null');
            $table->foreignId('to_cost_center_id')->nullable()->constrained('cost_centers')->onDelete('set null');
            $table->string('from_responsible_person')->nullable();
            $table->string('to_responsible_person')->nullable();
            $table->enum('transfer_type', ['internal', 'external', 'disposal', 'sale'])->default('internal');
            $table->text('reason')->nullable();
            $table->string('condition_before')->nullable();
            $table->string('condition_after')->nullable();
            $table->decimal('book_value_at_transfer', 15, 2);
            $table->decimal('transfer_cost', 15, 2)->default(0);
            $table->boolean('approval_required')->default(false);
            $table->enum('status', ['pending', 'approved', 'completed', 'cancelled'])->default('pending');
            $table->foreignId('transferred_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('received_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'status']);
            $table->index(['fixed_asset_id']);
            $table->index(['transfer_date']);
            $table->index(['transfer_type']);
            $table->index(['from_location_id']);
            $table->index(['to_location_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_transfers');
    }
};
