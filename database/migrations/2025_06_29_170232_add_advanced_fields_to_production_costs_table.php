<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('production_costs', function (Blueprint $table) {
            $table->foreignId('product_id')->nullable()->after('work_order_id')->constrained()->onDelete('set null');
            $table->string('cost_element', 50)->nullable()->after('cost_category');
            $table->foreignId('cost_center_id')->nullable()->after('account_id')->constrained()->onDelete('set null');
            $table->foreignId('department_id')->nullable()->after('cost_center_id')->constrained()->onDelete('set null');
            $table->string('allocation_base', 50)->nullable()->after('unit_cost');
            $table->decimal('allocation_rate', 15, 6)->default(0)->after('allocation_base');
            $table->integer('period_month')->nullable()->after('cost_date');
            $table->integer('period_year')->nullable()->after('period_month');
            $table->boolean('is_allocated')->default(false)->after('reference_number');
            $table->timestamp('allocated_at')->nullable()->after('is_allocated');

            $table->index(['cost_element']);
            $table->index(['period_year', 'period_month']);
            $table->index(['is_allocated']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('production_costs', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropForeign(['cost_center_id']);
            $table->dropForeign(['department_id']);

            $table->dropIndex(['cost_element']);
            $table->dropIndex(['period_year', 'period_month']);
            $table->dropIndex(['is_allocated']);

            $table->dropColumn([
                'product_id',
                'cost_element',
                'cost_center_id',
                'department_id',
                'allocation_base',
                'allocation_rate',
                'period_month',
                'period_year',
                'is_allocated',
                'allocated_at',
            ]);
        });
    }
};
