<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('production_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('bom_id')->nullable()->constrained('bill_of_materials')->onDelete('set null');
            $table->foreignId('work_center_id')->nullable()->constrained()->onDelete('set null');
            $table->string('wo_number', 50)->unique();
            $table->integer('operation_sequence')->default(1);
            $table->string('operation_name', 100);
            $table->text('description')->nullable();
            $table->decimal('quantity_to_process', 12, 2);
            $table->decimal('quantity_completed', 12, 2)->default(0);
            $table->decimal('quantity_scrapped', 12, 2)->default(0);
            $table->foreignId('unit_id')->constrained('unit_of_measures')->onDelete('restrict');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->datetime('planned_start_date')->nullable();
            $table->datetime('planned_end_date')->nullable();
            $table->datetime('actual_start_date')->nullable();
            $table->datetime('actual_end_date')->nullable();
            $table->decimal('setup_time_planned', 8, 2)->default(0);
            $table->decimal('setup_time_actual', 8, 2)->default(0);
            $table->decimal('run_time_planned', 8, 2)->default(0);
            $table->decimal('run_time_actual', 8, 2)->default(0);
            $table->decimal('labor_hours_planned', 8, 2)->default(0);
            $table->decimal('labor_hours_actual', 8, 2)->default(0);
            $table->decimal('machine_hours_planned', 8, 2)->default(0);
            $table->decimal('machine_hours_actual', 8, 2)->default(0);
            $table->decimal('labor_cost', 15, 2)->default(0);
            $table->decimal('machine_cost', 15, 2)->default(0);
            $table->decimal('overhead_cost', 15, 2)->default(0);
            $table->decimal('total_cost', 15, 2)->default(0);
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'status']);
            $table->index(['production_order_id']);
            $table->index(['assigned_to']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_orders');
    }
};
