<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_opname_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('stock_opname_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('restrict');
            $table->foreignId('inventory_id')->constrained()->onDelete('restrict');
            $table->decimal('system_quantity', 15, 6);
            $table->decimal('physical_quantity', 15, 6)->nullable();
            $table->decimal('variance_quantity', 15, 6)->default(0);
            $table->decimal('unit_cost', 15, 4);
            $table->decimal('variance_value', 15, 2)->default(0);
            $table->enum('status', ['pending', 'counted', 'adjusted'])->default('pending');
            $table->timestamp('count_date')->nullable();
            $table->foreignId('counted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->string('batch_number', 50)->nullable();
            $table->string('serial_number', 100)->nullable();
            $table->date('expiry_date')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'status']);
            $table->index(['stock_opname_id']);
            $table->index(['product_id']);
            $table->index(['variance_quantity']);
            $table->index(['batch_number']);
            $table->index(['serial_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_opname_items');
    }
};
