<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cost_variance_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('production_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('restrict');
            $table->string('report_period', 20);
            $table->date('report_date');
            $table->decimal('total_standard_cost', 15, 2);
            $table->decimal('total_actual_cost', 15, 2);
            $table->decimal('total_variance', 15, 2);
            $table->decimal('material_variance', 15, 2)->default(0);
            $table->decimal('labor_variance', 15, 2)->default(0);
            $table->decimal('overhead_variance', 15, 2)->default(0);
            $table->decimal('variance_percentage', 8, 4);
            $table->string('performance_rating', 20);
            $table->integer('significant_variances_count')->default(0);
            $table->json('recommendations')->nullable();
            $table->json('action_items')->nullable();
            $table->enum('status', ['draft', 'reviewed', 'approved'])->default('draft');
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'report_period']);
            $table->index(['status']);
            $table->index(['performance_rating']);
            $table->index(['variance_percentage']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cost_variance_reports');
    }
};
