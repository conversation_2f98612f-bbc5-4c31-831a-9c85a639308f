<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('depreciations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('fixed_asset_id')->constrained()->onDelete('cascade');
            $table->date('depreciation_date');
            $table->string('depreciation_method', 50);
            $table->decimal('depreciation_amount', 15, 2);
            $table->decimal('accumulated_depreciation', 15, 2);
            $table->decimal('book_value_before', 15, 2);
            $table->decimal('book_value_after', 15, 2);
            $table->decimal('useful_life_remaining', 8, 2);
            $table->integer('period_year');
            $table->integer('period_month');
            $table->boolean('is_manual')->default(false);
            $table->text('notes')->nullable();
            $table->foreignId('journal_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'period_year', 'period_month']);
            $table->index(['fixed_asset_id']);
            $table->index(['depreciation_date']);
            $table->index(['depreciation_method']);
            $table->unique(['fixed_asset_id', 'period_year', 'period_month']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('depreciations');
    }
};
