<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_centers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('location_id')->nullable()->constrained()->onDelete('set null');
            $table->string('work_center_code', 20)->unique();
            $table->string('work_center_name', 100);
            $table->text('description')->nullable();
            $table->enum('work_center_type', ['manual', 'semi_automatic', 'automatic'])->default('manual');
            $table->decimal('capacity_per_hour', 10, 2)->default(0);
            $table->foreignId('capacity_unit_id')->nullable()->constrained('unit_of_measures')->onDelete('set null');
            $table->decimal('labor_rate', 10, 2)->default(0);
            $table->decimal('machine_rate', 10, 2)->default(0);
            $table->decimal('overhead_rate', 5, 2)->default(0);
            $table->decimal('setup_time_default', 8, 2)->default(0);
            $table->decimal('efficiency_percentage', 5, 2)->default(100);
            $table->decimal('utilization_percentage', 5, 2)->default(100);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'is_active']);
            $table->index(['work_center_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_centers');
    }
};
