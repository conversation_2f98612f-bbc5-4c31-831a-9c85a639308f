<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('budgets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->string('budget_name');
            $table->text('description')->nullable();
            $table->integer('budget_year');
            $table->enum('budget_type', ['annual', 'quarterly', 'monthly'])->default('annual');
            $table->enum('status', ['draft', 'submitted', 'approved', 'rejected', 'active', 'closed'])->default('draft');
            $table->date('period_start');
            $table->date('period_end');
            $table->decimal('total_revenue_budget', 20, 2)->default(0);
            $table->decimal('total_expense_budget', 20, 2)->default(0);
            $table->decimal('net_income_budget', 20, 2)->default(0);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'budget_year']);
            $table->index(['client_id', 'status']);
            $table->unique(['client_id', 'budget_name', 'budget_year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('budgets');
    }
};
