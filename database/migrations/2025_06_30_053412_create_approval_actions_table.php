<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_actions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_request_id')->constrained()->onDelete('cascade');
            $table->integer('step_number');
            $table->foreignId('approver_id')->constrained('users')->onDelete('restrict');
            $table->enum('action', ['approved', 'rejected', 'delegated', 'returned'])->default('approved');
            $table->text('comments')->nullable();
            $table->foreignId('delegated_to')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('action_date')->useCurrent();
            $table->json('action_data')->nullable(); // Additional action data
            $table->timestamps();

            $table->index(['approval_request_id', 'step_number']);
            $table->index(['approver_id']);
            $table->index(['action']);
            $table->index(['action_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_actions');
    }
};
