<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('product_categories')->onDelete('set null');
            $table->foreignId('unit_id')->constrained('unit_of_measures')->onDelete('restrict');
            $table->string('product_code', 50)->unique();
            $table->string('product_name', 200);
            $table->text('description')->nullable();
            $table->enum('product_type', ['raw_material', 'finished_good', 'semi_finished', 'service'])->default('finished_good');
            $table->string('barcode', 100)->nullable();
            $table->string('sku', 100)->nullable();
            $table->decimal('standard_cost', 15, 2)->default(0);
            $table->decimal('selling_price', 15, 2)->default(0);
            $table->decimal('minimum_stock', 12, 2)->default(0);
            $table->decimal('maximum_stock', 12, 2)->default(0);
            $table->decimal('reorder_point', 12, 2)->default(0);
            $table->integer('lead_time_days')->default(0);
            $table->integer('shelf_life_days')->nullable();
            $table->decimal('weight', 10, 3)->nullable();
            $table->json('dimensions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_stockable')->default(true);
            $table->boolean('is_purchasable')->default(true);
            $table->boolean('is_saleable')->default(true);
            $table->boolean('is_manufactured')->default(false);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'is_active']);
            $table->index(['category_id']);
            $table->index(['product_type']);
            $table->index(['barcode']);
            $table->index(['sku']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
