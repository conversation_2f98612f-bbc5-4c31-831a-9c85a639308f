<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cost_centers', function (Blueprint $table) {
            $table->dropUnique(['cost_center_code']);
            $table->unique(['client_id', 'cost_center_code']);
        });

        Schema::table('departments', function (Blueprint $table) {
            $table->dropUnique(['department_code']);
            $table->unique(['client_id', 'department_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cost_centers', function (Blueprint $table) {
            $table->dropUnique(['client_id', 'cost_center_code']);
            $table->unique(['cost_center_code']);
        });

        Schema::table('departments', function (Blueprint $table) {
            $table->dropUnique(['client_id', 'department_code']);
            $table->unique(['department_code']);
        });
    }
};
