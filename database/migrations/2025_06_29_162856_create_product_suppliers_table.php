<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_suppliers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->string('supplier_product_code', 50)->nullable();
            $table->string('supplier_product_name', 200)->nullable();
            $table->decimal('purchase_price', 15, 2);
            $table->decimal('minimum_order_quantity', 15, 2)->default(1);
            $table->integer('lead_time_days')->default(7);
            $table->string('currency_code', 3)->default('IDR');
            $table->boolean('is_preferred')->default(false);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['client_id', 'product_id', 'supplier_id'], 'unique_product_supplier');
            $table->index(['client_id', 'is_active']);
            $table->index(['is_preferred']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_suppliers');
    }
};
