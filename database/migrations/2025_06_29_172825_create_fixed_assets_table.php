<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fixed_assets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->string('asset_code', 50)->unique();
            $table->string('asset_name');
            $table->enum('asset_category', ['building', 'machinery', 'vehicle', 'furniture', 'equipment', 'computer']);
            $table->enum('asset_type', ['tangible', 'intangible'])->default('tangible');
            $table->text('description')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('barcode')->nullable();
            $table->date('purchase_date');
            $table->decimal('acquisition_cost', 15, 2);
            $table->decimal('installation_cost', 15, 2)->default(0);
            $table->decimal('other_costs', 15, 2)->default(0);
            $table->decimal('total_cost', 15, 2);
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('purchase_order_id')->nullable()->constrained()->onDelete('set null');
            $table->string('invoice_number')->nullable();
            $table->date('warranty_start_date')->nullable();
            $table->date('warranty_end_date')->nullable();
            $table->foreignId('location_id')->constrained()->onDelete('restrict');
            $table->foreignId('department_id')->constrained()->onDelete('restrict');
            $table->foreignId('cost_center_id')->nullable()->constrained()->onDelete('set null');
            $table->string('responsible_person')->nullable();
            $table->enum('condition', ['excellent', 'good', 'fair', 'poor', 'damaged'])->default('good');
            $table->enum('status', ['active', 'inactive', 'disposed', 'sold', 'stolen', 'damaged'])->default('active');
            $table->enum('depreciation_method', ['straight_line', 'declining_balance', 'units_of_production', 'sum_of_years'])->default('straight_line');
            $table->integer('useful_life_years');
            $table->integer('useful_life_units')->nullable();
            $table->decimal('salvage_value', 15, 2)->default(0);
            $table->decimal('depreciation_rate', 8, 6)->default(0);
            $table->decimal('accumulated_depreciation', 15, 2)->default(0);
            $table->decimal('book_value', 15, 2);
            $table->decimal('fair_value', 15, 2)->nullable();
            $table->date('last_revaluation_date')->nullable();
            $table->string('insurance_policy')->nullable();
            $table->decimal('insurance_value', 15, 2)->nullable();
            $table->date('insurance_expiry')->nullable();
            $table->string('maintenance_schedule')->nullable();
            $table->date('last_maintenance_date')->nullable();
            $table->date('next_maintenance_date')->nullable();
            $table->date('disposal_date')->nullable();
            $table->enum('disposal_method', ['sale', 'scrap', 'donation', 'trade_in'])->nullable();
            $table->decimal('disposal_value', 15, 2)->nullable();
            $table->decimal('disposal_gain_loss', 15, 2)->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'asset_category']);
            $table->index(['status']);
            $table->index(['condition']);
            $table->index(['location_id']);
            $table->index(['department_id']);
            $table->index(['purchase_date']);
            $table->index(['next_maintenance_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fixed_assets');
    }
};
