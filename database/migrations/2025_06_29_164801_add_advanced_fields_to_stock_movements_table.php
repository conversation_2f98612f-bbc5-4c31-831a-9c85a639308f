<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->foreignId('location_id')->nullable()->after('inventory_id')->constrained()->onDelete('set null');
            $table->foreignId('from_location_id')->nullable()->after('location_id')->constrained('locations')->onDelete('set null');
            $table->foreignId('to_location_id')->nullable()->after('from_location_id')->constrained('locations')->onDelete('set null');
            $table->string('reason_code', 50)->nullable()->after('transaction_type');
            $table->decimal('running_value', 15, 2)->default(0)->after('running_balance');
            $table->string('batch_number', 50)->nullable()->after('reference_number');
            $table->string('serial_number', 100)->nullable()->after('batch_number');
            $table->date('expiry_date')->nullable()->after('serial_number');
            $table->string('lot_number', 50)->nullable()->after('expiry_date');
            $table->foreignId('work_order_id')->nullable()->after('lot_number')->constrained()->onDelete('set null');
            $table->foreignId('production_order_id')->nullable()->after('work_order_id')->constrained()->onDelete('set null');
            $table->foreignId('purchase_order_id')->nullable()->after('production_order_id')->constrained()->onDelete('set null');
            $table->unsignedBigInteger('sales_order_id')->nullable()->after('purchase_order_id');
            $table->boolean('is_reversed')->default(false)->after('sales_order_id');
            $table->foreignId('reversed_by_id')->nullable()->after('is_reversed')->constrained('users')->onDelete('set null');
            $table->timestamp('reversed_at')->nullable()->after('reversed_by_id');
            $table->foreignId('approved_by')->nullable()->after('created_by')->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable()->after('approved_by');

            $table->index(['reason_code']);
            $table->index(['batch_number']);
            $table->index(['serial_number']);
            $table->index(['lot_number']);
            $table->index(['is_reversed']);
            $table->index(['movement_date', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->dropForeign(['location_id']);
            $table->dropForeign(['from_location_id']);
            $table->dropForeign(['to_location_id']);
            $table->dropForeign(['work_order_id']);
            $table->dropForeign(['production_order_id']);
            $table->dropForeign(['purchase_order_id']);
            $table->dropForeign(['reversed_by_id']);
            $table->dropForeign(['approved_by']);

            $table->dropIndex(['reason_code']);
            $table->dropIndex(['batch_number']);
            $table->dropIndex(['serial_number']);
            $table->dropIndex(['lot_number']);
            $table->dropIndex(['is_reversed']);
            $table->dropIndex(['movement_date', 'product_id']);

            $table->dropColumn([
                'location_id',
                'from_location_id',
                'to_location_id',
                'reason_code',
                'running_value',
                'batch_number',
                'serial_number',
                'expiry_date',
                'lot_number',
                'work_order_id',
                'production_order_id',
                'purchase_order_id',
                'sales_order_id',
                'is_reversed',
                'reversed_by_id',
                'reversed_at',
                'approved_by',
                'approved_at',
            ]);
        });
    }
};
