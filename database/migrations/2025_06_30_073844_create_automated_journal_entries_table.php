<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('automated_journal_entries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->string('entry_type'); // depreciation, accrual, allocation, recurring
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('configuration'); // JSON configuration for the automation
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])->nullable();
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->date('next_run_date')->nullable();
            $table->date('last_run_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->decimal('amount', 15, 2)->nullable(); // For fixed amount entries
            $table->string('reference_model')->nullable(); // Model class for dynamic entries
            $table->unsignedBigInteger('reference_id')->nullable(); // Model ID for dynamic entries
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'entry_type'], 'aje_client_type_idx');
            $table->index(['client_id', 'is_active', 'next_run_date'], 'aje_client_active_next_idx');
            $table->index(['reference_model', 'reference_id'], 'aje_reference_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('automated_journal_entries');
    }
};
