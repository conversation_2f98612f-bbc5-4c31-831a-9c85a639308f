<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_costs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('production_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('work_order_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('cost_type', ['material', 'labor', 'overhead', 'other']);
            $table->enum('cost_category', ['direct', 'indirect']);
            $table->foreignId('account_id')->nullable()->constrained()->onDelete('set null');
            $table->string('description', 200);
            $table->decimal('standard_cost', 15, 2)->default(0);
            $table->decimal('actual_cost', 15, 2)->default(0);
            $table->decimal('variance_amount', 15, 2)->default(0);
            $table->decimal('variance_percentage', 8, 2)->default(0);
            $table->enum('variance_type', ['favorable', 'unfavorable'])->nullable();
            $table->decimal('quantity', 12, 6)->default(0);
            $table->decimal('unit_cost', 15, 4)->default(0);
            $table->date('cost_date');
            $table->string('reference_number', 100)->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'cost_type']);
            $table->index(['production_order_id']);
            $table->index(['cost_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_costs');
    }
};
