<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            $table->decimal('committed_stock', 15, 6)->default(0)->after('available_stock');
            $table->decimal('on_order_stock', 15, 6)->default(0)->after('committed_stock');
            $table->decimal('standard_cost', 15, 4)->default(0)->after('last_cost');
            $table->decimal('fifo_cost', 15, 4)->default(0)->after('standard_cost');
            $table->decimal('lifo_cost', 15, 4)->default(0)->after('fifo_cost');
            $table->enum('valuation_method', ['fifo', 'lifo', 'average', 'standard'])->default('average')->after('lifo_cost');
            $table->enum('abc_classification', ['A', 'B', 'C', 'D'])->nullable()->after('cycle_count_due_date');
            $table->decimal('safety_stock', 15, 6)->default(0)->after('abc_classification');
            $table->decimal('max_stock', 15, 6)->default(0)->after('safety_stock');
            $table->decimal('reorder_point', 15, 6)->default(0)->after('max_stock');

            $table->index(['valuation_method']);
            $table->index(['abc_classification']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            $table->dropIndex(['valuation_method']);
            $table->dropIndex(['abc_classification']);

            $table->dropColumn([
                'committed_stock',
                'on_order_stock',
                'standard_cost',
                'fifo_cost',
                'lifo_cost',
                'valuation_method',
                'abc_classification',
                'safety_stock',
                'max_stock',
                'reorder_point',
            ]);
        });
    }
};
