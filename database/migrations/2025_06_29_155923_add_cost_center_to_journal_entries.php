<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('journal_entries', function (Blueprint $table) {
            $table->foreignId('cost_center_id')->nullable()->after('account_id')->constrained()->onDelete('set null');
            $table->foreignId('department_id')->nullable()->after('cost_center_id')->constrained()->onDelete('set null');

            $table->index(['cost_center_id']);
            $table->index(['department_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('journal_entries', function (Blueprint $table) {
            $table->dropForeign(['cost_center_id']);
            $table->dropForeign(['department_id']);
            $table->dropIndex(['cost_center_id']);
            $table->dropIndex(['department_id']);
            $table->dropColumn(['cost_center_id', 'department_id']);
        });
    }
};
