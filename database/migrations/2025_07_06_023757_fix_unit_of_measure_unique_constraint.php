<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unit_of_measures', function (Blueprint $table) {
            // Drop the existing unique constraint on unit_code
            $table->dropUnique(['unit_code']);

            // Add composite unique constraint for unit_code + client_id
            $table->unique(['client_id', 'unit_code'], 'unit_of_measures_client_unit_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_of_measures', function (Blueprint $table) {
            // Drop the composite unique constraint
            $table->dropUnique('unit_of_measures_client_unit_unique');

            // Restore the original unique constraint on unit_code
            $table->unique('unit_code');
        });
    }
};
