<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->string('account_category', 50)->nullable()->after('account_type');
            $table->string('account_subcategory', 50)->nullable()->after('account_category');
            $table->integer('level')->default(0)->after('parent_account_id');
            $table->integer('sort_order')->default(0)->after('level');
            $table->text('description')->nullable()->after('sort_order');
            $table->boolean('is_system')->default(false)->after('is_active');
            $table->boolean('is_header')->default(false)->after('is_system');
            $table->boolean('is_cash_account')->default(false)->after('is_header');
            $table->boolean('is_bank_account')->default(false)->after('is_cash_account');
            $table->boolean('is_control_account')->default(false)->after('is_bank_account');
            $table->string('tax_code', 20)->nullable()->after('is_control_account');

            $table->index(['account_category']);
            $table->index(['account_subcategory']);
            $table->index(['level']);
            $table->index(['sort_order']);
            $table->index(['is_header']);
            $table->index(['is_cash_account']);
            $table->index(['is_bank_account']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropIndex(['account_category']);
            $table->dropIndex(['account_subcategory']);
            $table->dropIndex(['level']);
            $table->dropIndex(['sort_order']);
            $table->dropIndex(['is_header']);
            $table->dropIndex(['is_cash_account']);
            $table->dropIndex(['is_bank_account']);

            $table->dropColumn([
                'account_category',
                'account_subcategory',
                'level',
                'sort_order',
                'description',
                'is_system',
                'is_header',
                'is_cash_account',
                'is_bank_account',
                'is_control_account',
                'tax_code',
            ]);
        });
    }
};
