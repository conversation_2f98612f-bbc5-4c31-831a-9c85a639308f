<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_workflow_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_workflow_id')->constrained()->onDelete('cascade');
            $table->integer('step_order');
            $table->string('step_name', 100);
            $table->text('step_description')->nullable();
            $table->enum('approver_type', ['user', 'role', 'department', 'dynamic'])->default('user');
            $table->foreignId('approver_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('approver_role')->nullable(); // Role name
            $table->foreignId('approver_department_id')->nullable()->constrained('departments')->onDelete('set null');
            $table->string('dynamic_approver_field')->nullable(); // Field name for dynamic approval
            $table->boolean('is_required')->default(true);
            $table->boolean('can_delegate')->default(false);
            $table->boolean('can_skip')->default(false);
            $table->integer('timeout_hours')->nullable(); // Auto-approve after timeout
            $table->decimal('min_amount', 15, 2)->nullable();
            $table->decimal('max_amount', 15, 2)->nullable();
            $table->json('conditions')->nullable(); // Additional conditions
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['approval_workflow_id', 'step_order']);
            $table->index(['approver_type']);
            $table->index(['approver_user_id']);
            $table->index(['approver_department_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_workflow_steps');
    }
};
