<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add POS fields to products table
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'pos_product_id')) {
                $table->string('pos_product_id')->nullable()->after('product_code');
                $table->timestamp('pos_synced_at')->nullable();
                $table->index('pos_product_id');
            }
        });

        // Add POS fields to customers table
        Schema::table('customers', function (Blueprint $table) {
            if (!Schema::hasColumn('customers', 'pos_customer_id')) {
                $table->string('pos_customer_id')->nullable()->after('customer_code');
                $table->timestamp('pos_synced_at')->nullable();
                $table->index('pos_customer_id');
            }
        });

        // sales_orders table removed - no longer needed
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['pos_product_id']);
            $table->dropColumn(['pos_product_id', 'pos_synced_at']);
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex(['pos_customer_id']);
            $table->dropColumn(['pos_customer_id', 'pos_synced_at']);
        });

        // sales_orders table removed - no longer needed
    }
};
