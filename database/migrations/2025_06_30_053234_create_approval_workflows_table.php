<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_workflows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->string('workflow_name', 100);
            $table->string('workflow_code', 50)->unique();
            $table->text('description')->nullable();
            $table->string('approvable_type'); // Model class that can be approved
            $table->json('conditions')->nullable(); // Conditions for when this workflow applies
            $table->boolean('is_active')->default(true);
            $table->integer('max_amount')->nullable(); // Maximum amount for this workflow
            $table->boolean('is_sequential')->default(true); // Sequential or parallel approval
            $table->boolean('allow_self_approval')->default(false);
            $table->boolean('auto_approve_below_limit')->default(false);
            $table->decimal('auto_approve_limit', 15, 2)->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'is_active']);
            $table->index(['approvable_type']);
            $table->index(['workflow_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_workflows');
    }
};
