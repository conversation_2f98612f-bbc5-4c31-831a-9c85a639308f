<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->decimal('purchase_price', 15, 2)->default(0)->after('selling_price');
            $table->decimal('tax_rate', 5, 4)->default(0)->after('dimensions');
            $table->decimal('discount_rate', 5, 4)->default(0)->after('tax_rate');
            $table->decimal('commission_rate', 5, 4)->default(0)->after('discount_rate');
            $table->integer('warranty_period')->default(0)->after('commission_rate');
            $table->string('brand', 100)->nullable()->after('warranty_period');
            $table->string('model', 100)->nullable()->after('brand');
            $table->string('color', 50)->nullable()->after('model');
            $table->string('size', 50)->nullable()->after('color');
            $table->string('material', 100)->nullable()->after('size');
            $table->string('origin_country', 50)->default('Indonesia')->after('material');
            $table->string('hs_code', 20)->nullable()->after('origin_country');
            $table->string('image_url')->nullable()->after('hs_code');
            $table->boolean('is_serialized')->default(false)->after('is_manufactured');
            $table->boolean('is_batch_tracked')->default(false)->after('is_serialized');
            $table->boolean('is_expirable')->default(false)->after('is_batch_tracked');

            $table->index(['brand']);
            $table->index(['is_serialized']);
            $table->index(['is_batch_tracked']);
            $table->index(['is_expirable']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['brand']);
            $table->dropIndex(['is_serialized']);
            $table->dropIndex(['is_batch_tracked']);
            $table->dropIndex(['is_expirable']);

            $table->dropColumn([
                'purchase_price',
                'tax_rate',
                'discount_rate',
                'commission_rate',
                'warranty_period',
                'brand',
                'model',
                'color',
                'size',
                'material',
                'origin_country',
                'hs_code',
                'image_url',
                'is_serialized',
                'is_batch_tracked',
                'is_expirable',
            ]);
        });
    }
};
