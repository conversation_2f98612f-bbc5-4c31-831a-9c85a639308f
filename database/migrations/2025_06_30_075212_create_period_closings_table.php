<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('period_closings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->integer('year');
            $table->integer('month');
            $table->date('period_start');
            $table->date('period_end');
            $table->enum('status', ['open', 'in_progress', 'closed', 'reopened'])->default('open');
            $table->json('checklist_items'); // JSON array of checklist items with status
            $table->json('validation_results')->nullable(); // JSON array of validation results
            $table->decimal('total_debits', 20, 2)->default(0);
            $table->decimal('total_credits', 20, 2)->default(0);
            $table->boolean('is_balanced')->default(false);
            $table->text('closing_notes')->nullable();
            $table->foreignId('closed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('closed_at')->nullable();
            $table->foreignId('reopened_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reopened_at')->nullable();
            $table->text('reopen_reason')->nullable();
            $table->timestamps();

            $table->unique(['client_id', 'year', 'month']);
            $table->index(['client_id', 'status']);
            $table->index(['year', 'month']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('period_closings');
    }
};
