<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_categories', function (Blueprint $table) {
            // Drop the existing unique constraint on category_code
            $table->dropUnique(['category_code']);

            // Add composite unique constraint for category_code + client_id
            $table->unique(['client_id', 'category_code'], 'product_categories_client_category_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_categories', function (Blueprint $table) {
            // Drop the composite unique constraint
            $table->dropUnique('product_categories_client_category_unique');

            // Restore the original unique constraint on category_code
            $table->unique('category_code');
        });
    }
};
