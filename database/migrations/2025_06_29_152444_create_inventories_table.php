<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('location_id')->nullable()->constrained('locations')->onDelete('set null');
            $table->decimal('current_stock', 12, 6)->default(0);
            $table->decimal('reserved_stock', 12, 6)->default(0);
            $table->decimal('available_stock', 12, 6)->default(0);
            $table->decimal('average_cost', 15, 4)->default(0);
            $table->decimal('last_cost', 15, 4)->default(0);
            $table->decimal('total_value', 15, 2)->default(0);
            $table->timestamp('last_movement_date')->nullable();
            $table->timestamp('last_count_date')->nullable();
            $table->date('cycle_count_due_date')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['client_id', 'product_id', 'location_id']);
            $table->index(['client_id', 'current_stock']);
            $table->index(['cycle_count_due_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventories');
    }
};
