<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bill_of_materials', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('bom_code', 50)->unique();
            $table->string('version', 20)->default('1.0');
            $table->text('description')->nullable();
            $table->decimal('quantity_produced', 12, 2)->default(1);
            $table->decimal('labor_cost_per_unit', 15, 2)->default(0);
            $table->decimal('overhead_cost_per_unit', 15, 2)->default(0);
            $table->integer('setup_time_minutes')->default(0);
            $table->integer('production_time_minutes')->default(0);
            $table->boolean('is_active')->default(true);
            $table->date('effective_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['client_id', 'is_active']);
            $table->index(['product_id']);
            $table->index(['effective_date', 'expiry_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bill_of_materials');
    }
};
