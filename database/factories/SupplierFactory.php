<?php

namespace Database\Factories;

use App\Models\Supplier;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

class SupplierFactory extends Factory
{
    protected $model = Supplier::class;

    public function definition(): array
    {
        $supplierTypes = ['material', 'service', 'both'];

        return [
            'client_id' => Client::factory(),
            'supplier_code' => 'SUPP-' . $this->faker->unique()->numerify('###'),
            'supplier_name' => $this->faker->company(),
            'supplier_type' => $this->faker->randomElement($supplierTypes),
            'contact_person' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'postal_code' => $this->faker->postcode(),
            'country' => 'Indonesia',
            'tax_number' => $this->faker->optional()->numerify('##.###.###.#-###.###'),
            'payment_terms' => $this->faker->randomElement([7, 14, 21, 30, 45, 60]),
            'credit_limit' => $this->faker->numberBetween(10000000, *********),
            'currency_code' => 'IDR',
            'is_active' => true,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => ['is_active' => false]);
    }
}
