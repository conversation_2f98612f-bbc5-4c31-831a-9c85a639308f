<?php

namespace Database\Factories;

use App\Models\WorkOrder;
use App\Models\Client;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkOrderFactory extends Factory
{
    protected $model = WorkOrder::class;

    public function definition(): array
    {
        $plannedStartDate = $this->faker->dateTimeBetween('-30 days', '+30 days');
        $plannedEndDate = $this->faker->dateTimeBetween($plannedStartDate, '+60 days');
        $quantityToProcess = $this->faker->numberBetween(10, 1000);
        $totalCost = $this->faker->numberBetween(50000, 500000);

        return [
            'client_id' => Client::factory(),
            'production_order_id' => 1, // Will be created by seeder
            'unit_id' => 1, // Will be created by seeder
            'wo_number' => 'WO-' . $plannedStartDate->format('Ymd') . '-' . $this->faker->unique()->numerify('###'),
            'operation_sequence' => $this->faker->numberBetween(1, 10),
            'operation_name' => $this->faker->randomElement(['Cutting', 'Assembly', 'Welding', 'Painting', 'Quality Check', 'Packaging']),
            'description' => $this->faker->optional()->sentence(),
            'quantity_to_process' => $quantityToProcess,
            'quantity_completed' => $this->faker->numberBetween(0, $quantityToProcess),
            'quantity_scrapped' => $this->faker->numberBetween(0, intval($quantityToProcess * 0.1)),
            'status' => $this->faker->randomElement(['pending', 'in_progress', 'completed', 'cancelled']),
            'planned_start_date' => $plannedStartDate,
            'planned_end_date' => $plannedEndDate,
            'actual_start_date' => $this->faker->optional()->dateTimeBetween($plannedStartDate, $plannedEndDate),
            'actual_end_date' => $this->faker->optional()->dateTimeBetween($plannedStartDate, $plannedEndDate),
            'setup_time_planned' => $this->faker->randomFloat(2, 0.5, 5),
            'setup_time_actual' => $this->faker->randomFloat(2, 0.5, 6),
            'run_time_planned' => $this->faker->randomFloat(2, 1, 20),
            'run_time_actual' => $this->faker->randomFloat(2, 1, 25),
            'labor_hours_planned' => $this->faker->randomFloat(2, 1, 40),
            'labor_hours_actual' => $this->faker->randomFloat(2, 1, 45),
            'machine_hours_planned' => $this->faker->randomFloat(2, 1, 30),
            'machine_hours_actual' => $this->faker->randomFloat(2, 1, 35),
            'labor_cost' => $this->faker->numberBetween(100000, 1000000),
            'machine_cost' => $this->faker->numberBetween(50000, 500000),
            'overhead_cost' => $this->faker->numberBetween(25000, 250000),
            'total_cost' => $totalCost,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    public function completed(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'completed',
            'actual_start_date' => $attributes['planned_start_date'],
            'actual_end_date' => $attributes['planned_end_date'],
            'quantity_completed' => $attributes['quantity_to_process'],
        ]);
    }

    public function inProgress(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'in_progress',
            'actual_start_date' => $attributes['planned_start_date'],
        ]);
    }

    public function planned(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'planned',
        ]);
    }
}
