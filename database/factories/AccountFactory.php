<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Account>
 */
class AccountFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Account::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $accountTypes = ['asset', 'liability', 'equity', 'revenue', 'expense'];
        $accountType = $this->faker->randomElement($accountTypes);
        
        return [
            'client_id' => Client::factory(),
            'account_code' => $this->generateAccountCode($accountType),
            'account_name' => $this->generateAccountName($accountType),
            'account_type' => $accountType,
            'parent_account_id' => null,
            'is_active' => true,
            'description' => $this->faker->optional()->sentence(),
            'normal_balance' => $this->getNormalBalance($accountType),
        ];
    }

    /**
     * Generate account code based on type.
     */
    private function generateAccountCode(string $type): string
    {
        $prefixes = [
            'asset' => '1',
            'liability' => '2',
            'equity' => '3',
            'revenue' => '4',
            'expense' => '5',
        ];

        $prefix = $prefixes[$type] ?? '1';
        $number = $this->faker->unique()->numberBetween(111, 999);
        
        return $prefix . $number;
    }

    /**
     * Generate account name based on type.
     */
    private function generateAccountName(string $type): string
    {
        $names = [
            'asset' => [
                'Kas', 'Bank', 'Piutang Usaha', 'Persediaan', 'Aset Tetap',
                'Peralatan', 'Kendaraan', 'Gedung', 'Tanah', 'Investasi'
            ],
            'liability' => [
                'Hutang Usaha', 'Hutang Bank', 'Hutang Pajak', 'Hutang Gaji',
                'Hutang Jangka Panjang', 'Hutang Bunga', 'Hutang Lainnya'
            ],
            'equity' => [
                'Modal', 'Laba Ditahan', 'Modal Disetor', 'Cadangan',
                'Saldo Laba', 'Modal Pemilik'
            ],
            'revenue' => [
                'Pendapatan Penjualan', 'Pendapatan Jasa', 'Pendapatan Bunga',
                'Pendapatan Lainnya', 'Pendapatan Sewa'
            ],
            'expense' => [
                'Beban Gaji', 'Beban Listrik', 'Beban Telepon', 'Beban Sewa',
                'Beban Transportasi', 'Beban Operasional', 'Beban Bunga',
                'Harga Pokok Penjualan'
            ],
        ];

        return $this->faker->randomElement($names[$type] ?? $names['asset']);
    }

    /**
     * Get normal balance for account type.
     */
    private function getNormalBalance(string $type): string
    {
        return in_array($type, ['asset', 'expense']) ? 'debit' : 'credit';
    }

    /**
     * Indicate that the account is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create an account with specific type.
     */
    public function ofType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'account_type' => $type,
            'account_code' => $this->generateAccountCode($type),
            'account_name' => $this->generateAccountName($type),
            'normal_balance' => $this->getNormalBalance($type),
        ]);
    }
}
