<?php

namespace Database\Factories;

use App\Models\PurchaseOrder;
use App\Models\Client;
use App\Models\Supplier;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PurchaseOrderFactory extends Factory
{
    protected $model = PurchaseOrder::class;

    public function definition(): array
    {
        $orderDate = $this->faker->dateTimeBetween('-6 months', 'now');
        $subtotal = $this->faker->numberBetween(5000000, *********);
        $discountPercentage = $this->faker->randomFloat(2, 0, 10);
        $discountAmount = $subtotal * ($discountPercentage / 100);
        $taxPercentage = 11; // PPN 11%
        $taxAmount = ($subtotal - $discountAmount) * ($taxPercentage / 100);
        $totalAmount = $subtotal - $discountAmount + $taxAmount;

        return [
            'client_id' => Client::factory(),
            'supplier_id' => Supplier::factory(),
            'purchaser_id' => User::factory(),
            'order_number' => 'PO-' . date('Ymd', strtotime($orderDate)) . '-' . $this->faker->unique()->numerify('###'),
            'order_date' => $orderDate,
            'expected_delivery_date' => $this->faker->dateTimeBetween($orderDate, '+45 days'),
            'status' => $this->faker->randomElement(['draft', 'sent', 'confirmed', 'partial_received', 'completed', 'cancelled']),
            'subtotal' => $subtotal,
            'discount_percentage' => $discountPercentage,
            'discount_amount' => $discountAmount,
            'tax_percentage' => $taxPercentage,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
            'notes' => $this->faker->optional()->paragraph(),
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }
}
