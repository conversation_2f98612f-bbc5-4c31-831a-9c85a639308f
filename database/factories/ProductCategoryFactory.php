<?php

namespace Database\Factories;

use App\Models\ProductCategory;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductCategory>
 */
class ProductCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Electronics', 'Computers', 'Mobile Phones', 'Accessories',
            'Furniture', 'Office Supplies', 'Stationery', 'Books',
            'Clothing', 'Shoes', 'Bags', 'Watches',
            'Home Appliances', 'Kitchen', 'Bathroom', 'Garden',
            'Sports', 'Automotive', 'Tools', 'Hardware'
        ];

        return [
            'client_id' => Client::factory(),
            'parent_id' => null,
            'category_code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{3}'),
            'category_name' => $this->faker->unique()->randomElement($categories),
            'description' => $this->faker->optional()->sentence(),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the category has a parent.
     */
    public function withParent(ProductCategory $parent): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parent->id,
            'client_id' => $parent->client_id,
        ]);
    }
}
