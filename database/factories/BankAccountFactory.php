<?php

namespace Database\Factories;

use App\Models\BankAccount;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

class BankAccountFactory extends Factory
{
    protected $model = BankAccount::class;

    public function definition(): array
    {
        $banks = [
            'Bank Mandiri',
            'Bank BCA',
            'Bank BNI',
            'Bank BRI',
            'Bank CIMB Niaga',
            'Bank Danamon',
            'Bank Permata',
            'Bank Maybank',
        ];

        return [
            'client_id' => Client::factory(),
            'account_name' => $this->faker->company() . ' - ' . $this->faker->randomElement(['Checking', 'Savings', 'Investment']),
            'account_number' => $this->faker->unique()->numerify('##########'),
            'bank_name' => $this->faker->randomElement($banks),
            'branch_name' => $this->faker->city(),
            'account_type' => $this->faker->randomElement(['checking', 'savings', 'credit_line', 'investment']),
            'currency_code' => 'IDR',
            'opening_balance' => $this->faker->numberBetween(0, *********),
            'current_balance' => $this->faker->numberBetween(0, *********),
            'available_balance' => $this->faker->numberBetween(0, *********),
            'is_active' => true,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => ['is_active' => false]);
    }

    public function checking(): static
    {
        return $this->state(fn(array $attributes) => [
            'account_type' => 'checking',
            'account_name' => $this->faker->company() . ' - Checking Account',
        ]);
    }

    public function savings(): static
    {
        return $this->state(fn(array $attributes) => [
            'account_type' => 'savings',
            'account_name' => $this->faker->company() . ' - Savings Account',
        ]);
    }
}
