<?php

namespace Database\Factories;

use App\Models\Journal;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

class JournalFactory extends Factory
{
    protected $model = Journal::class;

    public function definition(): array
    {
        return [
            'client_id' => Client::factory(),
            'journal_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'description' => $this->faker->sentence(),
            'reference_transaction_id' => $this->faker->optional()->numberBetween(1, 1000),
            'is_posted' => $this->faker->boolean(80), // 80% chance of being posted
            'posted_at' => function (array $attributes) {
                return $attributes['is_posted'] ? $this->faker->dateTimeBetween($attributes['journal_date'], 'now') : null;
            },
        ];
    }

    public function posted(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_posted' => true,
            'posted_at' => $this->faker->dateTimeBetween($attributes['journal_date'] ?? '-1 month', 'now'),
        ]);
    }

    public function unposted(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_posted' => false,
            'posted_at' => null,
        ]);
    }
}
