<?php

namespace Database\Factories;

use App\Models\UnitOfMeasure;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

class UnitOfMeasureFactory extends Factory
{
    protected $model = UnitOfMeasure::class;

    public function definition(): array
    {
        $units = [
            ['PCS', 'Pieces'],
            ['KG', 'Kilogram'],
            ['LTR', 'Liter'],
            ['MTR', 'Meter'],
            ['BOX', 'Box'],
            ['SET', 'Set'],
            ['UNIT', 'Unit'],
            ['PACK', 'Pack'],
        ];

        $unit = $this->faker->randomElement($units);

        return [
            'client_id' => Client::factory(),
            'unit_code' => $unit[0] . '-' . $this->faker->unique()->numerify('##'),
            'unit_name' => $unit[1],
            'description' => $this->faker->optional()->sentence(),
            'is_active' => true,
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => ['is_active' => false]);
    }
}
