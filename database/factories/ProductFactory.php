<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Client;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $products = [
            'Laptop ASUS',
            'Laptop HP',
            'Laptop Dell',
            'Laptop Lenovo',
            'Mouse Wireless',
            'Keyboard Mechanical',
            'Monitor LED',
            'Printer Inkjet',
            'Smartphone Samsung',
            'Smartphone iPhone',
            'Tablet iPad',
            'Headphone Sony',
            'Speaker Bluetooth',
            'Charger USB-C',
            'Power Bank',
            'Memory Card',
            'Hard Drive External',
            'SSD Internal',
            'RAM DDR4',
            'Motherboard'
        ];

        $standardCost = $this->faker->numberBetween(100000, ********);
        $sellingPrice = $standardCost * $this->faker->randomFloat(2, 1.2, 2.5); // 20-150% markup

        return [
            'client_id' => Client::factory(),
            'category_id' => ProductCategory::factory(),
            'unit_id' => UnitOfMeasure::factory(),
            'product_code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{4}'),
            'product_name' => $this->faker->randomElement($products),
            'sku' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{4}'),
            'barcode' => $this->faker->optional()->ean13(),
            'description' => $this->faker->optional()->paragraph(),
            'product_type' => $this->faker->randomElement(['raw_material', 'finished_good', 'semi_finished', 'service']),
            'standard_cost' => $standardCost,
            'selling_price' => $sellingPrice,
            'weight' => $this->faker->optional()->randomFloat(2, 0.1, 10),
            'dimensions' => $this->faker->optional()->regexify('[0-9]{2}x[0-9]{2}x[0-9]{2}'),
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
        ];
    }

    /**
     * Indicate that the product is a service.
     */
    public function service(): static
    {
        return $this->state(fn(array $attributes) => [
            'product_type' => 'service',
            'is_stockable' => false,
            'weight' => null,
            'dimensions' => null,
        ]);
    }

    /**
     * Indicate that the product is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the product doesn't track inventory.
     */
    public function noInventoryTracking(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_stockable' => false,
        ]);
    }
}
