<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $customerTypes = ['retail', 'wholesale', 'corporate'];

        return [
            'client_id' => Client::factory(),
            'customer_code' => 'CUST-' . $this->faker->unique()->numerify('###'),
            'customer_name' => $this->faker->company(),
            'customer_type' => $this->faker->randomElement($customerTypes),
            'contact_person' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'postal_code' => $this->faker->postcode(),
            'country' => 'Indonesia',
            'tax_number' => $this->faker->optional()->numerify('##.###.###.#-###.###'),
            'payment_terms' => $this->faker->randomElement([7, 14, 21, 30, 45, 60]),
            'credit_limit' => $this->faker->numberBetween(10000000, 1000000000),
            'currency_code' => 'IDR',
            'is_active' => true,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the customer is an individual.
     */
    public function individual(): static
    {
        return $this->state(fn(array $attributes) => [
            'customer_type' => 'individual',
            'customer_name' => $this->faker->name(),
            'tax_number' => null,
        ]);
    }

    /**
     * Indicate that the customer is corporate.
     */
    public function corporate(): static
    {
        return $this->state(fn(array $attributes) => [
            'customer_type' => 'corporate',
            'customer_name' => 'PT. ' . $this->faker->company(),
            'tax_number' => $this->faker->numerify('##.###.###.#-###.###'),
        ]);
    }

    /**
     * Indicate that the customer is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }
}
