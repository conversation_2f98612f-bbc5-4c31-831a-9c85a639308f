<?php

namespace Database\Factories;

use App\Models\PurchaseOrderItem;
use App\Models\PurchaseOrder;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class PurchaseOrderItemFactory extends Factory
{
    protected $model = PurchaseOrderItem::class;

    public function definition(): array
    {
        $quantityOrdered = $this->faker->numberBetween(10, 100);
        $unitPrice = $this->faker->numberBetween(50000, 2000000);
        $lineTotal = $quantityOrdered * $unitPrice;

        return [
            'purchase_order_id' => PurchaseOrder::factory(),
            'product_id' => Product::factory(),
            'quantity_ordered' => $quantityOrdered,
            'quantity_received' => $this->faker->numberBetween(0, $quantityOrdered),
            'unit_price' => $unitPrice,
            'line_total' => $lineTotal,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    public function fullyReceived(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity_received' => $attributes['quantity_ordered'],
            ];
        });
    }

    public function notReceived(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_received' => 0,
        ]);
    }
}
