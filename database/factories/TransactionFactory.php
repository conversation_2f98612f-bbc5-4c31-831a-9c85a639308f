<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Transaction>
 */
class TransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'client_id' => 1, // Will be overridden in tests
            'transaction_date' => $this->faker->date(),
            'type' => $this->faker->randomElement(['sales', 'purchase', 'expense', 'asset_acquisition']),
            'description' => $this->faker->sentence(),
            'amount' => $this->faker->randomFloat(2, 10000, 1000000),
            'reference_number' => $this->faker->unique()->numerify('TXN-#####'),
            'status' => $this->faker->randomElement(['pending', 'processed', 'completed', 'rejected']),
            'created_by' => 1, // Will be overridden in tests
            'customer_id' => null,
            'payment_method' => null,
            'payment_status' => 'unpaid',
            'tax_amount' => 0,
            'discount_amount' => 0,
            'subtotal' => 0,
            'notes' => $this->faker->optional()->sentence(),
        ];
    }
}
