<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Electronics', 'Computers', 'Mobile Phones', 'Accessories',
            'Furniture', 'Office Supplies', 'Stationery', 'Books',
            'Clothing', 'Shoes', 'Bags', 'Watches',
            'Home Appliances', 'Kitchen', 'Bathroom', 'Garden',
            'Sports', 'Automotive', 'Tools', 'Hardware'
        ];

        return [
            'client_id' => Client::factory(),
            'category_name' => $this->faker->unique()->randomElement($categories),
            'description' => $this->faker->optional()->sentence(),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the category is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
