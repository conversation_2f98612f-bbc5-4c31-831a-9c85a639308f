<?php

namespace Database\Factories;

use App\Models\JournalEntry;
use App\Models\Journal;
use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

class JournalEntryFactory extends Factory
{
    protected $model = JournalEntry::class;

    public function definition(): array
    {
        $amount = $this->faker->numberBetween(100000, ********);
        $isDebit = $this->faker->boolean();

        return [
            'journal_id' => Journal::factory(),
            'account_id' => Account::factory(),
            'debit' => $isDebit ? $amount : 0,
            'credit' => $isDebit ? 0 : $amount,
            'description' => $this->faker->sentence(),
        ];
    }

    public function debit(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'debit' => $amount,
            'credit' => 0,
        ]);
    }

    public function credit(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'debit' => 0,
            'credit' => $amount,
        ]);
    }
}
