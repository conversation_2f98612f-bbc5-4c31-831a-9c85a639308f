<?php

namespace Database\Factories;

use App\Models\Inventory;
use App\Models\Client;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class InventoryFactory extends Factory
{
    protected $model = Inventory::class;

    public function definition(): array
    {
        $currentStock = $this->faker->numberBetween(0, 1000);
        $averageCost = $this->faker->numberBetween(50000, 5000000);
        $reservedStock = $this->faker->numberBetween(0, intval($currentStock * 0.1));

        return [
            'client_id' => Client::factory(),
            'product_id' => Product::factory(),
            'current_stock' => $currentStock,
            'reserved_stock' => $reservedStock,
            'available_stock' => $currentStock - $reservedStock,
            'average_cost' => $averageCost,
            'last_cost' => $averageCost * $this->faker->randomFloat(2, 0.8, 1.2),
            'total_value' => $currentStock * $averageCost,
            'last_movement_date' => $this->faker->optional()->dateTimeBetween('-30 days', 'now'),
            'last_count_date' => $this->faker->optional()->dateTimeBetween('-90 days', 'now'),
            'cycle_count_due_date' => $this->faker->optional()->dateTimeBetween('now', '+30 days'),
        ];
    }

    public function outOfStock(): static
    {
        return $this->state(fn(array $attributes) => [
            'current_stock' => 0,
        ]);
    }

    public function lowStock(): static
    {
        return $this->state(function (array $attributes) {
            $lowStock = $this->faker->numberBetween(1, 10);
            return [
                'current_stock' => $lowStock,
                'available_stock' => $lowStock,
                'total_value' => $lowStock * ($attributes['average_cost'] ?? 100000),
            ];
        });
    }
}
