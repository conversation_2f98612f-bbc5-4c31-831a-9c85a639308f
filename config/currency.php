<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Currency Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for multi-currency support
    |
    */

    'base_currency' => env('CURRENCY_BASE', 'IDR'),

    'default_provider' => env('CURRENCY_PROVIDER', 'currencylayer'),

    'providers' => [
        'currencylayer' => [
            'name' => 'CurrencyLayer',
            'url' => 'http://api.currencylayer.com/live',
            'api_key' => env('CURRENCYLAYER_API_KEY'),
            'timeout' => 30,
        ],

        'fixer' => [
            'name' => 'Fixer.io',
            'url' => 'http://data.fixer.io/api/latest',
            'api_key' => env('FIXER_API_KEY'),
            'timeout' => 30,
        ],

        'exchangerate' => [
            'name' => 'ExchangeRate-API',
            'url' => 'https://v6.exchangerate-api.com/v6',
            'api_key' => env('EXCHANGERATE_API_KEY'),
            'timeout' => 30,
        ],

        'bank_indonesia' => [
            'name' => 'Bank Indonesia',
            'url' => 'https://api.bi.go.id/SDDS/exchange_rate',
            'api_key' => env('BI_API_KEY'),
            'timeout' => 30,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    */

    'supported_currencies' => [
        'IDR' => ['name' => 'Rupiah Indonesia', 'symbol' => 'Rp', 'decimal_places' => 0],
        'USD' => ['name' => 'Dolar Amerika', 'symbol' => '$', 'decimal_places' => 2],
        'EUR' => ['name' => 'Euro', 'symbol' => '€', 'decimal_places' => 2],
        'GBP' => ['name' => 'Pound Inggris', 'symbol' => '£', 'decimal_places' => 2],
        'JPY' => ['name' => 'Yen Jepang', 'symbol' => '¥', 'decimal_places' => 0],
        'AUD' => ['name' => 'Dolar Australia', 'symbol' => 'A$', 'decimal_places' => 2],
        'CAD' => ['name' => 'Dolar Kanada', 'symbol' => 'C$', 'decimal_places' => 2],
        'CHF' => ['name' => 'Franc Swiss', 'symbol' => 'CHF', 'decimal_places' => 2],
        'CNY' => ['name' => 'Yuan China', 'symbol' => '¥', 'decimal_places' => 2],
        'SGD' => ['name' => 'Dolar Singapura', 'symbol' => 'S$', 'decimal_places' => 2],
        'MYR' => ['name' => 'Ringgit Malaysia', 'symbol' => 'RM', 'decimal_places' => 2],
        'THB' => ['name' => 'Baht Thailand', 'symbol' => '฿', 'decimal_places' => 2],
        'PHP' => ['name' => 'Peso Filipina', 'symbol' => '₱', 'decimal_places' => 2],
        'VND' => ['name' => 'Dong Vietnam', 'symbol' => '₫', 'decimal_places' => 0],
        'KRW' => ['name' => 'Won Korea Selatan', 'symbol' => '₩', 'decimal_places' => 0],
        'INR' => ['name' => 'Rupee India', 'symbol' => '₹', 'decimal_places' => 2],
    ],

    /*
    |--------------------------------------------------------------------------
    | Exchange Rate Configuration
    |--------------------------------------------------------------------------
    */

    'exchange_rates' => [
        'auto_update' => env('CURRENCY_AUTO_UPDATE', false),
        'update_frequency' => env('CURRENCY_UPDATE_FREQUENCY', 'daily'), // hourly, daily, weekly
        'cache_duration_hours' => env('CURRENCY_CACHE_DURATION', 1),
        'retry_attempts' => env('CURRENCY_RETRY_ATTEMPTS', 3),
        'retry_delay_seconds' => env('CURRENCY_RETRY_DELAY', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Revaluation Configuration
    |--------------------------------------------------------------------------
    */

    'revaluation' => [
        'auto_revaluation' => env('CURRENCY_AUTO_REVALUATION', false),
        'revaluation_schedule' => env('CURRENCY_REVALUATION_SCHEDULE', 'monthly'), // daily, weekly, monthly, quarterly
        'minimum_variance_threshold' => env('CURRENCY_MIN_VARIANCE_THRESHOLD', 0.01),
        'revaluation_account_code' => env('CURRENCY_REVALUATION_ACCOUNT', '7100'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Display Configuration
    |--------------------------------------------------------------------------
    */

    'display' => [
        'default_decimal_places' => env('CURRENCY_DEFAULT_DECIMAL_PLACES', 2),
        'thousand_separator' => env('CURRENCY_THOUSAND_SEPARATOR', ','),
        'decimal_separator' => env('CURRENCY_DECIMAL_SEPARATOR', '.'),
        'symbol_position' => env('CURRENCY_SYMBOL_POSITION', 'before'), // before, after
    ],
];
