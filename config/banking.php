<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Banking Integration Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for bank API integrations
    |
    */

    'providers' => [
        'BCA' => [
            'name' => 'Bank Central Asia',
            'base_url' => env('BCA_API_BASE_URL', 'https://sandbox.bca.co.id'),
            'balance_endpoint' => '/banking/v3/corporates/{corporate_id}/accounts/{account_number}',
            'transactions_endpoint' => '/banking/v3/corporates/{corporate_id}/accounts/{account_number}/statements',
            'auth_endpoint' => '/api/oauth/token',
            'client_id' => env('BCA_CLIENT_ID'),
            'client_secret' => env('BCA_CLIENT_SECRET'),
            'timeout' => 30,
        ],

        'MANDIRI' => [
            'name' => 'Bank Mandiri',
            'base_url' => env('MANDIRI_API_BASE_URL', 'https://developers.bankmandiri.co.id'),
            'balance_endpoint' => '/openapi/v1.0/account-inquiry',
            'transactions_endpoint' => '/openapi/v1.0/account-statement',
            'auth_endpoint' => '/openapi/v1.0/access-token/b2b',
            'client_id' => env('MANDIRI_CLIENT_ID'),
            'client_secret' => env('MANDIRI_CLIENT_SECRET'),
            'timeout' => 30,
        ],

        'BNI' => [
            'name' => 'Bank Negara Indonesia',
            'base_url' => env('BNI_API_BASE_URL', 'https://apibni.bni.co.id'),
            'balance_endpoint' => '/api/account-inquiry',
            'transactions_endpoint' => '/api/account-statement',
            'auth_endpoint' => '/api/oauth/token',
            'client_id' => env('BNI_CLIENT_ID'),
            'client_secret' => env('BNI_CLIENT_SECRET'),
            'timeout' => 30,
        ],

        'BRI' => [
            'name' => 'Bank Rakyat Indonesia',
            'base_url' => env('BRI_API_BASE_URL', 'https://partner.bri.co.id'),
            'balance_endpoint' => '/v1/account/inquiry',
            'transactions_endpoint' => '/v1/account/statement',
            'auth_endpoint' => '/oauth/client_credential/accesstoken',
            'client_id' => env('BRI_CLIENT_ID'),
            'client_secret' => env('BRI_CLIENT_SECRET'),
            'timeout' => 30,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Sync Configuration
    |--------------------------------------------------------------------------
    */

    'sync' => [
        'auto_sync_enabled' => env('BANK_AUTO_SYNC_ENABLED', false),
        'sync_interval_minutes' => env('BANK_SYNC_INTERVAL_MINUTES', 60),
        'max_sync_days' => env('BANK_MAX_SYNC_DAYS', 30),
        'retry_attempts' => env('BANK_RETRY_ATTEMPTS', 3),
        'retry_delay_seconds' => env('BANK_RETRY_DELAY_SECONDS', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Reconciliation Configuration
    |--------------------------------------------------------------------------
    */

    'reconciliation' => [
        'auto_match_enabled' => env('BANK_AUTO_MATCH_ENABLED', true),
        'match_tolerance_amount' => env('BANK_MATCH_TOLERANCE_AMOUNT', 0.01),
        'match_tolerance_days' => env('BANK_MATCH_TOLERANCE_DAYS', 2),
        'require_manual_approval' => env('BANK_REQUIRE_MANUAL_APPROVAL', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */

    'security' => [
        'encrypt_credentials' => env('BANK_ENCRYPT_CREDENTIALS', true),
        'token_refresh_threshold_minutes' => env('BANK_TOKEN_REFRESH_THRESHOLD_MINUTES', 30),
        'max_failed_attempts' => env('BANK_MAX_FAILED_ATTEMPTS', 5),
        'lockout_duration_minutes' => env('BANK_LOCKOUT_DURATION_MINUTES', 60),
    ],
];
