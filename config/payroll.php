<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payroll Integration Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for payroll system integrations
    |
    */

    'default_provider' => env('PAYROLL_DEFAULT_PROVIDER', 'talenta'),

    'providers' => [
        'talenta' => [
            'name' => 'Talenta',
            'base_url' => env('TALENTA_BASE_URL', 'https://api.talenta.co'),
            'api_key' => env('TALENTA_API_KEY'),
            'timeout' => env('TALENTA_TIMEOUT', 30),
            'retry_attempts' => env('TALENTA_RETRY_ATTEMPTS', 3),
        ],

        'gadjian' => [
            'name' => 'Gadjian',
            'base_url' => env('GADJIAN_BASE_URL', 'https://api.gadjian.com'),
            'api_key' => env('GADJIAN_API_KEY'),
            'timeout' => env('GADJIAN_TIMEOUT', 30),
            'retry_attempts' => env('GADJIAN_RETRY_ATTEMPTS', 3),
        ],

        'sleekr' => [
            'name' => 'Sleekr',
            'base_url' => env('SLEEKR_BASE_URL', 'https://api.sleekr.co'),
            'api_key' => env('SLEEKR_API_KEY'),
            'timeout' => env('SLEEKR_TIMEOUT', 30),
            'retry_attempts' => env('SLEEKR_RETRY_ATTEMPTS', 3),
        ],

        'karyaone' => [
            'name' => 'KaryaONE',
            'base_url' => env('KARYAONE_BASE_URL', 'https://api.karyaone.co.id'),
            'api_key' => env('KARYAONE_API_KEY'),
            'timeout' => env('KARYAONE_TIMEOUT', 30),
            'retry_attempts' => env('KARYAONE_RETRY_ATTEMPTS', 3),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Mapping Configuration
    |--------------------------------------------------------------------------
    */

    'account_mapping' => [
        'salary_expense' => env('PAYROLL_SALARY_EXPENSE_ACCOUNT', '6100'),
        'tax_payable' => env('PAYROLL_TAX_PAYABLE_ACCOUNT', '2300'),
        'social_security_payable' => env('PAYROLL_SOCIAL_SECURITY_PAYABLE_ACCOUNT', '2310'),
        'other_payable' => env('PAYROLL_OTHER_PAYABLE_ACCOUNT', '2320'),
        'cash_account' => env('PAYROLL_CASH_ACCOUNT', '1100'),
        'bank_account' => env('PAYROLL_BANK_ACCOUNT', '1110'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Sync Configuration
    |--------------------------------------------------------------------------
    */

    'sync' => [
        'auto_sync_enabled' => env('PAYROLL_AUTO_SYNC_ENABLED', false),
        'sync_schedule' => env('PAYROLL_SYNC_SCHEDULE', 'monthly'), // daily, weekly, monthly
        'sync_day_of_month' => env('PAYROLL_SYNC_DAY_OF_MONTH', 1),
        'batch_size' => env('PAYROLL_SYNC_BATCH_SIZE', 50),
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Configuration
    |--------------------------------------------------------------------------
    */

    'validation' => [
        'require_approval' => env('PAYROLL_REQUIRE_APPROVAL', true),
        'validate_totals' => env('PAYROLL_VALIDATE_TOTALS', true),
        'check_duplicates' => env('PAYROLL_CHECK_DUPLICATES', true),
        'max_variance_percentage' => env('PAYROLL_MAX_VARIANCE_PERCENTAGE', 5),
    ],
];
