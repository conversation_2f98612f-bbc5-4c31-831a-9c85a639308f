<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Tax Integration Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for tax system integrations
    |
    */

    'default_provider' => env('TAX_DEFAULT_PROVIDER', 'djp'),

    'providers' => [
        'djp' => [
            'name' => 'Direktorat Jenderal Pajak',
            'base_url' => env('DJP_BASE_URL', 'https://api.pajak.go.id'),
            'api_key' => env('DJP_API_KEY'),
            'timeout' => env('DJP_TIMEOUT', 30),
            'retry_attempts' => env('DJP_RETRY_ATTEMPTS', 3),
        ],

        'klikpajak' => [
            'name' => 'Klikpajak',
            'base_url' => env('KLIKPAJAK_BASE_URL', 'https://api.klikpajak.id'),
            'api_key' => env('KLIKPAJAK_API_KEY'),
            'timeout' => env('KLIKPAJAK_TIMEOUT', 30),
            'retry_attempts' => env('KLIKPAJAK_RETRY_ATTEMPTS', 3),
        ],

        'taxomate' => [
            'name' => 'Taxomate',
            'base_url' => env('TAXOMATE_BASE_URL', 'https://api.taxomate.com'),
            'api_key' => env('TAXOMATE_API_KEY'),
            'timeout' => env('TAXOMATE_TIMEOUT', 30),
            'retry_attempts' => env('TAXOMATE_RETRY_ATTEMPTS', 3),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tax Rates Configuration
    |--------------------------------------------------------------------------
    */

    'rates' => [
        'ppn' => [
            'standard' => env('TAX_PPN_STANDARD_RATE', 11),
            'export' => env('TAX_PPN_EXPORT_RATE', 0),
            'luxury' => env('TAX_PPN_LUXURY_RATE', 11),
        ],
        
        'pph' => [
            'article_21' => env('TAX_PPH_21_RATE', 5),
            'article_22' => env('TAX_PPH_22_RATE', 1.5),
            'article_23' => env('TAX_PPH_23_RATE', 2),
            'article_25' => env('TAX_PPH_25_RATE', 1),
            'final' => env('TAX_PPH_FINAL_RATE', 0.5),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Mapping Configuration
    |--------------------------------------------------------------------------
    */

    'account_mapping' => [
        'ppn_output' => env('TAX_PPN_OUTPUT_ACCOUNT', '2200'),
        'ppn_input' => env('TAX_PPN_INPUT_ACCOUNT', '1300'),
        'pph_receivable' => env('TAX_PPH_RECEIVABLE_ACCOUNT', '1310'),
        'pph_payable' => env('TAX_PPH_PAYABLE_ACCOUNT', '2210'),
        'tax_expense' => env('TAX_EXPENSE_ACCOUNT', '6200'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Reporting Configuration
    |--------------------------------------------------------------------------
    */

    'reporting' => [
        'auto_generate' => env('TAX_AUTO_GENERATE_REPORTS', false),
        'report_schedule' => env('TAX_REPORT_SCHEDULE', 'monthly'), // monthly, quarterly
        'submission_deadline_days' => env('TAX_SUBMISSION_DEADLINE_DAYS', 20),
        'reminder_days_before' => env('TAX_REMINDER_DAYS_BEFORE', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Configuration
    |--------------------------------------------------------------------------
    */

    'validation' => [
        'require_tax_number' => env('TAX_REQUIRE_TAX_NUMBER', true),
        'validate_rates' => env('TAX_VALIDATE_RATES', true),
        'check_thresholds' => env('TAX_CHECK_THRESHOLDS', true),
        'pkp_threshold' => env('TAX_PKP_THRESHOLD', 4800000000), // 4.8 billion IDR
    ],

    /*
    |--------------------------------------------------------------------------
    | E-Faktur Configuration
    |--------------------------------------------------------------------------
    */

    'efaktur' => [
        'enabled' => env('TAX_EFAKTUR_ENABLED', false),
        'prefix' => env('TAX_EFAKTUR_PREFIX', '010'),
        'auto_generate' => env('TAX_EFAKTUR_AUTO_GENERATE', false),
        'batch_size' => env('TAX_EFAKTUR_BATCH_SIZE', 100),
    ],
];
