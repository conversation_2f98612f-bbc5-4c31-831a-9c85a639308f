@php
    $reportData = $this->getFinancialReportData();
@endphp

<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Controls -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON><PERSON></h1>
                    <p class="text-gray-600">{{ $reportData['period_info']['company_name'] }}</p>
                </div>

                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                    <!-- Period Selector -->
                    <select wire:model.live="selectedPeriod" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="current_month"><PERSON>ula<PERSON> Ini</option>
                        <option value="last_month"><PERSON>ula<PERSON></option>
                        <option value="current_year">Tahun Ini</option>
                        <option value="last_year"><PERSON>hun <PERSON></option>
                        <option value="custom">Custom</option>
                    </select>

                    <!-- Report Type Selector -->
                    <select wire:model.live="selectedReport" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="profit_loss">Laba Rugi</option>
                        <option value="balance_sheet">Neraca</option>
                        <option value="cash_flow">Arus Kas</option>
                        <option value="trial_balance">Neraca Saldo</option>
                    </select>

                    <!-- Export Button -->
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        📄 Export PDF
                    </button>
                </div>
            </div>

            @if($selectedPeriod === 'custom')
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Mulai</label>
                        <input type="date" wire:model.live="startDate" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Akhir</label>
                        <input type="date" wire:model.live="endDate" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                </div>
            @endif
        </div>

        <!-- Report Content -->
        <div class="bg-white rounded-lg shadow">
            @if($selectedReport === 'profit_loss')
                <!-- Profit & Loss Statement -->
                <div class="p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-xl font-bold text-gray-900">LAPORAN LABA RUGI</h2>
                        <h3 class="text-lg font-semibold text-gray-700">{{ $reportData['period_info']['company_name'] }}</h3>
                        <p class="text-gray-600">Periode: {{ date('d M Y', strtotime($reportData['period_info']['start_date'])) }} - {{ date('d M Y', strtotime($reportData['period_info']['end_date'])) }}</p>
                    </div>

                    <div class="space-y-6">
                        <!-- Revenue Section -->
                        <div>
                            <h4 class="font-bold text-gray-900 border-b border-gray-300 pb-2 mb-3">PENDAPATAN</h4>
                            @foreach($reportData['profit_loss']['revenue']['details'] as $item)
                                <div class="flex justify-between py-1">
                                    <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                    <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                </div>
                            @endforeach
                            <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                <span>Total Pendapatan</span>
                                <span>Rp {{ number_format($reportData['profit_loss']['revenue']['total'], 0, ',', '.') }}</span>
                            </div>
                        </div>

                        <!-- COGS Section -->
                        @if($reportData['profit_loss']['cogs']['total'] > 0)
                        <div>
                            <h4 class="font-bold text-gray-900 border-b border-gray-300 pb-2 mb-3">HARGA POKOK PENJUALAN</h4>
                            @foreach($reportData['profit_loss']['cogs']['details'] as $item)
                                <div class="flex justify-between py-1">
                                    <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                    <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                </div>
                            @endforeach
                            <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                <span>Total HPP</span>
                                <span>Rp {{ number_format($reportData['profit_loss']['cogs']['total'], 0, ',', '.') }}</span>
                            </div>
                        </div>

                        <!-- Gross Profit -->
                        <div class="bg-blue-50 p-3 rounded">
                            <div class="flex justify-between font-bold text-blue-900">
                                <span>LABA KOTOR</span>
                                <span>Rp {{ number_format($reportData['profit_loss']['gross_profit'], 0, ',', '.') }}</span>
                            </div>
                        </div>
                        @endif

                        <!-- Operating Expenses -->
                        <div>
                            <h4 class="font-bold text-gray-900 border-b border-gray-300 pb-2 mb-3">BIAYA OPERASIONAL</h4>
                            @foreach($reportData['profit_loss']['operating_expenses']['details'] as $item)
                                <div class="flex justify-between py-1">
                                    <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                    <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                </div>
                            @endforeach
                            <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                <span>Total Biaya Operasional</span>
                                <span>Rp {{ number_format($reportData['profit_loss']['operating_expenses']['total'], 0, ',', '.') }}</span>
                            </div>
                        </div>

                        <!-- Operating Profit -->
                        <div class="bg-green-50 p-3 rounded">
                            <div class="flex justify-between font-bold text-green-900">
                                <span>LABA OPERASIONAL</span>
                                <span>Rp {{ number_format($reportData['profit_loss']['operating_profit'], 0, ',', '.') }}</span>
                            </div>
                        </div>

                        <!-- Other Income & Expenses -->
                        @if($reportData['profit_loss']['other_income']['total'] > 0 || $reportData['profit_loss']['other_expenses']['total'] > 0)
                        <div class="space-y-4">
                            @if($reportData['profit_loss']['other_income']['total'] > 0)
                            <div>
                                <h4 class="font-bold text-gray-900 border-b border-gray-300 pb-2 mb-3">PENDAPATAN LAIN-LAIN</h4>
                                @foreach($reportData['profit_loss']['other_income']['details'] as $item)
                                    <div class="flex justify-between py-1">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Pendapatan Lain-lain</span>
                                    <span>Rp {{ number_format($reportData['profit_loss']['other_income']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>
                            @endif

                            @if($reportData['profit_loss']['other_expenses']['total'] > 0)
                            <div>
                                <h4 class="font-bold text-gray-900 border-b border-gray-300 pb-2 mb-3">BIAYA LAIN-LAIN</h4>
                                @foreach($reportData['profit_loss']['other_expenses']['details'] as $item)
                                    <div class="flex justify-between py-1">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Biaya Lain-lain</span>
                                    <span>Rp {{ number_format($reportData['profit_loss']['other_expenses']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>
                            @endif
                        </div>
                        @endif

                        <!-- Net Profit -->
                        <div class="bg-{{ $reportData['profit_loss']['net_profit'] >= 0 ? 'green' : 'red' }}-100 p-4 rounded-lg border-2 border-{{ $reportData['profit_loss']['net_profit'] >= 0 ? 'green' : 'red' }}-300">
                            <div class="flex justify-between text-xl font-bold text-{{ $reportData['profit_loss']['net_profit'] >= 0 ? 'green' : 'red' }}-900">
                                <span>{{ $reportData['profit_loss']['net_profit'] >= 0 ? 'LABA BERSIH' : 'RUGI BERSIH' }}</span>
                                <span>Rp {{ number_format($reportData['profit_loss']['net_profit'], 0, ',', '.') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif($selectedReport === 'balance_sheet')
                <!-- Balance Sheet -->
                <div class="p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-xl font-bold text-gray-900">NERACA</h2>
                        <h3 class="text-lg font-semibold text-gray-700">{{ $reportData['period_info']['company_name'] }}</h3>
                        <p class="text-gray-600">Per {{ date('d M Y', strtotime($reportData['period_info']['end_date'])) }}</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Assets -->
                        <div>
                            <h3 class="text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-2 mb-4">AKTIVA</h3>

                            <!-- Current Assets -->
                            <div class="mb-6">
                                <h4 class="font-bold text-gray-800 mb-3">Aktiva Lancar</h4>
                                @foreach($reportData['balance_sheet']['assets']['current_assets']['details'] as $item)
                                    <div class="flex justify-between py-1 pl-4">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Aktiva Lancar</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['assets']['current_assets']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>

                            <!-- Fixed Assets -->
                            <div class="mb-6">
                                <h4 class="font-bold text-gray-800 mb-3">Aktiva Tetap</h4>
                                @foreach($reportData['balance_sheet']['assets']['fixed_assets']['details'] as $item)
                                    <div class="flex justify-between py-1 pl-4">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Aktiva Tetap</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['assets']['fixed_assets']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>

                            <!-- Other Assets -->
                            @if($reportData['balance_sheet']['assets']['other_assets']['total'] > 0)
                            <div class="mb-6">
                                <h4 class="font-bold text-gray-800 mb-3">Aktiva Lain-lain</h4>
                                @foreach($reportData['balance_sheet']['assets']['other_assets']['details'] as $item)
                                    <div class="flex justify-between py-1 pl-4">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Aktiva Lain-lain</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['assets']['other_assets']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>
                            @endif

                            <!-- Total Assets -->
                            <div class="bg-blue-100 p-3 rounded border-2 border-blue-300">
                                <div class="flex justify-between text-lg font-bold text-blue-900">
                                    <span>TOTAL AKTIVA</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['assets']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Liabilities & Equity -->
                        <div>
                            <h3 class="text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-2 mb-4">PASSIVA</h3>

                            <!-- Current Liabilities -->
                            <div class="mb-6">
                                <h4 class="font-bold text-gray-800 mb-3">Kewajiban Lancar</h4>
                                @foreach($reportData['balance_sheet']['liabilities']['current_liabilities']['details'] as $item)
                                    <div class="flex justify-between py-1 pl-4">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Kewajiban Lancar</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['liabilities']['current_liabilities']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>

                            <!-- Long Term Liabilities -->
                            @if($reportData['balance_sheet']['liabilities']['long_term_liabilities']['total'] > 0)
                            <div class="mb-6">
                                <h4 class="font-bold text-gray-800 mb-3">Kewajiban Jangka Panjang</h4>
                                @foreach($reportData['balance_sheet']['liabilities']['long_term_liabilities']['details'] as $item)
                                    <div class="flex justify-between py-1 pl-4">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Kewajiban Jangka Panjang</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['liabilities']['long_term_liabilities']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>
                            @endif

                            <!-- Total Liabilities -->
                            <div class="flex justify-between py-2 border-t-2 border-gray-300 font-bold mb-6">
                                <span>Total Kewajiban</span>
                                <span>Rp {{ number_format($reportData['balance_sheet']['liabilities']['total'], 0, ',', '.') }}</span>
                            </div>

                            <!-- Equity -->
                            <div class="mb-6">
                                <h4 class="font-bold text-gray-800 mb-3">Modal</h4>
                                @foreach($reportData['balance_sheet']['equity']['details'] as $item)
                                    <div class="flex justify-between py-1 pl-4">
                                        <span class="text-gray-700">{{ $item['account_name'] }}</span>
                                        <span class="font-medium">Rp {{ number_format($item['balance'], 0, ',', '.') }}</span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between py-2 border-t border-gray-200 font-bold">
                                    <span>Total Modal</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['equity']['total'], 0, ',', '.') }}</span>
                                </div>
                            </div>

                            <!-- Total Liabilities & Equity -->
                            <div class="bg-blue-100 p-3 rounded border-2 border-blue-300">
                                <div class="flex justify-between text-lg font-bold text-blue-900">
                                    <span>TOTAL PASSIVA</span>
                                    <span>Rp {{ number_format($reportData['balance_sheet']['total_liabilities_equity'], 0, ',', '.') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @elseif($selectedReport === 'trial_balance')
                <!-- Trial Balance -->
                <div class="p-6">
                    <div class="text-center mb-6">
                        <h2 class="text-xl font-bold text-gray-900">NERACA SALDO</h2>
                        <h3 class="text-lg font-semibold text-gray-700">{{ $reportData['period_info']['company_name'] }}</h3>
                        <p class="text-gray-600">Periode: {{ date('d M Y', strtotime($reportData['period_info']['start_date'])) }} - {{ date('d M Y', strtotime($reportData['period_info']['end_date'])) }}</p>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kode Akun</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nama Akun</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Kredit</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($reportData['trial_balance']['accounts'] as $account)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $account['account_code'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $account['account_name'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        {{ $account['debit'] > 0 ? 'Rp ' . number_format($account['debit'], 0, ',', '.') : '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        {{ $account['credit'] > 0 ? 'Rp ' . number_format($account['credit'], 0, ',', '.') : '-' }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="bg-gray-50">
                                <tr class="font-bold">
                                    <td colspan="2" class="px-6 py-4 text-sm text-gray-900">TOTAL</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 text-right">Rp {{ number_format($reportData['trial_balance']['total_debit'], 0, ',', '.') }}</td>
                                    <td class="px-6 py-4 text-sm text-gray-900 text-right">Rp {{ number_format($reportData['trial_balance']['total_credit'], 0, ',', '.') }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-filament-panels::page>
