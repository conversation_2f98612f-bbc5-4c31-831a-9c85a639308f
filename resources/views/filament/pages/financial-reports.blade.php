<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4"><PERSON><PERSON><PERSON></h2>
            <p class="text-gray-600">Aks<PERSON> semua laporan keuangan perusahaan <PERSON>a</p>
        </div>

        <!-- Financial Reports Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
                $tenant = \Filament\Facades\Filament::getTenant();
                $reports = [
                    [
                        'title' => 'Laporan Laba Rugi',
                        'description' => 'Income Statement - Pendapatan dan beban perusahaan',
                        'icon' => '📊',
                        'url' => route('filament.admin.pages.income-statement', ['tenant' => $tenant]),
                        'color' => 'green',
                    ],
                    [
                        'title' => 'Neraca',
                        'description' => 'Balance Sheet - Aset, kewajiban, dan ekuitas',
                        'icon' => '⚖️',
                        'url' => route('filament.admin.pages.balance-sheet', ['tenant' => $tenant]),
                        'color' => 'blue',
                    ],
                    [
                        'title' => 'Laporan Arus Kas',
                        'description' => 'Cash Flow Statement - Arus kas masuk dan keluar',
                        'icon' => '💰',
                        'url' => route('filament.admin.pages.cash-flow-statement', ['tenant' => $tenant]),
                        'color' => 'purple',
                    ],
                    [
                        'title' => 'Buku Besar',
                        'description' => 'General Ledger - Detail transaksi per akun',
                        'icon' => '📚',
                        'url' => route('filament.admin.pages.general-ledger', ['tenant' => $tenant]),
                        'color' => 'orange',
                    ],
                    [
                        'title' => 'Analisis Rasio Keuangan',
                        'description' => 'Financial Ratio Analysis - Analisis kesehatan keuangan',
                        'icon' => '📈',
                        'url' => route('filament.admin.pages.financial-ratio-analysis', ['tenant' => $tenant]),
                        'color' => 'red',
                    ],
                    [
                        'title' => 'Analisis Profitabilitas',
                        'description' => 'Profitability Analysis - Analisis keuntungan',
                        'icon' => '💹',
                        'url' => route('filament.admin.pages.profitability-analysis', ['tenant' => $tenant]),
                        'color' => 'indigo',
                    ],
                ];
            @endphp

            @foreach($reports as $report)
                <a href="{{ $report['url'] }}"
                   class="block p-6 bg-{{ $report['color'] }}-50 hover:bg-{{ $report['color'] }}-100 rounded-lg border border-{{ $report['color'] }}-200 transition-all duration-200 hover:shadow-md group">
                    <div class="flex items-start space-x-4">
                        <div class="text-3xl">{{ $report['icon'] }}</div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-{{ $report['color'] }}-900 group-hover:text-{{ $report['color'] }}-800 mb-2">
                                {{ $report['title'] }}
                            </h3>
                            <p class="text-sm text-{{ $report['color'] }}-600 leading-relaxed">
                                {{ $report['description'] }}
                            </p>
                        </div>
                        <div class="text-{{ $report['color'] }}-400 group-hover:text-{{ $report['color'] }}-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>
            @endforeach
        </div>

        <!-- Quick Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Ringkasan Keuangan</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">Rp 0</div>
                    <div class="text-sm text-gray-600">Total Pendapatan</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">Rp 0</div>
                    <div class="text-sm text-gray-600">Total Beban</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">Rp 0</div>
                    <div class="text-sm text-gray-600">Laba Bersih</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">Rp 0</div>
                    <div class="text-sm text-gray-600">Total Aset</div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
