<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Analisis
            </x-slot>

            {{ $this->form }}

            <div class="mt-4">
                <x-filament::button wire:click="updateAnalysis" color="primary">
                    <x-heroicon-o-arrow-path class="w-4 h-4 mr-2" />
                    Refresh Analisis
                </x-filament::button>
            </div>
        </x-filament::section>

        <!-- Summary Cards -->
        @if(isset($summary))
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <x-filament::section class="bg-primary-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary-600">
                        Rp {{ number_format($summary['total_revenue'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Total Pendapatan</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-success-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-success-600">
                        Rp {{ number_format($summary['total_profit'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Total Laba Kotor</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-warning-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-warning-600">
                        {{ number_format($summary['avg_margin'] ?? 0, 2) }}%
                    </div>
                    <div class="text-sm text-gray-600">Margin Rata-rata</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-info-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-info-600">
                        {{ number_format($summary['total_items'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Total Item</div>
                </div>
            </x-filament::section>
        </div>
        @endif

        <!-- Profitability Chart -->
        @if(isset($trends) && count($trends) > 0)
        <x-filament::section>
            <x-slot name="heading">
                Trend Profitabilitas (6 Bulan)
            </x-slot>

            <div class="h-96 w-full bg-white p-4 rounded-lg border">
                <canvas id="profitabilityChart" style="width: 100%; height: 100%;"></canvas>

                <!-- Fallback content if chart fails -->
                <div id="chartFallback" style="display: none;" class="text-center py-8">
                    <p class="text-gray-600 mb-4">Chart sedang dimuat...</p>
                    <div class="text-sm text-gray-500">
                        <p>Data Trends:</p>
                        @if(isset($trends))
                            @foreach($trends as $trend)
                                <p>{{ $trend['period'] }}: Rp {{ number_format($trend['revenue']) }} ({{ number_format($trend['margin'], 1) }}%)</p>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </x-filament::section>
        @else
        <x-filament::section>
            <x-slot name="heading">
                Trend Profitabilitas (6 Bulan)
            </x-slot>

            <div class="text-center py-8 text-gray-500">
                Tidak ada data trend untuk ditampilkan
            </div>
        </x-filament::section>
        @endif

        <!-- Data Table -->
        <x-filament::section>
            <x-slot name="heading">
                Detail Analisis Profitabilitas
            </x-slot>

            @if(isset($tableData) && $tableData->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $analysisType === 'product' ? 'Produk' : ($analysisType === 'customer' ? 'Pelanggan' : ($analysisType === 'sales_person' ? 'Sales Person' : 'Wilayah')) }}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pendapatan
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Biaya
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Laba Kotor
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Margin (%)
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Kuantitas
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($tableData as $item)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ is_object($item) ? ($item->name ?? 'N/A') : (isset($item['name']) ? $item['name'] : 'N/A') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format(is_object($item) ? ($item->revenue ?? 0) : (isset($item['revenue']) ? $item['revenue'] : 0), 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format(is_object($item) ? ($item->cost ?? 0) : (isset($item['cost']) ? $item['cost'] : 0), 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format(is_object($item) ? ($item->gross_profit ?? 0) : (isset($item['gross_profit']) ? $item['gross_profit'] : 0), 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ number_format(is_object($item) ? ($item->gross_margin ?? 0) : (isset($item['gross_margin']) ? $item['gross_margin'] : 0), 2) }}%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ number_format(is_object($item) ? ($item->quantity ?? 0) : (isset($item['quantity']) ? $item['quantity'] : 0), 0, ',', '.') }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="text-center py-8 text-gray-500">
                Tidak ada data untuk periode yang dipilih
            </div>
            @endif
        </x-filament::section>
    </div>

    @if(isset($trends) && count($trends) > 0)
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing chart...');
            setTimeout(initProfitabilityChart, 1000);
        });

        // Also try on window load as backup
        window.addEventListener('load', function() {
            console.log('Window loaded, backup chart init...');
            setTimeout(function() {
                if (!window.profitabilityChart) {
                    console.log('Chart not created yet, trying again...');
                    initProfitabilityChart();
                }
            }, 2000);
        });

        function initProfitabilityChart() {
            console.log('=== CHART INITIALIZATION START ===');

            if (typeof Chart === 'undefined') {
                console.error('Chart.js not loaded!');
                setTimeout(initProfitabilityChart, 500);
                return;
            }
            console.log('Chart.js is available');

            const canvas = document.getElementById('profitabilityChart');
            if (!canvas) {
                console.error('Canvas element not found!');
                return;
            }
            console.log('Canvas element found');

            // Properly destroy existing chart
            if (window.profitabilityChart) {
                if (typeof window.profitabilityChart.destroy === 'function') {
                    console.log('Destroying existing chart');
                    window.profitabilityChart.destroy();
                }
                window.profitabilityChart = null;
            }

            const ctx = canvas.getContext('2d');
            const trendsData = @json($trends);

            console.log('Trends data:', trendsData);

            if (!trendsData || trendsData.length === 0) {
                console.error('No trends data!');
                return;
            }

            const labels = trendsData.map(item => item.period);
            const revenue = trendsData.map(item => item.revenue);
            const profit = trendsData.map(item => item.profit);
            const margin = trendsData.map(item => item.margin);

            try {
                window.profitabilityChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Pendapatan',
                            data: revenue,
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: false
                        }, {
                            label: 'Laba Kotor',
                            data: profit,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 2,
                            fill: false
                        }, {
                            label: 'Margin (%)',
                            data: margin,
                            borderColor: 'rgb(251, 191, 36)',
                            backgroundColor: 'rgba(251, 191, 36, 0.1)',
                            borderWidth: 2,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Periode'
                                }
                            },
                            y: {
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: 'Nilai'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toLocaleString('id-ID');
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.dataset.label === 'Margin (%)') {
                                            label += context.parsed.y + '%';
                                        } else {
                                            label += 'Rp ' + context.parsed.y.toLocaleString('id-ID');
                                        }
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('=== CHART CREATED SUCCESSFULLY ===');
                console.log('Chart instance:', window.profitabilityChart);
                console.log('Chart type:', window.profitabilityChart.config.type);
                console.log('Chart datasets:', window.profitabilityChart.data.datasets.length);

            } catch (error) {
                console.error('=== ERROR CREATING CHART ===');
                console.error('Error:', error);

                // Simple fallback
                try {
                    window.profitabilityChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Revenue',
                                data: revenue,
                                backgroundColor: 'rgba(59, 130, 246, 0.5)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                    console.log('Fallback chart created');
                } catch (fallbackError) {
                    console.error('Fallback failed:', fallbackError);
                }
            }
        }
    </script>
    @else
    <script>
        console.log('No trends data available');
    </script>
    @endif
</x-filament-panels::page>
