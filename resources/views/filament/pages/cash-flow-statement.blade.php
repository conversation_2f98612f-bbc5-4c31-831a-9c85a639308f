<x-filament-panels::page>
    @php
        $data = $this->getCashFlowData();
    @endphp

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900">Laporan Arus Kas</h2>
                <p class="text-gray-600">Untuk Tahun yang Berakhir 31 Desember {{ $data['year'] }}</p>
                @if(filament()->getTenant())
                    <p class="text-sm text-gray-500">{{ filament()->getTenant()->company_name }}</p>
                @endif
            </div>
        </div>

        <!-- Cash Flow Content -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Laporan A<PERSON>s</h3>
            </div>

            <div class="p-6">
                <table class="w-full">
                    <tbody class="space-y-2">
                        <!-- Operating Activities -->
                        <tr class="border-b border-gray-200">
                            <td class="py-3 font-semibold text-gray-900">ARUS KAS DARI AKTIVITAS OPERASI</td>
                            <td class="py-3 text-right font-semibold text-gray-900"></td>
                        </tr>
                        <tr>
                            <td class="py-2 pl-4 text-gray-700">Laba Bersih</td>
                            <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['operating_cash_flow'], 0, ',', '.') }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 pl-4 text-gray-700">Penyesuaian untuk merekonsiliasi laba bersih ke kas</td>
                            <td class="py-2 text-right text-gray-900">-</td>
                        </tr>
                        <tr class="border-t border-gray-300">
                            <td class="py-2 pl-4 font-medium text-gray-900">Kas Bersih dari Aktivitas Operasi</td>
                            <td class="py-2 text-right font-medium text-gray-900">Rp {{ number_format($data['cash_from_operations'], 0, ',', '.') }}</td>
                        </tr>

                        <!-- Investing Activities -->
                        <tr class="border-b border-gray-200">
                            <td class="py-3 font-semibold text-gray-900">ARUS KAS DARI AKTIVITAS INVESTASI</td>
                            <td class="py-3 text-right font-semibold text-gray-900"></td>
                        </tr>
                        <tr>
                            <td class="py-2 pl-4 text-gray-700">Pembelian peralatan</td>
                            <td class="py-2 text-right text-gray-900">-</td>
                        </tr>
                        <tr class="border-t border-gray-300">
                            <td class="py-2 pl-4 font-medium text-gray-900">Kas Bersih dari Aktivitas Investasi</td>
                            <td class="py-2 text-right font-medium text-gray-900">Rp 0</td>
                        </tr>

                        <!-- Financing Activities -->
                        <tr class="border-b border-gray-200">
                            <td class="py-3 font-semibold text-gray-900">ARUS KAS DARI AKTIVITAS PENDANAAN</td>
                            <td class="py-3 text-right font-semibold text-gray-900"></td>
                        </tr>
                        <tr>
                            <td class="py-2 pl-4 text-gray-700">Kontribusi pemilik</td>
                            <td class="py-2 text-right text-gray-900">-</td>
                        </tr>
                        <tr class="border-t border-gray-300">
                            <td class="py-2 pl-4 font-medium text-gray-900">Kas Bersih dari Aktivitas Pendanaan</td>
                            <td class="py-2 text-right font-medium text-gray-900">Rp 0</td>
                        </tr>

                        <!-- Net Change in Cash -->
                        <tr class="border-t-2 border-gray-900">
                            <td class="py-3 font-bold text-gray-900">PERUBAHAN BERSIH KAS</td>
                            <td class="py-3 text-right font-bold {{ $data['net_change'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                Rp {{ number_format($data['net_change'], 0, ',', '.') }}
                            </td>
                        </tr>

                        <!-- Cash Balances -->
                        <tr>
                            <td class="py-2 text-gray-700">Kas pada awal tahun</td>
                            <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['beginning_cash'], 0, ',', '.') }}</td>
                        </tr>
                        <tr class="border-t-2 border-gray-900">
                            <td class="py-3 font-bold text-gray-900">KAS PADA AKHIR TAHUN</td>
                            <td class="py-3 text-right font-bold text-blue-600">Rp {{ number_format($data['ending_cash'], 0, ',', '.') }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Operating Cash Flow -->
            <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">Operasi</p>
                        <p class="text-lg font-bold text-blue-900">Rp {{ number_format($data['cash_from_operations'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Beginning Cash -->
            <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Kas Awal</p>
                        <p class="text-lg font-bold text-gray-900">Rp {{ number_format($data['beginning_cash'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Net Change -->
            <div class="bg-{{ $data['net_change'] >= 0 ? 'green' : 'red' }}-50 rounded-lg p-6 border border-{{ $data['net_change'] >= 0 ? 'green' : 'red' }}-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-{{ $data['net_change'] >= 0 ? 'green' : 'red' }}-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($data['net_change'] >= 0)
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                @endif
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-{{ $data['net_change'] >= 0 ? 'green' : 'red' }}-600">Perubahan Bersih</p>
                        <p class="text-lg font-bold text-{{ $data['net_change'] >= 0 ? 'green' : 'red' }}-900">
                            Rp {{ number_format($data['net_change'], 0, ',', '.') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Ending Cash -->
            <div class="bg-purple-50 rounded-lg p-6 border border-purple-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-purple-600">Kas Akhir</p>
                        <p class="text-lg font-bold text-purple-900">Rp {{ number_format($data['ending_cash'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
