<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Analisis
            </x-slot>

            {{ $this->form }}

            <div class="mt-4">
                <x-filament::button wire:click="updateAnalysis">
                    Refresh Analisis
                </x-filament::button>
            </div>
        </x-filament::section>

        <!-- Liquidity Ratios -->
        @if(isset($liquidity_ratios))
        <x-filament::section>
            <x-slot name="heading">
                Rasio Likuiditas
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ number_format($liquidity_ratios['current_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Current Ratio</div>
                    <div class="text-xs text-gray-500 mt-1">Ideal: 1.5 - 3.0</div>
                </div>

                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">
                        {{ number_format($liquidity_ratios['quick_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Quick Ratio</div>
                    <div class="text-xs text-gray-500 mt-1">Ideal: 1.0 - 1.5</div>
                </div>

                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ number_format($liquidity_ratios['cash_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Cash Ratio</div>
                    <div class="text-xs text-gray-500 mt-1">Ideal: 0.1 - 0.2</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Profitability Ratios -->
        @if(isset($profitability_ratios))
        <x-filament::section>
            <x-slot name="heading">
                Rasio Profitabilitas
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">
                        {{ number_format($profitability_ratios['gross_profit_margin'] ?? 0, 2) }}%
                    </div>
                    <div class="text-sm text-gray-600">Gross Profit Margin</div>
                </div>

                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">
                        {{ number_format($profitability_ratios['operating_profit_margin'] ?? 0, 2) }}%
                    </div>
                    <div class="text-sm text-gray-600">Operating Profit Margin</div>
                </div>

                <div class="text-center p-4 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">
                        {{ number_format($profitability_ratios['net_profit_margin'] ?? 0, 2) }}%
                    </div>
                    <div class="text-sm text-gray-600">Net Profit Margin</div>
                </div>

                <div class="text-center p-4 bg-indigo-50 rounded-lg">
                    <div class="text-2xl font-bold text-indigo-600">
                        {{ number_format($profitability_ratios['return_on_assets'] ?? 0, 2) }}%
                    </div>
                    <div class="text-sm text-gray-600">Return on Assets</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Efficiency Ratios -->
        @if(isset($efficiency_ratios))
        <x-filament::section>
            <x-slot name="heading">
                Rasio Efisiensi
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-teal-50 rounded-lg">
                    <div class="text-2xl font-bold text-teal-600">
                        {{ number_format($efficiency_ratios['asset_turnover'] ?? 0, 2) }}x
                    </div>
                    <div class="text-sm text-gray-600">Asset Turnover</div>
                </div>

                <div class="text-center p-4 bg-cyan-50 rounded-lg">
                    <div class="text-2xl font-bold text-cyan-600">
                        {{ number_format($efficiency_ratios['inventory_turnover'] ?? 0, 2) }}x
                    </div>
                    <div class="text-sm text-gray-600">Inventory Turnover</div>
                </div>

                <div class="text-center p-4 bg-pink-50 rounded-lg">
                    <div class="text-2xl font-bold text-pink-600">
                        {{ number_format($efficiency_ratios['receivables_turnover'] ?? 0, 2) }}x
                    </div>
                    <div class="text-sm text-gray-600">Receivables Turnover</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Leverage Ratios -->
        @if(isset($leverage_ratios))
        <x-filament::section>
            <x-slot name="heading">
                Rasio Leverage
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-600">
                        {{ number_format($leverage_ratios['debt_to_equity'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Debt to Equity</div>
                </div>

                <div class="text-center p-4 bg-slate-50 rounded-lg">
                    <div class="text-2xl font-bold text-slate-600">
                        {{ number_format($leverage_ratios['debt_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Debt Ratio</div>
                </div>

                <div class="text-center p-4 bg-zinc-50 rounded-lg">
                    <div class="text-2xl font-bold text-zinc-600">
                        {{ number_format($leverage_ratios['times_interest_earned'] ?? 0, 2) }}x
                    </div>
                    <div class="text-sm text-gray-600">Times Interest Earned</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Ratio Trends Chart -->
        @if(isset($ratio_trends) && count($ratio_trends) > 0)
        <x-filament::section>
            <x-slot name="heading">
                Trend Rasio Keuangan (6 Bulan)
            </x-slot>

            <div class="h-96 w-full bg-white p-4 rounded-lg border">
                <canvas id="ratioTrendsChart" style="width: 100%; height: 100%;"></canvas>

                <!-- Fallback content if chart fails -->
                <div id="ratioTrendsChartFallback" style="display: none;" class="text-center py-8">
                    <p class="text-gray-600 mb-4">Chart sedang dimuat...</p>
                    <div class="text-sm text-gray-500">
                        <p>Data Trend Rasio:</p>
                        @foreach($ratio_trends as $trend)
                            <p>{{ $trend['period'] }}: Current Ratio {{ number_format($trend['current_ratio'], 2) }} | ROA {{ number_format($trend['roa'], 2) }}%</p>
                        @endforeach
                    </div>
                </div>
            </div>
        </x-filament::section>
        @else
        <x-filament::section>
            <x-slot name="heading">
                Trend Rasio Keuangan (6 Bulan)
            </x-slot>

            <div class="text-center py-8 text-gray-500">
                Tidak ada data trend untuk ditampilkan
            </div>
        </x-filament::section>
        @endif

        <!-- Ratio Interpretation -->
        <x-filament::section>
            <x-slot name="heading">
                Interpretasi Rasio
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Rasio Likuiditas</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Current Ratio > 2: Likuiditas baik</li>
                        <li>• Quick Ratio > 1: Kemampuan bayar jangka pendek baik</li>
                        <li>• Cash Ratio 0.1-0.2: Manajemen kas optimal</li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Rasio Profitabilitas</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Gross Margin > 30%: Margin kotor sehat</li>
                        <li>• Net Margin > 10%: Profitabilitas baik</li>
                        <li>• ROA > 5%: Efisiensi aset baik</li>
                    </ul>
                </div>
            </div>
        </x-filament::section>
    </div>

    @if(isset($ratio_trends) && count($ratio_trends) > 0)
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing ratio trends chart...');
            setTimeout(initRatioTrendsChart, 1000);
        });

        function initRatioTrendsChart() {
            console.log('=== RATIO TRENDS CHART INITIALIZATION START ===');

            if (typeof Chart === 'undefined') {
                console.error('Chart.js not loaded!');
                setTimeout(initRatioTrendsChart, 500);
                return;
            }
            console.log('Chart.js is available');

            const canvas = document.getElementById('ratioTrendsChart');
            if (!canvas) {
                console.error('Ratio trends chart canvas element not found!');
                return;
            }
            console.log('Ratio trends chart canvas element found');

            // Properly destroy existing chart
            if (window.ratioTrendsChart) {
                if (typeof window.ratioTrendsChart.destroy === 'function') {
                    console.log('Destroying existing ratio trends chart');
                    window.ratioTrendsChart.destroy();
                }
                window.ratioTrendsChart = null;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get 2D context from canvas');
                return;
            }

            // Prepare data
            const labels = @json(array_column($ratio_trends, 'period'));
            const currentRatio = @json(array_column($ratio_trends, 'current_ratio'));
            const quickRatio = @json(array_column($ratio_trends, 'quick_ratio'));
            const roa = @json(array_column($ratio_trends, 'roa'));
            const roe = @json(array_column($ratio_trends, 'roe'));
            const debtToEquity = @json(array_column($ratio_trends, 'debt_to_equity'));
            const grossProfitMargin = @json(array_column($ratio_trends, 'gross_profit_margin'));

            console.log('Chart data prepared:', {
                labels: labels,
                currentRatio: currentRatio,
                quickRatio: quickRatio,
                roa: roa
            });

            try {
                window.ratioTrendsChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Current Ratio',
                            data: currentRatio,
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        }, {
                            label: 'Quick Ratio',
                            data: quickRatio,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y'
                        }, {
                            label: 'ROA (%)',
                            data: roa,
                            borderColor: 'rgb(251, 191, 36)',
                            backgroundColor: 'rgba(251, 191, 36, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }, {
                            label: 'ROE (%)',
                            data: roe,
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }, {
                            label: 'Gross Profit Margin (%)',
                            data: grossProfitMargin,
                            borderColor: 'rgb(168, 85, 247)',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Periode'
                                }
                            },
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: 'Rasio'
                                },
                                beginAtZero: true
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Persentase (%)'
                                },
                                grid: {
                                    drawOnChartArea: false,
                                },
                                beginAtZero: true
                            },
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.y !== null) {
                                            if (label.includes('%')) {
                                                label += context.parsed.y.toFixed(2) + '%';
                                            } else {
                                                label += context.parsed.y.toFixed(2);
                                            }
                                        }
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('Ratio trends chart created successfully');
            } catch (error) {
                console.error('Error creating ratio trends chart:', error);
                document.getElementById('ratioTrendsChartFallback').style.display = 'block';
            }
        }
    </script>
    @else
    <script>
        console.log('No ratio trends data available');
    </script>
    @endif
</x-filament-panels::page>
