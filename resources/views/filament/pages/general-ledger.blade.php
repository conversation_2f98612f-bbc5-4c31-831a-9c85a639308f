<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Options</h3>
            {{ $this->form }}

            <div class="mt-4">
                <x-filament::button wire:click="$refresh" type="button">
                    Apply Filters
                </x-filament::button>
            </div>
        </div>

        <!-- General <PERSON> Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">General Ledger</h3>
                <p class="text-sm text-gray-600">
                    Period: {{ $start_date ? \Carbon\Carbon::parse($start_date)->format('M d, Y') : 'All' }} -
                    {{ $end_date ? \Carbon\Carbon::parse($end_date)->format('M d, Y') : 'All' }}
                </p>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @php
                            $entries = $this->getGeneralLedgerData();
                            $runningBalance = 0;
                            $totalDebit = 0;
                            $totalCredit = 0;
                        @endphp

                        @forelse($entries as $entry)
                            @php
                                $runningBalance += $entry->debit - $entry->credit;
                                $totalDebit += $entry->debit;
                                $totalCredit += $entry->credit;
                            @endphp
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $entry->journal->journal_date->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div>
                                        <div class="font-medium">{{ $entry->account->account_code }}</div>
                                        <div class="text-gray-500">{{ $entry->account->account_name }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <div>
                                        <div>{{ $entry->journal->description }}</div>
                                        @if($entry->description)
                                            <div class="text-gray-500 text-xs">{{ $entry->description }}</div>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                    @if($entry->debit > 0)
                                        {{ number_format($entry->debit, 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                    @if($entry->credit > 0)
                                        {{ number_format($entry->credit, 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                                    {{ number_format($runningBalance, 2) }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    No journal entries found for the selected criteria.
                                </td>
                            </tr>
                        @endforelse

                        @if($entries->count() > 0)
                            <tr class="bg-gray-50 font-medium">
                                <td colspan="3" class="px-6 py-4 text-right text-sm text-gray-900">
                                    <strong>Total:</strong>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                    <strong>{{ number_format($totalDebit, 2) }}</strong>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                    <strong>{{ number_format($totalCredit, 2) }}</strong>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                    <strong>{{ number_format($runningBalance, 2) }}</strong>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</x-filament-panels::page>
