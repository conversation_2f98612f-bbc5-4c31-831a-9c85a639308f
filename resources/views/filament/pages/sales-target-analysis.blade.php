<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Analisis
            </x-slot>

            {{ $this->form }}

            <div class="mt-4">
                <x-filament::button wire:click="updateAnalysis">
                    Refresh Analisis
                </x-filament::button>
            </div>
        </x-filament::section>

        <!-- Summary Cards -->
        @if(isset($summary))
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <x-filament::section class="bg-primary-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary-600">
                        Rp {{ number_format($summary['total_target'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Total Target</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-success-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-success-600">
                        Rp {{ number_format($summary['total_actual'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Total Aktual</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-warning-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-warning-600">
                        {{ number_format($summary['overall_achievement'] ?? 0, 2) }}%
                    </div>
                    <div class="text-sm text-gray-600">Pencapaian Keseluruhan</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-info-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-info-600">
                        {{ $summary['achievers_count'] ?? 0 }}/{{ $summary['total_count'] ?? 0 }}
                    </div>
                    <div class="text-sm text-gray-600">Yang Mencapai Target</div>
                </div>
            </x-filament::section>
        </div>
        @endif

        <!-- Target vs Actual Chart -->
        @if(isset($trends) && count($trends) > 0)
        <x-filament::section>
            <x-slot name="heading">
                Trend Target vs Aktual (6 Bulan)
            </x-slot>

            <div class="h-96 w-full bg-white p-4 rounded-lg border">
                <canvas id="targetChart" style="width: 100%; height: 100%;"></canvas>

                <!-- Fallback content if chart fails -->
                <div id="targetChartFallback" style="display: none;" class="text-center py-8">
                    <p class="text-gray-600 mb-4">Chart sedang dimuat...</p>
                    <div class="text-sm text-gray-500">
                        <p>Data Target vs Aktual:</p>
                        @foreach($trends as $trend)
                            <p>{{ $trend['period'] }}: Target Rp {{ number_format($trend['target']) }} | Aktual Rp {{ number_format($trend['actual']) }}</p>
                        @endforeach
                    </div>
                </div>
            </div>
        </x-filament::section>
        @else
        <x-filament::section>
            <x-slot name="heading">
                Trend Target vs Aktual (6 Bulan)
            </x-slot>

            <div class="text-center py-8 text-gray-500">
                Tidak ada data trend untuk ditampilkan
            </div>
        </x-filament::section>
        @endif

        <!-- Achievement Distribution -->
        @if(isset($summary['achievement_distribution']))
        <x-filament::section>
            <x-slot name="heading">
                Distribusi Pencapaian
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-success-50 rounded-lg">
                    <div class="text-3xl font-bold text-success-600">
                        {{ $summary['achievement_distribution']['above_100'] ?? 0 }}
                    </div>
                    <div class="text-sm text-gray-600">Di Atas Target (≥100%)</div>
                </div>

                <div class="text-center p-4 bg-warning-50 rounded-lg">
                    <div class="text-3xl font-bold text-warning-600">
                        {{ $summary['achievement_distribution']['80_to_100'] ?? 0 }}
                    </div>
                    <div class="text-sm text-gray-600">Mendekati Target (80-99%)</div>
                </div>

                <div class="text-center p-4 bg-danger-50 rounded-lg">
                    <div class="text-3xl font-bold text-danger-600">
                        {{ $summary['achievement_distribution']['below_80'] ?? 0 }}
                    </div>
                    <div class="text-sm text-gray-600">Di Bawah Target (<80%)</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Data Table -->
        <x-filament::section>
            <x-slot name="heading">
                Detail Analisis Target Penjualan
            </x-slot>

            @if(isset($tableData) && $tableData->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $analysisType === 'sales_person' ? 'Sales Person' : ($analysisType === 'product' ? 'Produk' : ($analysisType === 'region' ? 'Wilayah' : 'Pelanggan')) }}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Target
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Aktual
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Selisih
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pencapaian (%)
                            </th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($tableData as $item)
                        @php
                            $name = is_object($item) ? ($item->name ?? 'N/A') : (isset($item['name']) ? $item['name'] : 'N/A');
                            $targetAmount = is_object($item) ? ($item->target_amount ?? 0) : (isset($item['target_amount']) ? $item['target_amount'] : 0);
                            $actualAmount = is_object($item) ? ($item->actual_amount ?? 0) : (isset($item['actual_amount']) ? $item['actual_amount'] : 0);
                            $varianceAmount = is_object($item) ? ($item->variance_amount ?? 0) : (isset($item['variance_amount']) ? $item['variance_amount'] : 0);
                            $achievementPercentage = is_object($item) ? ($item->achievement_percentage ?? 0) : (isset($item['achievement_percentage']) ? $item['achievement_percentage'] : 0);
                            $status = is_object($item) ? ($item->status ?? 'unknown') : (isset($item['status']) ? $item['status'] : 'unknown');
                        @endphp
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ $name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format($targetAmount, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format($actualAmount, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right {{ $varianceAmount >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                Rp {{ number_format($varianceAmount, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $achievementPercentage >= 100 ? 'bg-green-100 text-green-800' : ($achievementPercentage >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    {{ number_format($achievementPercentage, 2) }}%
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $status === 'achieved' ? 'bg-green-100 text-green-800' : ($status === 'on_track' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    {{ $status === 'achieved' ? 'Tercapai' : ($status === 'on_track' ? 'On Track' : 'Tertinggal') }}
                                </span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="text-center py-8 text-gray-500">
                Tidak ada data untuk periode yang dipilih
            </div>
            @endif
        </x-filament::section>
    </div>

    @if(isset($trends) && count($trends) > 0)
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing target chart...');
            setTimeout(initTargetChart, 1000);
        });

        function initTargetChart() {
            console.log('=== TARGET CHART INITIALIZATION START ===');

            if (typeof Chart === 'undefined') {
                console.error('Chart.js not loaded!');
                setTimeout(initTargetChart, 500);
                return;
            }
            console.log('Chart.js is available');

            const canvas = document.getElementById('targetChart');
            if (!canvas) {
                console.error('Target chart canvas element not found!');
                return;
            }
            console.log('Target chart canvas element found');

            // Properly destroy existing chart
            if (window.targetChart) {
                if (typeof window.targetChart.destroy === 'function') {
                    console.log('Destroying existing target chart');
                    window.targetChart.destroy();
                }
                window.targetChart = null;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get target chart canvas context!');
                return;
            }
            console.log('Target chart canvas context obtained');

            // Prepare chart data
            const trendsData = @json($trends);
            console.log('Target trends data:', trendsData);

            if (!trendsData || trendsData.length === 0) {
                console.error('No target trends data available!');
                return;
            }

            const labels = trendsData.map(item => item.period);
            const target = trendsData.map(item => item.target);
            const actual = trendsData.map(item => item.actual);

            console.log('Target chart labels:', labels);
            console.log('Target data:', target);
            console.log('Actual data:', actual);

            try {
                window.targetChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Target',
                            data: target,
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgb(59, 130, 246)',
                            borderWidth: 1
                        }, {
                            label: 'Aktual',
                            data: actual,
                            backgroundColor: 'rgba(34, 197, 94, 0.8)',
                            borderColor: 'rgb(34, 197, 94)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Periode'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Rupiah'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toLocaleString('id-ID');
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += 'Rp ' + context.parsed.y.toLocaleString('id-ID');
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('=== TARGET CHART CREATED SUCCESSFULLY ===');
                console.log('Target chart instance:', window.targetChart);

            } catch (error) {
                console.error('=== ERROR CREATING TARGET CHART ===');
                console.error('Error:', error);

                // Simple fallback
                try {
                    window.targetChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Target',
                                data: target,
                                backgroundColor: 'rgba(59, 130, 246, 0.5)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                    console.log('Target chart fallback created');
                } catch (fallbackError) {
                    console.error('Target chart fallback failed:', fallbackError);
                }
            }
        }
    </script>
    @else
    <script>
        console.log('No target trends data available');
    </script>
    @endif
</x-filament-panels::page>
