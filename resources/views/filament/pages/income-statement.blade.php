<x-filament-panels::page>
    @php
        $data = $this->getIncomeStatementData();
    @endphp

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900">Laporan Laba Rugi</h2>
                <p class="text-gray-600">Untuk Tahun yang Berakhir 31 Desember {{ $data['year'] }}</p>
                @if(filament()->getTenant())
                    <p class="text-sm text-gray-500">{{ filament()->getTenant()->company_name }}</p>
                @endif
            </div>
        </div>

        <!-- Income Statement Content -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Laporan Laba Rugi</h3>
            </div>

            <div class="p-6">
                <table class="w-full">
                    <tbody class="space-y-2">
                        <!-- Revenue Section -->
                        <tr class="border-b border-gray-200">
                            <td class="py-3 font-semibold text-gray-900">PENDAPATAN</td>
                            <td class="py-3 text-right font-semibold text-gray-900"></td>
                        </tr>
                        <tr>
                            <td class="py-2 pl-4 text-gray-700">Total Pendapatan</td>
                            <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['revenue'], 0, ',', '.') }}</td>
                        </tr>

                        <!-- Expenses Section -->
                        <tr class="border-b border-gray-200">
                            <td class="py-3 font-semibold text-gray-900">BEBAN</td>
                            <td class="py-3 text-right font-semibold text-gray-900"></td>
                        </tr>
                        <tr>
                            <td class="py-2 pl-4 text-gray-700">Total Beban</td>
                            <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['expenses'], 0, ',', '.') }}</td>
                        </tr>

                        <!-- Net Income -->
                        <tr class="border-t-2 border-gray-900">
                            <td class="py-3 font-bold text-gray-900">LABA BERSIH</td>
                            <td class="py-3 text-right font-bold {{ $data['net_income'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                Rp {{ number_format($data['net_income'], 0, ',', '.') }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Revenue Card -->
            <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">Total Pendapatan</p>
                        <p class="text-2xl font-bold text-blue-900">Rp {{ number_format($data['revenue'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Expenses Card -->
            <div class="bg-red-50 rounded-lg p-6 border border-red-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-red-600">Total Beban</p>
                        <p class="text-2xl font-bold text-red-900">Rp {{ number_format($data['expenses'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Net Income Card -->
            <div class="bg-{{ $data['net_income'] >= 0 ? 'green' : 'red' }}-50 rounded-lg p-6 border border-{{ $data['net_income'] >= 0 ? 'green' : 'red' }}-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-{{ $data['net_income'] >= 0 ? 'green' : 'red' }}-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-{{ $data['net_income'] >= 0 ? 'green' : 'red' }}-600">Laba Bersih</p>
                        <p class="text-2xl font-bold text-{{ $data['net_income'] >= 0 ? 'green' : 'red' }}-900">
                            Rp {{ number_format($data['net_income'], 0, ',', '.') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
