<x-filament-panels::page>
    @php
        $data = $this->getBalanceSheetData();
    @endphp

    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900">Neraca</h2>
                <p class="text-gray-600">Per 31 Desember {{ $data['year'] }}</p>
                @if(filament()->getTenant())
                    <p class="text-sm text-gray-500">{{ filament()->getTenant()->company_name }}</p>
                @endif
            </div>
        </div>

        <!-- Balance Sheet Content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Assets -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 bg-blue-50 border-b border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-900">ASET</h3>
                </div>

                <div class="p-6">
                    <table class="w-full">
                        <tbody>
                            <tr>
                                <td class="py-2 text-gray-700">Aset Lancar</td>
                                <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['assets'], 0, ',', '.') }}</td>
                            </tr>
                            <tr class="border-t-2 border-blue-900">
                                <td class="py-3 font-bold text-blue-900">TOTAL ASET</td>
                                <td class="py-3 text-right font-bold text-blue-900">Rp {{ number_format($data['assets'], 0, ',', '.') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Liabilities & Equity -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 bg-green-50 border-b border-green-200">
                    <h3 class="text-lg font-semibold text-green-900">KEWAJIBAN & EKUITAS</h3>
                </div>

                <div class="p-6">
                    <table class="w-full">
                        <tbody>
                            <!-- Liabilities -->
                            <tr>
                                <td class="py-2 font-semibold text-gray-900">KEWAJIBAN</td>
                                <td class="py-2 text-right font-semibold text-gray-900"></td>
                            </tr>
                            <tr>
                                <td class="py-2 pl-4 text-gray-700">Kewajiban Lancar</td>
                                <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['liabilities'], 0, ',', '.') }}</td>
                            </tr>

                            <!-- Equity -->
                            <tr class="border-t border-gray-200">
                                <td class="py-2 font-semibold text-gray-900">EKUITAS</td>
                                <td class="py-2 text-right font-semibold text-gray-900"></td>
                            </tr>
                            <tr>
                                <td class="py-2 pl-4 text-gray-700">Modal Pemilik</td>
                                <td class="py-2 text-right text-gray-900">Rp {{ number_format($data['equity'], 0, ',', '.') }}</td>
                            </tr>

                            <tr class="border-t-2 border-green-900">
                                <td class="py-3 font-bold text-green-900">TOTAL KEWAJIBAN & EKUITAS</td>
                                <td class="py-3 text-right font-bold text-green-900">Rp {{ number_format($data['total_liabilities_equity'], 0, ',', '.') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Balance Check -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-center">
                @if($data['assets'] == $data['total_liabilities_equity'])
                    <div class="flex items-center justify-center space-x-2 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-lg font-semibold">Neraca Seimbang</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">Aset = Kewajiban + Ekuitas</p>
                @else
                    <div class="flex items-center justify-center space-x-2 text-red-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-lg font-semibold">Neraca TIDAK Seimbang</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                        Selisih: Rp {{ number_format(abs($data['assets'] - $data['total_liabilities_equity']), 0, ',', '.') }}
                    </p>
                @endif
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Assets Card -->
            <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">Total Aset</p>
                        <p class="text-2xl font-bold text-blue-900">Rp {{ number_format($data['assets'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Liabilities Card -->
            <div class="bg-red-50 rounded-lg p-6 border border-red-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-red-600">Total Kewajiban</p>
                        <p class="text-2xl font-bold text-red-900">Rp {{ number_format($data['liabilities'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Equity Card -->
            <div class="bg-green-50 rounded-lg p-6 border border-green-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-green-600">Total Ekuitas</p>
                        <p class="text-2xl font-bold text-green-900">Rp {{ number_format($data['equity'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
