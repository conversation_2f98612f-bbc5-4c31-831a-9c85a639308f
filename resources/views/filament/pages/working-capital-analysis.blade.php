<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <x-filament::section>
            <x-slot name="heading">
                Filter Analisis
            </x-slot>

            {{ $this->form }}

            <div class="mt-4">
                <x-filament::button wire:click="updateAnalysis">
                    Refresh Analisis
                </x-filament::button>
            </div>
        </x-filament::section>

        <!-- Working Capital Summary -->
        @if(isset($working_capital))
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <x-filament::section class="bg-primary-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary-600">
                        Rp {{ number_format($working_capital['current_assets'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Aset Lancar</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-warning-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-warning-600">
                        Rp {{ number_format($working_capital['current_liabilities'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Kewajiban Lancar</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-success-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-success-600">
                        Rp {{ number_format($working_capital['net_working_capital'] ?? 0, 0, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-600">Modal Kerja Bersih</div>
                </div>
            </x-filament::section>

            <x-filament::section class="bg-info-50">
                <div class="text-center">
                    <div class="text-2xl font-bold text-info-600">
                        {{ number_format($working_capital['working_capital_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Rasio Modal Kerja</div>
                </div>
            </x-filament::section>
        </div>
        @endif

        <!-- Cash Conversion Cycle -->
        @if(isset($cash_conversion))
        <x-filament::section>
            <x-slot name="heading">
                Siklus Konversi Kas
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">
                        {{ number_format($cash_conversion['days_sales_outstanding'] ?? 0, 1) }}
                    </div>
                    <div class="text-sm text-gray-600">Hari Piutang (DSO)</div>
                </div>

                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">
                        {{ number_format($cash_conversion['days_inventory_outstanding'] ?? 0, 1) }}
                    </div>
                    <div class="text-sm text-gray-600">Hari Persediaan (DIO)</div>
                </div>

                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">
                        {{ number_format($cash_conversion['days_payable_outstanding'] ?? 0, 1) }}
                    </div>
                    <div class="text-sm text-gray-600">Hari Hutang (DPO)</div>
                </div>

                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ number_format($cash_conversion['cash_conversion_cycle'] ?? 0, 1) }}
                    </div>
                    <div class="text-sm text-gray-600">Siklus Konversi Kas</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Liquidity Ratios -->
        @if(isset($liquidity_ratios))
        <x-filament::section>
            <x-slot name="heading">
                Rasio Likuiditas
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-indigo-50 rounded-lg">
                    <div class="text-2xl font-bold text-indigo-600">
                        {{ number_format($liquidity_ratios['current_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Current Ratio</div>
                    <div class="text-xs text-gray-500 mt-1">Ideal: 1.5 - 3.0</div>
                </div>

                <div class="text-center p-4 bg-pink-50 rounded-lg">
                    <div class="text-2xl font-bold text-pink-600">
                        {{ number_format($liquidity_ratios['quick_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Quick Ratio</div>
                    <div class="text-xs text-gray-500 mt-1">Ideal: 1.0 - 1.5</div>
                </div>

                <div class="text-center p-4 bg-teal-50 rounded-lg">
                    <div class="text-2xl font-bold text-teal-600">
                        {{ number_format($liquidity_ratios['cash_ratio'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-600">Cash Ratio</div>
                    <div class="text-xs text-gray-500 mt-1">Ideal: 0.1 - 0.2</div>
                </div>
            </div>
        </x-filament::section>
        @endif

        <!-- Working Capital Components -->
        @if(isset($components))
        <x-filament::section>
            <x-slot name="heading">
                Komponen Modal Kerja
            </x-slot>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Komponen
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jumlah
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Persentase
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @if(isset($components['current_assets']))
                        <tr class="bg-green-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                Aset Lancar
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format($components['current_assets']['total'] ?? 0, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ number_format(($components['current_assets']['total'] ?? 0) > 0 ? (($components['current_assets']['total'] ?? 0) / (($components['current_assets']['total'] ?? 0) + ($components['current_liabilities']['total'] ?? 0))) * 100 : 0, 2) }}%
                            </td>
                        </tr>
                        @if(isset($components['current_assets']['breakdown']))
                            @foreach($components['current_assets']['breakdown'] as $key => $value)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 pl-12">
                                    {{ ucfirst(str_replace('_', ' ', $key)) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                    Rp {{ number_format($value ?? 0, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                    {{ number_format(($components['current_assets']['total'] ?? 0) > 0 ? (($value ?? 0) / ($components['current_assets']['total'] ?? 0)) * 100 : 0, 2) }}%
                                </td>
                            </tr>
                            @endforeach
                        @endif
                        @endif

                        @if(isset($components['current_liabilities']))
                        <tr class="bg-red-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                Kewajiban Lancar
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format($components['current_liabilities']['total'] ?? 0, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ number_format(($components['current_liabilities']['total'] ?? 0) > 0 ? (($components['current_liabilities']['total'] ?? 0) / (($components['current_assets']['total'] ?? 0) + ($components['current_liabilities']['total'] ?? 0))) * 100 : 0, 2) }}%
                            </td>
                        </tr>
                        @if(isset($components['current_liabilities']['breakdown']))
                            @foreach($components['current_liabilities']['breakdown'] as $key => $value)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 pl-12">
                                    {{ ucfirst(str_replace('_', ' ', $key)) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                    Rp {{ number_format($value ?? 0, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                    {{ number_format(($components['current_liabilities']['total'] ?? 0) > 0 ? (($value ?? 0) / ($components['current_liabilities']['total'] ?? 0)) * 100 : 0, 2) }}%
                                </td>
                            </tr>
                            @endforeach
                        @endif
                        @endif

                        <tr class="bg-blue-50 font-bold">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                Modal Kerja Bersih
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                Rp {{ number_format($components['net_working_capital'] ?? 0, 0, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                100%
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </x-filament::section>
        @endif

        <!-- Working Capital Trends -->
        @if(isset($trends) && count($trends) > 0)
        <x-filament::section>
            <x-slot name="heading">
                Trend Modal Kerja (12 Bulan)
            </x-slot>

            <div class="h-96 w-full bg-white p-4 rounded-lg border">
                <canvas id="workingCapitalChart" style="width: 100%; height: 100%;"></canvas>

                <!-- Fallback content if chart fails -->
                <div id="workingCapitalChartFallback" style="display: none;" class="text-center py-8">
                    <p class="text-gray-600 mb-4">Chart sedang dimuat...</p>
                    <div class="text-sm text-gray-500">
                        <p>Data Modal Kerja:</p>
                        @foreach($trends as $trend)
                            <p>{{ $trend['period'] }}: Aset Lancar Rp {{ number_format($trend['current_assets']) }} | Modal Kerja Rp {{ number_format($trend['working_capital']) }}</p>
                        @endforeach
                    </div>
                </div>
            </div>
        </x-filament::section>
        @else
        <x-filament::section>
            <x-slot name="heading">
                Trend Modal Kerja (12 Bulan)
            </x-slot>

            <div class="text-center py-8 text-gray-500">
                Tidak ada data trend untuk ditampilkan
            </div>
        </x-filament::section>
        @endif
    </div>

    @if(isset($trends) && count($trends) > 0)
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing working capital chart...');
            setTimeout(initWorkingCapitalChart, 1000);
        });

        function initWorkingCapitalChart() {
            console.log('=== WORKING CAPITAL CHART INITIALIZATION START ===');

            if (typeof Chart === 'undefined') {
                console.error('Chart.js not loaded!');
                setTimeout(initWorkingCapitalChart, 500);
                return;
            }
            console.log('Chart.js is available');

            const canvas = document.getElementById('workingCapitalChart');
            if (!canvas) {
                console.error('Working capital chart canvas element not found!');
                return;
            }
            console.log('Working capital chart canvas element found');

            // Properly destroy existing chart
            if (window.workingCapitalChart) {
                if (typeof window.workingCapitalChart.destroy === 'function') {
                    console.log('Destroying existing working capital chart');
                    window.workingCapitalChart.destroy();
                }
                window.workingCapitalChart = null;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Could not get working capital chart canvas context!');
                return;
            }
            console.log('Working capital chart canvas context obtained');

            // Prepare chart data
            const trendsData = @json($trends);
            console.log('Working capital trends data:', trendsData);

            if (!trendsData || trendsData.length === 0) {
                console.error('No working capital trends data available!');
                return;
            }

            const labels = trendsData.map(item => item.period);
            const currentAssets = trendsData.map(item => item.current_assets);
            const currentLiabilities = trendsData.map(item => item.current_liabilities);
            const workingCapital = trendsData.map(item => item.working_capital);

            console.log('Working capital chart labels:', labels);
            console.log('Current assets data:', currentAssets);
            console.log('Current liabilities data:', currentLiabilities);
            console.log('Working capital data:', workingCapital);

            try {
                window.workingCapitalChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Aset Lancar',
                            data: currentAssets,
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        }, {
                            label: 'Kewajiban Lancar',
                            data: currentLiabilities,
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        }, {
                            label: 'Modal Kerja Bersih',
                            data: workingCapital,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Periode'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Rupiah'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toLocaleString('id-ID');
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += 'Rp ' + context.parsed.y.toLocaleString('id-ID');
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('=== WORKING CAPITAL CHART CREATED SUCCESSFULLY ===');
                console.log('Working capital chart instance:', window.workingCapitalChart);

            } catch (error) {
                console.error('=== ERROR CREATING WORKING CAPITAL CHART ===');
                console.error('Error:', error);

                // Simple fallback
                try {
                    window.workingCapitalChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Modal Kerja',
                                data: workingCapital,
                                borderColor: 'rgb(34, 197, 94)',
                                backgroundColor: 'rgba(34, 197, 94, 0.1)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                    console.log('Working capital chart fallback created');
                } catch (fallbackError) {
                    console.error('Working capital chart fallback failed:', fallbackError);
                }
            }
        }
    </script>
    @else
    <script>
        console.log('No working capital trends data available');
    </script>
    @endif
</x-filament-panels::page>
