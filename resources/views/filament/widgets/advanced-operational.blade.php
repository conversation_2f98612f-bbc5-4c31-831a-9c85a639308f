@php
    $data = $this->getViewData();
@endphp

<x-filament-widgets::widget>
    <!-- Header with Period & View Selectors -->
    <div class="mb-6 flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Operational Command Center</h2>

        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <!-- View Selector -->
            <div class="flex space-x-2">
                @foreach($data['view_selector']['options'] as $key => $label)
                    <button
                        wire:click="$set('selectedView', '{{ $key }}')"
                        class="px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
                               {{ $data['view_selector']['current'] === $key
                                  ? 'bg-green-600 text-white shadow-lg'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600' }}">
                        {{ $label }}
                    </button>
                @endforeach
            </div>

            <!-- Period Selector -->
            <div class="flex space-x-2">
                @foreach($data['period_selector']['options'] as $key => $label)
                    <button
                        wire:click="$set('selectedPeriod', '{{ $key }}')"
                        class="px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
                               {{ $data['period_selector']['current'] === $key
                                  ? 'bg-blue-600 text-white shadow-lg'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600' }}">
                        {{ $label }}
                    </button>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Operational Efficiency Score -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-green-500 via-teal-500 to-blue-500 rounded-2xl p-8 text-white shadow-2xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-xl font-semibold mb-2">Operational Efficiency Score</h3>
                    <div class="flex items-center space-x-4">
                        <div class="text-6xl font-bold">{{ $data['operational_efficiency']['score'] }}</div>
                        <div>
                            <div class="text-3xl font-bold">{{ $data['operational_efficiency']['grade'] }}</div>
                            <div class="text-sm opacity-80">Grade</div>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="w-32 h-32 relative">
                        <canvas id="efficiency-score-chart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Efficiency Metrics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-white border-opacity-20">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['operational_efficiency']['metrics']['on_time_delivery'], 1) }}%</div>
                    <div class="text-sm opacity-80">On-Time Delivery</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['operational_efficiency']['metrics']['production_efficiency'], 1) }}%</div>
                    <div class="text-sm opacity-80">Production Efficiency</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['operational_efficiency']['metrics']['inventory_turnover'], 1) }}x</div>
                    <div class="text-sm opacity-80">Inventory Turnover</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['operational_efficiency']['metrics']['total_orders']) }}</div>
                    <div class="text-sm opacity-80">Total Orders</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Operational Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Production Metrics -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Production Performance</h3>

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ number_format($data['production_metrics']['current']['production_volume']) }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Production Volume</div>
                    <div class="text-xs {{ $data['production_metrics']['growth']['volume'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $data['production_metrics']['growth']['volume'] >= 0 ? '+' : '' }}{{ number_format($data['production_metrics']['growth']['volume'], 1) }}%
                    </div>
                </div>
                <div class="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">Rp {{ number_format($data['production_metrics']['current']['production_cost'] / 1000000, 1) }}M</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Production Cost</div>
                    <div class="text-xs {{ $data['production_metrics']['growth']['cost'] <= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $data['production_metrics']['growth']['cost'] >= 0 ? '+' : '' }}{{ number_format($data['production_metrics']['growth']['cost'], 1) }}%
                    </div>
                </div>
                <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">Rp {{ number_format($data['production_metrics']['current']['cost_per_unit'], 0) }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Cost per Unit</div>
                    <div class="text-xs text-gray-500">Per unit</div>
                </div>
            </div>

            <div class="h-64">
                <canvas id="production-chart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Capacity Utilization -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Capacity Utilization</h3>

            <div class="space-y-4 mb-6">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Overall</span>
                    <span class="font-semibold">{{ number_format($data['capacity_utilization']['overall_utilization'], 1) }}%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $data['capacity_utilization']['overall_utilization'] }}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Machine</span>
                    <span class="font-semibold">{{ number_format($data['capacity_utilization']['machine_utilization'], 1) }}%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ $data['capacity_utilization']['machine_utilization'] }}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Labor</span>
                    <span class="font-semibold">{{ number_format($data['capacity_utilization']['labor_utilization'], 1) }}%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-yellow-600 h-2 rounded-full" style="width: {{ $data['capacity_utilization']['labor_utilization'] }}%"></div>
                </div>
            </div>

            <div class="h-32">
                <canvas id="capacity-chart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Secondary Metrics Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Inventory Analysis -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Inventory Health</h3>

            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-xl font-bold text-green-600">{{ number_format($data['inventory_analysis']['stock_health'], 1) }}%</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Stock Health</div>
                </div>
                <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-xl font-bold text-blue-600">{{ number_format($data['inventory_analysis']['turnover_rate'], 1) }}x</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Turnover Rate</div>
                </div>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm">Total Value</span>
                    <span class="font-semibold">Rp {{ number_format($data['inventory_analysis']['total_value'] / 1000000, 1) }}M</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Low Stock Items</span>
                    <span class="font-semibold text-yellow-600">{{ $data['inventory_analysis']['low_stock_items'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Stockout Items</span>
                    <span class="font-semibold text-red-600">{{ $data['inventory_analysis']['stockout_items'] }}</span>
                </div>
            </div>

            <div class="mt-4 h-32">
                <canvas id="inventory-chart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Quality Metrics -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quality Performance</h3>

            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-xl font-bold text-green-600">{{ number_format($data['quality_metrics']['quality_score'], 1) }}</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Quality Score</div>
                </div>
                <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-xl font-bold text-blue-600">{{ number_format($data['quality_metrics']['first_pass_yield'], 1) }}%</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">First Pass Yield</div>
                </div>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm">Defect Rate</span>
                    <span class="font-semibold {{ $data['quality_metrics']['defect_rate'] <= 3 ? 'text-green-600' : 'text-red-600' }}">
                        {{ number_format($data['quality_metrics']['defect_rate'], 1) }}%
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Rework Rate</span>
                    <span class="font-semibold">{{ number_format($data['quality_metrics']['rework_rate'], 1) }}%</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Customer Complaints</span>
                    <span class="font-semibold">{{ $data['quality_metrics']['customer_complaints'] }}</span>
                </div>
            </div>

            <div class="mt-4 h-32">
                <canvas id="quality-chart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Order Fulfillment -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Fulfillment</h3>

            <div class="grid grid-cols-3 gap-3 mb-4">
                <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-lg font-bold text-green-600">{{ number_format($data['order_fulfillment']['fulfillment_rate'], 1) }}%</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Fulfillment Rate</div>
                </div>
                <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-lg font-bold text-blue-600">{{ number_format($data['order_fulfillment']['average_lead_time'], 1) }}</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Avg Lead Time (days)</div>
                </div>
                <div class="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div class="text-lg font-bold text-purple-600">{{ number_format($data['order_fulfillment']['order_accuracy'], 1) }}%</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Order Accuracy</div>
                </div>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm">On-Time Delivery</span>
                    <span class="font-semibold {{ $data['order_fulfillment']['on_time_delivery'] >= 90 ? 'text-green-600' : 'text-yellow-600' }}">
                        {{ number_format($data['order_fulfillment']['on_time_delivery'], 1) }}%
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Pending Orders</span>
                    <span class="font-semibold">{{ $data['order_fulfillment']['pending_orders'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Total Orders</span>
                    <span class="font-semibold">{{ $data['order_fulfillment']['total_orders'] }}</span>
                </div>
            </div>
        </div>

        <!-- Supply Chain Health -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Supply Chain Health</h3>

            <div class="text-center mb-4">
                <div class="text-4xl font-bold text-blue-600">{{ number_format($data['supply_chain_health']['supply_chain_score'], 0) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Supply Chain Score</div>
            </div>

            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm">Supplier Performance</span>
                    <span class="font-semibold {{ $data['supply_chain_health']['supplier_performance'] >= 90 ? 'text-green-600' : 'text-yellow-600' }}">
                        {{ number_format($data['supply_chain_health']['supplier_performance'], 1) }}%
                    </span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $data['supply_chain_health']['supplier_performance'] }}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm">Active Suppliers</span>
                    <span class="font-semibold">{{ $data['supply_chain_health']['active_suppliers'] }}</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm">Purchase Orders</span>
                    <span class="font-semibold">{{ $data['supply_chain_health']['total_purchase_orders'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Trends & Resource Optimization -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Performance Trends -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Trends (6 Months)</h3>

            <div class="h-64">
                <canvas id="performance-trends-chart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Resource Optimization -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Resource Optimization</h3>

            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-xl font-bold text-green-600">{{ number_format($data['resource_optimization']['resource_score'], 1) }}</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Resource Score</div>
                </div>
                <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-xl font-bold text-blue-600">{{ number_format($data['resource_optimization']['labor_productivity'], 1) }}</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Labor Productivity</div>
                </div>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm">Machine Efficiency</span>
                    <span class="font-semibold">{{ number_format($data['resource_optimization']['machine_efficiency'], 1) }}%</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Energy per Unit</span>
                    <span class="font-semibold">{{ number_format($data['resource_optimization']['energy_consumption'], 1) }} kWh</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Waste Reduction</span>
                    <span class="font-semibold text-green-600">{{ number_format($data['resource_optimization']['waste_reduction'], 1) }}%</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm">Cost Savings</span>
                    <span class="font-semibold text-green-600">Rp {{ number_format($data['resource_optimization']['cost_savings'] / 1000, 0) }}K</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottleneck Analysis & Alerts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Bottleneck Stations -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Bottleneck Analysis</h3>

            <div class="space-y-4">
                @foreach($data['capacity_utilization']['bottleneck_stations'] as $station)
                    <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">{{ $station['name'] }}</span>
                            <span class="text-sm {{ $station['utilization'] >= 90 ? 'text-red-600' : ($station['utilization'] >= 80 ? 'text-yellow-600' : 'text-green-600') }}">
                                {{ number_format($station['utilization'], 1) }}%
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                            <div class="h-2 rounded-full {{ $station['utilization'] >= 90 ? 'bg-red-500' : ($station['utilization'] >= 80 ? 'bg-yellow-500' : 'bg-green-500') }}"
                                 style="width: {{ $station['utilization'] }}%"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Operational Alerts -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Operational Alerts</h3>

            @if(!empty($data['operational_alerts']))
                <div class="space-y-4">
                    @foreach($data['operational_alerts'] as $alert)
                        <div class="p-4 rounded-lg border-l-4 {{ $alert['type'] === 'critical' ? 'bg-red-50 border-red-500 dark:bg-red-900/20' : 'bg-yellow-50 border-yellow-500 dark:bg-yellow-900/20' }}">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    @if($alert['icon'] === 'heroicon-m-exclamation-triangle')
                                        <x-heroicon-m-exclamation-triangle class="w-5 h-5 {{ $alert['type'] === 'critical' ? 'text-red-500' : 'text-yellow-500' }}" />
                                    @elseif($alert['icon'] === 'heroicon-m-clock')
                                        <x-heroicon-m-clock class="w-5 h-5 {{ $alert['type'] === 'critical' ? 'text-red-500' : 'text-yellow-500' }}" />
                                    @else
                                        <x-heroicon-m-cog-6-tooth class="w-5 h-5 {{ $alert['type'] === 'critical' ? 'text-red-500' : 'text-yellow-500' }}" />
                                    @endif
                                </div>
                                <div class="ml-3">
                                    <h4 class="font-medium {{ $alert['type'] === 'critical' ? 'text-red-800 dark:text-red-200' : 'text-yellow-800 dark:text-yellow-200' }}">
                                        {{ $alert['title'] }}
                                    </h4>
                                    <p class="text-sm {{ $alert['type'] === 'critical' ? 'text-red-700 dark:text-red-300' : 'text-yellow-700 dark:text-yellow-300' }} mt-1">
                                        {{ $alert['message'] }}
                                    </p>
                                    <p class="text-xs {{ $alert['type'] === 'critical' ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400' }} mt-2">
                                        <strong>Action:</strong> {{ $alert['action'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <x-heroicon-o-check-circle class="w-12 h-12 mx-auto mb-2 text-green-500" />
                    <p>All operations running smoothly!</p>
                </div>
            @endif
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Efficiency Score Doughnut Chart
                const efficiencyCtx = document.getElementById('efficiency-score-chart');
                if (efficiencyCtx) {
                    new Chart(efficiencyCtx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [{{ $data['operational_efficiency']['score'] }}, {{ 100 - $data['operational_efficiency']['score'] }}],
                                backgroundColor: ['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.2)'],
                                borderWidth: 0,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            cutout: '70%',
                            plugins: {
                                legend: { display: false },
                                tooltip: { enabled: false }
                            }
                        }
                    });
                }

                // Production Chart
                const productionCtx = document.getElementById('production-chart');
                if (productionCtx) {
                    // Get monthly production data
                    const productionData = @json($data['production_monthly'] ?? []);
                    const labels = productionData.map(item => item.month);
                    const volumeData = productionData.map(item => item.production_volume);
                    const costData = productionData.map(item => item.production_cost);

                    new Chart(productionCtx, {
                        type: 'line',
                        data: {
                            labels: labels.length > 0 ? labels : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                            datasets: [{
                                label: 'Production Volume',
                                data: volumeData.length > 0 ? volumeData : [0, 0, 0, 0, 0, 0],
                                borderColor: 'rgb(59, 130, 246)',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4,
                                fill: true,
                            }, {
                                label: 'Production Cost (M)',
                                data: costData.length > 0 ? costData : [0, 0, 0, 0, 0, 0],
                                borderColor: 'rgb(249, 115, 22)',
                                backgroundColor: 'rgba(249, 115, 22, 0.1)',
                                tension: 0.4,
                                fill: true,
                                yAxisID: 'y1',
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index',
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    titleColor: '#fff',
                                    bodyColor: '#fff',
                                    borderColor: '#3b82f6',
                                    borderWidth: 1,
                                }
                            },
                            scales: {
                                y: {
                                    type: 'linear',
                                    display: true,
                                    position: 'left',
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.1)',
                                    },
                                    ticks: {
                                        callback: function(value) {
                                            return value + ' units';
                                        }
                                    }
                                },
                                y1: {
                                    type: 'linear',
                                    display: true,
                                    position: 'right',
                                    beginAtZero: true,
                                    grid: {
                                        drawOnChartArea: false,
                                    },
                                    ticks: {
                                        callback: function(value) {
                                            return 'Rp ' + value + 'M';
                                        }
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false,
                                    }
                                }
                            },
                            animation: {
                                duration: 2000,
                                easing: 'easeInOutQuart',
                            }
                        }
                    });
                }

                // Capacity Utilization Chart
                const capacityCtx = document.getElementById('capacity-chart');
                if (capacityCtx) {
                    new Chart(capacityCtx, {
                        type: 'radar',
                        data: {
                            labels: ['Overall', 'Machine', 'Labor', 'Available'],
                            datasets: [{
                                label: 'Utilization %',
                                data: [
                                    {{ $data['capacity_utilization']['overall_utilization'] }},
                                    {{ $data['capacity_utilization']['machine_utilization'] }},
                                    {{ $data['capacity_utilization']['labor_utilization'] }},
                                    {{ $data['capacity_utilization']['available_capacity'] }}
                                ],
                                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                                borderColor: 'rgb(34, 197, 94)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgb(34, 197, 94)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgb(34, 197, 94)'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.parsed.r.toFixed(1) + '%';
                                        }
                                    }
                                }
                            },
                            scales: {
                                r: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        stepSize: 20
                                    }
                                }
                            },
                            animation: {
                                duration: 1500,
                                easing: 'easeOutBounce',
                            }
                        }
                    });
                }

                // Inventory Chart
                const inventoryCtx = document.getElementById('inventory-chart');
                if (inventoryCtx) {
                    new Chart(inventoryCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Healthy Stock', 'Low Stock', 'Stockout'],
                            datasets: [{
                                data: [
                                    {{ $data['inventory_analysis']['total_items'] - $data['inventory_analysis']['low_stock_items'] - $data['inventory_analysis']['stockout_items'] }},
                                    {{ $data['inventory_analysis']['low_stock_items'] }},
                                    {{ $data['inventory_analysis']['stockout_items'] }}
                                ],
                                backgroundColor: [
                                    'rgba(34, 197, 94, 0.8)',
                                    'rgba(234, 179, 8, 0.8)',
                                    'rgba(239, 68, 68, 0.8)'
                                ],
                                borderColor: [
                                    'rgb(34, 197, 94)',
                                    'rgb(234, 179, 8)',
                                    'rgb(239, 68, 68)'
                                ],
                                borderWidth: 2,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        usePointStyle: true,
                                        padding: 15,
                                        font: {
                                            size: 10
                                        }
                                    }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.label + ': ' + context.parsed + ' items';
                                        }
                                    }
                                }
                            },
                            animation: {
                                animateRotate: true,
                                animateScale: true,
                                duration: 2000,
                            }
                        }
                    });
                }

                // Quality Chart
                const qualityCtx = document.getElementById('quality-chart');
                if (qualityCtx) {
                    new Chart(qualityCtx, {
                        type: 'bar',
                        data: {
                            labels: ['Quality Score', 'First Pass Yield', 'Inspection Pass'],
                            datasets: [{
                                data: [
                                    {{ $data['quality_metrics']['quality_score'] }},
                                    {{ $data['quality_metrics']['first_pass_yield'] }},
                                    {{ $data['quality_metrics']['inspection_pass_rate'] }}
                                ],
                                backgroundColor: [
                                    'rgba(34, 197, 94, 0.8)',
                                    'rgba(59, 130, 246, 0.8)',
                                    'rgba(139, 92, 246, 0.8)'
                                ],
                                borderColor: [
                                    'rgb(34, 197, 94)',
                                    'rgb(59, 130, 246)',
                                    'rgb(139, 92, 246)'
                                ],
                                borderWidth: 2,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.parsed.y.toFixed(1) + '%';
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 1500,
                                easing: 'easeOutBounce',
                            }
                        }
                    });
                }

                // Performance Trends Chart
                const performanceCtx = document.getElementById('performance-trends-chart');
                if (performanceCtx) {
                    new Chart(performanceCtx, {
                        type: 'line',
                        data: {
                            labels: [@foreach($data['performance_trends'] as $trend)'{{ $trend['month'] }}',@endforeach],
                            datasets: [{
                                label: 'Production',
                                data: [@foreach($data['performance_trends'] as $trend){{ $trend['production'] }},@endforeach],
                                borderColor: 'rgb(59, 130, 246)',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y',
                            }, {
                                label: 'Orders',
                                data: [@foreach($data['performance_trends'] as $trend){{ $trend['orders'] }},@endforeach],
                                borderColor: 'rgb(34, 197, 94)',
                                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y',
                            }, {
                                label: 'Efficiency %',
                                data: [@foreach($data['performance_trends'] as $trend){{ $trend['efficiency'] }},@endforeach],
                                borderColor: 'rgb(249, 115, 22)',
                                backgroundColor: 'rgba(249, 115, 22, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y1',
                            }, {
                                label: 'Quality %',
                                data: [@foreach($data['performance_trends'] as $trend){{ $trend['quality'] }},@endforeach],
                                borderColor: 'rgb(139, 92, 246)',
                                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y1',
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index',
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    callbacks: {
                                        label: function(context) {
                                            if (context.datasetIndex >= 2) {
                                                return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                                            }
                                            return context.dataset.label + ': ' + context.parsed.y;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    type: 'linear',
                                    display: true,
                                    position: 'left',
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Volume'
                                    }
                                },
                                y1: {
                                    type: 'linear',
                                    display: true,
                                    position: 'right',
                                    beginAtZero: true,
                                    max: 100,
                                    grid: {
                                        drawOnChartArea: false,
                                    },
                                    title: {
                                        display: true,
                                        text: 'Percentage (%)'
                                    }
                                }
                            },
                            animation: {
                                duration: 2000,
                                easing: 'easeInOutQuart',
                            }
                        }
                    });
                }
            });
        </script>
    @endpush
</x-filament-widgets::widget>
