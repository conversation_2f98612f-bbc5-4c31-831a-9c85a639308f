@php
    $data = $this->getViewData();
@endphp

<x-filament-widgets::widget>
    <!-- Period Selector -->
    <div class="mb-6 flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Financial Command Center</h2>
        <div class="flex space-x-2">
            @foreach($data['period_selector']['options'] as $key => $label)
                <button
                    wire:click="$set('selectedPeriod', '{{ $key }}')"
                    class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                           {{ $data['period_selector']['current'] === $key
                              ? 'bg-blue-600 text-white shadow-lg'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600' }}">
                    {{ $label }}
                </button>
            @endforeach
        </div>
    </div>

    <!-- Financial Health Score -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-8 text-white shadow-2xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-xl font-semibold mb-2">Financial Health Score</h3>
                    <div class="flex items-center space-x-4">
                        <div class="text-6xl font-bold">{{ $data['financial_health']['score'] }}</div>
                        <div>
                            <div class="text-3xl font-bold">{{ $data['financial_health']['grade'] }}</div>
                            <div class="text-sm opacity-80">Grade</div>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="w-32 h-32 relative">
                        <canvas id="health-score-chart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Health Metrics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-white border-opacity-20">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['financial_health']['metrics']['profit_margin'], 1) }}%</div>
                    <div class="text-sm opacity-80">Profit Margin</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['financial_health']['metrics']['current_ratio'], 2) }}</div>
                    <div class="text-sm opacity-80">Current Ratio</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ number_format($data['financial_health']['metrics']['cash_ratio'], 2) }}</div>
                    <div class="text-sm opacity-80">Cash Ratio</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold">Rp {{ number_format($data['financial_health']['metrics']['revenue'] / 1000000, 1) }}M</div>
                    <div class="text-sm opacity-80">Revenue</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Profitability Analysis -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Profitability Analysis</h3>

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">Rp {{ number_format($data['profitability_analysis']['current']['revenue'] / 1000000, 1) }}M</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Revenue</div>
                    <div class="text-xs {{ $data['profitability_analysis']['growth']['revenue'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $data['profitability_analysis']['growth']['revenue'] >= 0 ? '+' : '' }}{{ number_format($data['profitability_analysis']['growth']['revenue'], 1) }}%
                    </div>
                </div>
                <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">Rp {{ number_format($data['profitability_analysis']['current']['expenses'] / 1000000, 1) }}M</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Expenses</div>
                    <div class="text-xs {{ $data['profitability_analysis']['growth']['expenses'] <= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $data['profitability_analysis']['growth']['expenses'] >= 0 ? '+' : '' }}{{ number_format($data['profitability_analysis']['growth']['expenses'], 1) }}%
                    </div>
                </div>
                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">Rp {{ number_format($data['profitability_analysis']['current']['profit'] / 1000000, 1) }}M</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Profit</div>
                    <div class="text-xs {{ $data['profitability_analysis']['growth']['profit'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ $data['profitability_analysis']['growth']['profit'] >= 0 ? '+' : '' }}{{ number_format($data['profitability_analysis']['growth']['profit'], 1) }}%
                    </div>
                </div>
            </div>

            <div class="h-64">
                <canvas id="profitability-chart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Working Capital -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Working Capital</h3>

            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Working Capital</span>
                    <span class="font-semibold">Rp {{ number_format($data['working_capital']['working_capital'] / 1000000, 1) }}M</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Current Ratio</span>
                    <span class="font-semibold {{ $data['working_capital']['current_ratio'] >= 1.5 ? 'text-green-600' : 'text-red-600' }}">
                        {{ number_format($data['working_capital']['current_ratio'], 2) }}
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Quick Ratio</span>
                    <span class="font-semibold {{ $data['working_capital']['quick_ratio'] >= 1.0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ number_format($data['working_capital']['quick_ratio'], 2) }}
                    </span>
                </div>
            </div>

            <div class="mt-6 h-32">
                <canvas id="working-capital-chart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Cash Flow Forecast -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Cash Flow Forecast (3 Months)</h3>

            <div class="space-y-4">
                @foreach($data['cash_flow_forecast'] as $forecast)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <div class="font-medium">{{ $forecast['month'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                Net: Rp {{ number_format($forecast['net_flow'] / 1000000, 1) }}M
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-green-600">+Rp {{ number_format($forecast['projected_inflow'] / 1000000, 1) }}M</div>
                            <div class="text-sm text-red-600">-Rp {{ number_format($forecast['projected_outflow'] / 1000000, 1) }}M</div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="mt-6 h-48">
                <canvas id="cash-flow-forecast-chart" class="w-full h-full"></canvas>
            </div>
        </div>

        <!-- Financial Ratios -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Ratios</h3>

            <div class="space-y-6">
                <!-- Profitability Ratios -->
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Profitability</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm">Gross Margin</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['profitability']['gross_margin'], 1) }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">ROA</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['profitability']['roa'], 1) }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">ROE</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['profitability']['roe'], 1) }}%</span>
                        </div>
                    </div>
                </div>

                <!-- Liquidity Ratios -->
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Liquidity</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm">Current Ratio</span>
                            <span class="font-medium {{ $data['financial_ratios']['liquidity']['current_ratio'] >= 1.5 ? 'text-green-600' : 'text-red-600' }}">
                                {{ number_format($data['financial_ratios']['liquidity']['current_ratio'], 2) }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">Quick Ratio</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['liquidity']['quick_ratio'], 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">Cash Ratio</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['liquidity']['cash_ratio'], 2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Leverage Ratios -->
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Leverage</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm">Debt to Equity</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['leverage']['debt_to_equity'], 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">Debt to Assets</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['leverage']['debt_to_assets'], 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm">Equity Ratio</span>
                            <span class="font-medium">{{ number_format($data['financial_ratios']['leverage']['equity_ratio'], 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers & Expense Trends -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Top Customers -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Customers</h3>

            <div class="space-y-3">
                @foreach($data['top_customers'] as $customer)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                        <div>
                            <div class="font-medium">{{ $customer['name'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                Rp {{ number_format($customer['revenue'] / 1000000, 1) }}M
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm {{ $customer['growth'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                {{ $customer['growth'] >= 0 ? '+' : '' }}{{ number_format($customer['growth'], 1) }}%
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Expense Trends -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Expense Trends (6 Months)</h3>

            <div class="h-64">
                <canvas id="expense-trends-chart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Budget Variance & Alerts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Budget Variance -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Budget vs Actual</h3>

            <div class="space-y-4">
                @foreach($data['budget_variance'] as $category => $variance)
                    <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium capitalize">{{ $category }}</span>
                            <span class="text-sm {{ $variance['variance'] >= 0 ? 'text-red-600' : 'text-green-600' }}">
                                {{ $variance['variance'] >= 0 ? '+' : '' }}{{ number_format($variance['variance'], 1) }}%
                            </span>
                        </div>
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                            <span>Budget: Rp {{ number_format($variance['budget'] / 1000000, 1) }}M</span>
                            <span>Actual: Rp {{ number_format($variance['actual'] / 1000000, 1) }}M</span>
                        </div>
                        <div class="mt-2 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $variance['budget'] > 0 ? min(100, ($variance['actual'] / $variance['budget']) * 100) : 0 }}%"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Advanced Alerts -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Smart Alerts</h3>

            @if(!empty($data['alerts']))
                <div class="space-y-4">
                    @foreach($data['alerts'] as $alert)
                        <div class="p-4 rounded-lg border-l-4 {{ $alert['type'] === 'critical' ? 'bg-red-50 border-red-500 dark:bg-red-900/20' : 'bg-yellow-50 border-yellow-500 dark:bg-yellow-900/20' }}">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    @if($alert['icon'] === 'heroicon-m-exclamation-triangle')
                                        <x-heroicon-m-exclamation-triangle class="w-5 h-5 {{ $alert['type'] === 'critical' ? 'text-red-500' : 'text-yellow-500' }}" />
                                    @else
                                        <x-heroicon-m-arrow-trending-down class="w-5 h-5 {{ $alert['type'] === 'critical' ? 'text-red-500' : 'text-yellow-500' }}" />
                                    @endif
                                </div>
                                <div class="ml-3">
                                    <h4 class="font-medium {{ $alert['type'] === 'critical' ? 'text-red-800 dark:text-red-200' : 'text-yellow-800 dark:text-yellow-200' }}">
                                        {{ $alert['title'] }}
                                    </h4>
                                    <p class="text-sm {{ $alert['type'] === 'critical' ? 'text-red-700 dark:text-red-300' : 'text-yellow-700 dark:text-yellow-300' }} mt-1">
                                        {{ $alert['message'] }}
                                    </p>
                                    <p class="text-xs {{ $alert['type'] === 'critical' ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400' }} mt-2">
                                        <strong>Action:</strong> {{ $alert['action'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <x-heroicon-o-check-circle class="w-12 h-12 mx-auto mb-2 text-green-500" />
                    <p>All systems are running smoothly!</p>
                </div>
            @endif
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Health Score Doughnut Chart
                const healthCtx = document.getElementById('health-score-chart');
                if (healthCtx) {
                    new Chart(healthCtx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [{{ $data['financial_health']['score'] }}, {{ 100 - $data['financial_health']['score'] }}],
                                backgroundColor: ['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.2)'],
                                borderWidth: 0,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            cutout: '70%',
                            plugins: {
                                legend: { display: false },
                                tooltip: { enabled: false }
                            }
                        }
                    });
                }

                // Profitability Chart
                const profitCtx = document.getElementById('profitability-chart');
                if (profitCtx) {
                    // Get monthly data for the last 6 months
                    const monthlyData = @json($data['profitability_monthly'] ?? []);
                    const labels = monthlyData.map(item => item.month);
                    const revenueData = monthlyData.map(item => item.revenue / 1000000);
                    const expenseData = monthlyData.map(item => item.expenses / 1000000);
                    const profitData = monthlyData.map(item => item.profit / 1000000);

                    new Chart(profitCtx, {
                        type: 'line',
                        data: {
                            labels: labels.length > 0 ? labels : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                            datasets: [{
                                label: 'Revenue',
                                data: revenueData.length > 0 ? revenueData : [0, 0, 0, 0, 0, 0],
                                borderColor: 'rgb(34, 197, 94)',
                                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                                tension: 0.4,
                                fill: true,
                            }, {
                                label: 'Expenses',
                                data: expenseData.length > 0 ? expenseData : [0, 0, 0, 0, 0, 0],
                                borderColor: 'rgb(239, 68, 68)',
                                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                tension: 0.4,
                                fill: true,
                            }, {
                                label: 'Profit',
                                data: profitData.length > 0 ? profitData : [0, 0, 0, 0, 0, 0],
                                borderColor: 'rgb(59, 130, 246)',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4,
                                fill: true,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index',
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    titleColor: '#fff',
                                    bodyColor: '#fff',
                                    borderColor: '#3b82f6',
                                    borderWidth: 1,
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.1)',
                                    },
                                    ticks: {
                                        callback: function(value) {
                                            return 'Rp ' + value + 'M';
                                        }
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false,
                                    }
                                }
                            },
                            animation: {
                                duration: 2000,
                                easing: 'easeInOutQuart',
                            }
                        }
                    });
                }

                // Working Capital Chart
                const workingCapitalCtx = document.getElementById('working-capital-chart');
                if (workingCapitalCtx) {
                    new Chart(workingCapitalCtx, {
                        type: 'bar',
                        data: {
                            labels: ['Current Assets', 'Current Liabilities', 'Working Capital'],
                            datasets: [{
                                data: [
                                    {{ $data['working_capital']['components']['current_assets'] / 1000000 }},
                                    {{ $data['working_capital']['components']['current_liabilities'] / 1000000 }},
                                    {{ $data['working_capital']['working_capital'] / 1000000 }}
                                ],
                                backgroundColor: [
                                    'rgba(34, 197, 94, 0.8)',
                                    'rgba(239, 68, 68, 0.8)',
                                    'rgba(59, 130, 246, 0.8)'
                                ],
                                borderColor: [
                                    'rgb(34, 197, 94)',
                                    'rgb(239, 68, 68)',
                                    'rgb(59, 130, 246)'
                                ],
                                borderWidth: 2,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return 'Rp ' + context.parsed.y.toFixed(1) + 'M';
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return 'Rp ' + value + 'M';
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 1500,
                                easing: 'easeOutBounce',
                            }
                        }
                    });
                }

                // Cash Flow Forecast Chart
                const cashFlowCtx = document.getElementById('cash-flow-forecast-chart');
                if (cashFlowCtx) {
                    new Chart(cashFlowCtx, {
                        type: 'line',
                        data: {
                            labels: [@foreach($data['cash_flow_forecast'] as $forecast)'{{ $forecast['month'] }}',@endforeach],
                            datasets: [{
                                label: 'Projected Inflow',
                                data: [@foreach($data['cash_flow_forecast'] as $forecast){{ $forecast['projected_inflow'] / 1000000 }},@endforeach],
                                borderColor: 'rgb(34, 197, 94)',
                                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                                tension: 0.4,
                                fill: false,
                            }, {
                                label: 'Projected Outflow',
                                data: [@foreach($data['cash_flow_forecast'] as $forecast){{ $forecast['projected_outflow'] / 1000000 }},@endforeach],
                                borderColor: 'rgb(239, 68, 68)',
                                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                tension: 0.4,
                                fill: false,
                            }, {
                                label: 'Net Flow',
                                data: [@foreach($data['cash_flow_forecast'] as $forecast){{ $forecast['net_flow'] / 1000000 }},@endforeach],
                                borderColor: 'rgb(59, 130, 246)',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4,
                                fill: true,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index',
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    callbacks: {
                                        label: function(context) {
                                            return context.dataset.label + ': Rp ' + context.parsed.y.toFixed(1) + 'M';
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return 'Rp ' + value + 'M';
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 2000,
                                easing: 'easeInOutQuart',
                            }
                        }
                    });
                }

                // Expense Trends Chart
                const expenseCtx = document.getElementById('expense-trends-chart');
                if (expenseCtx) {
                    new Chart(expenseCtx, {
                        type: 'line',
                        data: {
                            labels: [@foreach($data['expense_trends'] as $trend)'{{ $trend['month'] }}',@endforeach],
                            datasets: [{
                                label: 'Operational',
                                data: [@foreach($data['expense_trends'] as $trend){{ $trend['operational'] / 1000000 }},@endforeach],
                                borderColor: 'rgb(239, 68, 68)',
                                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                                tension: 0.4,
                            }, {
                                label: 'Administrative',
                                data: [@foreach($data['expense_trends'] as $trend){{ $trend['administrative'] / 1000000 }},@endforeach],
                                borderColor: 'rgb(249, 115, 22)',
                                backgroundColor: 'rgba(249, 115, 22, 0.1)',
                                tension: 0.4,
                            }, {
                                label: 'Marketing',
                                data: [@foreach($data['expense_trends'] as $trend){{ $trend['marketing'] / 1000000 }},@endforeach],
                                borderColor: 'rgb(139, 92, 246)',
                                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                tension: 0.4,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index',
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    callbacks: {
                                        label: function(context) {
                                            return context.dataset.label + ': Rp ' + context.parsed.y.toFixed(1) + 'M';
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    stacked: false,
                                    ticks: {
                                        callback: function(value) {
                                            return 'Rp ' + value + 'M';
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 2000,
                                easing: 'easeInOutQuart',
                            }
                        }
                    });
                }
            });
        </script>
    @endpush
</x-filament-widgets::widget>
