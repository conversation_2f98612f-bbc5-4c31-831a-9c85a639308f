@php
    $data = $this->getViewData();
@endphp

<x-filament-widgets::widget>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Revenue Card -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium">Pendapatan Bulan Ini</p>
                    <p class="text-2xl font-bold">Rp {{ number_format($data['revenue']['current'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        @if($data['revenue']['growth'] >= 0)
                            <x-heroicon-m-arrow-trending-up class="w-4 h-4 text-green-300 mr-1" />
                            <span class="text-green-300 text-sm">+{{ number_format($data['revenue']['growth'], 1) }}%</span>
                        @else
                            <x-heroicon-m-arrow-trending-down class="w-4 h-4 text-red-300 mr-1" />
                            <span class="text-red-300 text-sm">{{ number_format($data['revenue']['growth'], 1) }}%</span>
                        @endif
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3">
                    <x-heroicon-o-banknotes class="w-8 h-8" />
                </div>
            </div>
            
            <!-- Mini Chart -->
            <div class="mt-4">
                <canvas id="revenue-mini-chart" class="w-full h-12"></canvas>
            </div>
        </div>

        <!-- Expenses Card -->
        <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-red-100 text-sm font-medium">Biaya Bulan Ini</p>
                    <p class="text-2xl font-bold">Rp {{ number_format($data['expenses']['current'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        @if($data['expenses']['growth'] >= 0)
                            <x-heroicon-m-arrow-trending-up class="w-4 h-4 text-orange-300 mr-1" />
                            <span class="text-orange-300 text-sm">+{{ number_format($data['expenses']['growth'], 1) }}%</span>
                        @else
                            <x-heroicon-m-arrow-trending-down class="w-4 h-4 text-green-300 mr-1" />
                            <span class="text-green-300 text-sm">{{ number_format($data['expenses']['growth'], 1) }}%</span>
                        @endif
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3">
                    <x-heroicon-o-credit-card class="w-8 h-8" />
                </div>
            </div>
            
            <!-- Mini Chart -->
            <div class="mt-4">
                <canvas id="expenses-mini-chart" class="w-full h-12"></canvas>
            </div>
        </div>

        <!-- Profit Card -->
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">Laba Bulan Ini</p>
                    <p class="text-2xl font-bold">Rp {{ number_format($data['profit']['current'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        @if($data['profit']['growth'] >= 0)
                            <x-heroicon-m-arrow-trending-up class="w-4 h-4 text-green-300 mr-1" />
                            <span class="text-green-300 text-sm">+{{ number_format($data['profit']['growth'], 1) }}%</span>
                        @else
                            <x-heroicon-m-arrow-trending-down class="w-4 h-4 text-red-300 mr-1" />
                            <span class="text-red-300 text-sm">{{ number_format($data['profit']['growth'], 1) }}%</span>
                        @endif
                    </div>
                    <p class="text-green-200 text-xs mt-1">Margin: {{ number_format($data['profit']['margin'], 1) }}%</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3">
                    <x-heroicon-o-chart-bar class="w-8 h-8" />
                </div>
            </div>
        </div>

        <!-- Cash Position Card -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium">Posisi Kas</p>
                    <p class="text-2xl font-bold">Rp {{ number_format($data['cash']['current'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        <span class="text-purple-200 text-sm">Rasio Lancar: {{ number_format($data['cash']['ratio'], 2) }}</span>
                    </div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3">
                    <x-heroicon-o-wallet class="w-8 h-8" />
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Section -->
    @if(!empty($data['alerts']))
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Peringatan & Notifikasi</h3>
            <div class="space-y-3">
                @foreach($data['alerts'] as $alert)
                    <div class="flex items-center p-4 rounded-lg {{ $alert['type'] === 'danger' ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200' }}">
                        <div class="flex-shrink-0">
                            @if($alert['icon'] === 'heroicon-m-exclamation-triangle')
                                <x-heroicon-m-exclamation-triangle class="w-5 h-5 {{ $alert['type'] === 'danger' ? 'text-red-400' : 'text-yellow-400' }}" />
                            @else
                                <x-heroicon-m-arrow-trending-down class="w-5 h-5 {{ $alert['type'] === 'danger' ? 'text-red-400' : 'text-yellow-400' }}" />
                            @endif
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium {{ $alert['type'] === 'danger' ? 'text-red-800' : 'text-yellow-800' }}">
                                {{ $alert['message'] }}
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Revenue mini chart
                const revenueCtx = document.getElementById('revenue-mini-chart');
                if (revenueCtx) {
                    new Chart(revenueCtx, {
                        type: 'line',
                        data: {
                            labels: ['', '', '', '', '', ''],
                            datasets: [{
                                data: @json($data['revenue']['trend']),
                                borderColor: 'rgba(255, 255, 255, 0.8)',
                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4,
                                pointRadius: 0,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: { enabled: false }
                            },
                            scales: {
                                x: { display: false },
                                y: { display: false }
                            },
                            elements: {
                                point: { radius: 0 }
                            }
                        }
                    });
                }

                // Expenses mini chart
                const expensesCtx = document.getElementById('expenses-mini-chart');
                if (expensesCtx) {
                    new Chart(expensesCtx, {
                        type: 'line',
                        data: {
                            labels: ['', '', '', '', '', ''],
                            datasets: [{
                                data: @json($data['expenses']['trend']),
                                borderColor: 'rgba(255, 255, 255, 0.8)',
                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4,
                                pointRadius: 0,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: { display: false },
                                tooltip: { enabled: false }
                            },
                            scales: {
                                x: { display: false },
                                y: { display: false }
                            },
                            elements: {
                                point: { radius: 0 }
                            }
                        }
                    });
                }
            });
        </script>
    @endpush
</x-filament-widgets::widget>
