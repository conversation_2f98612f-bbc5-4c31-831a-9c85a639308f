<x-filament-widgets::widget>
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-lg mb-6">
        <div class="flex items-center space-x-3">
            <div class="text-4xl">🏠</div>
            <div>
                <h1 class="text-2xl font-bold">Selamat Datang di ERP System</h1>
                <p class="text-blue-100">Akses cepat ke semua fitur sistem manajemen perusahaan</p>
            </div>
        </div>
    </div>

    @foreach($this->getQuickActions() as $sectionKey => $section)
        <x-filament::section class="mb-6">
            <x-slot name="heading">
                <div class="flex items-center space-x-3">
                    @if($sectionKey === 'quick_actions')
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <span class="text-green-600 text-sm">⚡</span>
                        </div>
                    @elseif($sectionKey === 'dashboards')
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 text-sm">📊</span>
                        </div>
                    @elseif($sectionKey === 'data_master')
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <span class="text-purple-600 text-sm">🗃️</span>
                        </div>
                    @elseif($sectionKey === 'sales_crm')
                        <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                            <span class="text-emerald-600 text-sm">💰</span>
                        </div>
                    @elseif($sectionKey === 'purchasing')
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                            <span class="text-orange-600 text-sm">🛒</span>
                        </div>
                    @elseif($sectionKey === 'financial')
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <span class="text-yellow-600 text-sm">🏦</span>
                        </div>
                    @elseif($sectionKey === 'reports')
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <span class="text-red-600 text-sm">📈</span>
                        </div>
                    @elseif($sectionKey === 'fixed_assets')
                        <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                            <span class="text-indigo-600 text-sm">🏢</span>
                        </div>
                    @elseif($sectionKey === 'project_management')
                        <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                            <span class="text-teal-600 text-sm">⚙️</span>
                        </div>
                    @elseif($sectionKey === 'human_resources')
                        <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                            <span class="text-pink-600 text-sm">👥</span>
                        </div>
                    @else
                        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-sm">⚙️</span>
                        </div>
                    @endif
                    <span class="text-lg font-semibold text-gray-800">{{ $section['title'] }}</span>
                </div>
            </x-slot>

            <x-slot name="description">
                <span class="text-gray-600">{{ $section['description'] }}</span>
            </x-slot>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                @foreach($section['items'] as $item)
                    <a href="{{ $item['url'] }}"
                       class="block p-3 bg-white hover:bg-blue-50 rounded-lg border border-gray-200 hover:border-blue-300 transition-all duration-200 group hover:shadow-lg transform hover:-translate-y-1">
                        <div class="text-center">
                            <div class="text-2xl mb-2 group-hover:scale-110 transition-transform duration-200">{{ $item['icon'] }}</div>
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 text-xs mb-1 leading-tight">
                                {{ $item['label'] }}
                            </h3>
                            <p class="text-xs text-gray-500 group-hover:text-blue-500 leading-tight">
                                {{ $item['description'] }}
                            </p>
                        </div>
                    </a>
                @endforeach
            </div>
        </x-filament::section>
    @endforeach
</x-filament-widgets::widget>
