<div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    @php
        $data = $this->getData();
        $quadrants = $data['quadrant_data'];
        $stats = $data['summary_stats'];
        $trends = $data['trend_data'];
    @endphp

    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Product Engineering Analysis</h2>
        <p class="text-sm text-gray-600 dark:text-gray-400">
            Analisis produk berdasarkan margin dan frekuensi penjualan ({{ $data['period']['start'] }} - {{ $data['period']['end'] }})
        </p>
    </div>

    <!-- Summary Stats -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ $stats['total_products'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Produk</div>
        </div>
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{{ number_format($stats['avg_margin'], 1) }}%</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Rata-rata Margin</div>
        </div>
        <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">{{ number_format($stats['avg_frequency'], 1) }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Rata-rata Frekuensi</div>
        </div>
        <div class="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-orange-600">Rp {{ number_format($stats['total_revenue'] / 1000000, 1) }}M</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
        </div>
        <div class="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 text-center">
            <div class="text-lg font-bold text-indigo-600">
                {{ $stats['top_performer'] ? $stats['top_performer']['product_code'] : 'N/A' }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Top Performer</div>
        </div>
    </div>

    <!-- 4 Quadrant Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Quadrant 1: High Margin, High Frequency (Stars) -->
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 border-2 border-green-200 dark:border-green-700">
            <div class="flex items-center mb-4">
                <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <h3 class="text-lg font-semibold text-green-800 dark:text-green-200">Stars (Margin Tinggi, Frekuensi Tinggi)</h3>
            </div>
            <p class="text-sm text-green-700 dark:text-green-300 mb-4">
                Produk unggulan dengan margin dan penjualan tinggi. Fokus pada optimalisasi dan ekspansi.
            </p>
            <div class="text-2xl font-bold text-green-600 mb-2">{{ count($quadrants['high_margin_high_frequency']) }} Produk</div>
            <div class="space-y-2 max-h-40 overflow-y-auto">
                @foreach(array_slice($quadrants['high_margin_high_frequency'], 0, 5) as $product)
                <div class="bg-white dark:bg-gray-700 rounded p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600"
                     onclick="toggleProductDetails('{{ $product['id'] }}')">
                    <div class="flex justify-between items-center text-sm">
                        <div>
                            <span class="font-medium">{{ $product['product_name'] }}</span>
                            <div class="text-xs text-gray-500">{{ $product['product_code'] }} | {{ $product['category'] }}</div>
                        </div>
                        <span class="text-green-600">{{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x</span>
                    </div>
                    <div id="details-{{ $product['id'] }}" class="hidden mt-2 text-xs text-gray-600 dark:text-gray-400">
                        <div>Revenue: Rp {{ number_format($product['revenue']) }}</div>
                        <div>Profit: Rp {{ number_format($product['total_profit']) }}</div>
                        <div>Velocity: {{ $product['velocity'] }}/bulan</div>
                        @if($product['last_sale_date'])
                        <div>Last Sale: {{ \Carbon\Carbon::parse($product['last_sale_date'])->format('d M Y') }}</div>
                        @endif
                    </div>
                </div>
                @endforeach
                @if(count($quadrants['high_margin_high_frequency']) > 5)
                <div class="text-xs text-gray-500">+{{ count($quadrants['high_margin_high_frequency']) - 5 }} produk lainnya</div>
                @endif
            </div>
        </div>

        <!-- Quadrant 2: High Margin, Low Frequency (Question Marks) -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6 border-2 border-yellow-200 dark:border-yellow-700">
            <div class="flex items-center mb-4">
                <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200">Question Marks (Margin Tinggi, Frekuensi Rendah)</h3>
            </div>
            <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-4">
                Produk dengan margin baik tapi penjualan rendah. Perlu strategi marketing dan promosi.
            </p>
            <div class="text-2xl font-bold text-yellow-600 mb-2">{{ count($quadrants['high_margin_low_frequency']) }} Produk</div>
            <div class="space-y-2 max-h-40 overflow-y-auto">
                @foreach(array_slice($quadrants['high_margin_low_frequency'], 0, 5) as $product)
                <div class="bg-white dark:bg-gray-700 rounded p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600"
                     onclick="toggleProductDetails('{{ $product['id'] }}')">
                    <div class="flex justify-between items-center text-sm">
                        <div>
                            <span class="font-medium">{{ $product['product_name'] }}</span>
                            <div class="text-xs text-gray-500">{{ $product['product_code'] }} | {{ $product['category'] }}</div>
                        </div>
                        <span class="text-yellow-600">{{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x</span>
                    </div>
                    <div id="details-{{ $product['id'] }}" class="hidden mt-2 text-xs text-gray-600 dark:text-gray-400">
                        <div>Revenue: Rp {{ number_format($product['revenue']) }}</div>
                        <div>Profit: Rp {{ number_format($product['total_profit']) }}</div>
                        <div>Velocity: {{ $product['velocity'] }}/bulan</div>
                        @if($product['last_sale_date'])
                        <div>Last Sale: {{ \Carbon\Carbon::parse($product['last_sale_date'])->format('d M Y') }}</div>
                        @endif
                    </div>
                </div>
                @endforeach
                @if(count($quadrants['high_margin_low_frequency']) > 5)
                <div class="text-xs text-gray-500">+{{ count($quadrants['high_margin_low_frequency']) - 5 }} produk lainnya</div>
                @endif
            </div>
        </div>

        <!-- Quadrant 3: Low Margin, High Frequency (Cash Cows) -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border-2 border-blue-200 dark:border-blue-700">
            <div class="flex items-center mb-4">
                <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200">Cash Cows (Margin Rendah, Frekuensi Tinggi)</h3>
            </div>
            <p class="text-sm text-blue-700 dark:text-blue-300 mb-4">
                Produk dengan penjualan tinggi tapi margin rendah. Fokus pada efisiensi cost dan volume.
            </p>
            <div class="text-2xl font-bold text-blue-600 mb-2">{{ count($quadrants['low_margin_high_frequency']) }} Produk</div>
            <div class="space-y-2 max-h-40 overflow-y-auto">
                @foreach(array_slice($quadrants['low_margin_high_frequency'], 0, 5) as $product)
                <div class="bg-white dark:bg-gray-700 rounded p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600"
                     onclick="toggleProductDetails('{{ $product['id'] }}')">
                    <div class="flex justify-between items-center text-sm">
                        <div>
                            <span class="font-medium">{{ $product['product_name'] }}</span>
                            <div class="text-xs text-gray-500">{{ $product['product_code'] }} | {{ $product['category'] }}</div>
                        </div>
                        <span class="text-blue-600">{{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x</span>
                    </div>
                    <div id="details-{{ $product['id'] }}" class="hidden mt-2 text-xs text-gray-600 dark:text-gray-400">
                        <div>Revenue: Rp {{ number_format($product['revenue']) }}</div>
                        <div>Profit: Rp {{ number_format($product['total_profit']) }}</div>
                        <div>Velocity: {{ $product['velocity'] }}/bulan</div>
                        @if($product['last_sale_date'])
                        <div>Last Sale: {{ \Carbon\Carbon::parse($product['last_sale_date'])->format('d M Y') }}</div>
                        @endif
                    </div>
                </div>
                @endforeach
                @if(count($quadrants['low_margin_high_frequency']) > 5)
                <div class="text-xs text-gray-500">+{{ count($quadrants['low_margin_high_frequency']) - 5 }} produk lainnya</div>
                @endif
            </div>
        </div>

        <!-- Quadrant 4: Low Margin, Low Frequency (Dogs) -->
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6 border-2 border-red-200 dark:border-red-700">
            <div class="flex items-center mb-4">
                <div class="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">Dogs (Margin Rendah, Frekuensi Rendah)</h3>
            </div>
            <p class="text-sm text-red-700 dark:text-red-300 mb-4">
                Produk dengan performa rendah. Pertimbangkan untuk discontinue atau reposisi.
            </p>
            <div class="text-2xl font-bold text-red-600 mb-2">{{ count($quadrants['low_margin_low_frequency']) }} Produk</div>
            <div class="space-y-2 max-h-40 overflow-y-auto">
                @foreach(array_slice($quadrants['low_margin_low_frequency'], 0, 5) as $product)
                <div class="bg-white dark:bg-gray-700 rounded p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600"
                     onclick="toggleProductDetails('{{ $product['id'] }}')">
                    <div class="flex justify-between items-center text-sm">
                        <div>
                            <span class="font-medium">{{ $product['product_name'] }}</span>
                            <div class="text-xs text-gray-500">{{ $product['product_code'] }} | {{ $product['category'] }}</div>
                        </div>
                        <span class="text-red-600">{{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x</span>
                    </div>
                    <div id="details-{{ $product['id'] }}" class="hidden mt-2 text-xs text-gray-600 dark:text-gray-400">
                        <div>Revenue: Rp {{ number_format($product['revenue']) }}</div>
                        <div>Profit: Rp {{ number_format($product['total_profit']) }}</div>
                        <div>Velocity: {{ $product['velocity'] }}/bulan</div>
                        @if($product['last_sale_date'])
                        <div>Last Sale: {{ \Carbon\Carbon::parse($product['last_sale_date'])->format('d M Y') }}</div>
                        @endif
                    </div>
                </div>
                @endforeach
                @if(count($quadrants['low_margin_low_frequency']) > 5)
                <div class="text-xs text-gray-500">+{{ count($quadrants['low_margin_low_frequency']) - 5 }} produk lainnya</div>
                @endif
            </div>
        </div>
    </div>

    <!-- Visual Matrix Chart (Alternative to Scatter Plot) -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Portfolio Matrix</h3>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <div class="grid grid-cols-2 gap-4 h-96">
                <!-- High Margin, Low Frequency (Question Marks) -->
                <div class="bg-yellow-100 dark:bg-yellow-900/20 border-2 border-yellow-300 rounded-lg p-4 flex flex-col">
                    <div class="text-center mb-2">
                        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200">Question Marks</h4>
                        <p class="text-xs text-yellow-600">Margin Tinggi, Frekuensi Rendah</p>
                    </div>
                    <div class="flex-1 overflow-y-auto">
                        @foreach($quadrants['high_margin_low_frequency'] as $product)
                        <div class="mb-2 p-2 bg-white dark:bg-gray-700 rounded text-xs">
                            <div class="font-medium">{{ $product['product_code'] }}</div>
                            <div class="text-gray-600 dark:text-gray-400">
                                {{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- High Margin, High Frequency (Stars) -->
                <div class="bg-green-100 dark:bg-green-900/20 border-2 border-green-300 rounded-lg p-4 flex flex-col">
                    <div class="text-center mb-2">
                        <h4 class="font-semibold text-green-800 dark:text-green-200">Stars</h4>
                        <p class="text-xs text-green-600">Margin Tinggi, Frekuensi Tinggi</p>
                    </div>
                    <div class="flex-1 overflow-y-auto">
                        @foreach($quadrants['high_margin_high_frequency'] as $product)
                        <div class="mb-2 p-2 bg-white dark:bg-gray-700 rounded text-xs">
                            <div class="font-medium">{{ $product['product_code'] }}</div>
                            <div class="text-gray-600 dark:text-gray-400">
                                {{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Low Margin, Low Frequency (Dogs) -->
                <div class="bg-red-100 dark:bg-red-900/20 border-2 border-red-300 rounded-lg p-4 flex flex-col">
                    <div class="text-center mb-2">
                        <h4 class="font-semibold text-red-800 dark:text-red-200">Dogs</h4>
                        <p class="text-xs text-red-600">Margin Rendah, Frekuensi Rendah</p>
                    </div>
                    <div class="flex-1 overflow-y-auto">
                        @foreach($quadrants['low_margin_low_frequency'] as $product)
                        <div class="mb-2 p-2 bg-white dark:bg-gray-700 rounded text-xs">
                            <div class="font-medium">{{ $product['product_code'] }}</div>
                            <div class="text-gray-600 dark:text-gray-400">
                                {{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Low Margin, High Frequency (Cash Cows) -->
                <div class="bg-blue-100 dark:bg-blue-900/20 border-2 border-blue-300 rounded-lg p-4 flex flex-col">
                    <div class="text-center mb-2">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-200">Cash Cows</h4>
                        <p class="text-xs text-blue-600">Margin Rendah, Frekuensi Tinggi</p>
                    </div>
                    <div class="flex-1 overflow-y-auto">
                        @foreach($quadrants['low_margin_high_frequency'] as $product)
                        <div class="mb-2 p-2 bg-white dark:bg-gray-700 rounded text-xs">
                            <div class="font-medium">{{ $product['product_code'] }}</div>
                            <div class="text-gray-600 dark:text-gray-400">
                                {{ number_format($product['margin_percentage'], 1) }}% | {{ $product['sales_frequency'] }}x
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Matrix Labels -->
            <div class="mt-4 flex justify-between text-sm text-gray-600 dark:text-gray-400">
                <div class="text-left">
                    <div>← Frekuensi Rendah</div>
                </div>
                <div class="text-right">
                    <div>Frekuensi Tinggi →</div>
                </div>
            </div>
            <div class="flex justify-center mt-2">
                <div class="text-center text-sm text-gray-600 dark:text-gray-400">
                    <div>↑ Margin Tinggi</div>
                    <div class="mt-8">↓ Margin Rendah</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Trend Table -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Trend Analisis Kuadran (12 Bulan Terakhir)</h3>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b border-gray-200 dark:border-gray-700">
                            <th class="text-left py-2">Bulan</th>
                            <th class="text-center py-2 text-green-600">Stars</th>
                            <th class="text-center py-2 text-yellow-600">Question Marks</th>
                            <th class="text-center py-2 text-blue-600">Cash Cows</th>
                            <th class="text-center py-2 text-red-600">Dogs</th>
                            <th class="text-right py-2">Total Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($trends as $trend)
                        <tr class="border-b border-gray-100 dark:border-gray-700">
                            <td class="py-2">{{ $trend['month'] }}</td>
                            <td class="text-center py-2">{{ $trend['high_margin_high_frequency_count'] }}</td>
                            <td class="text-center py-2">{{ $trend['high_margin_low_frequency_count'] }}</td>
                            <td class="text-center py-2">{{ $trend['low_margin_high_frequency_count'] }}</td>
                            <td class="text-center py-2">{{ $trend['low_margin_low_frequency_count'] }}</td>
                            <td class="text-right py-2">Rp {{ number_format($trend['total_revenue'] / 1000000, 1) }}M</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Performance Analysis -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Analisis Performa Produk</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Top Revenue Products -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">Top Revenue Products</h4>
                <div class="space-y-2">
                    @foreach(array_slice($data['performance_analysis']['top_revenue_products'], 0, 3) as $product)
                    <div class="flex justify-between items-center text-sm">
                        <span class="font-medium">{{ $product['name'] }}</span>
                        <span class="text-blue-600">Rp {{ number_format($product['revenue'] / 1000000, 1) }}M</span>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Top Profit Products -->
            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">Top Profit Products</h4>
                <div class="space-y-2">
                    @foreach(array_slice($data['performance_analysis']['top_profit_products'], 0, 3) as $product)
                    <div class="flex justify-between items-center text-sm">
                        <span class="font-medium">{{ $product['name'] }}</span>
                        <span class="text-green-600">Rp {{ number_format($product['total_profit'] / 1000000, 1) }}M</span>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Fastest Moving Products -->
            <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                <h4 class="font-semibold text-purple-800 dark:text-purple-200 mb-3">Fastest Moving</h4>
                <div class="space-y-2">
                    @foreach(array_slice($data['performance_analysis']['fastest_moving_products'], 0, 3) as $product)
                    <div class="flex justify-between items-center text-sm">
                        <span class="font-medium">{{ $product['name'] }}</span>
                        <span class="text-purple-600">{{ number_format($product['velocity'], 1) }}/bulan</span>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- High Margin Low Sales -->
            <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-3">High Margin, Low Sales</h4>
                <div class="space-y-2">
                    @foreach(array_slice($data['performance_analysis']['high_margin_low_sales'], 0, 3) as $product)
                    <div class="flex justify-between items-center text-sm">
                        <span class="font-medium">{{ $product['name'] }}</span>
                        <span class="text-yellow-600">{{ number_format($product['margin'], 1) }}%</span>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Declining Products -->
            <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                <h4 class="font-semibold text-red-800 dark:text-red-200 mb-3">Declining Products</h4>
                <div class="space-y-2">
                    @foreach(array_slice($data['performance_analysis']['declining_products'], 0, 3) as $product)
                    <div class="flex justify-between items-center text-sm">
                        <span class="font-medium">{{ $product['name'] }}</span>
                        <span class="text-red-600 text-xs">
                            @if($product['last_sale_date'])
                                {{ \Carbon\Carbon::parse($product['last_sale_date'])->diffForHumans() }}
                            @else
                                No sales
                            @endif
                        </span>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Slowest Moving Products -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">Slowest Moving</h4>
                <div class="space-y-2">
                    @foreach(array_slice($data['performance_analysis']['slowest_moving_products'], 0, 3) as $product)
                    <div class="flex justify-between items-center text-sm">
                        <span class="font-medium">{{ $product['name'] }}</span>
                        <span class="text-gray-600">{{ number_format($product['velocity'], 1) }}/bulan</span>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Product Recommendations -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Rekomendasi Produk (Berdasarkan Prioritas)</h3>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="space-y-4">
                @foreach(array_slice($data['recommendations'], 0, 10) as $productId => $recommendation)
                <div class="bg-white dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="font-semibold text-gray-900 dark:text-white">{{ $recommendation['product_name'] }}</h4>
                        <span class="px-2 py-1 text-xs rounded-full
                            @if($recommendation['priority'] >= 6) bg-red-100 text-red-800
                            @elseif($recommendation['priority'] >= 4) bg-yellow-100 text-yellow-800
                            @else bg-green-100 text-green-800
                            @endif">
                            Priority: {{ $recommendation['priority'] }}
                        </span>
                    </div>
                    <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        @foreach($recommendation['recommendations'] as $rec)
                        <li>• {{ $rec }}</li>
                        @endforeach
                    </ul>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Strategic Recommendations -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Rekomendasi Strategis</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h4 class="font-medium text-green-600 mb-2">Stars - Investasi & Ekspansi</h4>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Tingkatkan kapasitas produksi</li>
                    <li>• Ekspansi pasar dan distribusi</li>
                    <li>• Pertahankan kualitas dan inovasi</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-yellow-600 mb-2">Question Marks - Marketing Push</h4>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Tingkatkan aktivitas marketing</li>
                    <li>• Analisis target market yang tepat</li>
                    <li>• Evaluasi pricing strategy</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-blue-600 mb-2">Cash Cows - Efisiensi Cost</h4>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Optimalisasi proses produksi</li>
                    <li>• Negosiasi supplier untuk cost reduction</li>
                    <li>• Leverage economies of scale</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-red-600 mb-2">Dogs - Reposisi atau Discontinue</h4>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Evaluasi untuk discontinue</li>
                    <li>• Reposisi ke niche market</li>
                    <li>• Bundling dengan produk lain</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function toggleProductDetails(productId) {
    const detailsElement = document.getElementById('details-' + productId);
    if (detailsElement) {
        detailsElement.classList.toggle('hidden');
    }
}

// Add click handlers for sales history modal
function showSalesHistory(productId, productName) {
    // This would open a modal with sales history
    // For now, we'll just show an alert
    alert('Sales history for ' + productName + ' (Product ID: ' + productId + ')');
}
</script>
