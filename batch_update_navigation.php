<?php

/**
 * Batch update navigation groups and labels to Indonesian
 */

$updates = [
    'UnitOfMeasureResource.php' => [
        'old_group' => 'Master Data',
        'new_group' => 'Data Master',
        'old_label' => 'Unit of Measure',
        'new_label' => 'Satuan'
    ],
    'StockMovementResource.php' => [
        'old_group' => 'Inventory',
        'new_group' => 'Inventori',
        'old_label' => 'Stock Movement',
        'new_label' => 'Pergerakan Stok'
    ],
    'StockOpnameResource.php' => [
        'old_group' => 'Inventory',
        'new_group' => 'Inventori',
        'old_label' => 'Stock Opname',
        'new_label' => 'Stock Opname'
    ],
    'BankAccountResource.php' => [
        'old_group' => 'Financial',
        'new_group' => 'Keuangan',
        'old_label' => 'Bank Account',
        'new_label' => 'Rekening Bank'
    ],
    'BankTransactionResource.php' => [
        'old_group' => 'Financial',
        'new_group' => 'Keuangan',
        'old_label' => 'Bank Transaction',
        'new_label' => 'Transaksi Bank'
    ],
    'BudgetResource.php' => [
        'old_group' => 'Financial',
        'new_group' => 'Keuangan',
        'old_label' => 'Budget',
        'new_label' => 'Anggaran'
    ],
    'CashFlowCategoryResource.php' => [
        'old_group' => 'Financial',
        'new_group' => 'Keuangan',
        'old_label' => 'Cash Flow Category',
        'new_label' => 'Kategori Arus Kas'
    ],
    'ProductionOrderResource.php' => [
        'old_group' => 'Manufacturing',
        'new_group' => 'Produksi',
        'old_label' => 'Production Order',
        'new_label' => 'Order Produksi'
    ],
    'WorkOrderResource.php' => [
        'old_group' => 'Manufacturing',
        'new_group' => 'Produksi',
        'old_label' => 'Work Order',
        'new_label' => 'Order Kerja'
    ],
    'WorkCenterResource.php' => [
        'old_group' => 'Manufacturing',
        'new_group' => 'Produksi',
        'old_label' => 'Work Center',
        'new_label' => 'Pusat Kerja'
    ],
    'FixedAssetResource.php' => [
        'old_group' => 'Assets',
        'new_group' => 'Aset',
        'old_label' => 'Fixed Asset',
        'new_label' => 'Aset Tetap'
    ],
    'AssetTransferResource.php' => [
        'old_group' => 'Assets',
        'new_group' => 'Aset',
        'old_label' => 'Asset Transfer',
        'new_label' => 'Transfer Aset'
    ],
    'DepreciationResource.php' => [
        'old_group' => 'Assets',
        'new_group' => 'Aset',
        'old_label' => 'Depreciation',
        'new_label' => 'Depresiasi'
    ],
    'AuditLogResource.php' => [
        'old_group' => 'System',
        'new_group' => 'Sistem',
        'old_label' => 'Audit Log',
        'new_label' => 'Log Audit'
    ],
    'SystemNotificationResource.php' => [
        'old_group' => 'System',
        'new_group' => 'Sistem',
        'old_label' => 'System Notification',
        'new_label' => 'Notifikasi Sistem'
    ],
    'ApprovalWorkflowResource.php' => [
        'old_group' => 'Workflow',
        'new_group' => 'Alur Kerja',
        'old_label' => 'Approval Workflow',
        'new_label' => 'Alur Persetujuan'
    ],
    'ApprovalRequestResource.php' => [
        'old_group' => 'Workflow',
        'new_group' => 'Alur Kerja',
        'old_label' => 'Approval Request',
        'new_label' => 'Permintaan Persetujuan'
    ],
    'AutomatedJournalEntryResource.php' => [
        'old_group' => 'Workflow',
        'new_group' => 'Alur Kerja',
        'old_label' => 'Automated Journal Entry',
        'new_label' => 'Jurnal Entry Otomatis'
    ],
    'CostVarianceReportResource.php' => [
        'old_group' => 'Reports',
        'new_group' => 'Laporan',
        'old_label' => 'Cost Variance Report',
        'new_label' => 'Laporan Varians Biaya'
    ],
    'PeriodClosingResource.php' => [
        'old_group' => 'Reports',
        'new_group' => 'Laporan',
        'old_label' => 'Period Closing',
        'new_label' => 'Penutupan Periode'
    ]
];

echo "Batch navigation updates ready for " . count($updates) . " resources\n";

// This provides the mapping for batch updates
// The actual updates will be done by the main process
