<?php

/**
 * Script to fix duplicated properties in Filament Resources
 */

function fixResourceFile($filePath) {
    if (!file_exists($filePath)) {
        return;
    }
    
    $content = file_get_contents($filePath);
    $lines = explode("\n", $content);
    $cleanedLines = [];
    $seenProperties = [];
    
    foreach ($lines as $line) {
        $trimmedLine = trim($line);
        
        // Check for navigation properties
        if (preg_match('/protected static \?\w+ \$navigation\w+ = /', $trimmedLine)) {
            // Extract property name
            preg_match('/\$(\w+) =/', $trimmedLine, $matches);
            if (isset($matches[1])) {
                $propertyName = $matches[1];
                
                // Skip if we've already seen this property
                if (in_array($propertyName, $seenProperties)) {
                    echo "Removing duplicate: {$propertyName} in {$filePath}\n";
                    continue;
                }
                
                $seenProperties[] = $propertyName;
            }
        }
        
        $cleanedLines[] = $line;
    }
    
    $cleanedContent = implode("\n", $cleanedLines);
    file_put_contents($filePath, $cleanedContent);
}

// Get all resource files
$resourceDir = 'app/Filament/Resources';
$resourceFiles = glob($resourceDir . '/*Resource.php');

echo "Fixing duplications in Filament Resources...\n\n";

foreach ($resourceFiles as $file) {
    $fileName = basename($file);
    echo "Processing: {$fileName}\n";
    fixResourceFile($file);
}

echo "\n✅ All duplications fixed!\n";
