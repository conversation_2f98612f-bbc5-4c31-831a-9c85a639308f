<?php

/**
 * Script to update all navigation groups to Indonesian
 */

$navigationGroupTranslations = [
    'Master Data' => 'Data Master',
    'Financial' => 'Keuangan', 
    'Inventory' => 'Inventori',
    'Manufacturing' => 'Manufaktur',
    'Production' => 'Produksi',
    'Purchasing' => 'Pembelian',
    'Sales' => 'Penjualan',
    'Reports' => 'Laporan',
    'Administration' => 'Administrasi',
    'System' => 'Sistem',
    'Workflow' => 'Alur Kerja',
    'Organization' => 'Organisasi',
    'Assets' => 'Aset',
    'Dashboard' => 'Dashboard',
];

$navigationLabelTranslations = [
    // Master Data
    'Customer' => 'Pelanggan',
    'Customers' => 'Pelanggan',
    'Supplier' => 'Pemasok',
    'Suppliers' => 'Pemasok',
    'Product' => 'Produk',
    'Products' => 'Produk',
    'Product Category' => 'Kategori Produk',
    'Product Categories' => 'Kategori Produk',
    'Unit of Measure' => 'Satuan',
    'Units of Measure' => 'Satuan',
    'Location' => 'Lokasi',
    'Locations' => 'Lokasi',
    
    // Financial
    'Chart of Accounts' => 'Bagan Akun',
    'Accounts' => 'Akun',
    'Journal' => 'Jurnal',
    'Journals' => 'Jurnal',
    'Transaction' => 'Transaksi',
    'Transactions' => 'Transaksi',
    'Bank Account' => 'Rekening Bank',
    'Bank Accounts' => 'Rekening Bank',
    'Bank Transaction' => 'Transaksi Bank',
    'Bank Transactions' => 'Transaksi Bank',
    'Budget' => 'Anggaran',
    'Budgets' => 'Anggaran',
    'Cash Flow Category' => 'Kategori Arus Kas',
    'Cash Flow Categories' => 'Kategori Arus Kas',
    
    // Inventory
    'Inventory' => 'Inventori',
    'Inventories' => 'Inventori',
    'Stock Movement' => 'Pergerakan Stok',
    'Stock Movements' => 'Pergerakan Stok',
    'Stock Opname' => 'Stock Opname',
    
    // Manufacturing/Production
    'Bill of Material' => 'Daftar Bahan',
    'Bills of Material' => 'Daftar Bahan',
    'Work Center' => 'Pusat Kerja',
    'Work Centers' => 'Pusat Kerja',
    'Production Order' => 'Order Produksi',
    'Production Orders' => 'Order Produksi',
    'Work Order' => 'Order Kerja',
    'Work Orders' => 'Order Kerja',
    
    // Purchasing
    'Purchase Order' => 'Pesanan Pembelian',
    'Purchase Orders' => 'Pesanan Pembelian',
    
    // Assets
    'Fixed Asset' => 'Aset Tetap',
    'Fixed Assets' => 'Aset Tetap',
    'Asset Transfer' => 'Transfer Aset',
    'Asset Transfers' => 'Transfer Aset',
    'Depreciation' => 'Depresiasi',
    'Depreciations' => 'Depresiasi',
    
    // Organization
    'Department' => 'Departemen',
    'Departments' => 'Departemen',
    'Cost Center' => 'Pusat Biaya',
    'Cost Centers' => 'Pusat Biaya',
    
    // Administration
    'User' => 'Pengguna',
    'Users' => 'Pengguna',
    'Client' => 'Klien',
    'Clients' => 'Klien',
    
    // System
    'Audit Log' => 'Log Audit',
    'Audit Logs' => 'Log Audit',
    'System Notification' => 'Notifikasi Sistem',
    'System Notifications' => 'Notifikasi Sistem',
    
    // Workflow
    'Approval Workflow' => 'Alur Persetujuan',
    'Approval Workflows' => 'Alur Persetujuan',
    'Approval Request' => 'Permintaan Persetujuan',
    'Approval Requests' => 'Permintaan Persetujuan',
    'Automated Journal Entry' => 'Jurnal Entry Otomatis',
    'Automated Journal Entries' => 'Jurnal Entry Otomatis',
    
    // Reports
    'Cost Variance Report' => 'Laporan Varians Biaya',
    'Cost Variance Reports' => 'Laporan Varians Biaya',
    'Period Closing' => 'Penutupan Periode',
    'Period Closings' => 'Penutupan Periode',
];

// Resources that need navigation group updates
$resourcesToUpdate = [
    'AccountResource.php' => ['group' => 'Keuangan', 'label' => 'Bagan Akun'],
    'CustomerResource.php' => ['group' => 'Data Master', 'label' => 'Pelanggan'],
    'SupplierResource.php' => ['group' => 'Data Master', 'label' => 'Pemasok'],
    'ProductResource.php' => ['group' => 'Data Master', 'label' => 'Produk'],
    'ProductCategoryResource.php' => ['group' => 'Data Master', 'label' => 'Kategori Produk'],
    'UnitOfMeasureResource.php' => ['group' => 'Data Master', 'label' => 'Satuan'],
    'LocationResource.php' => ['group' => 'Data Master', 'label' => 'Lokasi'],
    'InventoryResource.php' => ['group' => 'Inventori', 'label' => 'Inventori'],
    'StockMovementResource.php' => ['group' => 'Inventori', 'label' => 'Pergerakan Stok'],
    'StockOpnameResource.php' => ['group' => 'Inventori', 'label' => 'Stock Opname'],
    'JournalResource.php' => ['group' => 'Keuangan', 'label' => 'Jurnal'],
    'TransactionResource.php' => ['group' => 'Keuangan', 'label' => 'Transaksi'],
    'BankAccountResource.php' => ['group' => 'Keuangan', 'label' => 'Rekening Bank'],
    'BankTransactionResource.php' => ['group' => 'Keuangan', 'label' => 'Transaksi Bank'],
    'BudgetResource.php' => ['group' => 'Keuangan', 'label' => 'Anggaran'],
    'CashFlowCategoryResource.php' => ['group' => 'Keuangan', 'label' => 'Kategori Arus Kas'],
    'PurchaseOrderResource.php' => ['group' => 'Pembelian', 'label' => 'Pesanan Pembelian'],
    'ProductionOrderResource.php' => ['group' => 'Produksi', 'label' => 'Order Produksi'],
    'WorkOrderResource.php' => ['group' => 'Produksi', 'label' => 'Order Kerja'],
    'WorkCenterResource.php' => ['group' => 'Produksi', 'label' => 'Pusat Kerja'],
    'BillOfMaterialResource.php' => ['group' => 'Produksi', 'label' => 'Daftar Bahan'],
    'FixedAssetResource.php' => ['group' => 'Aset', 'label' => 'Aset Tetap'],
    'AssetTransferResource.php' => ['group' => 'Aset', 'label' => 'Transfer Aset'],
    'DepreciationResource.php' => ['group' => 'Aset', 'label' => 'Depresiasi'],
    'DepartmentResource.php' => ['group' => 'Organisasi', 'label' => 'Departemen'],
    'CostCenterResource.php' => ['group' => 'Organisasi', 'label' => 'Pusat Biaya'],
    'UserResource.php' => ['group' => 'Administrasi', 'label' => 'Pengguna'],
    'ClientResource.php' => ['group' => 'Administrasi', 'label' => 'Klien'],
    'AuditLogResource.php' => ['group' => 'Sistem', 'label' => 'Log Audit'],
    'SystemNotificationResource.php' => ['group' => 'Sistem', 'label' => 'Notifikasi Sistem'],
    'ApprovalWorkflowResource.php' => ['group' => 'Alur Kerja', 'label' => 'Alur Persetujuan'],
    'ApprovalRequestResource.php' => ['group' => 'Alur Kerja', 'label' => 'Permintaan Persetujuan'],
    'AutomatedJournalEntryResource.php' => ['group' => 'Alur Kerja', 'label' => 'Jurnal Entry Otomatis'],
    'CostVarianceReportResource.php' => ['group' => 'Laporan', 'label' => 'Laporan Varians Biaya'],
    'PeriodClosingResource.php' => ['group' => 'Laporan', 'label' => 'Penutupan Periode'],
];

echo "Navigation group and label translations loaded.\n";
echo "Resources to update: " . count($resourcesToUpdate) . "\n";
echo "Ready to process...\n";
