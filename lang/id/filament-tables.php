<?php

return [

    'columns' => [

        'select_all' => [
            'label' => 'Pilih semua',
        ],

        'text' => [
            'actions' => [
                'collapse_list' => [
                    'label' => 'Tampilkan :count lebih sedikit',
                ],
                'expand_list' => [
                    'label' => 'Tampilkan :count lagi',
                ],
            ],
            'more_list_items' => 'dan :count lagi',
        ],

    ],

    'fields' => [

        'bulk_select_page' => [
            'label' => 'Pilih/batalkan pilihan semua item untuk aksi bulk.',
        ],

        'bulk_select_record' => [
            'label' => 'Pilih/batalkan pilihan item :key untuk aksi bulk.',
        ],

        'bulk_select_group' => [
            'label' => 'Pilih/batalkan pilihan grup :title untuk aksi bulk.',
        ],

        'search' => [
            'label' => 'Cari',
            'placeholder' => 'Cari',
            'indicator' => 'Cari',
        ],

    ],

    'summary' => [

        'heading' => 'Ringkasan',

        'subheadings' => [
            'all' => 'Semua :label',
            'group' => 'Ringkasan :group',
            'page' => 'Halaman ini',
        ],

        'summarizers' => [

            'average' => [
                'label' => 'Rata-rata',
            ],

            'count' => [
                'label' => 'Jumlah',
            ],

            'sum' => [
                'label' => 'Total',
            ],

        ],

    ],

    'actions' => [

        'disable_reordering' => [
            'label' => 'Selesai mengurutkan ulang record',
        ],

        'enable_reordering' => [
            'label' => 'Urutkan ulang record',
        ],

        'filter' => [
            'label' => 'Filter',
        ],

        'group' => [
            'label' => 'Grup',
        ],

        'open_bulk_actions' => [
            'label' => 'Aksi bulk',
        ],

        'toggle_columns' => [
            'label' => 'Toggle kolom',
        ],

    ],

    'empty' => [

        'heading' => 'Tidak ada :model',

        'description' => 'Buat :model untuk memulai.',

    ],

    'filters' => [

        'actions' => [

            'remove' => [
                'label' => 'Hapus filter',
            ],

            'remove_all' => [
                'label' => 'Hapus semua filter',
                'tooltip' => 'Hapus semua filter',
            ],

            'reset' => [
                'label' => 'Reset',
            ],

        ],

        'heading' => 'Filter',

        'indicator' => 'Filter aktif',

        'multi_select' => [
            'placeholder' => 'Semua',
        ],

        'select' => [
            'placeholder' => 'Semua',
        ],

        'trashed' => [

            'label' => 'Record yang dihapus',

            'only_trashed' => 'Hanya record yang dihapus',

            'with_trashed' => 'Dengan record yang dihapus',

            'without_trashed' => 'Tanpa record yang dihapus',

        ],

    ],

    'grouping' => [

        'fields' => [

            'group' => [
                'label' => 'Grup berdasarkan',
                'placeholder' => 'Grup berdasarkan',
            ],

            'direction' => [

                'label' => 'Arah grup',

                'options' => [
                    'asc' => 'Naik',
                    'desc' => 'Turun',
                ],

            ],

        ],

    ],

    'pagination' => [

        'label' => 'Navigasi pagination',

        'overview' => '{1} Menampilkan 1 hasil|[2,*] Menampilkan :first sampai :last dari :total hasil',

        'fields' => [

            'records_per_page' => [

                'label' => 'per halaman',

                'options' => [
                    'all' => 'Semua',
                ],

            ],

        ],

        'actions' => [

            'first' => [
                'label' => 'Pertama',
            ],

            'go_to_page' => [
                'label' => 'Ke halaman :page',
            ],

            'last' => [
                'label' => 'Terakhir',
            ],

            'next' => [
                'label' => 'Berikutnya',
            ],

            'previous' => [
                'label' => 'Sebelumnya',
            ],

        ],

    ],

    'reorder_indicator' => 'Tarik dan lepas record ke urutan yang diinginkan.',

    'selection_indicator' => [

        'selected_count' => '{1} 1 record dipilih|[2,*] :count record dipilih',

        'actions' => [

            'select_all' => [
                'label' => 'Pilih semua :count',
            ],

            'deselect_all' => [
                'label' => 'Batalkan pilihan semua',
            ],

        ],

    ],

    'sorting' => [

        'fields' => [

            'column' => [
                'label' => 'Urutkan berdasarkan',
            ],

            'direction' => [

                'label' => 'Arah urutan',

                'options' => [
                    'asc' => 'Naik',
                    'desc' => 'Turun',
                ],

            ],

        ],

    ],

];
