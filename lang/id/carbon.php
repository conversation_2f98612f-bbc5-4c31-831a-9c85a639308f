<?php

return [
    'year' => ':count tahun',
    'a_year' => '{1}setahun|]1,Inf[:count tahun',
    'y' => ':count thn',
    'month' => ':count bulan',
    'a_month' => '{1}sebulan|]1,Inf[:count bulan',
    'm' => ':count bln',
    'week' => ':count minggu',
    'a_week' => '{1}seminggu|]1,Inf[:count minggu',
    'w' => ':count mgg',
    'day' => ':count hari',
    'a_day' => '{1}sehari|]1,Inf[:count hari',
    'd' => ':count hr',
    'hour' => ':count jam',
    'a_hour' => '{1}sejam|]1,Inf[:count jam',
    'h' => ':count j',
    'minute' => ':count menit',
    'a_minute' => '{1}semenit|]1,Inf[:count menit',
    'min' => ':count mnt',
    'second' => ':count detik',
    'a_second' => '{1}beberapa detik|]1,Inf[:count detik',
    's' => ':count dtk',
    'ago' => ':time yang lalu',
    'from_now' => ':time dari sekarang',
    'after' => ':time setelah',
    'before' => ':time sebelum',
    'diff_now' => 'sekarang',
    'diff_today' => 'Hari ini',
    'diff_yesterday' => 'Kemarin',
    'diff_tomorrow' => 'Besok',
    'diff_before_yesterday' => 'Kemarin dulu',
    'diff_after_tomorrow' => 'Lusa',
    'period_recurrences' => 'sekali|:count kali',
    'period_interval' => 'setiap :interval',
    'period_start_date' => 'dari :date',
    'period_end_date' => 'sampai :date',
    'months' => ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'],
    'months_short' => ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'],
    'weekdays' => ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'],
    'weekdays_short' => ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
    'weekdays_min' => ['Mg', 'Sn', 'Sl', 'Rb', 'Km', 'Jm', 'Sb'],
    'ordinal' => 'ke-:number',
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 1,
    'list' => [', ', ' dan '],
];
