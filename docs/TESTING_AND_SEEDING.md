# Testing dan Seeding Guide

## Super Admin Seeder

### <PERSON><PERSON><PERSON>an Seeder

Untuk membuat user super admin dan demo user:

```bash
# Jalankan seeder super admin
php artisan db:seed --class=SuperAdminSeeder

# Atau jalankan semua seeder
php artisan db:seed
```

### User yang Dibuat

#### Super Admin

-   **Email**: `<EMAIL>`
-   **Password**: `password`
-   **Type**: `super_admin`
-   **Client**: Default Company

#### Demo User

-   **Email**: `<EMAIL>`
-   **Password**: `demo123`
-   **Type**: `client`
-   **Client**: Default Company

### Default Client

Seeder juga membuat client default:

-   **Name**: Default Company
-   **Email**: <EMAIL>
-   **Phone**: 021-1234567
-   **Address**: Jakarta, Indonesia

## Testing dengan Preserved Users

### Trait PreservesUsers

Untuk test yang tidak ingin menghapus data user, gunakan trait `PreservesUsers`:

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\PreservesUsers;

class MyFeatureTest extends TestCase
{
    use PreservesUsers;

    protected function setUp(): void
    {
        parent::setUp();

        // Setup test environment dengan preserved users
        $this->setUpPreservesUsers();
    }

    public function test_my_feature(): void
    {
        // Gunakan test user yang sudah dibuat
        $this->actingAs($this->getTestUser());

        // Test client juga tersedia
        $client = $this->getTestClient();

        // Lakukan testing...
    }
}
```

### Perbedaan dengan RefreshDatabase

| Aspect                | RefreshDatabase             | PreservesUsers                        |
| --------------------- | --------------------------- | ------------------------------------- |
| **Database Strategy** | Truncate semua tabel        | RefreshDatabase + Seeder              |
| **User Data**         | Dihapus setiap test         | Dibuat ulang dari seeder              |
| **Performance**       | Cepat                       | Sedikit lebih lambat (karena seeder)  |
| **Isolation**         | Complete isolation          | Complete isolation + consistent users |
| **Use Case**          | Test yang butuh clean state | Test yang butuh existing users        |

### Kapan Menggunakan PreservesUsers

✅ **Gunakan PreservesUsers untuk:**

-   Test import/export functionality
-   Test yang membutuhkan user authentication
-   Test yang tidak mengubah struktur database
-   Test yang fokus pada business logic
-   Test integration dengan existing data

❌ **Jangan gunakan PreservesUsers untuk:**

-   Test yang mengubah schema database
-   Test yang membutuhkan clean state
-   Test migration atau seeder
-   Test yang mengubah user data

### Test User yang Tersedia

Saat menggunakan `PreservesUsers`, user berikut tersedia:

#### Test User (dibuat otomatis)

-   **Email**: `<EMAIL>`
-   **Password**: `password`
-   **Type**: `client`
-   **Client**: Test Company

#### Super Admin (dari seeder)

-   **Email**: `<EMAIL>`
-   **Password**: `password`
-   **Type**: `super_admin`

#### Demo User (dari seeder)

-   **Email**: `<EMAIL>`
-   **Password**: `demo123`
-   **Type**: `client`

## Contoh Penggunaan

### Test Import dengan Preserved Users

```php
public function test_can_import_products(): void
{
    $this->actingAs($this->getTestUser());

    $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
    $csvContent .= "PROD001,Test Product,Electronics,PCS\n";

    Storage::fake('local');
    $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

    $import = new ProductImport();
    Excel::import($import, $file);

    $this->assertDatabaseHas('products', [
        'client_id' => $this->getTestClient()->id,
        'product_code' => 'PROD001',
    ]);
}
```

### Test dengan Multiple Users

```php
public function test_different_user_types(): void
{
    // Test dengan super admin
    $superAdmin = User::where('email', '<EMAIL>')->first();
    $this->actingAs($superAdmin);
    // ... test super admin functionality

    // Test dengan demo user
    $demoUser = User::where('email', '<EMAIL>')->first();
    $this->actingAs($demoUser);
    // ... test demo user functionality

    // Test dengan test user
    $this->actingAs($this->getTestUser());
    // ... test regular user functionality
}
```

## Best Practices

### 1. Pilih Strategy yang Tepat

```php
// Untuk test yang butuh clean state
class DatabaseStructureTest extends TestCase
{
    use RefreshDatabase;
    // ...
}

// Untuk test business logic dengan existing users
class ProductImportTest extends TestCase
{
    use PreservesUsers;
    // ...
}
```

### 2. Setup yang Konsisten

```php
protected function setUp(): void
{
    parent::setUp();

    if (method_exists($this, 'setUpPreservesUsers')) {
        $this->setUpPreservesUsers();
    }
}
```

### 3. Cleanup yang Proper

```php
// PreservesUsers menggunakan DatabaseTransactions
// Jadi data akan di-rollback otomatis setelah test
public function test_create_product(): void
{
    $this->actingAs($this->getTestUser());

    // Create product
    $product = Product::create([...]);

    // Test assertions
    $this->assertDatabaseHas('products', [...]);

    // No manual cleanup needed - akan di-rollback otomatis
}
```

### 4. Isolasi Test

```php
// Pastikan test tidak saling bergantung
public function test_first_feature(): void
{
    // Test ini tidak boleh bergantung pada test lain
}

public function test_second_feature(): void
{
    // Test ini juga harus bisa berjalan sendiri
}
```

## Troubleshooting

### Error "User not found"

Pastikan seeder sudah dijalankan:

```bash
php artisan db:seed --class=SuperAdminSeeder
```

### Error "Client not found"

Pastikan menggunakan `setUpPreservesUsers()` di setUp:

```php
protected function setUp(): void
{
    parent::setUp();
    $this->setUpPreservesUsers();
}
```

### Test Lambat

Jika test lambat, pertimbangkan:

1. Gunakan `PreservesUsers` untuk test yang tidak butuh clean state
2. Group test yang related dalam satu class
3. Minimize database operations

### Data Tidak Konsisten

Pastikan seeder berjalan dengan benar:

```php
use Tests\Traits\PreservesUsers; // Sudah include RefreshDatabase + Seeder
```

Jika masih bermasalah, coba jalankan seeder manual:

```bash
php artisan db:seed --class=SuperAdminSeeder
```

## Command Shortcuts

### Database & Seeding

```bash
# Jalankan seeder
php artisan db:seed --class=SuperAdminSeeder

# Reset database dan jalankan seeder
php artisan migrate:fresh --seed
```

### User Management

```bash
# Buat user baru (interactive)
php artisan user:create

# Buat user dengan parameter
php artisan user:create --name="John Doe" --email="<EMAIL>" --password="password123" --type="client"

# Buat super admin
php artisan user:create --name="Admin" --email="<EMAIL>" --password="secret" --type="super_admin"
```

### Testing

```bash
# Test dengan preserved users
php artisan test tests/Feature/ProductImportWithPreservedUsersTest.php

# Test semua dengan preserved users
php artisan test --filter="PreservedUsers"

# Test import functionality
php artisan test tests/Feature/ProductImportTest.php
```
