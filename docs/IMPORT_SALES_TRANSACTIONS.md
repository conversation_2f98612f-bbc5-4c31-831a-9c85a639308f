# Import Transaksi Penjualan

Fitur import transaksi penjualan memungkinkan Anda untuk mengimpor data transaksi penjualan secara bulk menggunakan file Excel atau CSV.

## Cara Menggunakan

### 1. Download Template
1. Masuk ke halaman **Transaksi** di admin panel
2. <PERSON><PERSON> tombol **"Download Template"** di bagian atas
3. File template Excel akan terdownload dengan nama `template-import-transaksi-penjualan.xlsx`

### 2. Isi Data di Template
Template terdiri dari 5 sheet:

#### Sheet "Template"
Sheet kosong untuk mengisi data Anda. Gunakan sheet ini untuk import.

#### Sheet "Contoh Data"
Berisi contoh data dengan format yang benar untuk referensi.

#### Sheet "Daftar Produk"
Menampilkan semua produk aktif dengan kode produk yang bisa digunakan.

#### Sheet "Daftar Pelanggan"
Menampilkan semua pelanggan aktif yang sudah ada di sistem.

#### Sheet "Panduan"
Berisi panduan lengkap cara penggunaan dan format data.

## Format Data

### Kolom Wajib
| Kolom | Format | Keterangan |
|-------|--------|------------|
| `tanggal_transaksi` | YYYY-MM-DD atau DD/MM/YYYY | Tanggal transaksi |
| `nama_pelanggan` | Text | Nama pelanggan (akan dibuat otomatis jika belum ada) |
| `kode_produk` | Text | Kode produk yang sudah ada di sistem |
| `jumlah` | Number | Jumlah produk (desimal diperbolehkan) |

### Kolom Opsional
| Kolom | Format | Default | Keterangan |
|-------|--------|---------|------------|
| `nomor_transaksi` | Text | Auto-generate | Nomor transaksi unik |
| `email_pelanggan` | Email | - | Email pelanggan |
| `satuan` | Text | Satuan produk | Satuan unit |
| `harga_satuan` | Number | Harga jual produk | Harga per unit |
| `diskon` | Number | 0 | Diskon dalam rupiah |
| `deskripsi` | Text | Nama produk | Deskripsi item |
| `metode_pembayaran` | Text | cash | cash, transfer, check, credit, debit_card, credit_card |
| `status_pembayaran` | Text | unpaid | unpaid, partial, paid, overpaid |
| `status_transaksi` | Text | completed | pending, processed, completed, rejected |
| `catatan` | Text | - | Catatan untuk item |
| `deskripsi_transaksi` | Text | - | Deskripsi untuk transaksi |

## Contoh Data

```csv
tanggal_transaksi,nama_pelanggan,kode_produk,jumlah,harga_satuan,diskon
2024-01-15,PT. Maju Jaya,LAPTOP001,2,15000000,500000
2024-01-15,PT. Maju Jaya,MOUSE001,2,500000,0
2024-01-16,CV. Sukses Mandiri,KEYBOARD001,1,750000,50000
```

## Fitur Import

### 1. Validasi Otomatis
- **Pelanggan baru** akan dibuat otomatis jika belum ada
- **Produk** harus sudah ada di sistem
- **Satuan** harus sudah ada di sistem
- **Format tanggal** fleksibel (YYYY-MM-DD, DD/MM/YYYY, dll)
- **Nomor transaksi** generate otomatis jika kosong

### 2. Perhitungan Otomatis
- **Line Total** = Jumlah × Harga Satuan
- **Subtotal** = Total Line Items - Diskon
- **PPN** = Subtotal × 11%
- **Total** = Subtotal + PPN

### 3. Grouping Transaksi
- Item dengan `nomor_transaksi` yang sama akan digabung dalam satu transaksi
- Jika `nomor_transaksi` kosong, akan generate otomatis per baris

### 4. Error Handling
- Sistem akan memberikan detail error per baris jika ada masalah
- Data yang berhasil akan tetap tersimpan meski ada error di baris lain
- Error report mencakup nomor baris dan deskripsi masalah

## Cara Import

### 1. Upload File
1. Di halaman **Transaksi**, klik tombol **"Import Penjualan"**
2. Pilih file Excel/CSV yang sudah diisi
3. Klik **"Import"**

### 2. Review Hasil
Setelah import, sistem akan menampilkan:
- **Jumlah data berhasil** diimport
- **Jumlah data dilewati** karena error
- **Detail error** jika ada masalah

## Tips Import

### ✅ Best Practices
- Gunakan template yang sudah disediakan
- Cek daftar produk dan pelanggan sebelum import
- Pastikan format tanggal konsisten
- Validasi data di Excel sebelum upload
- Import dalam batch kecil untuk data besar

### ❌ Hindari
- Mengubah nama kolom di template
- Menggunakan kode produk yang tidak ada
- Format tanggal yang tidak standar
- Nilai negatif untuk jumlah atau harga
- Karakter khusus di nomor transaksi

## Troubleshooting

### Error "Produk tidak ditemukan"
- Pastikan kode produk sesuai dengan yang ada di sistem
- Cek sheet "Daftar Produk" untuk referensi

### Error "Format tanggal tidak valid"
- Gunakan format YYYY-MM-DD (2024-01-15)
- Atau DD/MM/YYYY (15/01/2024)

### Error "Satuan tidak ditemukan"
- Pastikan satuan sudah ada di master data
- Kosongkan kolom satuan untuk menggunakan default produk

### Import tidak memproses data
- Pastikan file tidak kosong
- Cek apakah ada data di baris setelah header
- Pastikan format file Excel (.xlsx) atau CSV

## Batasan

- **File size**: Maksimal 10MB
- **Jumlah baris**: Disarankan maksimal 1000 baris per import
- **Format file**: Excel (.xlsx) atau CSV
- **Encoding**: UTF-8 untuk file CSV

## Support

Jika mengalami masalah dengan import, silakan:
1. Cek panduan di sheet "Panduan" pada template
2. Validasi data sesuai format yang ditentukan
3. Hubungi administrator sistem untuk bantuan lebih lanjut
