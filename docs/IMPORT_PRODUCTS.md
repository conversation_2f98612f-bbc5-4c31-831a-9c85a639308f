# Import Produk (Versi <PERSON>)

Fitur import produk memungkinkan Anda untuk mengimpor data produk secara bulk menggunakan file Excel atau CSV dengan format yang disederhanakan untuk kemudahan penggunaan.

## Cara Menggunakan

### 1. Download Template

1. Masuk ke halaman **Produk** di admin panel
2. <PERSON><PERSON> tombol **"Download Template"** di bagian atas
3. File template Excel akan terdownload dengan nama `template-import-produk.xlsx`

### 2. Isi Data di Template

Template terdiri dari 5 sheet:

#### Sheet "Template"

Sheet kosong untuk mengisi data Anda. Gunakan sheet ini untuk import.

#### Sheet "Contoh Data"

Berisi contoh data dengan format yang benar untuk referensi.

#### Sheet "Daftar Kategori"

Menampilkan semua kategori produk aktif yang sudah ada di sistem.

#### Sheet "Daftar Satuan"

Menampilkan semua satuan unit aktif yang sudah ada di sistem.

#### Sheet "Panduan"

Berisi panduan lengkap cara penggunaan dan format data.

## Format Data

### Kolom Wajib

| Kolom         | Format         | Keterangan                                                  |
| ------------- | -------------- | ----------------------------------------------------------- |
| `kode_produk` | Text (max 50)  | Kode unik produk                                            |
| `nama_produk` | Text (max 255) | Nama produk                                                 |
| `kategori`    | Text           | Nama kategori (akan dibuat otomatis jika belum ada)         |
| `satuan`      | Text           | Kode atau nama satuan (akan dibuat otomatis jika belum ada) |

### Kolom Opsional

| Kolom                | Format  | Default | Keterangan                                       |
| -------------------- | ------- | ------- | ------------------------------------------------ |
| `deskripsi`          | Text    | -       | Deskripsi produk                                 |
| `harga_pokok`        | Number  | 0       | Harga pokok produksi                             |
| `harga_jual`         | Number  | 0       | Harga jual ke customer                           |
| `harga_beli`         | Number  | 0       | Harga beli dari supplier                         |
| `stok_minimum`       | Number  | 0       | Stok minimum                                     |
| `stok_maksimum`      | Number  | 0       | Stok maksimum                                    |
| `titik_reorder`      | Number  | 0       | Titik reorder stok                               |
| `barcode`            | Text    | -       | Kode barcode produk                              |
| `merek`              | Text    | -       | Merek produk                                     |
| `berat`              | Number  | 0       | Berat produk dalam kg                            |
| `dimensi`            | Text    | -       | Dimensi produk (contoh: 35x25x3 cm)              |
| `dapat_dimanufaktur` | Boolean | tidak   | Apakah produk bisa diproduksi/dirakit (ya/tidak) |

## Contoh Data

```csv
kode_produk,nama_produk,kategori,satuan,deskripsi,harga_pokok,harga_jual,harga_beli,stok_minimum,stok_maksimum,titik_reorder,barcode,merek,berat,dimensi,dapat_dimanufaktur
LAPTOP001,Laptop Gaming ROG Strix,Elektronik,PCS,Laptop gaming dengan spesifikasi tinggi,12000000,15000000,11000000,5,50,10,1234567890123,ASUS,2.5,35x25x3 cm,tidak
MOUSE001,Gaming Mouse Wireless,Elektronik,PCS,Mouse gaming wireless dengan RGB,300000,500000,250000,10,100,20,1234567890124,Logitech,0.15,12x7x4 cm,tidak
FURNITURE001,Meja Kayu Custom,Furniture,PCS,Meja kayu yang dirakit sesuai pesanan,800000,1200000,0,2,10,3,,Custom Wood,15,120x60x75 cm,ya
```

## Fitur Import

### 1. Validasi Otomatis

-   **Kategori baru** akan dibuat otomatis jika belum ada
-   **Satuan baru** akan dibuat otomatis jika belum ada
-   **Kode produk** harus unik dalam sistem
-   **Tipe produk** otomatis diset ke 'finished_good' (produk jadi)
-   **Default values** untuk field yang tidak diisi

### 2. Update vs Create

-   **Produk baru** akan dibuat jika kode produk belum ada
-   **Produk existing** akan diupdate jika kode produk sudah ada
-   **Kategori dan satuan** akan menggunakan yang existing atau dibuat baru

### 3. Parsing Otomatis

-   **Format angka** fleksibel (1.500.000, 1,500,000, Rp 1.500.000)
-   **Default values** untuk field yang kosong
-   **Auto-generate SKU** dari kode produk

### 4. Error Handling

-   **Authentication Check** - Validasi user login dan client association
-   **Transaction Rollback** - Jika ada error fatal, semua perubahan di-rollback
-   **Per-row Error Reporting** - Detail error per baris dengan nomor baris
-   **Partial Success** - Data yang berhasil tetap tersimpan meski ada error di baris lain
-   **Comprehensive Logging** - Error report mencakup nomor baris dan deskripsi masalah

## Cara Import

### 1. Upload File

1. Di halaman **Produk**, klik tombol **"Import Produk"**
2. Pilih file Excel/CSV yang sudah diisi
3. Klik **"Import"**

### 2. Review Hasil

Setelah import, sistem akan menampilkan:

-   **Jumlah produk baru** yang berhasil dibuat
-   **Jumlah produk existing** yang diupdate
-   **Jumlah data dilewati** karena error
-   **Detail error** jika ada masalah

## Tips Import

### ✅ Best Practices

-   Gunakan template yang sudah disediakan
-   Cek daftar kategori dan satuan sebelum import
-   Pastikan kode produk unik dan konsisten
-   Validasi data di Excel sebelum upload
-   Import dalam batch kecil untuk data besar

### ❌ Hindari

-   Mengubah nama kolom di template
-   Menggunakan kode produk yang duplikat
-   Format angka yang tidak standar
-   Nilai negatif untuk harga atau stok
-   Menambah kolom yang tidak ada di template

## Troubleshooting

### Error "User must be authenticated to import products"

-   Pastikan Anda sudah login ke sistem
-   Refresh halaman dan coba lagi
-   Hubungi administrator jika masalah berlanjut

### Error "User must be associated with a client"

-   User harus terkait dengan client/perusahaan
-   Hubungi administrator untuk mengatur client association
-   Pastikan user memiliki akses yang tepat

### Error "Field 'client_id' doesn't have a default value"

-   Error ini sudah diperbaiki dengan validasi user authentication
-   Pastikan user sudah login dan memiliki client_id
-   Restart browser jika masalah berlanjut

### Error "Kode produk sudah ada"

-   Sistem akan mengupdate produk existing dengan data baru
-   Pastikan ini adalah behavior yang diinginkan

### Error "Kategori tidak dapat dibuat"

-   Periksa nama kategori tidak mengandung karakter khusus
-   Pastikan nama kategori tidak terlalu panjang

### Error "Format angka tidak valid"

-   Pastikan harga dan stok berupa angka
-   Gunakan format yang konsisten (tanpa huruf atau simbol khusus)

### Import tidak memproses data

-   Pastikan file tidak kosong
-   Cek apakah ada data di baris setelah header
-   Pastikan format file Excel (.xlsx) atau CSV

## Format Angka

### Harga dan Nilai Moneter

-   Gunakan angka tanpa simbol mata uang: `1500000`
-   Atau dengan pemisah ribuan: `1.500.000` atau `1,500,000`
-   Sistem akan membersihkan format otomatis

### Berat dan Dimensi

-   Berat dalam kg: `2.5`
-   Dimensi dalam format bebas: `35x25x3 cm`

### Field Dapat Dimanufaktur

-   **Format**: ya/tidak, true/false, 1/0
-   **Default**: tidak (false)
-   **Kegunaan**: Menandai produk yang bisa diproduksi/dirakit
-   **Contoh produk manufaktur**: Furniture custom, elektronik rakitan, makanan olahan
-   **Contoh produk non-manufaktur**: Barang jadi yang dibeli langsung dari supplier

## Batasan

-   **File size**: Maksimal 10MB
-   **Jumlah baris**: Disarankan maksimal 1000 baris per import
-   **Format file**: Excel (.xlsx) atau CSV
-   **Encoding**: UTF-8 untuk file CSV
-   **Kode produk**: Maksimal 50 karakter, harus unik
-   **Nama produk**: Maksimal 255 karakter
-   **Kolom**: 16 kolom (disederhanakan untuk kemudahan penggunaan)

## Support

Jika mengalami masalah dengan import, silakan:

1. Cek panduan di sheet "Panduan" pada template
2. Validasi data sesuai format yang ditentukan
3. Hubungi administrator sistem untuk bantuan lebih lanjut
