<?php

/**
 * <PERSON><PERSON>t to update navigation groups for all Filament Resources
 * Run this script to organize resources by feature groups
 */

$resourceGroups = [
    // Master Data Group
    'Master Data' => [
        'ProductCategoryResource' => [
            'icon' => 'heroicon-o-tag',
            'sort' => 1,
            'label' => 'Kategori Produk'
        ],
        'UnitOfMeasureResource' => [
            'icon' => 'heroicon-o-scale',
            'sort' => 2,
            'label' => 'Satuan'
        ],
        'ProductResource' => [
            'icon' => 'heroicon-o-cube',
            'sort' => 3,
            'label' => 'Produk'
        ],
        'SupplierResource' => [
            'icon' => 'heroicon-o-truck',
            'sort' => 4,
            'label' => 'Supplier'
        ],
        'CustomerResource' => [
            'icon' => 'heroicon-o-user-group',
            'sort' => 5,
            'label' => 'Customer'
        ],
        'LocationResource' => [
            'icon' => 'heroicon-o-map-pin',
            'sort' => 6,
            'label' => 'Lokasi'
        ],
    ],

    // Manufacturing Group
    'Manufacturing' => [
        'BillOfMaterialResource' => [
            'icon' => 'heroicon-o-document-text',
            'sort' => 1,
            'label' => 'Bill of Materials'
        ],
        'WorkCenterResource' => [
            'icon' => 'heroicon-o-cog-6-tooth',
            'sort' => 2,
            'label' => 'Work Center'
        ],
        'ProductionOrderResource' => [
            'icon' => 'heroicon-o-clipboard-document-list',
            'sort' => 3,
            'label' => 'Production Order'
        ],
        'WorkOrderResource' => [
            'icon' => 'heroicon-o-wrench-screwdriver',
            'sort' => 4,
            'label' => 'Work Order'
        ],
    ],

    // Inventory Group
    'Inventory' => [
        'InventoryResource' => [
            'icon' => 'heroicon-o-archive-box',
            'sort' => 1,
            'label' => 'Inventory'
        ],
    ],

    // Purchasing Group
    'Purchasing' => [
        'PurchaseOrderResource' => [
            'icon' => 'heroicon-o-shopping-cart',
            'sort' => 1,
            'label' => 'Purchase Order'
        ],
    ],

    // Financial Group
    'Financial' => [
        'AccountResource' => [
            'icon' => 'heroicon-o-banknotes',
            'sort' => 1,
            'label' => 'Chart of Accounts'
        ],
        'JournalResource' => [
            'icon' => 'heroicon-o-book-open',
            'sort' => 2,
            'label' => 'Journal Entries'
        ],
        'TransactionResource' => [
            'icon' => 'heroicon-o-arrows-right-left',
            'sort' => 3,
            'label' => 'Transactions'
        ],
        'BankAccountResource' => [
            'icon' => 'heroicon-o-building-library',
            'sort' => 4,
            'label' => 'Bank Accounts'
        ],
        'BankTransactionResource' => [
            'icon' => 'heroicon-o-credit-card',
            'sort' => 5,
            'label' => 'Bank Transactions'
        ],
        'CashFlowCategoryResource' => [
            'icon' => 'heroicon-o-chart-bar',
            'sort' => 6,
            'label' => 'Cash Flow Categories'
        ],
    ],

    // Organization Group
    'Organization' => [
        'DepartmentResource' => [
            'icon' => 'heroicon-o-building-office',
            'sort' => 1,
            'label' => 'Departments'
        ],
        'CostCenterResource' => [
            'icon' => 'heroicon-o-calculator',
            'sort' => 2,
            'label' => 'Cost Centers'
        ],
    ],

    // Administration Group
    'Administration' => [
        'ClientResource' => [
            'icon' => 'heroicon-o-building-office-2',
            'sort' => 1,
            'label' => 'Clients'
        ],
        'UserResource' => [
            'icon' => 'heroicon-o-users',
            'sort' => 2,
            'label' => 'Users'
        ],
    ],
];

// Function to update resource file
function updateResourceFile($resourceName, $groupName, $config) {
    $filePath = "app/Filament/Resources/{$resourceName}.php";
    
    if (!file_exists($filePath)) {
        echo "File not found: {$filePath}\n";
        return;
    }
    
    $content = file_get_contents($filePath);
    
    // Add navigation group
    if (!str_contains($content, 'protected static ?string $navigationGroup')) {
        $pattern = '/protected static \?string \$navigationIcon = \'[^\']+\';/';
        $replacement = "protected static ?string \$navigationIcon = '{$config['icon']}';

    protected static ?string \$navigationGroup = '{$groupName}';

    protected static ?int \$navigationSort = {$config['sort']};

    protected static ?string \$navigationLabel = '{$config['label']}';";
        
        $content = preg_replace($pattern, $replacement, $content);
    } else {
        // Update existing values
        $content = preg_replace(
            '/protected static \?string \$navigationIcon = \'[^\']+\';/',
            "protected static ?string \$navigationIcon = '{$config['icon']}';",
            $content
        );
        
        $content = preg_replace(
            '/protected static \?string \$navigationGroup = \'[^\']*\';/',
            "protected static ?string \$navigationGroup = '{$groupName}';",
            $content
        );
        
        $content = preg_replace(
            '/protected static \?int \$navigationSort = \d+;/',
            "protected static ?int \$navigationSort = {$config['sort']};",
            $content
        );
        
        $content = preg_replace(
            '/protected static \?string \$navigationLabel = \'[^\']*\';/',
            "protected static ?string \$navigationLabel = '{$config['label']}';",
            $content
        );
    }
    
    file_put_contents($filePath, $content);
    echo "Updated: {$resourceName} -> {$groupName}\n";
}

// Update all resources
foreach ($resourceGroups as $groupName => $resources) {
    echo "\n=== Updating {$groupName} Group ===\n";
    foreach ($resources as $resourceName => $config) {
        updateResourceFile($resourceName, $groupName, $config);
    }
}

echo "\n✅ All navigation groups updated successfully!\n";
echo "\n📋 Navigation Structure:\n";
foreach ($resourceGroups as $groupName => $resources) {
    echo "📁 {$groupName}\n";
    foreach ($resources as $resourceName => $config) {
        echo "  └── {$config['label']}\n";
    }
}
