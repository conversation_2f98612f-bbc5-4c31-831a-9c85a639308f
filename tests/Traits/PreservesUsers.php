<?php

namespace Tests\Traits;

use App\Models\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

trait PreservesUsers
{
    use RefreshDatabase;

    protected $testClient;
    protected $testUser;

    /**
     * Setup test environment with preserved users.
     */
    protected function setUpPreservesUsers(): void
    {
        // Run seeder to ensure users exist
        $this->artisan('db:seed', ['--class' => 'SuperAdminSeeder']);

        // Get or create a test client
        $this->testClient = Client::firstOrCreate(
            ['name' => 'Test Company'],
            [
                'name' => 'Test Company',
                'email' => '<EMAIL>',
                'phone' => '021-9999999',
                'address' => 'Test Address',
                'is_active' => true,
            ]
        );

        // Get or create a test user
        $this->testUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'user_type' => 'client',
                'client_id' => $this->testClient->id,
                'email_verified_at' => now(),
            ]
        );
    }

    /**
     * Get the test client.
     */
    protected function getTestClient(): Client
    {
        return $this->testClient;
    }

    /**
     * Get the test user.
     */
    protected function getTestUser(): User
    {
        return $this->testUser;
    }
}
