<?php

namespace Tests\Feature;

use App\Filament\Widgets\ProductEngineeringWidget;
use App\Models\Client;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductEngineeringWidgetTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;
    protected $category;
    protected $unit;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
        $this->category = ProductCategory::factory()->create(['client_id' => $this->client->id]);
        $this->unit = UnitOfMeasure::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_widget_can_be_instantiated(): void
    {
        $this->actingAs($this->user);

        $widget = new ProductEngineeringWidget();

        $this->assertInstanceOf(ProductEngineeringWidget::class, $widget);
    }

    public function test_widget_returns_correct_data_structure(): void
    {
        $this->actingAs($this->user);

        // Create a product
        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'selling_price' => 100000,
            'standard_cost' => 70000,
        ]);

        // Create a sales transaction with items
        $transaction = Transaction::create([
            'client_id' => $this->client->id,
            'transaction_date' => now(),
            'type' => 'sales',
            'description' => 'Test sales',
            'amount' => 200000,
            'status' => 'completed',
            'created_by' => $this->user->id,
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => 100000,
            'line_total' => 200000,
            'unit_id' => $this->unit->id,
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        // Check data structure
        $this->assertArrayHasKey('products', $data);
        $this->assertArrayHasKey('summary', $data);
        $this->assertArrayHasKey('chartData', $data);
        $this->assertArrayHasKey('quadrant_data', $data);
        $this->assertArrayHasKey('summary_stats', $data);
        $this->assertArrayHasKey('trend_data', $data);
        $this->assertArrayHasKey('performance_analysis', $data);
        $this->assertArrayHasKey('recommendations', $data);
        $this->assertArrayHasKey('period', $data);

        // Check products data
        $this->assertCount(1, $data['products']);
        $productData = $data['products'][0];

        $this->assertArrayHasKey('id', $productData);
        $this->assertArrayHasKey('code', $productData);
        $this->assertArrayHasKey('name', $productData);
        $this->assertArrayHasKey('category', $productData);
        $this->assertArrayHasKey('margin', $productData);
        $this->assertArrayHasKey('frequency', $productData);
        $this->assertArrayHasKey('revenue', $productData);
        $this->assertArrayHasKey('total_profit', $productData);
        $this->assertArrayHasKey('velocity', $productData);
        $this->assertArrayHasKey('sales_history', $productData);
        $this->assertArrayHasKey('quadrant', $productData);

        // Check summary data
        $this->assertArrayHasKey('total_products', $data['summary']);
        $this->assertEquals(1, $data['summary']['total_products']);

        // Check chart data
        $this->assertArrayHasKey('datasets', $data['chartData']);
        $this->assertCount(4, $data['chartData']['datasets']); // 4 quadrants
    }

    public function test_widget_calculates_margin_correctly(): void
    {
        $this->actingAs($this->user);

        // Create a product with known costs
        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'selling_price' => 100000,
            'standard_cost' => 60000, // 40% margin
        ]);

        // Create sales transaction
        $transaction = Transaction::create([
            'client_id' => $this->client->id,
            'transaction_date' => now(),
            'type' => 'sales',
            'description' => 'Test sales',
            'amount' => 100000,
            'status' => 'completed',
            'created_by' => $this->user->id,
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 100000,
            'line_total' => 100000,
            'unit_id' => $this->unit->id,
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = $data['products'][0];

        // Margin should be 40% ((100000 - 60000) / 100000 * 100)
        $this->assertEquals(40, $productData['margin']);
    }

    public function test_widget_categorizes_products_into_quadrants(): void
    {
        $this->actingAs($this->user);

        // Create a high-margin, high-frequency product (Star)
        $starProduct = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'selling_price' => 100000,
            'standard_cost' => 50000, // 50% margin (high)
        ]);

        // Create multiple transactions for high frequency
        for ($i = 0; $i < 6; $i++) {
            $transaction = Transaction::create([
                'client_id' => $this->client->id,
                'transaction_date' => now()->subDays($i),
                'type' => 'sales',
                'description' => "Test sales $i",
                'amount' => 100000,
                'status' => 'completed',
                'created_by' => $this->user->id,
            ]);

            TransactionItem::create([
                'client_id' => $this->client->id,
                'transaction_id' => $transaction->id,
                'product_id' => $starProduct->id,
                'quantity' => 1,
                'unit_price' => 100000,
                'line_total' => 100000,
                'unit_id' => $this->unit->id,
            ]);
        }

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = $data['products'][0];

        // Should be categorized as Star Product (high margin + high frequency)
        $this->assertEquals('Star Products', $productData['quadrant']);
        $this->assertEquals(6, $productData['frequency']);
        $this->assertEquals(50, $productData['margin']);
    }

    public function test_widget_includes_sales_history(): void
    {
        $this->actingAs($this->user);

        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
        ]);

        // Create a customer for the transaction
        $customer = \App\Models\Customer::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Create sales transaction
        $transaction = \App\Models\Transaction::create([
            'client_id' => $this->client->id,
            'transaction_date' => now(),
            'type' => 'sales',
            'description' => 'Test sales',
            'customer_id' => $customer->id,
            'amount' => 100000,
            'status' => 'completed',
            'created_by' => $this->user->id,
        ]);

        \App\Models\TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 100000,
            'line_total' => 100000,
            'unit_id' => $this->unit->id,
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = $data['products'][0];

        // Check sales history exists
        $this->assertArrayHasKey('sales_history', $productData);
        $this->assertIsArray($productData['sales_history']);
        $this->assertCount(1, $productData['sales_history']);

        $historyItem = $productData['sales_history'][0];
        $this->assertArrayHasKey('customer', $historyItem);
        $this->assertArrayHasKey('quantity', $historyItem);
        $this->assertArrayHasKey('unit_price', $historyItem);
        $this->assertEquals($customer->customer_name, $historyItem['customer']);
    }

    public function test_widget_includes_performance_analysis(): void
    {
        $this->actingAs($this->user);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $this->assertArrayHasKey('performance_analysis', $data);
        $performanceAnalysis = $data['performance_analysis'];

        $this->assertArrayHasKey('top_revenue_products', $performanceAnalysis);
        $this->assertArrayHasKey('top_profit_products', $performanceAnalysis);
        $this->assertArrayHasKey('fastest_moving_products', $performanceAnalysis);
        $this->assertArrayHasKey('slowest_moving_products', $performanceAnalysis);
        $this->assertArrayHasKey('high_margin_low_sales', $performanceAnalysis);
        $this->assertArrayHasKey('declining_products', $performanceAnalysis);
    }

    public function test_widget_includes_recommendations(): void
    {
        $this->actingAs($this->user);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $this->assertArrayHasKey('recommendations', $data);
        $this->assertIsArray($data['recommendations']);
    }

    public function test_widget_handles_division_by_zero_scenarios(): void
    {
        $this->actingAs($this->user);

        // Test 1: Product with zero cost
        $productZeroCost = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'ZEROCOST',
            'product_name' => 'Zero Cost Product',
            'standard_cost' => 0, // Zero cost
            'selling_price' => 150000,
        ]);

        // Test 2: Product with zero selling price in transaction
        $productZeroPrice = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'ZEROPRICE',
            'product_name' => 'Zero Price Product',
            'standard_cost' => 100000,
            'selling_price' => 150000,
        ]);

        // Create transaction with zero values
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $productZeroPrice->id,
            'unit_id' => $this->unit->id,
            'quantity' => 0, // Zero quantity
            'unit_price' => 0, // Zero price
            'line_total' => 0,
        ]);

        $widget = new ProductEngineeringWidget();

        // This should not throw division by zero error
        $data = $widget->getData();

        $this->assertIsArray($data);
        $this->assertArrayHasKey('products', $data);

        // Should handle all edge cases gracefully
        foreach ($data['products'] as $productData) {
            $this->assertIsNumeric($productData['margin']);
            $this->assertIsNumeric($productData['frequency']);
            $this->assertIsNumeric($productData['revenue']);
            $this->assertIsNumeric($productData['avg_order_value']);
            $this->assertIsNumeric($productData['velocity']);
            $this->assertIsNumeric($productData['stock_turnover']);

            // All values should be finite (not infinite or NaN)
            $this->assertTrue(is_finite($productData['margin']));
            $this->assertTrue(is_finite($productData['avg_order_value']));
            $this->assertTrue(is_finite($productData['velocity']));
            $this->assertTrue(is_finite($productData['stock_turnover']));
        }
    }
}
