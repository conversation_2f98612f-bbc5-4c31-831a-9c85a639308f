<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductResourceFormTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;
    protected $category;
    protected $unit;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
        
        $this->category = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'TEST',
            'category_name' => 'Test Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);
        
        $this->unit = UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'PCS',
            'unit_name' => 'Pieces',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);
    }

    public function test_can_create_product_with_category_and_unit(): void
    {
        $this->actingAs($this->user);

        $productData = [
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'TESTPROD001',
            'product_name' => 'Test Product',
            'product_type' => 'finished_good',
            'sku' => 'TESTPROD001',
            'standard_cost' => 100000,
            'selling_price' => 150000,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'reorder_point' => 20,
            'lead_time_days' => 7,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => false,
        ];

        $product = Product::create($productData);

        $this->assertDatabaseHas('products', [
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'TESTPROD001',
            'product_name' => 'Test Product',
        ]);

        // Test relationships
        $this->assertEquals($this->category->id, $product->category_id);
        $this->assertEquals($this->unit->id, $product->unit_id);
        $this->assertEquals($this->category->category_name, $product->category->category_name);
        $this->assertEquals($this->unit->unit_name, $product->unit->unit_name);
    }

    public function test_product_belongs_to_correct_client(): void
    {
        $this->actingAs($this->user);

        $product = Product::create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'TESTPROD002',
            'product_name' => 'Test Product 2',
            'product_type' => 'finished_good',
            'sku' => 'TESTPROD002',
            'standard_cost' => 100000,
            'selling_price' => 150000,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => false,
        ]);

        $this->assertEquals($this->client->id, $product->client_id);
        $this->assertEquals($this->client->name, $product->client->name);
    }

    public function test_can_query_products_by_client(): void
    {
        $this->actingAs($this->user);

        // Create product for current client
        $myProduct = Product::create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'MYPROD001',
            'product_name' => 'My Product',
            'product_type' => 'finished_good',
            'sku' => 'MYPROD001',
            'standard_cost' => 100000,
            'selling_price' => 150000,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => false,
        ]);

        // Create another client with product
        $anotherClient = Client::factory()->create();
        $anotherCategory = ProductCategory::create([
            'client_id' => $anotherClient->id,
            'category_code' => 'OTHER',
            'category_name' => 'Other Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);
        $anotherUnit = UnitOfMeasure::create([
            'client_id' => $anotherClient->id,
            'unit_code' => 'KG',
            'unit_name' => 'Kilogram',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        $otherProduct = Product::create([
            'client_id' => $anotherClient->id,
            'category_id' => $anotherCategory->id,
            'unit_id' => $anotherUnit->id,
            'product_code' => 'OTHERPROD001',
            'product_name' => 'Other Product',
            'product_type' => 'finished_good',
            'sku' => 'OTHERPROD001',
            'standard_cost' => 100000,
            'selling_price' => 150000,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => false,
        ]);

        // Query should only return products for current client
        $myProducts = Product::where('client_id', $this->user->client_id)->get();

        $this->assertCount(1, $myProducts);
        $this->assertEquals('MYPROD001', $myProducts->first()->product_code);
        $this->assertNotContains($otherProduct->id, $myProducts->pluck('id'));
    }

    public function test_category_and_unit_options_scoped_to_client(): void
    {
        $this->actingAs($this->user);

        // Create another client with category and unit
        $anotherClient = Client::factory()->create();
        $anotherCategory = ProductCategory::create([
            'client_id' => $anotherClient->id,
            'category_code' => 'OTHER',
            'category_name' => 'Other Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);
        $anotherUnit = UnitOfMeasure::create([
            'client_id' => $anotherClient->id,
            'unit_code' => 'KG',
            'unit_name' => 'Kilogram',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Query categories for current client
        $myCategories = ProductCategory::where('client_id', $this->user->client_id)
            ->where('is_active', true)
            ->get();

        $this->assertCount(1, $myCategories);
        $this->assertEquals('TEST', $myCategories->first()->category_code);
        $this->assertNotContains($anotherCategory->id, $myCategories->pluck('id'));

        // Query units for current client
        $myUnits = UnitOfMeasure::where('client_id', $this->user->client_id)
            ->where('is_active', true)
            ->get();

        $this->assertCount(1, $myUnits);
        $this->assertEquals('PCS', $myUnits->first()->unit_code);
        $this->assertNotContains($anotherUnit->id, $myUnits->pluck('id'));
    }

    public function test_can_update_product_category_and_unit(): void
    {
        $this->actingAs($this->user);

        // Create product
        $product = Product::create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'UPDATETEST',
            'product_name' => 'Update Test Product',
            'product_type' => 'finished_good',
            'sku' => 'UPDATETEST',
            'standard_cost' => 100000,
            'selling_price' => 150000,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => false,
        ]);

        // Create new category and unit
        $newCategory = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'NEW',
            'category_name' => 'New Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        $newUnit = UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'KG',
            'unit_name' => 'Kilogram',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Update product
        $product->update([
            'category_id' => $newCategory->id,
            'unit_id' => $newUnit->id,
            'product_name' => 'Updated Product Name',
        ]);

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'category_id' => $newCategory->id,
            'unit_id' => $newUnit->id,
            'product_name' => 'Updated Product Name',
        ]);

        // Refresh and test relationships
        $product->refresh();
        $this->assertEquals($newCategory->category_name, $product->category->category_name);
        $this->assertEquals($newUnit->unit_name, $product->unit->unit_name);
    }
}
