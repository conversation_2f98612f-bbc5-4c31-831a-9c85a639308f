<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Client;
use App\Models\User;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Customer;
use App\Models\Supplier;

class BasicSystemTest extends TestCase
{
    use RefreshDatabase;

    public function test_client_can_be_created(): void
    {
        $client = Client::factory()->create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('clients', [
            'name' => 'Test Client',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_belongs_to_client(): void
    {
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);

        $this->assertEquals($client->id, $user->client_id);
        $this->assertInstanceOf(Client::class, $user->client);
    }

    public function test_account_can_be_created_with_client(): void
    {
        $client = Client::factory()->create();
        
        $account = Account::factory()->create([
            'client_id' => $client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        $this->assertDatabaseHas('accounts', [
            'client_id' => $client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
        ]);
    }

    public function test_journal_can_be_created_with_entries(): void
    {
        $client = Client::factory()->create();
        
        $cashAccount = Account::factory()->create([
            'client_id' => $client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        $revenueAccount = Account::factory()->create([
            'client_id' => $client->id,
            'account_code' => '4101',
            'account_name' => 'Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $client->id,
            'journal_date' => now(),
            'description' => 'Test Journal',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Cash debit',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $revenueAccount->id,
            'debit' => 0,
            'credit' => 100000,
            'description' => 'Revenue credit',
        ]);

        $this->assertDatabaseHas('journals', [
            'client_id' => $client->id,
            'description' => 'Test Journal',
        ]);

        $this->assertDatabaseHas('journal_entries', [
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'debit' => 100000,
        ]);

        $this->assertDatabaseHas('journal_entries', [
            'journal_id' => $journal->id,
            'account_id' => $revenueAccount->id,
            'credit' => 100000,
        ]);
    }

    public function test_journal_balance_calculation(): void
    {
        $client = Client::factory()->create();
        
        $account1 = Account::factory()->create(['client_id' => $client->id]);
        $account2 = Account::factory()->create(['client_id' => $client->id, 'account_code' => '4101']);

        $journal = Journal::factory()->create(['client_id' => $client->id]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account1->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Debit entry',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account2->id,
            'debit' => 0,
            'credit' => 100000,
            'description' => 'Credit entry',
        ]);

        $totalDebit = $journal->journalEntries()->sum('debit');
        $totalCredit = $journal->journalEntries()->sum('credit');

        $this->assertEquals(100000, $totalDebit);
        $this->assertEquals(100000, $totalCredit);
        $this->assertEquals($totalDebit, $totalCredit);
    }

    public function test_customer_can_be_created(): void
    {
        $client = Client::factory()->create();
        
        $customer = Customer::factory()->create([
            'client_id' => $client->id,
            'customer_code' => 'CUST-001',
            'customer_name' => 'Test Customer',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('customers', [
            'client_id' => $client->id,
            'customer_code' => 'CUST-001',
            'customer_name' => 'Test Customer',
        ]);
    }

    public function test_supplier_can_be_created(): void
    {
        $client = Client::factory()->create();
        
        $supplier = Supplier::factory()->create([
            'client_id' => $client->id,
            'supplier_code' => 'SUPP-001',
            'supplier_name' => 'Test Supplier',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('suppliers', [
            'client_id' => $client->id,
            'supplier_code' => 'SUPP-001',
            'supplier_name' => 'Test Supplier',
        ]);
    }

    public function test_multi_tenant_isolation(): void
    {
        $client1 = Client::factory()->create(['name' => 'Client 1']);
        $client2 = Client::factory()->create(['name' => 'Client 2']);

        $account1 = Account::factory()->create([
            'client_id' => $client1->id,
            'account_code' => '1101',
        ]);

        $account2 = Account::factory()->create([
            'client_id' => $client2->id,
            'account_code' => '1101', // Same code but different client
        ]);

        // Each client should only see their own accounts
        $client1Accounts = Account::where('client_id', $client1->id)->get();
        $client2Accounts = Account::where('client_id', $client2->id)->get();

        $this->assertCount(1, $client1Accounts);
        $this->assertCount(1, $client2Accounts);
        $this->assertTrue($client1Accounts->contains($account1));
        $this->assertFalse($client1Accounts->contains($account2));
        $this->assertTrue($client2Accounts->contains($account2));
        $this->assertFalse($client2Accounts->contains($account1));
    }

    public function test_soft_deletes_work(): void
    {
        $client = Client::factory()->create();
        $account = Account::factory()->create(['client_id' => $client->id]);
        $journal = Journal::factory()->create(['client_id' => $client->id]);
        $customer = Customer::factory()->create(['client_id' => $client->id]);

        // Soft delete
        $account->delete();
        $journal->delete();
        $customer->delete();

        // Should be soft deleted
        $this->assertSoftDeleted($account);
        $this->assertSoftDeleted($journal);
        $this->assertSoftDeleted($customer);

        // Should not appear in normal queries
        $this->assertCount(0, Account::where('client_id', $client->id)->get());
        $this->assertCount(0, Journal::where('client_id', $client->id)->get());
        $this->assertCount(0, Customer::where('client_id', $client->id)->get());

        // Should appear in withTrashed queries
        $this->assertCount(1, Account::withTrashed()->where('client_id', $client->id)->get());
        $this->assertCount(1, Journal::withTrashed()->where('client_id', $client->id)->get());
        $this->assertCount(1, Customer::withTrashed()->where('client_id', $client->id)->get());
    }

    public function test_account_balance_calculation(): void
    {
        $client = Client::factory()->create();
        
        $cashAccount = Account::factory()->create([
            'client_id' => $client->id,
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        $journal = Journal::factory()->create(['client_id' => $client->id]);

        // Add some transactions
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Initial deposit',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'debit' => 50000,
            'credit' => 0,
            'description' => 'Additional deposit',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'debit' => 0,
            'credit' => 30000,
            'description' => 'Withdrawal',
        ]);

        // For asset account: balance = total debits - total credits
        $expectedBalance = 100000 + 50000 - 30000; // 120000
        
        $totalDebits = $cashAccount->journalEntries()->sum('debit');
        $totalCredits = $cashAccount->journalEntries()->sum('credit');
        $balance = $totalDebits - $totalCredits;

        $this->assertEquals(150000, $totalDebits);
        $this->assertEquals(30000, $totalCredits);
        $this->assertEquals(120000, $balance);
    }
}
