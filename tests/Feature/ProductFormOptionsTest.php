<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class ProductFormOptionsTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_category_options_returns_correct_data(): void
    {
        $this->actingAs($this->user);

        // Create categories for current client
        $category1 = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'CAT1',
            'category_name' => 'Category 1',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        $category2 = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'CAT2',
            'category_name' => 'Category 2',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        // Create inactive category (should not appear)
        $inactiveCategory = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'INACTIVE',
            'category_name' => 'Inactive Category',
            'is_active' => false,
            'sort_order' => 0,
        ]);

        // Create category for another client (should not appear)
        $anotherClient = Client::factory()->create();
        $otherCategory = ProductCategory::create([
            'client_id' => $anotherClient->id,
            'category_code' => 'OTHER',
            'category_name' => 'Other Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        // Test the options query that would be used in the form
        $options = ProductCategory::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('category_name', 'id');

        $this->assertCount(2, $options);
        $this->assertTrue($options->has($category1->id));
        $this->assertTrue($options->has($category2->id));
        $this->assertFalse($options->has($inactiveCategory->id));
        $this->assertFalse($options->has($otherCategory->id));

        $this->assertEquals('Category 1', $options[$category1->id]);
        $this->assertEquals('Category 2', $options[$category2->id]);
    }

    public function test_unit_options_returns_correct_data(): void
    {
        $this->actingAs($this->user);

        // Create units for current client
        $unit1 = UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'PCS',
            'unit_name' => 'Pieces',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        $unit2 = UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'KG',
            'unit_name' => 'Kilogram',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Create inactive unit (should not appear)
        $inactiveUnit = UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'INACTIVE',
            'unit_name' => 'Inactive Unit',
            'is_active' => false,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Create unit for another client (should not appear)
        $anotherClient = Client::factory()->create();
        $otherUnit = UnitOfMeasure::create([
            'client_id' => $anotherClient->id,
            'unit_code' => 'OTHER',
            'unit_name' => 'Other Unit',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Test the options query that would be used in the form
        $options = UnitOfMeasure::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('unit_name', 'id');

        $this->assertCount(2, $options);
        $this->assertTrue($options->has($unit1->id));
        $this->assertTrue($options->has($unit2->id));
        $this->assertFalse($options->has($inactiveUnit->id));
        $this->assertFalse($options->has($otherUnit->id));

        $this->assertEquals('Pieces', $options[$unit1->id]);
        $this->assertEquals('Kilogram', $options[$unit2->id]);
    }

    public function test_options_empty_when_no_data(): void
    {
        $this->actingAs($this->user);

        // No categories or units created
        $categoryOptions = ProductCategory::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('category_name', 'id');

        $unitOptions = UnitOfMeasure::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('unit_name', 'id');

        $this->assertCount(0, $categoryOptions);
        $this->assertCount(0, $unitOptions);
    }

    public function test_options_work_with_different_clients(): void
    {
        // Test with first client
        $this->actingAs($this->user);

        $category1 = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'CLIENT1CAT',
            'category_name' => 'Client 1 Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        $options1 = ProductCategory::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('category_name', 'id');

        $this->assertCount(1, $options1);
        $this->assertEquals('Client 1 Category', $options1[$category1->id]);

        // Test with second client
        $client2 = Client::factory()->create();
        $user2 = User::factory()->create(['client_id' => $client2->id]);
        $this->actingAs($user2);

        $category2 = ProductCategory::create([
            'client_id' => $client2->id,
            'category_code' => 'CLIENT2CAT',
            'category_name' => 'Client 2 Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        $options2 = ProductCategory::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('category_name', 'id');

        $this->assertCount(1, $options2);
        $this->assertEquals('Client 2 Category', $options2[$category2->id]);
        $this->assertFalse($options2->has($category1->id)); // Should not see other client's data
    }

    public function test_form_options_closure_works(): void
    {
        $this->actingAs($this->user);

        // Create test data
        ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'TEST',
            'category_name' => 'Test Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'PCS',
            'unit_name' => 'Pieces',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Simulate the closure used in ProductResource form
        $categoryOptionsClosure = function () {
            return \App\Models\ProductCategory::where('client_id', Auth::user()?->client_id)
                ->where('is_active', true)
                ->pluck('category_name', 'id');
        };

        $unitOptionsClosure = function () {
            return \App\Models\UnitOfMeasure::where('client_id', Auth::user()?->client_id)
                ->where('is_active', true)
                ->pluck('unit_name', 'id');
        };

        $categoryOptions = $categoryOptionsClosure();
        $unitOptions = $unitOptionsClosure();

        $this->assertCount(1, $categoryOptions);
        $this->assertCount(1, $unitOptions);
        $this->assertContains('Test Category', $categoryOptions);
        $this->assertContains('Pieces', $unitOptions);
    }
}
