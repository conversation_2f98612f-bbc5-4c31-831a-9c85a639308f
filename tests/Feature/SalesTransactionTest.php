<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

use Tests\TestCase;

class SalesTransactionTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;
    protected $customer;
    protected $product;
    protected $unit;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
        $this->customer = Customer::factory()->create(['client_id' => $this->client->id]);
        $this->unit = UnitOfMeasure::factory()->create(['client_id' => $this->client->id]);
        $this->product = Product::factory()->create([
            'client_id' => $this->client->id,
            'unit_id' => $this->unit->id,
            'selling_price' => 100000,
        ]);
    }

    public function test_can_create_sales_transaction_with_items(): void
    {
        $transaction = Transaction::create([
            'client_id' => $this->client->id,
            'transaction_date' => now(),
            'type' => 'sales',
            'description' => 'Test sales transaction',
            'customer_id' => $this->customer->id,
            'payment_method' => 'cash',
            'payment_status' => 'paid',
            'amount' => 0, // Will be calculated
            'status' => 'completed',
            'created_by' => $this->user->id,
        ]);

        // Add transaction items
        $item = TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'description' => $this->product->product_name,
            'quantity' => 2,
            'unit_price' => 100000,
            'line_total' => 200000,
            'unit_id' => $this->unit->id,
        ]);

        // Calculate totals
        $transaction->calculateTotals();

        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'type' => 'sales',
            'customer_id' => $this->customer->id,
            'subtotal' => 200000,
            'tax_amount' => 22000, // 11% PPN
            'amount' => 222000, // subtotal + tax
        ]);

        $this->assertDatabaseHas('transaction_items', [
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
            'unit_price' => 100000,
            'line_total' => 200000,
        ]);
    }

    public function test_transaction_has_correct_relationships(): void
    {
        $transaction = Transaction::create([
            'client_id' => $this->client->id,
            'transaction_date' => now(),
            'type' => 'sales',
            'description' => 'Test sales transaction',
            'customer_id' => $this->customer->id,
            'amount' => 100000,
            'status' => 'pending',
            'created_by' => $this->user->id,
        ]);

        // Test relationships
        $this->assertInstanceOf(Customer::class, $transaction->customer);
        $this->assertInstanceOf(User::class, $transaction->creator);
        $this->assertInstanceOf(Client::class, $transaction->client);
        $this->assertEquals($this->customer->id, $transaction->customer->id);
    }

    public function test_transaction_item_calculates_line_total_automatically(): void
    {
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
        ]);

        $item = new TransactionItem([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $this->product->id,
            'quantity' => 3,
            'unit_price' => 50000,
            'unit_id' => $this->unit->id,
        ]);

        $item->save();

        // Line total should be calculated automatically
        $this->assertEquals(150000, $item->line_total);
    }

    public function test_sales_transaction_helper_methods(): void
    {
        $salesTransaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
        ]);

        $expenseTransaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'expense',
        ]);

        $this->assertTrue($salesTransaction->isSales());
        $this->assertFalse($expenseTransaction->isSales());
    }
}
