<?php

namespace Tests\Feature;

use App\Filament\Widgets\ProductEngineeringWidget;
use App\Models\Client;
use App\Models\Customer;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductEngineeringCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;
    protected $customer;
    protected $category;
    protected $unit;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
        $this->category = ProductCategory::factory()->create(['client_id' => $this->client->id]);
        $this->unit = UnitOfMeasure::factory()->create(['client_id' => $this->client->id]);
        $this->customer = Customer::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_margin_calculation_is_correct(): void
    {
        $this->actingAs($this->user);

        // Create product with known costs
        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'selling_price' => 100000, // Rp 100,000
            'standard_cost' => 60000,  // Rp 60,000
        ]);

        // Create transaction with known selling price
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
            'customer_id' => $this->customer->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        // Create transaction item
        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'unit_id' => $this->unit->id,
            'quantity' => 2,
            'unit_price' => 90000, // Actual selling price Rp 90,000
            'line_total' => 180000, // 2 x 90,000
            'description' => 'Test item',
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = collect($data['products'])->firstWhere('id', $product->id);

        // Expected margin calculation:
        // Average selling price = 180,000 / 2 = 90,000
        // Margin = ((90,000 - 60,000) / 90,000) * 100 = 33.33%
        $expectedMargin = ((90000 - 60000) / 90000) * 100;

        $this->assertNotNull($productData);
        $this->assertEquals(round($expectedMargin, 2), $productData['margin']);
        $this->assertEquals(100000, $productData['selling_price']); // From product model (not transaction)
        $this->assertEquals(60000, $productData['standard_cost']);
    }

    public function test_revenue_calculation_is_correct(): void
    {
        $this->actingAs($this->user);

        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
        ]);

        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
            'customer_id' => $this->customer->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        // Create multiple transaction items
        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'unit_id' => $this->unit->id,
            'quantity' => 3,
            'unit_price' => 50000,
            'line_total' => 150000,
            'description' => 'Test item 1',
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'unit_id' => $this->unit->id,
            'quantity' => 2,
            'unit_price' => 75000,
            'line_total' => 150000,
            'description' => 'Test item 2',
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = collect($data['products'])->firstWhere('id', $product->id);

        // Expected calculations:
        // Total revenue = 150,000 + 150,000 = 300,000
        // Total quantity = 3 + 2 = 5
        // Sales frequency = 2 (two transaction items)

        $this->assertNotNull($productData);
        $this->assertEquals(300000, $productData['revenue']);
        $this->assertEquals(5, $productData['quantity_sold']);
        $this->assertEquals(2, $productData['frequency']);
    }

    public function test_average_calculations_in_summary_stats(): void
    {
        $this->actingAs($this->user);

        // Create multiple products with different margins and frequencies
        $product1 = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'standard_cost' => 50000,
        ]);

        $product2 = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'standard_cost' => 30000,
        ]);

        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
            'customer_id' => $this->customer->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        // Product 1: Margin = ((100,000 - 50,000) / 100,000) * 100 = 50%
        // Frequency = 1
        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product1->id,
            'unit_id' => $this->unit->id,
            'quantity' => 1,
            'unit_price' => 100000,
            'line_total' => 100000,
            'description' => 'Product 1',
        ]);

        // Product 2: Margin = ((60,000 - 30,000) / 60,000) * 100 = 50%
        // Frequency = 2
        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product2->id,
            'unit_id' => $this->unit->id,
            'quantity' => 1,
            'unit_price' => 60000,
            'line_total' => 60000,
            'description' => 'Product 2 - Item 1',
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product2->id,
            'unit_id' => $this->unit->id,
            'quantity' => 1,
            'unit_price' => 60000,
            'line_total' => 60000,
            'description' => 'Product 2 - Item 2',
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $stats = $data['summary_stats'];

        // Expected calculations:
        // Total products = 2
        // Average margin = (50 + 50) / 2 = 50%
        // Average frequency = (1 + 2) / 2 = 1.5
        // Total revenue = 100,000 + 60,000 + 60,000 = 220,000

        $this->assertEquals(2, $stats['total_products']);
        $this->assertEquals(50, $stats['avg_margin']);
        $this->assertEquals(1.5, $stats['avg_frequency']);
        $this->assertEquals(220000, $stats['total_revenue']);
    }

    public function test_quadrant_classification_is_correct(): void
    {
        $this->actingAs($this->user);

        // Create products for each quadrant
        $starProduct = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'standard_cost' => 40000, // Will result in high margin
        ]);

        $dogProduct = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'standard_cost' => 90000, // Will result in low margin
        ]);

        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
            'customer_id' => $this->customer->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        // Star Product: High margin (>20%), High frequency (>5)
        for ($i = 0; $i < 6; $i++) {
            TransactionItem::create([
                'client_id' => $this->client->id,
                'transaction_id' => $transaction->id,
                'product_id' => $starProduct->id,
                'unit_id' => $this->unit->id,
                'quantity' => 1,
                'unit_price' => 100000, // Margin = ((100,000 - 40,000) / 100,000) * 100 = 60%
                'line_total' => 100000,
                'description' => "Star item $i",
            ]);
        }

        // Dog Product: Low margin (<20%), Low frequency (<5)
        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $dogProduct->id,
            'unit_id' => $this->unit->id,
            'quantity' => 1,
            'unit_price' => 100000, // Margin = ((100,000 - 90,000) / 100,000) * 100 = 10%
            'line_total' => 100000,
            'description' => 'Dog item',
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $starProductData = collect($data['products'])->firstWhere('id', $starProduct->id);
        $dogProductData = collect($data['products'])->firstWhere('id', $dogProduct->id);

        $this->assertEquals('Star Products', $starProductData['quadrant']);
        $this->assertEquals('Dogs', $dogProductData['quadrant']);
        $this->assertEquals(60, $starProductData['margin']);
        $this->assertEquals(6, $starProductData['frequency']);
        $this->assertEquals(10, $dogProductData['margin']);
        $this->assertEquals(1, $dogProductData['frequency']);
    }

    public function test_velocity_calculation_is_correct(): void
    {
        $this->actingAs($this->user);

        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
        ]);

        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
            'customer_id' => $this->customer->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'unit_id' => $this->unit->id,
            'quantity' => 12, // 12 units sold
            'unit_price' => 50000,
            'line_total' => 600000,
            'description' => 'Test item',
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = collect($data['products'])->firstWhere('id', $product->id);

        // Expected velocity = 12 units / 6 months = 2 units per month
        $this->assertEquals(2, $productData['velocity']);
    }

    public function test_stock_turnover_calculation_is_correct(): void
    {
        $this->actingAs($this->user);

        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
        ]);

        // Create inventory for the product
        Inventory::create([
            'client_id' => $this->client->id,
            'product_id' => $product->id,
            'current_stock' => 10, // Current stock
            'available_stock' => 10,
            'reserved_stock' => 0,
            'average_cost' => 50000,
            'total_value' => 500000,
        ]);

        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
            'customer_id' => $this->customer->id,
            'transaction_date' => Carbon::now()->subDays(30),
        ]);

        TransactionItem::create([
            'client_id' => $this->client->id,
            'transaction_id' => $transaction->id,
            'product_id' => $product->id,
            'unit_id' => $this->unit->id,
            'quantity' => 20, // 20 units sold
            'unit_price' => 50000,
            'line_total' => 1000000,
            'description' => 'Test item',
        ]);

        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = collect($data['products'])->firstWhere('id', $product->id);

        // Expected stock turnover = 20 units sold / 10 current stock = 2.0
        $this->assertEquals(2, $productData['stock_turnover']);
    }

    public function test_zero_division_handling(): void
    {
        $this->actingAs($this->user);

        // Create product with zero standard cost
        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'standard_cost' => 0,
        ]);

        // Create inventory with zero stock
        Inventory::create([
            'client_id' => $this->client->id,
            'product_id' => $product->id,
            'current_stock' => 0,
            'available_stock' => 0,
            'reserved_stock' => 0,
            'average_cost' => 0,
            'total_value' => 0,
        ]);

        // No transactions (zero sales)
        $widget = new ProductEngineeringWidget();
        $data = $widget->getData();

        $productData = collect($data['products'])->firstWhere('id', $product->id);

        // Should handle zero divisions gracefully
        $this->assertEquals(0, $productData['margin']);
        $this->assertEquals(0, $productData['frequency']);
        $this->assertEquals(0, $productData['revenue']);
        $this->assertEquals(0, $productData['velocity']);
        $this->assertEquals(0, $productData['stock_turnover']);
    }
}
