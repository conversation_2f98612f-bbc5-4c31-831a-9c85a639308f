<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductCategoryResourceTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_can_create_product_category(): void
    {
        $this->actingAs($this->user);

        $categoryData = [
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'description' => 'Kategori untuk produk elektronik',
            'is_active' => true,
            'sort_order' => 1,
        ];

        // Test that category can be created without specifying client_id
        $category = ProductCategory::create(array_merge($categoryData, [
            'client_id' => $this->client->id
        ]));

        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'is_active' => true,
        ]);

        $this->assertEquals($this->client->id, $category->client_id);
    }

    public function test_category_code_must_be_unique_per_client(): void
    {
        $this->actingAs($this->user);

        // Create first category
        ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Try to create another category with same code for same client
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'ELEC', // Same code
            'category_name' => 'Electronics',
            'is_active' => true,
            'sort_order' => 2,
        ]);
    }

    public function test_same_category_code_allowed_for_different_clients(): void
    {
        $this->actingAs($this->user);

        // Create another client
        $anotherClient = Client::factory()->create();

        // Create category for first client
        ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create category with same code for different client - should work
        $category2 = ProductCategory::create([
            'client_id' => $anotherClient->id,
            'category_code' => 'ELEC', // Same code but different client
            'category_name' => 'Electronics',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
        ]);

        $this->assertDatabaseHas('product_categories', [
            'client_id' => $anotherClient->id,
            'category_code' => 'ELEC',
            'category_name' => 'Electronics',
        ]);
    }

    public function test_can_create_hierarchical_categories(): void
    {
        $this->actingAs($this->user);

        // Create parent category
        $parentCategory = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create child category
        $childCategory = ProductCategory::create([
            'client_id' => $this->client->id,
            'parent_id' => $parentCategory->id,
            'category_code' => 'LAPTOP',
            'category_name' => 'Laptop',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $this->assertEquals($parentCategory->id, $childCategory->parent_id);
        $this->assertEquals($this->client->id, $childCategory->client_id);
    }

    public function test_category_belongs_to_correct_client(): void
    {
        $this->actingAs($this->user);

        $category = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'TEST',
            'category_name' => 'Test Category',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $this->assertEquals($this->client->id, $category->client_id);
        $this->assertEquals($this->client->name, $category->client->name);
    }

    public function test_can_update_category(): void
    {
        $this->actingAs($this->user);

        $category = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'description' => 'Old description',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $category->update([
            'category_name' => 'Electronics Updated',
            'description' => 'New description',
            'sort_order' => 2,
        ]);

        $this->assertDatabaseHas('product_categories', [
            'id' => $category->id,
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Electronics Updated',
            'description' => 'New description',
            'sort_order' => 2,
        ]);
    }

    public function test_can_deactivate_category(): void
    {
        $this->actingAs($this->user);

        $category = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'ELEC',
            'category_name' => 'Elektronik',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $category->update(['is_active' => false]);

        $this->assertDatabaseHas('product_categories', [
            'id' => $category->id,
            'is_active' => false,
        ]);
    }
}
