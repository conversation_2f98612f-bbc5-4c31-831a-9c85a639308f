<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use Filament\Facades\Filament;

class DivisionByZeroFixTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test client
        $this->client = Client::factory()->create();

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Authenticate user first
        $this->actingAs($this->user);

        // Then set current tenant
        Filament::setTenant($this->client);
    }

    public function test_advanced_financial_widget_handles_zero_budget(): void
    {
        // Create accounts but no journal entries (zero budget scenario)
        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Sales Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '6101',
            'account_name' => 'Operating Expenses',
            'account_type' => 'expense',
            'normal_balance' => 'debit',
        ]);

        // Test widget with zero budget scenario
        $widget = new \App\Filament\Widgets\AdvancedFinancialWidget();

        // This should not throw a division by zero error
        $data = $widget->getViewData();

        $this->assertIsArray($data);
        $this->assertArrayHasKey('budget_variance', $data);

        // Verify budget variance handles zero budget gracefully
        $budgetVariance = $data['budget_variance'];
        foreach (['revenue', 'expenses', 'profit'] as $category) {
            $this->assertArrayHasKey($category, $budgetVariance);
            $this->assertArrayHasKey('variance', $budgetVariance[$category]);
            $this->assertIsNumeric($budgetVariance[$category]['variance']);
            // Should not be infinite or NaN
            $this->assertFalse(is_infinite($budgetVariance[$category]['variance']));
            $this->assertFalse(is_nan($budgetVariance[$category]['variance']));
        }
    }

    public function test_advanced_operational_widget_handles_zero_values(): void
    {
        // Test widget with no work orders or sales orders (zero values scenario)
        $widget = new \App\Filament\Widgets\AdvancedOperationalWidget();

        // This should not throw a division by zero error
        $data = $widget->getViewData();

        $this->assertIsArray($data);

        // Verify operational efficiency handles zero values gracefully
        $this->assertArrayHasKey('operational_efficiency', $data);
        $efficiency = $data['operational_efficiency'];
        $this->assertIsNumeric($efficiency['score']);
        $this->assertFalse(is_infinite($efficiency['score']));
        $this->assertFalse(is_nan($efficiency['score']));

        // Verify production metrics handle zero values
        $this->assertArrayHasKey('production_metrics', $data);
        $production = $data['production_metrics'];
        $this->assertIsNumeric($production['current']['cost_per_unit']);
        $this->assertFalse(is_infinite($production['current']['cost_per_unit']));
        $this->assertFalse(is_nan($production['current']['cost_per_unit']));

        // Verify quality metrics handle zero values
        $this->assertArrayHasKey('quality_metrics', $data);
        $quality = $data['quality_metrics'];
        foreach (['defect_rate', 'first_pass_yield', 'quality_score'] as $metric) {
            $this->assertIsNumeric($quality[$metric]);
            $this->assertFalse(is_infinite($quality[$metric]));
            $this->assertFalse(is_nan($quality[$metric]));
        }
    }

    public function test_interactive_kpi_widget_handles_zero_values(): void
    {
        // Create accounts but no journal entries
        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Sales Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '6101',
            'account_name' => 'Operating Expenses',
            'account_type' => 'expense',
            'normal_balance' => 'debit',
        ]);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        // Test widget with zero values
        $widget = new \App\Filament\Widgets\InteractiveKpiWidget();

        // This should not throw a division by zero error
        $data = $widget->getViewData();

        $this->assertIsArray($data);

        // Verify all KPI calculations handle zero values
        foreach (['revenue', 'expenses', 'profit', 'cash'] as $kpi) {
            $this->assertArrayHasKey($kpi, $data);
            if (isset($data[$kpi]['growth'])) {
                $this->assertIsNumeric($data[$kpi]['growth']);
                $this->assertFalse(is_infinite($data[$kpi]['growth']));
                $this->assertFalse(is_nan($data[$kpi]['growth']));
            }
            if (isset($data[$kpi]['margin'])) {
                $this->assertIsNumeric($data[$kpi]['margin']);
                $this->assertFalse(is_infinite($data[$kpi]['margin']));
                $this->assertFalse(is_nan($data[$kpi]['margin']));
            }
        }
    }

    public function test_dashboard_view_renders_without_division_by_zero(): void
    {
        // Create minimal data setup
        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Sales Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        // Test that the dashboard page loads without errors
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}");

        $response->assertStatus(200);

        // Should not contain any error messages about division by zero
        $response->assertDontSee('Division by zero');
        $response->assertDontSee('DivisionByZeroError');
    }

    /** @test */
    public function it_provides_monthly_profitability_data_for_charts()
    {
        $widget = new \App\Filament\Widgets\AdvancedFinancialWidget();
        $widget->selectedPeriod = 'month';

        // Mock the tenant context
        Filament::setTenant($this->client);

        $data = $widget->getViewData();

        // Verify monthly profitability data exists
        $this->assertArrayHasKey('profitability_monthly', $data);
        $this->assertIsArray($data['profitability_monthly']);

        // Should have 6 months of data
        $this->assertCount(6, $data['profitability_monthly']);

        // Each month should have required fields
        foreach ($data['profitability_monthly'] as $monthData) {
            $this->assertArrayHasKey('month', $monthData);
            $this->assertArrayHasKey('revenue', $monthData);
            $this->assertArrayHasKey('expenses', $monthData);
            $this->assertArrayHasKey('profit', $monthData);
            $this->assertArrayHasKey('margin', $monthData);

            // Values should be numeric
            $this->assertIsNumeric($monthData['revenue']);
            $this->assertIsNumeric($monthData['expenses']);
            $this->assertIsNumeric($monthData['profit']);
            $this->assertIsNumeric($monthData['margin']);
        }
    }
}
