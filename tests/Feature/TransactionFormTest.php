<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\Customer;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Transaction;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionFormTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;
    protected $customer;
    protected $product;
    protected $category;
    protected $unit;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);

        // Create supporting data
        $this->category = ProductCategory::factory()->create(['client_id' => $this->client->id]);
        $this->unit = UnitOfMeasure::factory()->create(['client_id' => $this->client->id]);
        $this->customer = Customer::factory()->create(['client_id' => $this->client->id]);
        $this->product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'selling_price' => 100000,
        ]);
    }

    public function test_transaction_form_structure_is_clear(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check that the form has clear sections
        $response->assertSee('Informasi Dasar');
        $response->assertSee('Jenis Transaksi');
        $response->assertSee('Tanggal Transaksi');
        $response->assertSee('Nomor Referensi');
        $response->assertSee('Deskripsi');
    }

    public function test_sales_transaction_shows_appropriate_sections(): void
    {
        $this->actingAs($this->user);

        // Create a sales transaction to test visibility
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'customer_id' => $this->customer->id,
        ]);

        $response = $this->get("/admin/transactions/{$transaction->id}/edit");

        $response->assertStatus(200);
        
        // Sales transactions should show customer, product, and payment sections
        $response->assertSee('Informasi Pelanggan');
        $response->assertSee('Detail Produk');
        $response->assertSee('Detail Pembayaran');
        
        // Should not show simple transaction detail section
        $response->assertDontSee('Detail Transaksi');
    }

    public function test_non_sales_transaction_shows_simple_form(): void
    {
        $this->actingAs($this->user);

        // Create a non-sales transaction
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'expense',
        ]);

        $response = $this->get("/admin/transactions/{$transaction->id}/edit");

        $response->assertStatus(200);
        
        // Non-sales transactions should show simple detail section
        $response->assertSee('Detail Transaksi');
        $response->assertSee('Jumlah Transaksi');
        
        // Should not show sales-specific sections
        $response->assertDontSee('Informasi Pelanggan');
        $response->assertDontSee('Detail Produk');
        $response->assertDontSee('Detail Pembayaran');
    }

    public function test_form_has_helpful_descriptions(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check for helpful descriptions
        $response->assertSee('Informasi umum transaksi');
        $response->assertSee('Pilih jenis transaksi yang akan dibuat');
        $response->assertSee('Nomor invoice, kwitansi, atau referensi lainnya');
        $response->assertSee('Jelaskan tujuan atau detail transaksi');
    }

    public function test_form_fields_have_proper_labels(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check for Indonesian labels
        $response->assertSee('Jenis Transaksi');
        $response->assertSee('Tanggal Transaksi');
        $response->assertSee('Nomor Referensi');
        $response->assertSee('Deskripsi');
        $response->assertSee('Status');
    }

    public function test_sales_form_has_proper_product_fields(): void
    {
        $this->actingAs($this->user);

        // Create a sales transaction with items
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'customer_id' => $this->customer->id,
        ]);

        $response = $this->get("/admin/transactions/{$transaction->id}/edit");

        $response->assertStatus(200);
        
        // Check for product-related fields
        $response->assertSee('Daftar produk yang dijual');
        $response->assertSee('Item Produk');
        $response->assertSee('Tambah Produk');
    }

    public function test_payment_section_has_clear_fields(): void
    {
        $this->actingAs($this->user);

        // Create a sales transaction
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'customer_id' => $this->customer->id,
        ]);

        $response = $this->get("/admin/transactions/{$transaction->id}/edit");

        $response->assertStatus(200);
        
        // Check for payment fields with clear labels
        $response->assertSee('Informasi pembayaran dan total transaksi');
        $response->assertSee('Subtotal');
        $response->assertSee('Diskon');
        $response->assertSee('Pajak (PPN)');
        $response->assertSee('Total Akhir');
        $response->assertSee('Metode Pembayaran');
        $response->assertSee('Status Pembayaran');
        $response->assertSee('Catatan Pembayaran');
    }

    public function test_form_has_helpful_helper_texts(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check for helpful helper texts
        $response->assertSee('Pilih jenis transaksi yang akan dibuat');
        $response->assertSee('Nomor invoice, kwitansi, atau referensi lainnya');
        $response->assertSee('Jelaskan tujuan atau detail transaksi');
    }

    public function test_sales_form_shows_customer_section(): void
    {
        $this->actingAs($this->user);

        // Create a sales transaction
        $transaction = Transaction::factory()->create([
            'client_id' => $this->client->id,
            'type' => 'sales',
            'customer_id' => $this->customer->id,
        ]);

        $response = $this->get("/admin/transactions/{$transaction->id}/edit");

        $response->assertStatus(200);
        
        // Check customer section
        $response->assertSee('Informasi Pelanggan');
        $response->assertSee('Data pelanggan untuk transaksi penjualan');
        $response->assertSee('Pilih pelanggan untuk transaksi penjualan');
    }

    public function test_form_sections_are_properly_organized(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check that sections are in logical order
        $content = $response->getContent();
        
        $informasiDasarPos = strpos($content, 'Informasi Dasar');
        $detailTransaksiPos = strpos($content, 'Detail Transaksi');
        
        // Informasi Dasar should come before Detail Transaksi
        $this->assertLessThan($detailTransaksiPos, $informasiDasarPos);
    }

    public function test_form_validation_messages_are_clear(): void
    {
        $this->actingAs($this->user);

        // Try to create transaction without required fields
        $response = $this->post('/admin/transactions', []);

        // Should redirect back with validation errors
        $response->assertSessionHasErrors();
        
        // Check that required fields are validated
        $this->assertSessionHasErrorsIn('default', ['type', 'transaction_date', 'description']);
    }

    public function test_transaction_types_are_properly_displayed(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check that transaction types are available
        $transactionTypes = Transaction::getTransactionTypes();
        
        foreach ($transactionTypes as $key => $label) {
            $response->assertSee($label);
        }
    }

    public function test_form_layout_is_responsive(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check that form uses proper column layouts
        $response->assertSee('columns(2)'); // Should have 2-column layouts
    }

    public function test_form_has_proper_field_types(): void
    {
        $this->actingAs($this->user);

        $response = $this->get('/admin/transactions/create');

        $response->assertStatus(200);
        
        // Check for proper input types
        $response->assertSee('type="date"'); // Date picker
        $response->assertSee('type="number"'); // Numeric inputs
        $response->assertSee('textarea'); // Text areas
    }
}
