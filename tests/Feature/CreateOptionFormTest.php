<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class CreateOptionFormTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_create_option_using_returns_correct_id_for_category(): void
    {
        $this->actingAs($this->user);

        // Simulate the createOptionUsing closure for category
        $createCategoryOption = function (array $data) {
            $data['client_id'] = Auth::user()?->client_id;
            $data['is_active'] = true;
            $data['sort_order'] = 0;
            $category = \App\Models\ProductCategory::create($data);
            return $category->id;
        };

        $categoryData = [
            'category_code' => 'NEWCAT',
            'category_name' => 'New Category',
        ];

        $result = $createCategoryOption($categoryData);

        // Should return the ID of the created category
        $this->assertIsInt($result);
        $this->assertGreaterThan(0, $result);

        // Verify the category was created correctly
        $this->assertDatabaseHas('product_categories', [
            'id' => $result,
            'client_id' => $this->client->id,
            'category_code' => 'NEWCAT',
            'category_name' => 'New Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        // Verify we can retrieve the category by the returned ID
        $category = ProductCategory::find($result);
        $this->assertNotNull($category);
        $this->assertEquals('NEWCAT', $category->category_code);
        $this->assertEquals('New Category', $category->category_name);
    }

    public function test_create_option_using_returns_correct_id_for_unit(): void
    {
        $this->actingAs($this->user);

        // Simulate the createOptionUsing closure for unit
        $createUnitOption = function (array $data) {
            $data['client_id'] = Auth::user()?->client_id;
            $data['is_active'] = true;
            $data['conversion_factor'] = 1.0;
            $data['is_base_unit'] = false;
            $unit = \App\Models\UnitOfMeasure::create($data);
            return $unit->id;
        };

        $unitData = [
            'unit_code' => 'NEWUNIT',
            'unit_name' => 'New Unit',
        ];

        $result = $createUnitOption($unitData);

        // Should return the ID of the created unit
        $this->assertIsInt($result);
        $this->assertGreaterThan(0, $result);

        // Verify the unit was created correctly
        $this->assertDatabaseHas('unit_of_measures', [
            'id' => $result,
            'client_id' => $this->client->id,
            'unit_code' => 'NEWUNIT',
            'unit_name' => 'New Unit',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Verify we can retrieve the unit by the returned ID
        $unit = UnitOfMeasure::find($result);
        $this->assertNotNull($unit);
        $this->assertEquals('NEWUNIT', $unit->unit_code);
        $this->assertEquals('New Unit', $unit->unit_name);
    }

    public function test_created_category_appears_in_options(): void
    {
        $this->actingAs($this->user);

        // Create a category using the same logic as createOptionUsing
        $data = [
            'category_code' => 'TESTCAT',
            'category_name' => 'Test Category',
            'client_id' => Auth::user()?->client_id,
            'is_active' => true,
            'sort_order' => 0,
        ];

        $category = ProductCategory::create($data);

        // Test that it appears in the options query used by the form
        $options = ProductCategory::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('category_name', 'id');

        $this->assertTrue($options->has($category->id));
        $this->assertEquals('Test Category', $options[$category->id]);
    }

    public function test_created_unit_appears_in_options(): void
    {
        $this->actingAs($this->user);

        // Create a unit using the same logic as createOptionUsing
        $data = [
            'unit_code' => 'TESTUNIT',
            'unit_name' => 'Test Unit',
            'client_id' => Auth::user()?->client_id,
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ];

        $unit = UnitOfMeasure::create($data);

        // Test that it appears in the options query used by the form
        $options = UnitOfMeasure::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('unit_name', 'id');

        $this->assertTrue($options->has($unit->id));
        $this->assertEquals('Test Unit', $options[$unit->id]);
    }

    public function test_relationship_query_includes_created_options(): void
    {
        $this->actingAs($this->user);

        // Create category and unit
        $category = ProductCategory::create([
            'category_code' => 'RELCAT',
            'category_name' => 'Relationship Category',
            'client_id' => Auth::user()?->client_id,
            'is_active' => true,
            'sort_order' => 0,
        ]);

        $unit = UnitOfMeasure::create([
            'unit_code' => 'RELUNIT',
            'unit_name' => 'Relationship Unit',
            'client_id' => Auth::user()?->client_id,
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Test the relationship query used in the form
        $categoryQuery = ProductCategory::query()
            ->where('client_id', Auth::user()?->client_id)
            ->where('is_active', true);

        $unitQuery = UnitOfMeasure::query()
            ->where('client_id', Auth::user()?->client_id)
            ->where('is_active', true);

        $categories = $categoryQuery->get();
        $units = $unitQuery->get();

        $this->assertCount(1, $categories);
        $this->assertCount(1, $units);

        $this->assertEquals($category->id, $categories->first()->id);
        $this->assertEquals($unit->id, $units->first()->id);
    }

    public function test_create_option_with_duplicate_code_fails(): void
    {
        $this->actingAs($this->user);

        // Create first category
        ProductCategory::create([
            'category_code' => 'DUPLICATE',
            'category_name' => 'First Category',
            'client_id' => $this->client->id,
            'is_active' => true,
            'sort_order' => 0,
        ]);

        // Try to create another category with same code
        $this->expectException(\Illuminate\Database\QueryException::class);

        $createCategoryOption = function (array $data) {
            $data['client_id'] = Auth::user()?->client_id;
            $data['is_active'] = true;
            $data['sort_order'] = 0;
            $category = \App\Models\ProductCategory::create($data);
            return $category->id;
        };

        $categoryData = [
            'category_code' => 'DUPLICATE', // Same code
            'category_name' => 'Second Category',
        ];

        $createCategoryOption($categoryData);
    }

    public function test_create_option_scoped_to_correct_client(): void
    {
        $this->actingAs($this->user);

        // Create category for current client
        $createCategoryOption = function (array $data) {
            $data['client_id'] = Auth::user()?->client_id;
            $data['is_active'] = true;
            $data['sort_order'] = 0;
            $category = \App\Models\ProductCategory::create($data);
            return $category->id;
        };

        $categoryData = [
            'category_code' => 'SCOPED',
            'category_name' => 'Scoped Category',
        ];

        $categoryId = $createCategoryOption($categoryData);

        // Verify it belongs to the correct client
        $category = ProductCategory::find($categoryId);
        $this->assertEquals($this->client->id, $category->client_id);

        // Create another client and user
        $anotherClient = Client::factory()->create();
        $anotherUser = User::factory()->create(['client_id' => $anotherClient->id]);

        // Switch to the other user
        $this->actingAs($anotherUser);

        // The category should not appear in the other client's options
        $options = ProductCategory::where('client_id', Auth::user()->client_id)
            ->where('is_active', true)
            ->pluck('category_name', 'id');

        $this->assertFalse($options->has($categoryId));
    }
}
