<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\User;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class FinancialReportTest extends TestCase
{
    use RefreshDatabase;

    private Client $client;
    private User $user;
    private array $accounts = [];

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
        ]);
        
        $this->createTestAccounts();
        $this->createTestJournalEntries();
    }

    private function createTestAccounts(): void
    {
        $accountStructure = [
            ['1101', 'Kas', 'asset', 'debit'],
            ['1102', 'Bank', 'asset', 'debit'],
            ['1201', 'Piutang Usaha', 'asset', 'debit'],
            ['1301', 'Persediaan', 'asset', 'debit'],
            ['2101', 'Hutang Usaha', 'liability', 'credit'],
            ['3101', 'Modal', 'equity', 'credit'],
            ['4101', 'Penjualan', 'revenue', 'credit'],
            ['5101', 'HPP', 'expense', 'debit'],
            ['6101', 'Biaya Gaji', 'expense', 'debit'],
            ['6102', 'Biaya Listrik', 'expense', 'debit'],
        ];

        foreach ($accountStructure as $account) {
            $this->accounts[$account[0]] = Account::create([
                'client_id' => $this->client->id,
                'account_code' => $account[0],
                'account_name' => $account[1],
                'account_type' => $account[2],
                'normal_balance' => $account[3],
                'is_active' => true,
            ]);
        }
    }

    private function createTestJournalEntries(): void
    {
        $startDate = Carbon::now()->startOfMonth();
        
        // Opening balance
        $this->createJournal($startDate, [
            ['1101', ********, 0], // Kas
            ['1102', ********, 0], // Bank
            ['1301', 5000000, 0],  // Persediaan
            ['2101', 0, 5000000],  // Hutang
            ['3101', 0, ********], // Modal
        ]);

        // Sales transaction
        $this->createJournal($startDate->copy()->addDays(5), [
            ['1101', ********, 0], // Kas
            ['4101', 0, ********], // Penjualan
        ]);

        // COGS transaction
        $this->createJournal($startDate->copy()->addDays(5), [
            ['5101', 8000000, 0],  // HPP
            ['1301', 0, 8000000],  // Persediaan
        ]);

        // Expense transactions
        $this->createJournal($startDate->copy()->addDays(10), [
            ['6101', 3000000, 0],  // Biaya Gaji
            ['1101', 0, 3000000],  // Kas
        ]);

        $this->createJournal($startDate->copy()->addDays(15), [
            ['6102', 500000, 0],   // Biaya Listrik
            ['1101', 0, 500000],   // Kas
        ]);
    }

    private function createJournal(Carbon $date, array $entries): Journal
    {
        $totalAmount = collect($entries)->sum(function ($entry) {
            return max($entry[1], $entry[2]);
        });

        $journal = Journal::create([
            'client_id' => $this->client->id,
            'journal_number' => 'JE-' . $date->format('Ymd') . '-' . rand(100, 999),
            'journal_date' => $date,
            'description' => 'Test journal entry',
            'total_amount' => $totalAmount,
            'is_posted' => true,
            'posted_at' => $date,
            'created_by' => $this->user->id,
        ]);

        foreach ($entries as $entry) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $this->accounts[$entry[0]]->id,
                'debit' => $entry[1],
                'credit' => $entry[2],
                'description' => 'Test entry for ' . $this->accounts[$entry[0]]->account_name,
            ]);
        }

        return $journal;
    }

    public function test_financial_report_dashboard_loads(): void
    {
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard");

        $response->assertStatus(200);
        $response->assertSee('Laporan Keuangan');
        $response->assertSee($this->client->company_name);
    }

    public function test_profit_loss_report_calculation(): void
    {
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard?selectedReport=profit_loss");

        $response->assertStatus(200);
        
        // Check that revenue is displayed
        $response->assertSee('15,000,000'); // Revenue amount
        
        // Check that COGS is displayed
        $response->assertSee('8,000,000'); // COGS amount
        
        // Check that expenses are displayed
        $response->assertSee('3,000,000'); // Salary expense
        $response->assertSee('500,000');   // Electricity expense
        
        // Check profit calculation (Revenue - COGS - Expenses = 15M - 8M - 3.5M = 3.5M)
        $response->assertSee('3,500,000'); // Net profit
    }

    public function test_balance_sheet_report_calculation(): void
    {
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard?selectedReport=balance_sheet");

        $response->assertStatus(200);
        
        // Check assets
        $response->assertSee('AKTIVA');
        $response->assertSee('Aktiva Lancar');
        
        // Check liabilities
        $response->assertSee('PASSIVA');
        $response->assertSee('Kewajiban Lancar');
        
        // Check equity
        $response->assertSee('Modal');
    }

    public function test_trial_balance_report(): void
    {
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard?selectedReport=trial_balance");

        $response->assertStatus(200);
        
        $response->assertSee('NERACA SALDO');
        $response->assertSee('Kode Akun');
        $response->assertSee('Nama Akun');
        $response->assertSee('Debit');
        $response->assertSee('Kredit');
        
        // Check that accounts with balances are shown
        $response->assertSee('1101'); // Kas account code
        $response->assertSee('Kas');  // Kas account name
    }

    public function test_period_filtering_works(): void
    {
        // Create journal entry in previous month
        $previousMonth = Carbon::now()->subMonth();
        $this->createJournal($previousMonth, [
            ['1101', 1000000, 0],
            ['4101', 0, 1000000],
        ]);

        // Test current month filter
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard?selectedPeriod=current_month");

        $response->assertStatus(200);
        
        // Should not include previous month transaction
        $response->assertDontSee('16,000,000'); // Would be 15M + 1M if previous month included
    }

    public function test_custom_date_range_filtering(): void
    {
        $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');

        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard?selectedPeriod=custom&startDate={$startDate}&endDate={$endDate}");

        $response->assertStatus(200);
        $response->assertSee('Laporan Keuangan');
    }

    public function test_report_data_accuracy(): void
    {
        // Test the actual calculation logic
        $page = new \App\Filament\Pages\FinancialReportDashboard();
        $page->selectedPeriod = 'current_month';
        $page->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $page->endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        
        // Mock the tenant
        \Filament\Facades\Filament::shouldReceive('getTenant')
            ->andReturn($this->client);

        $reportData = $page->getFinancialReportData();

        // Verify profit & loss calculations
        $this->assertEquals(********, $reportData['profit_loss']['revenue']['total']);
        $this->assertEquals(8000000, $reportData['profit_loss']['cogs']['total']);
        $this->assertEquals(3500000, $reportData['profit_loss']['operating_expenses']['total']);
        $this->assertEquals(3500000, $reportData['profit_loss']['net_profit']);

        // Verify balance sheet calculations
        $this->assertGreaterThan(0, $reportData['balance_sheet']['assets']['total']);
        $this->assertGreaterThan(0, $reportData['balance_sheet']['liabilities']['total']);
        $this->assertGreaterThan(0, $reportData['balance_sheet']['equity']['total']);

        // Verify trial balance
        $this->assertGreaterThan(0, count($reportData['trial_balance']['accounts']));
        $this->assertEquals(
            $reportData['trial_balance']['total_debit'],
            $reportData['trial_balance']['total_credit']
        );
    }

    public function test_multi_tenant_isolation(): void
    {
        // Create another client with different data
        $client2 = Client::factory()->create();
        $user2 = User::factory()->create(['client_id' => $client2->id]);

        // User 1 should only see their client's data
        $response = $this->actingAs($this->user)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard");

        $response->assertStatus(200);
        $response->assertSee($this->client->company_name);

        // User 2 should not be able to access client 1's data
        $response = $this->actingAs($user2)
            ->get("/admin/client/{$this->client->id}/financial-report-dashboard");

        $response->assertStatus(403); // Or redirect, depending on your policy
    }

    public function test_report_handles_no_data_gracefully(): void
    {
        // Create a new client with no transactions
        $emptyClient = Client::factory()->create();
        $emptyUser = User::factory()->create(['client_id' => $emptyClient->id]);

        $response = $this->actingAs($emptyUser)
            ->get("/admin/client/{$emptyClient->id}/financial-report-dashboard");

        $response->assertStatus(200);
        $response->assertSee('Laporan Keuangan');
        // Should handle empty data without errors
    }
}
