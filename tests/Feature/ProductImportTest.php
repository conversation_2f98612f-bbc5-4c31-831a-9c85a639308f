<?php

namespace Tests\Feature;

use App\Imports\ProductImport;
use App\Models\Client;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

class ProductImportTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_can_import_product_successfully(): void
    {
        $this->actingAs($this->user);

        // Create CSV content
        $csvContent = "kode_produk,nama_produk,kategori,satuan,harga_jual,harga_pokok\n";
        $csvContent .= "LAPTOP001,Laptop Gaming,Elektronik,PCS,15000000,12000000\n";

        // Create temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Assert product was created
        $this->assertDatabaseHas('products', [
            'client_id' => $this->client->id,
            'product_code' => 'LAPTOP001',
            'product_name' => 'Laptop Gaming',
            'selling_price' => 15000000,
            'standard_cost' => 12000000,
        ]);

        // Assert category was created
        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->client->id,
            'category_name' => 'Elektronik',
        ]);

        // Assert unit was created
        $this->assertDatabaseHas('unit_of_measures', [
            'client_id' => $this->client->id,
            'unit_code' => 'PCS',
        ]);

        // Check import statistics
        $this->assertEquals(1, $import->getImportedCount());
        $this->assertEquals(0, $import->getUpdatedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_creates_category_if_not_exists(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "MOUSE001,Gaming Mouse,Aksesoris Komputer,PCS\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->client->id,
            'category_name' => 'Aksesoris Komputer',
        ]);

        // Check that category_code was generated
        $category = ProductCategory::where('client_id', $this->client->id)
            ->where('category_name', 'Aksesoris Komputer')
            ->first();

        $this->assertNotNull($category->category_code);
        $this->assertNotEmpty($category->category_code);
    }

    public function test_import_creates_unit_if_not_exists(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "CABLE001,USB Cable,Elektronik,METER\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $this->assertDatabaseHas('unit_of_measures', [
            'client_id' => $this->client->id,
            'unit_code' => 'METER',
            'unit_name' => 'METER',
        ]);
    }

    public function test_import_uses_existing_category_and_unit(): void
    {
        $this->actingAs($this->user);

        // Create existing category and unit
        $category = ProductCategory::factory()->create([
            'client_id' => $this->client->id,
            'category_name' => 'Existing Category',
        ]);

        $unit = UnitOfMeasure::factory()->create([
            'client_id' => $this->client->id,
            'unit_code' => 'PCS',
            'unit_name' => 'Pieces',
        ]);

        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "TEST001,Test Product,Existing Category,PCS\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Should use existing category and unit, not create new ones
        $this->assertEquals(1, ProductCategory::where('client_id', $this->client->id)
            ->where('category_name', 'Existing Category')
            ->count());

        $this->assertEquals(1, UnitOfMeasure::where('client_id', $this->client->id)
            ->where('unit_code', 'PCS')
            ->count());

        $product = Product::where('client_id', $this->client->id)
            ->where('product_code', 'TEST001')
            ->first();

        $this->assertEquals($category->id, $product->category_id);
        $this->assertEquals($unit->id, $product->unit_id);
    }

    public function test_import_updates_existing_product(): void
    {
        $this->actingAs($this->user);

        // Create existing product
        $category = ProductCategory::factory()->create(['client_id' => $this->client->id]);
        $unit = UnitOfMeasure::factory()->create(['client_id' => $this->client->id]);

        $existingProduct = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $category->id,
            'unit_id' => $unit->id,
            'product_code' => 'UPDATE001',
            'product_name' => 'Old Name',
            'selling_price' => 100000,
        ]);

        $csvContent = "kode_produk,nama_produk,kategori,satuan,harga_jual\n";
        $csvContent .= "UPDATE001,New Name,{$category->category_name},{$unit->unit_code},200000\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Should update existing product
        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(1, $import->getUpdatedCount());

        $existingProduct->refresh();
        $this->assertEquals('New Name', $existingProduct->product_name);
        $this->assertEquals(200000, $existingProduct->selling_price);
    }

    public function test_import_handles_missing_required_fields(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "TEST001, ,Elektronik,PCS\n"; // Missing product name (space)

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Laravel Excel filters out rows with empty required fields automatically
        // So we expect no processing to occur
        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_parses_numeric_values_correctly(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan,harga_jual,harga_pokok,berat,tarif_pajak\n";
        $csvContent .= "NUM001,Test Product,Elektronik,PCS,1500000,1200000,2.5,0.11\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $product = Product::where('client_id', $this->client->id)
            ->where('product_code', 'NUM001')
            ->first();

        $this->assertEquals(1500000, $product->selling_price);
        $this->assertEquals(1200000, $product->standard_cost);
        $this->assertEquals(2.5, $product->weight);
        $this->assertEquals(0.11, $product->tax_rate);
    }

    public function test_import_parses_boolean_values_correctly(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "BOOL001,Test Product,Elektronik,PCS\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $product = Product::where('client_id', $this->client->id)
            ->where('product_code', 'BOOL001')
            ->first();

        // Default values should be set
        $this->assertTrue($product->is_active);
        $this->assertTrue($product->is_saleable);
        $this->assertTrue($product->is_purchasable);
    }

    public function test_import_validates_product_type(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "TYPE001,Test Product,Elektronik,PCS\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $product = Product::where('client_id', $this->client->id)
            ->where('product_code', 'TYPE001')
            ->first();

        // Should default to 'finished_good'
        $this->assertEquals('finished_good', $product->product_type);
    }

    public function test_import_handles_all_optional_fields(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan,deskripsi,harga_pokok,harga_jual,harga_beli,stok_minimum,stok_maksimum,titik_reorder,barcode,merek,berat,dimensi,dapat_dimanufaktur\n";
        $csvContent .= "FULL001,Full Product,Elektronik,PCS,\"Deskripsi lengkap\",500000,750000,450000,5,50,10,1234567890,ASUS,2.5,\"30x20x5 cm\",ya\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $product = Product::where('client_id', $this->client->id)
            ->where('product_code', 'FULL001')
            ->first();

        $this->assertEquals('Deskripsi lengkap', $product->description);
        $this->assertEquals(500000, $product->standard_cost);
        $this->assertEquals(750000, $product->selling_price);
        $this->assertEquals(450000, $product->purchase_price);
        $this->assertEquals(5, $product->minimum_stock);
        $this->assertEquals(50, $product->maximum_stock);
        $this->assertEquals(10, $product->reorder_point);
        $this->assertEquals('1234567890', $product->barcode);
        $this->assertEquals('ASUS', $product->brand);
        $this->assertEquals(2.5, $product->weight);
        $this->assertEquals('30x20x5 cm', $product->dimensions);
        $this->assertTrue($product->is_manufactured);
    }

    public function test_import_parses_manufactured_field_correctly(): void
    {
        $this->actingAs($this->user);

        $csvContent = "kode_produk,nama_produk,kategori,satuan,dapat_dimanufaktur\n";
        $csvContent .= "MFG001,Produk Manufaktur,Elektronik,PCS,ya\n";
        $csvContent .= "BUY001,Produk Beli Jadi,Elektronik,PCS,tidak\n";
        $csvContent .= "DEF001,Produk Default,Elektronik,PCS,\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $mfgProduct = Product::where('client_id', $this->client->id)
            ->where('product_code', 'MFG001')
            ->first();
        $buyProduct = Product::where('client_id', $this->client->id)
            ->where('product_code', 'BUY001')
            ->first();
        $defProduct = Product::where('client_id', $this->client->id)
            ->where('product_code', 'DEF001')
            ->first();

        $this->assertTrue($mfgProduct->is_manufactured);
        $this->assertFalse($buyProduct->is_manufactured);
        $this->assertFalse($defProduct->is_manufactured); // Default should be false
    }
}
