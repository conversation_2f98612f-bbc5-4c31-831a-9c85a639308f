<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Client;
use App\Services\BackupService;
use Filament\Facades\Filament;

class BackupManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test client
        $this->client = Client::factory()->create();

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Authenticate user first
        $this->actingAs($this->user);

        // Then set current tenant
        Filament::setTenant($this->client);
    }

    public function test_backup_service_exists(): void
    {
        $service = app(BackupService::class);
        $this->assertInstanceOf(BackupService::class, $service);
    }

    public function test_backup_service_can_list_backups(): void
    {
        $service = app(BackupService::class);
        $backups = $service->listBackups();

        $this->assertIsArray($backups);
    }

    public function test_backup_management_page_can_be_accessed(): void
    {
        $response = $this->get('/admin/client/' . $this->client->id . '/backup-management');
        $response->assertSuccessful();
    }

    public function test_backup_service_methods_exist(): void
    {
        $service = app(BackupService::class);

        $this->assertTrue(method_exists($service, 'createFullBackup'));
        $this->assertTrue(method_exists($service, 'listBackups'));
        $this->assertTrue(method_exists($service, 'downloadBackup'));
    }

    public function test_backup_directory_exists(): void
    {
        $backupPath = storage_path('app/backups');

        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }

        $this->assertTrue(is_dir($backupPath));
        $this->assertTrue(is_writable($backupPath));
    }

    public function test_backup_creation_data_structure(): void
    {
        // Test data structure that would be passed to createBackup method
        $testData = [
            'backup_type' => 'full',
            'backup_name' => 'test_backup',
            'description' => 'Test backup description',
        ];

        $this->assertArrayHasKey('backup_type', $testData);
        $this->assertEquals('full', $testData['backup_type']);
    }

    public function test_backup_creation_with_client_data(): void
    {
        // Test data structure for client-specific backup
        $testData = [
            'backup_type' => 'client',
            'client_id' => $this->client->id,
            'backup_name' => 'client_backup',
            'description' => 'Client specific backup',
        ];

        $this->assertArrayHasKey('backup_type', $testData);
        $this->assertArrayHasKey('client_id', $testData);
        $this->assertEquals('client', $testData['backup_type']);
        $this->assertEquals($this->client->id, $testData['client_id']);
    }

    public function test_backup_service_format_bytes_method(): void
    {
        $service = app(BackupService::class);

        // Use reflection to test protected method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('formatBytes');
        $method->setAccessible(true);

        $result = $method->invoke($service, 1024);
        $this->assertEquals('1024 B', $result);

        $result = $method->invoke($service, 1048576);
        $this->assertEquals('1024 KB', $result);
    }

    public function test_backup_page_has_correct_navigation(): void
    {
        $pageClass = \App\Filament\Pages\BackupManagement::class;

        $this->assertEquals('heroicon-o-shield-check', $pageClass::getNavigationIcon());
        $this->assertEquals('Backup & Recovery', $pageClass::getNavigationLabel());
        $this->assertEquals('Sistem', $pageClass::getNavigationGroup());
    }

    public function test_backup_page_methods_exist(): void
    {
        $pageClass = \App\Filament\Pages\BackupManagement::class;

        $this->assertTrue(method_exists($pageClass, 'getBackups'));
        $this->assertTrue(method_exists($pageClass, 'createBackup'));
        $this->assertTrue(method_exists($pageClass, 'downloadBackup'));
        $this->assertTrue(method_exists($pageClass, 'deleteBackup'));
    }

    public function test_backup_page_can_get_backups(): void
    {
        $page = new \App\Filament\Pages\BackupManagement();
        $backups = $page->getBackups();

        $this->assertIsArray($backups);
    }

    public function test_storage_disk_configuration(): void
    {
        $disk = \Illuminate\Support\Facades\Storage::disk('local');

        $this->assertNotNull($disk);

        // Test that we can create a test file
        $testContent = 'test backup content';
        $testFile = 'backups/test_file.txt';

        $disk->put($testFile, $testContent);
        $this->assertTrue($disk->exists($testFile));

        // Clean up
        $disk->delete($testFile);
        $this->assertFalse($disk->exists($testFile));
    }
}
