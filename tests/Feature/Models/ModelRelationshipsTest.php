<?php

namespace Tests\Feature\Models;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Client;
use App\Models\User;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Transaction;
use Illuminate\Database\QueryException;

class ModelRelationshipsTest extends TestCase
{
    use RefreshDatabase;

    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = Client::factory()->create();
    }

    // Client Relationships Tests
    public function test_client_has_many_users(): void
    {
        $user1 = User::factory()->create(['client_id' => $this->client->id]);
        $user2 = User::factory()->create(['client_id' => $this->client->id]);

        $this->assertCount(2, $this->client->users);
        $this->assertTrue($this->client->users->contains($user1));
        $this->assertTrue($this->client->users->contains($user2));
    }

    public function test_client_has_many_accounts(): void
    {
        $account1 = Account::factory()->create(['client_id' => $this->client->id]);
        $account2 = Account::factory()->create(['client_id' => $this->client->id]);

        $this->assertCount(2, $this->client->accounts);
        $this->assertTrue($this->client->accounts->contains($account1));
        $this->assertTrue($this->client->accounts->contains($account2));
    }

    public function test_client_has_many_journals(): void
    {
        $journal1 = Journal::factory()->create(['client_id' => $this->client->id]);
        $journal2 = Journal::factory()->create(['client_id' => $this->client->id]);

        $this->assertCount(2, $this->client->journals);
        $this->assertTrue($this->client->journals->contains($journal1));
        $this->assertTrue($this->client->journals->contains($journal2));
    }

    // User Relationships Tests
    public function test_user_belongs_to_client(): void
    {
        $user = User::factory()->create(['client_id' => $this->client->id]);

        $this->assertInstanceOf(Client::class, $user->client);
        $this->assertEquals($this->client->id, $user->client->id);
    }

    // Account Relationships Tests
    public function test_account_belongs_to_client(): void
    {
        $account = Account::factory()->create(['client_id' => $this->client->id]);

        $this->assertInstanceOf(Client::class, $account->client);
        $this->assertEquals($this->client->id, $account->client->id);
    }

    public function test_account_has_many_journal_entries(): void
    {
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        
        $entry1 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry 1',
        ]);

        $entry2 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 0,
            'credit' => 50000,
            'description' => 'Test entry 2',
        ]);

        $this->assertCount(2, $account->journalEntries);
        $this->assertTrue($account->journalEntries->contains($entry1));
        $this->assertTrue($account->journalEntries->contains($entry2));
    }

    public function test_account_hierarchy_relationships(): void
    {
        $parentAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1100',
            'account_name' => 'Current Assets',
        ]);

        $childAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'parent_account_id' => $parentAccount->id,
        ]);

        // Test parent relationship
        $this->assertInstanceOf(Account::class, $childAccount->parentAccount);
        $this->assertEquals($parentAccount->id, $childAccount->parentAccount->id);

        // Test children relationship
        $this->assertCount(1, $parentAccount->childAccounts);
        $this->assertTrue($parentAccount->childAccounts->contains($childAccount));
    }

    // Journal Relationships Tests
    public function test_journal_belongs_to_client(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);

        $this->assertInstanceOf(Client::class, $journal->client);
        $this->assertEquals($this->client->id, $journal->client->id);
    }

    public function test_journal_has_many_journal_entries(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        
        $entry1 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry 1',
        ]);

        $entry2 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 0,
            'credit' => 100000,
            'description' => 'Test entry 2',
        ]);

        $this->assertCount(2, $journal->journalEntries);
        $this->assertTrue($journal->journalEntries->contains($entry1));
        $this->assertTrue($journal->journalEntries->contains($entry2));
    }

    public function test_journal_entries_alias_relationship(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        // Test that entries() alias works
        $this->assertCount(1, $journal->entries);
        $this->assertEquals($journal->journalEntries->count(), $journal->entries->count());
    }

    // JournalEntry Relationships Tests
    public function test_journal_entry_belongs_to_journal(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        
        $entry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $this->assertInstanceOf(Journal::class, $entry->journal);
        $this->assertEquals($journal->id, $entry->journal->id);
    }

    public function test_journal_entry_belongs_to_account(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        
        $entry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $this->assertInstanceOf(Account::class, $entry->account);
        $this->assertEquals($account->id, $entry->account->id);
    }

    // Customer Relationships Tests
    public function test_customer_belongs_to_client(): void
    {
        $customer = Customer::factory()->create(['client_id' => $this->client->id]);

        $this->assertInstanceOf(Client::class, $customer->client);
        $this->assertEquals($this->client->id, $customer->client->id);
    }

    // Supplier Relationships Tests
    public function test_supplier_belongs_to_client(): void
    {
        $supplier = Supplier::factory()->create(['client_id' => $this->client->id]);

        $this->assertInstanceOf(Client::class, $supplier->client);
        $this->assertEquals($this->client->id, $supplier->client->id);
    }

    // Foreign Key Constraint Tests
    public function test_journal_entry_requires_valid_journal_id(): void
    {
        $account = Account::factory()->create(['client_id' => $this->client->id]);

        $this->expectException(QueryException::class);
        
        JournalEntry::create([
            'journal_id' => 99999, // Non-existent journal ID
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);
    }

    public function test_journal_entry_requires_valid_account_id(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);

        $this->expectException(QueryException::class);
        
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => 99999, // Non-existent account ID
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);
    }

    public function test_account_requires_valid_client_id(): void
    {
        $this->expectException(QueryException::class);
        
        Account::create([
            'client_id' => 99999, // Non-existent client ID
            'account_code' => '1101',
            'account_name' => 'Test Account',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);
    }

    // Cascade Delete Tests
    public function test_deleting_client_cascades_to_related_models(): void
    {
        $user = User::factory()->create(['client_id' => $this->client->id]);
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $customer = Customer::factory()->create(['client_id' => $this->client->id]);

        $this->client->delete();

        // Check that related models are also deleted
        $this->assertDatabaseMissing('users', ['id' => $user->id]);
        $this->assertDatabaseMissing('accounts', ['id' => $account->id]);
        $this->assertDatabaseMissing('journals', ['id' => $journal->id]);
        $this->assertDatabaseMissing('customers', ['id' => $customer->id]);
    }

    public function test_deleting_journal_cascades_to_journal_entries(): void
    {
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        
        $entry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $journal->delete();

        // Journal entry should also be deleted
        $this->assertDatabaseMissing('journal_entries', ['id' => $entry->id]);
    }

    // Soft Delete Tests
    public function test_models_support_soft_deletes(): void
    {
        $account = Account::factory()->create(['client_id' => $this->client->id]);
        $journal = Journal::factory()->create(['client_id' => $this->client->id]);
        $customer = Customer::factory()->create(['client_id' => $this->client->id]);

        // Soft delete the models
        $account->delete();
        $journal->delete();
        $customer->delete();

        // Models should be soft deleted (not physically removed)
        $this->assertSoftDeleted($account);
        $this->assertSoftDeleted($journal);
        $this->assertSoftDeleted($customer);

        // Models can be restored
        $account->restore();
        $journal->restore();
        $customer->restore();

        $this->assertDatabaseHas('accounts', ['id' => $account->id, 'deleted_at' => null]);
        $this->assertDatabaseHas('journals', ['id' => $journal->id, 'deleted_at' => null]);
        $this->assertDatabaseHas('customers', ['id' => $customer->id, 'deleted_at' => null]);
    }
}
