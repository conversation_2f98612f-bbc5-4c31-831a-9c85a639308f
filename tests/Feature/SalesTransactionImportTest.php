<?php

namespace Tests\Feature;

use App\Imports\SalesTransactionImport;
use App\Models\Client;
use App\Models\Customer;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

class SalesTransactionImportTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;
    protected $category;
    protected $unit;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
        $this->category = ProductCategory::factory()->create(['client_id' => $this->client->id]);
        $this->unit = UnitOfMeasure::factory()->create([
            'client_id' => $this->client->id,
            'unit_code' => 'PCS',
            'unit_name' => 'Pieces'
        ]);
        $this->product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'LAPTOP001',
            'product_name' => 'Laptop Gaming',
            'selling_price' => 15000000,
            'standard_cost' => 12000000,
        ]);
    }

    public function test_can_import_sales_transaction_successfully(): void
    {
        $this->actingAs($this->user);

        // Create CSV content
        $csvContent = "tanggal_transaksi,nama_pelanggan,kode_produk,jumlah,harga_satuan\n";
        $csvContent .= "2024-01-15,PT. Maju Jaya,LAPTOP001,2,15000000\n";

        // Create temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        // Assert transaction was created
        $this->assertDatabaseHas('transactions', [
            'client_id' => $this->client->id,
            'type' => 'sales',
            'status' => 'completed',
        ]);

        // Assert customer was created
        $this->assertDatabaseHas('customers', [
            'client_id' => $this->client->id,
            'customer_name' => 'PT. Maju Jaya',
        ]);

        // Assert transaction item was created
        $this->assertDatabaseHas('transaction_items', [
            'client_id' => $this->client->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
            'unit_price' => 15000000,
        ]);

        // Check import statistics
        $this->assertEquals(1, $import->getImportedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_creates_customer_if_not_exists(): void
    {
        $this->actingAs($this->user);

        $csvContent = "tanggal_transaksi,nama_pelanggan,email_pelanggan,kode_produk,jumlah\n";
        $csvContent .= "2024-01-15,New Customer,<EMAIL>,LAPTOP001,1\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        $this->assertDatabaseHas('customers', [
            'client_id' => $this->client->id,
            'customer_name' => 'New Customer',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_import_uses_existing_customer(): void
    {
        $this->actingAs($this->user);

        // Create existing customer
        $customer = Customer::factory()->create([
            'client_id' => $this->client->id,
            'customer_name' => 'Existing Customer',
        ]);

        $csvContent = "tanggal_transaksi,nama_pelanggan,kode_produk,jumlah\n";
        $csvContent .= "2024-01-15,Existing Customer,LAPTOP001,1\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        // Should use existing customer, not create new one
        $this->assertEquals(1, Customer::where('client_id', $this->client->id)
            ->where('customer_name', 'Existing Customer')
            ->count());

        $transaction = Transaction::where('client_id', $this->client->id)->first();
        $this->assertEquals($customer->id, $transaction->customer_id);
    }

    public function test_import_handles_invalid_product_code(): void
    {
        $this->actingAs($this->user);

        $csvContent = "tanggal_transaksi,nama_pelanggan,kode_produk,jumlah\n";
        $csvContent .= "2024-01-15,Test Customer,INVALID_CODE,1\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(1, $import->getSkippedCount());
        $this->assertNotEmpty($import->getErrors());
        $this->assertStringContainsString('tidak ditemukan', $import->getErrors()[0]);
    }

    public function test_import_handles_invalid_date_format(): void
    {
        $this->actingAs($this->user);

        $csvContent = "tanggal_transaksi,nama_pelanggan,kode_produk,jumlah\n";
        $csvContent .= "invalid-date,Test Customer,LAPTOP001,1\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(1, $import->getSkippedCount());
        $this->assertNotEmpty($import->getErrors());
    }

    public function test_import_handles_missing_required_fields(): void
    {
        $this->actingAs($this->user);

        $csvContent = "tanggal_transaksi,nama_pelanggan,kode_produk,jumlah\n";
        $csvContent .= "2024-01-15,Test Customer, ,1\n"; // Missing product code (space)

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        // Laravel Excel filters out rows with empty required fields automatically
        // So we expect no processing to occur
        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_calculates_transaction_totals_correctly(): void
    {
        $this->actingAs($this->user);

        $csvContent = "tanggal_transaksi,nama_pelanggan,kode_produk,jumlah,harga_satuan,diskon\n";
        $csvContent .= "2024-01-15,Test Customer,LAPTOP001,2,15000000,1000000\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        $transaction = Transaction::where('client_id', $this->client->id)->first();

        // Line total: 2 * 15000000 = 30000000
        // Discount: 1000000
        // Subtotal: 30000000 - 1000000 = 29000000
        // Tax (11%): 29000000 * 0.11 = 3190000
        // Total: 29000000 + 3190000 = 32190000

        $this->assertEquals(29000000, $transaction->subtotal);
        $this->assertEquals(3190000, $transaction->tax_amount);
        $this->assertEquals(32190000, $transaction->amount);
    }

    public function test_import_groups_items_by_transaction_number(): void
    {
        $this->actingAs($this->user);

        // Create another product
        Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $this->category->id,
            'unit_id' => $this->unit->id,
            'product_code' => 'MOUSE001',
            'product_name' => 'Gaming Mouse',
            'selling_price' => 500000,
        ]);

        $csvContent = "tanggal_transaksi,nomor_transaksi,nama_pelanggan,kode_produk,jumlah\n";
        $csvContent .= "2024-01-15,TXN-001,Test Customer,LAPTOP001,1\n";
        $csvContent .= "2024-01-15,TXN-001,Test Customer,MOUSE001,2\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new SalesTransactionImport();
        Excel::import($import, $file);

        // Should create only one transaction with two items
        $this->assertEquals(1, Transaction::where('client_id', $this->client->id)->count());
        $this->assertEquals(2, TransactionItem::where('client_id', $this->client->id)->count());

        $transaction = Transaction::where('client_id', $this->client->id)->first();
        $this->assertEquals('TXN-001', $transaction->reference_number);
        $this->assertEquals(2, $transaction->items->count());
    }
}
