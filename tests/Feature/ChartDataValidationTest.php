<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use Filament\Facades\Filament;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ChartDataValidationTest extends TestCase
{
    use RefreshDatabase;

    private Client $client;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->client = Client::factory()->create();

        $this->actingAs($this->user);
        Filament::setTenant($this->client);
    }

    /** @test */
    public function profitability_chart_uses_real_data_not_hardcoded()
    {
        // Create real financial data
        $this->createFinancialData();

        $widget = new \App\Filament\Widgets\AdvancedFinancialWidget();
        $widget->selectedPeriod = 'month';

        $data = $widget->getViewData();

        // Verify monthly profitability data exists and is not hardcoded
        $this->assertArrayHasKey('profitability_monthly', $data);
        $monthlyData = $data['profitability_monthly'];

        // Should have 6 months of data
        $this->assertCount(6, $monthlyData);

        // Check that data reflects our created entries
        $hasNonZeroData = false;
        foreach ($monthlyData as $month) {
            if ($month['revenue'] > 0 || $month['expenses'] > 0) {
                $hasNonZeroData = true;
                break;
            }
        }

        // If we created data, at least one month should have non-zero values
        $this->assertTrue($hasNonZeroData, 'Chart should use real data, not hardcoded zeros');
    }

    /** @test */
    public function chart_data_structure_is_correct()
    {
        $widget = new \App\Filament\Widgets\AdvancedFinancialWidget();
        $data = $widget->getViewData();

        // Verify all required chart data exists
        $this->assertArrayHasKey('profitability_monthly', $data);
        $this->assertArrayHasKey('cash_flow_forecast', $data);
        $this->assertArrayHasKey('expense_trends', $data);

        // Verify monthly profitability structure
        foreach ($data['profitability_monthly'] as $month) {
            $this->assertArrayHasKey('month', $month);
            $this->assertArrayHasKey('revenue', $month);
            $this->assertArrayHasKey('expenses', $month);
            $this->assertArrayHasKey('profit', $month);
            $this->assertArrayHasKey('margin', $month);

            // Verify data types
            $this->assertIsString($month['month']);
            $this->assertIsNumeric($month['revenue']);
            $this->assertIsNumeric($month['expenses']);
            $this->assertIsNumeric($month['profit']);
            $this->assertIsNumeric($month['margin']);
        }
    }

    /** @test */
    public function chart_handles_empty_data_gracefully()
    {
        // Test with no financial data
        $widget = new \App\Filament\Widgets\AdvancedFinancialWidget();
        $data = $widget->getViewData();

        // Should still provide structure even with no data
        $this->assertArrayHasKey('profitability_monthly', $data);
        $this->assertCount(6, $data['profitability_monthly']);

        // All values should be zero but structure intact
        foreach ($data['profitability_monthly'] as $month) {
            $this->assertEquals(0, $month['revenue']);
            $this->assertEquals(0, $month['expenses']);
            $this->assertEquals(0, $month['profit']);
            $this->assertEquals(0, $month['margin']);
        }
    }

    /** @test */
    public function operational_production_chart_uses_real_data()
    {
        $widget = new \App\Filament\Widgets\AdvancedOperationalWidget();
        $widget->selectedPeriod = 'month';

        $data = $widget->getViewData();

        // Verify production monthly data exists
        $this->assertArrayHasKey('production_monthly', $data);
        $this->assertIsArray($data['production_monthly']);

        // Should have 6 months of data
        $this->assertCount(6, $data['production_monthly']);

        // Each month should have required fields
        foreach ($data['production_monthly'] as $monthData) {
            $this->assertArrayHasKey('month', $monthData);
            $this->assertArrayHasKey('production_volume', $monthData);
            $this->assertArrayHasKey('production_cost', $monthData);

            // Values should be numeric
            $this->assertIsNumeric($monthData['production_volume']);
            $this->assertIsNumeric($monthData['production_cost']);
        }
    }

    /** @test */
    public function operational_performance_trends_use_real_data()
    {
        $widget = new \App\Filament\Widgets\AdvancedOperationalWidget();
        $data = $widget->getViewData();

        // Verify performance trends data structure
        $this->assertArrayHasKey('performance_trends', $data);
        $this->assertIsArray($data['performance_trends']);

        // Should have 6 months of data
        $this->assertCount(6, $data['performance_trends']);

        // Each month should have required fields
        foreach ($data['performance_trends'] as $trend) {
            $this->assertArrayHasKey('month', $trend);
            $this->assertArrayHasKey('production', $trend);
            $this->assertArrayHasKey('orders', $trend);
            $this->assertArrayHasKey('efficiency', $trend);
            $this->assertArrayHasKey('quality', $trend);

            // Values should be numeric
            $this->assertIsNumeric($trend['production']);
            $this->assertIsNumeric($trend['orders']);
            $this->assertIsNumeric($trend['efficiency']);
            $this->assertIsNumeric($trend['quality']);
        }
    }

    private function createFinancialData(): void
    {
        // Create accounts
        $revenueAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Sales Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        $expenseAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '6101',
            'account_name' => 'Operating Expenses',
            'account_type' => 'expense',
            'normal_balance' => 'debit',
        ]);

        $cashAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        // Create journal entries for current month
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Monthly Revenue',
        ]);

        // Revenue entry
        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $revenueAccount->id,
            'debit' => 0,
            'credit' => 1000000,
            'description' => 'Sales revenue',
        ]);

        // Cash entry
        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'debit' => 1000000,
            'credit' => 0,
            'description' => 'Cash received',
        ]);

        // Create expense entry
        $expenseJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Monthly Expenses',
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $expenseJournal->id,
            'account_id' => $expenseAccount->id,
            'debit' => 600000,
            'credit' => 0,
            'description' => 'Operating expenses',
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $expenseJournal->id,
            'account_id' => $cashAccount->id,
            'debit' => 0,
            'credit' => 600000,
            'description' => 'Cash paid',
        ]);
    }
}
