<?php

namespace Tests\Feature;

use App\Imports\ProductImport;
use App\Models\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

class ProductImportErrorHandlingTest extends TestCase
{
    use RefreshDatabase;

    public function test_import_fails_when_user_not_authenticated(): void
    {
        // Don't authenticate user
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('User must be authenticated to import products');

        $import = new ProductImport();
    }

    public function test_import_fails_when_user_has_no_client(): void
    {
        // Create user without client_id
        $user = User::factory()->create(['client_id' => null]);
        $this->actingAs($user);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('User must be associated with a client to import products');

        $import = new ProductImport();
    }

    public function test_import_works_with_valid_user_and_client(): void
    {
        // Create client and user
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);
        $this->actingAs($user);

        // Should not throw exception
        $import = new ProductImport();

        $this->assertInstanceOf(ProductImport::class, $import);
    }

    public function test_can_set_client_id_manually(): void
    {
        // Create client and user
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);
        $this->actingAs($user);

        $import = new ProductImport();

        // Test setter methods
        $newClientId = 999;
        $newUserId = 888;

        $result1 = $import->setClientId($newClientId);
        $result2 = $import->setUserId($newUserId);

        // Should return self for method chaining
        $this->assertSame($import, $result1);
        $this->assertSame($import, $result2);
    }

    public function test_import_with_invalid_csv_structure(): void
    {
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);
        $this->actingAs($user);

        // Create CSV with wrong headers
        $csvContent = "wrong_header,another_wrong_header\n";
        $csvContent .= "value1,value2\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Laravel Excel will not process rows with wrong headers
        // So no rows will be processed at all
        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_with_empty_file(): void
    {
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);
        $this->actingAs($user);

        // Create empty CSV
        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Should have no imports, no errors
        $this->assertEquals(0, $import->getImportedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_with_duplicate_product_codes_in_same_file(): void
    {
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);
        $this->actingAs($user);

        // Create CSV with duplicate product codes
        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "PROD001,Product 1,Category1,PCS\n";
        $csvContent .= "PROD001,Product 1 Updated,Category1,PCS\n"; // Same code

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // First should create, second should update
        $this->assertEquals(1, $import->getImportedCount());
        $this->assertEquals(1, $import->getUpdatedCount());
        $this->assertEquals(0, $import->getSkippedCount());

        // Check final product name is the updated one
        $this->assertDatabaseHas('products', [
            'client_id' => $client->id,
            'product_code' => 'PROD001',
            'product_name' => 'Product 1 Updated',
        ]);
    }

    public function test_import_rollback_on_database_error(): void
    {
        $client = Client::factory()->create();
        $user = User::factory()->create(['client_id' => $client->id]);
        $this->actingAs($user);

        // Create CSV with very long product name that exceeds database limit
        $csvContent = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent .= "PROD001," . str_repeat('A', 300) . ",Category1,PCS\n"; // 300 chars, exceeds limit

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();

        try {
            Excel::import($import, $file);
        } catch (\Exception) {
            // Expected to fail due to database constraint
        }

        // Should not have created any products due to rollback
        $this->assertDatabaseMissing('products', [
            'client_id' => $client->id,
            'product_code' => 'PROD001',
        ]);
    }
}
