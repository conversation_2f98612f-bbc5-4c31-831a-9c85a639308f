<?php

namespace Tests\Feature;

use App\Imports\ProductImport;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;
use Tests\Traits\PreservesUsers;

class ProductImportWithPreservedUsersTest extends TestCase
{
    use PreservesUsers;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Setup test environment with preserved users
        $this->setUpPreservesUsers();
    }

    public function test_can_import_product_with_preserved_users(): void
    {
        $this->actingAs($this->getTestUser());

        // Create CSV content
        $csvContent = "kode_produk,nama_produk,kategori,satuan,harga_jual,harga_pokok\n";
        $csvContent .= "LAPTOP001,Laptop Gaming,Elektronik,PCS,15000000,12000000\n";

        // Create temporary file
        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        // Assert product was created
        $this->assertDatabaseHas('products', [
            'client_id' => $this->getTestClient()->id,
            'product_code' => 'LAPTOP001',
            'product_name' => 'Laptop Gaming',
            'selling_price' => 15000000,
            'standard_cost' => 12000000,
        ]);

        // Assert category was created
        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->getTestClient()->id,
            'category_name' => 'Elektronik',
        ]);

        // Assert unit was created
        $this->assertDatabaseHas('unit_of_measures', [
            'client_id' => $this->getTestClient()->id,
            'unit_code' => 'PCS',
        ]);

        // Check import statistics
        $this->assertEquals(1, $import->getImportedCount());
        $this->assertEquals(0, $import->getUpdatedCount());
        $this->assertEquals(0, $import->getSkippedCount());
        $this->assertEmpty($import->getErrors());
    }

    public function test_import_with_manufactured_field(): void
    {
        $this->actingAs($this->getTestUser());

        $csvContent = "kode_produk,nama_produk,kategori,satuan,dapat_dimanufaktur\n";
        $csvContent .= "FURNITURE001,Meja Custom,Furniture,PCS,ya\n";

        Storage::fake('local');
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $import = new ProductImport();
        Excel::import($import, $file);

        $product = Product::where('client_id', $this->getTestClient()->id)
            ->where('product_code', 'FURNITURE001')
            ->first();

        $this->assertNotNull($product);
        $this->assertTrue($product->is_manufactured);
        $this->assertEquals('Meja Custom', $product->product_name);
    }

    public function test_users_are_preserved_across_tests(): void
    {
        // This test verifies that users created by SuperAdminSeeder are still available
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Super Admin',
        ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Demo User',
        ]);

        // Test user should also exist
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);
    }

    public function test_can_create_multiple_products_in_same_test(): void
    {
        $this->actingAs($this->getTestUser());

        // First import
        $csvContent1 = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent1 .= "PROD001,Product 1,Category1,PCS\n";

        Storage::fake('local');
        $file1 = UploadedFile::fake()->createWithContent('test1.csv', $csvContent1);

        $import1 = new ProductImport();
        Excel::import($import1, $file1);

        // Second import
        $csvContent2 = "kode_produk,nama_produk,kategori,satuan\n";
        $csvContent2 .= "PROD002,Product 2,Category2,PCS\n";

        $file2 = UploadedFile::fake()->createWithContent('test2.csv', $csvContent2);

        $import2 = new ProductImport();
        Excel::import($import2, $file2);

        // Both products should exist
        $this->assertDatabaseHas('products', [
            'client_id' => $this->getTestClient()->id,
            'product_code' => 'PROD001',
        ]);

        $this->assertDatabaseHas('products', [
            'client_id' => $this->getTestClient()->id,
            'product_code' => 'PROD002',
        ]);

        // Both categories should exist
        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->getTestClient()->id,
            'category_name' => 'Category1',
        ]);

        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->getTestClient()->id,
            'category_name' => 'Category2',
        ]);
    }
}
