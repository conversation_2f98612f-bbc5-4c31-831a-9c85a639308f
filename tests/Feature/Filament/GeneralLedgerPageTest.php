<?php

namespace Tests\Feature\Filament;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Filament\Pages\GeneralLedger;
use Filament\Facades\Filament;
use Livewire\Livewire;
use Carbon\Carbon;

class GeneralLedgerPageTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Client $client;
    protected Account $account;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test client
        $this->client = Client::factory()->create();

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Set current tenant and authenticate
        Filament::setTenant($this->client);
        $this->actingAs($this->user);

        // Create test account
        $this->account = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
        ]);
    }

    public function test_general_ledger_page_loads(): void
    {
        $response = $this->get('/admin/client/' . $this->client->id . '/general-ledger');
        $response->assertSuccessful();
    }

    public function test_general_ledger_form_has_required_fields(): void
    {
        $component = Livewire::test(GeneralLedger::class);
        
        $component->assertFormFieldExists('start_date')
            ->assertFormFieldExists('end_date')
            ->assertFormFieldExists('account_id');
    }

    public function test_general_ledger_default_date_range(): void
    {
        $component = Livewire::test(GeneralLedger::class);
        
        $expectedStartDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $expectedEndDate = Carbon::now()->endOfMonth()->format('Y-m-d');
        
        $this->assertEquals($expectedStartDate, $component->get('start_date'));
        $this->assertEquals($expectedEndDate, $component->get('end_date'));
    }

    public function test_general_ledger_displays_journal_entries(): void
    {
        // Create test journal with entries
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Test Journal',
            'is_posted' => true,
            'posted_at' => now(),
        ]);

        $journalEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        $data = $component->instance()->getGeneralLedgerData();
        
        $this->assertNotEmpty($data);
        $this->assertTrue($data->contains('id', $journalEntry->id));
    }

    public function test_general_ledger_filters_by_date_range(): void
    {
        // Create journals in different months
        $oldJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now()->subMonth(),
            'is_posted' => true,
        ]);

        $currentJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        JournalEntry::create([
            'journal_id' => $oldJournal->id,
            'account_id' => $this->account->id,
            'debit' => 50000,
            'credit' => 0,
            'description' => 'Old entry',
        ]);

        JournalEntry::create([
            'journal_id' => $currentJournal->id,
            'account_id' => $this->account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Current entry',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        // Set date range to current month
        $component->set('start_date', now()->startOfMonth()->format('Y-m-d'));
        $component->set('end_date', now()->endOfMonth()->format('Y-m-d'));
        
        $data = $component->instance()->getGeneralLedgerData();
        
        // Should only show current month entries
        $this->assertTrue($data->contains('journal_id', $currentJournal->id));
        $this->assertFalse($data->contains('journal_id', $oldJournal->id));
    }

    public function test_general_ledger_filters_by_account(): void
    {
        // Create another account
        $account2 = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1102',
            'account_name' => 'Bank',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        $entry1 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Cash entry',
        ]);

        $entry2 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account2->id,
            'debit' => 0,
            'credit' => 100000,
            'description' => 'Bank entry',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        // Filter by specific account
        $component->set('account_id', $this->account->id);
        
        $data = $component->instance()->getGeneralLedgerData();
        
        // Should only show entries for the selected account
        $this->assertTrue($data->contains('id', $entry1->id));
        $this->assertFalse($data->contains('id', $entry2->id));
    }

    public function test_general_ledger_only_shows_posted_journals(): void
    {
        $postedJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
            'posted_at' => now(),
        ]);

        $unpostedJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => false,
            'posted_at' => null,
        ]);

        JournalEntry::create([
            'journal_id' => $postedJournal->id,
            'account_id' => $this->account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Posted entry',
        ]);

        JournalEntry::create([
            'journal_id' => $unpostedJournal->id,
            'account_id' => $this->account->id,
            'debit' => 50000,
            'credit' => 0,
            'description' => 'Unposted entry',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        $data = $component->instance()->getGeneralLedgerData();
        
        // Should only show entries from posted journals
        $this->assertTrue($data->contains('journal_id', $postedJournal->id));
        $this->assertFalse($data->contains('journal_id', $unpostedJournal->id));
    }

    public function test_general_ledger_respects_multi_tenant_isolation(): void
    {
        // Create another client and journal
        $otherClient = Client::factory()->create();
        $otherAccount = Account::factory()->create([
            'client_id' => $otherClient->id,
            'account_code' => '1101',
            'account_name' => 'Other Cash',
        ]);

        $otherJournal = Journal::factory()->create([
            'client_id' => $otherClient->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        JournalEntry::create([
            'journal_id' => $otherJournal->id,
            'account_id' => $otherAccount->id,
            'debit' => 200000,
            'credit' => 0,
            'description' => 'Other client entry',
        ]);

        // Create entry for current client
        $myJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        JournalEntry::create([
            'journal_id' => $myJournal->id,
            'account_id' => $this->account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'My entry',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        $data = $component->instance()->getGeneralLedgerData();
        
        // Should only show entries for current client
        $this->assertTrue($data->contains('journal_id', $myJournal->id));
        $this->assertFalse($data->contains('journal_id', $otherJournal->id));
    }

    public function test_general_ledger_account_options_are_filtered_by_client(): void
    {
        // Create account for another client
        $otherClient = Client::factory()->create();
        $otherAccount = Account::factory()->create([
            'client_id' => $otherClient->id,
            'account_code' => '1102',
            'account_name' => 'Other Account',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        // Get form component and check account options
        $form = $component->instance()->form(\Filament\Forms\Form::make());
        $accountField = collect($form->getSchema())->first(fn($field) => $field->getName() === 'account_id');
        
        $this->assertNotNull($accountField);
        
        // Account options should only include accounts for current client
        $options = $accountField->getOptions();
        $this->assertArrayHasKey($this->account->id, $options);
        $this->assertArrayNotHasKey($otherAccount->id, $options);
    }

    public function test_general_ledger_data_includes_relationships(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $component = Livewire::test(GeneralLedger::class);
        
        $data = $component->instance()->getGeneralLedgerData();
        $entry = $data->first();
        
        // Should have journal and account relationships loaded
        $this->assertNotNull($entry->journal);
        $this->assertNotNull($entry->account);
        $this->assertEquals($journal->id, $entry->journal->id);
        $this->assertEquals($this->account->id, $entry->account->id);
    }
}
