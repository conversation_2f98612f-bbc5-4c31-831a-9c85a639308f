<?php

namespace Tests\Feature\Filament;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Filament\Pages\IncomeStatement;
use App\Filament\Pages\BalanceSheet;
use App\Filament\Pages\CashFlowStatement;
use Filament\Facades\Filament;
use Livewire\Livewire;
use Carbon\Carbon;

class FinancialReportsTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Client $client;
    protected Account $cashAccount;
    protected Account $revenueAccount;
    protected Account $expenseAccount;
    protected Account $liabilityAccount;
    protected Account $equityAccount;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test client
        $this->client = Client::factory()->create();

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Set current tenant and authenticate
        Filament::setTenant($this->client);
        $this->actingAs($this->user);

        // Create test accounts
        $this->createTestAccounts();
    }

    protected function createTestAccounts(): void
    {
        $this->cashAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        $this->revenueAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Sales Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        $this->expenseAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '6101',
            'account_name' => 'Office Expense',
            'account_type' => 'expense',
            'normal_balance' => 'debit',
        ]);

        $this->liabilityAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '2101',
            'account_name' => 'Accounts Payable',
            'account_type' => 'liability',
            'normal_balance' => 'credit',
        ]);

        $this->equityAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '3101',
            'account_name' => 'Owner Equity',
            'account_type' => 'equity',
            'normal_balance' => 'credit',
        ]);
    }

    protected function createTestJournalEntry(Account $debitAccount, Account $creditAccount, float $amount, Carbon $date = null): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => $date ?? now(),
            'description' => 'Test Journal',
            'is_posted' => true,
            'posted_at' => $date ?? now(),
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $debitAccount->id,
            'debit' => $amount,
            'credit' => 0,
            'description' => 'Test debit entry',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $creditAccount->id,
            'debit' => 0,
            'credit' => $amount,
            'description' => 'Test credit entry',
        ]);
    }

    // Income Statement Tests
    public function test_income_statement_page_loads(): void
    {
        $response = $this->get('/admin/client/' . $this->client->id . '/income-statement');
        $response->assertSuccessful();
    }

    public function test_income_statement_calculates_revenue_correctly(): void
    {
        // Create revenue transaction
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);

        $component = Livewire::test(IncomeStatement::class);
        $data = $component->instance()->getIncomeStatementData();

        $this->assertEquals(1000000, $data['revenue']);
    }

    public function test_income_statement_calculates_expenses_correctly(): void
    {
        // Create expense transaction
        $this->createTestJournalEntry($this->expenseAccount, $this->cashAccount, 500000);

        $component = Livewire::test(IncomeStatement::class);
        $data = $component->instance()->getIncomeStatementData();

        $this->assertEquals(500000, $data['expenses']);
    }

    public function test_income_statement_calculates_net_income(): void
    {
        // Create revenue and expense transactions
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);
        $this->createTestJournalEntry($this->expenseAccount, $this->cashAccount, 300000);

        $component = Livewire::test(IncomeStatement::class);
        $data = $component->instance()->getIncomeStatementData();

        $expectedNetIncome = 1000000 - 300000;
        $this->assertEquals($expectedNetIncome, $data['net_income']);
    }

    public function test_income_statement_filters_by_current_year(): void
    {
        // Create transaction for current year
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000, now());
        
        // Create transaction for previous year
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 500000, now()->subYear());

        $component = Livewire::test(IncomeStatement::class);
        $data = $component->instance()->getIncomeStatementData();

        // Should only include current year revenue
        $this->assertEquals(1000000, $data['revenue']);
    }

    // Balance Sheet Tests
    public function test_balance_sheet_page_loads(): void
    {
        $response = $this->get('/admin/client/' . $this->client->id . '/balance-sheet');
        $response->assertSuccessful();
    }

    public function test_balance_sheet_calculates_assets_correctly(): void
    {
        // Create asset transaction (increase cash)
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);

        $component = Livewire::test(BalanceSheet::class);
        $data = $component->instance()->getBalanceSheetData();

        $this->assertEquals(1000000, $data['assets']);
    }

    public function test_balance_sheet_calculates_liabilities_correctly(): void
    {
        // Create liability transaction
        $this->createTestJournalEntry($this->cashAccount, $this->liabilityAccount, 500000);

        $component = Livewire::test(BalanceSheet::class);
        $data = $component->instance()->getBalanceSheetData();

        $this->assertEquals(500000, $data['liabilities']);
    }

    public function test_balance_sheet_calculates_equity_correctly(): void
    {
        // Create equity transaction
        $this->createTestJournalEntry($this->cashAccount, $this->equityAccount, 800000);

        $component = Livewire::test(BalanceSheet::class);
        $data = $component->instance()->getBalanceSheetData();

        $this->assertEquals(800000, $data['equity']);
    }

    public function test_balance_sheet_equation_balances(): void
    {
        // Create balanced transactions
        $this->createTestJournalEntry($this->cashAccount, $this->equityAccount, 1000000); // Assets = Equity
        $this->createTestJournalEntry($this->cashAccount, $this->liabilityAccount, 500000); // Assets = Liabilities

        $component = Livewire::test(BalanceSheet::class);
        $data = $component->instance()->getBalanceSheetData();

        // Assets should equal Liabilities + Equity
        $this->assertEquals($data['assets'], $data['liabilities'] + $data['equity']);
    }

    // Cash Flow Statement Tests
    public function test_cash_flow_statement_page_loads(): void
    {
        $response = $this->get('/admin/client/' . $this->client->id . '/cash-flow-statement');
        $response->assertSuccessful();
    }

    public function test_cash_flow_statement_calculates_operating_cash_flow(): void
    {
        // Create operating cash flow transactions
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);
        $this->createTestJournalEntry($this->expenseAccount, $this->cashAccount, 300000);

        $component = Livewire::test(CashFlowStatement::class);
        $data = $component->instance()->getCashFlowData();

        // Operating cash flow should be positive (revenue - expenses)
        $this->assertGreaterThan(0, $data['operating_cash_flow']);
    }

    public function test_cash_flow_statement_calculates_cash_movements(): void
    {
        // Create cash movements
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);

        $component = Livewire::test(CashFlowStatement::class);
        $data = $component->instance()->getCashFlowData();

        $this->assertEquals(1000000, $data['cash_from_operations']);
    }

    // Multi-tenant isolation tests
    public function test_financial_reports_respect_multi_tenant_isolation(): void
    {
        // Create another client with transactions
        $otherClient = Client::factory()->create();
        $otherAccount = Account::factory()->create([
            'client_id' => $otherClient->id,
            'account_code' => '1101',
            'account_name' => 'Other Cash',
            'account_type' => 'asset',
        ]);

        $otherRevenueAccount = Account::factory()->create([
            'client_id' => $otherClient->id,
            'account_code' => '4101',
            'account_name' => 'Other Revenue',
            'account_type' => 'revenue',
        ]);

        // Create transaction for other client
        $otherJournal = Journal::factory()->create([
            'client_id' => $otherClient->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        JournalEntry::create([
            'journal_id' => $otherJournal->id,
            'account_id' => $otherAccount->id,
            'debit' => 2000000,
            'credit' => 0,
        ]);

        JournalEntry::create([
            'journal_id' => $otherJournal->id,
            'account_id' => $otherRevenueAccount->id,
            'debit' => 0,
            'credit' => 2000000,
        ]);

        // Create transaction for current client
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);

        // Test Income Statement isolation
        $incomeComponent = Livewire::test(IncomeStatement::class);
        $incomeData = $incomeComponent->instance()->getIncomeStatementData();
        
        // Should only show current client's revenue
        $this->assertEquals(1000000, $incomeData['revenue']);

        // Test Balance Sheet isolation
        $balanceComponent = Livewire::test(BalanceSheet::class);
        $balanceData = $balanceComponent->instance()->getBalanceSheetData();
        
        // Should only show current client's assets
        $this->assertEquals(1000000, $balanceData['assets']);
    }

    public function test_financial_reports_only_include_posted_journals(): void
    {
        // Create posted journal
        $this->createTestJournalEntry($this->cashAccount, $this->revenueAccount, 1000000);

        // Create unposted journal
        $unpostedJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => false,
        ]);

        JournalEntry::create([
            'journal_id' => $unpostedJournal->id,
            'account_id' => $this->cashAccount->id,
            'debit' => 500000,
            'credit' => 0,
        ]);

        JournalEntry::create([
            'journal_id' => $unpostedJournal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 500000,
        ]);

        $component = Livewire::test(IncomeStatement::class);
        $data = $component->instance()->getIncomeStatementData();

        // Should only include posted journal revenue
        $this->assertEquals(1000000, $data['revenue']);
    }
}
