<?php

namespace Tests\Feature\Filament;

use App\Filament\Resources\CustomerResource;
use App\Models\Customer;
use Livewire\Livewire;

class CustomerResourceTest extends BaseResourceTest
{
    protected string $resourceClass = CustomerResource::class;
    protected string $modelClass = Customer::class;

    protected function createTestRecord(array $attributes = []): Customer
    {
        return Customer::factory()->create(array_merge([
            'client_id' => $this->client->id,
            'customer_code' => 'CUST-001',
            'customer_name' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'address' => 'Test Address',
            'credit_limit' => 1000000,
            'is_active' => true,
        ], $attributes));
    }

    public function test_customer_table_displays_correct_columns(): void
    {
        $customer = $this->createTestRecord();

        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class)
            ->assertCanSeeTableRecords([$customer]);

        $component->assertSee($customer->customer_name)
            ->assertSee($customer->email)
            ->assertSee($customer->phone);
    }

    public function test_customer_form_validation(): void
    {
        $component = Livewire::test(CustomerResource\Pages\CreateCustomer::class);
        
        // Test required fields
        $component->fillForm([
            'customer_name' => '',
        ])->call('create')
            ->assertHasFormErrors(['customer_name']);
    }

    public function test_customer_email_validation(): void
    {
        $component = Livewire::test(CustomerResource\Pages\CreateCustomer::class);
        
        // Test invalid email format
        $component->fillForm([
            'customer_name' => 'Test Customer',
            'email' => 'invalid-email',
        ])->call('create')
            ->assertHasFormErrors(['email']);
    }

    public function test_customer_can_be_created_with_valid_data(): void
    {
        $component = Livewire::test(CustomerResource\Pages\CreateCustomer::class);
        
        $customerData = [
            'customer_name' => 'New Customer',
            'email' => '<EMAIL>',
            'phone' => '987654321',
            'address' => 'New Customer Address',
            'credit_limit' => 2000000,
            'is_active' => true,
        ];
        
        $component->fillForm($customerData)
            ->call('create')
            ->assertHasNoFormErrors();
            
        $this->assertDatabaseHas('customers', array_merge($customerData, [
            'client_id' => $this->client->id,
        ]));
    }

    public function test_customer_can_be_updated(): void
    {
        $customer = $this->createTestRecord();
        
        $component = Livewire::test(CustomerResource\Pages\EditCustomer::class, [
            'record' => $customer->id,
        ]);
        
        $newData = [
            'customer_name' => 'Updated Customer Name',
            'email' => '<EMAIL>',
            'credit_limit' => 3000000,
        ];
        
        $component->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();
            
        $customer->refresh();
        $this->assertEquals('Updated Customer Name', $customer->customer_name);
        $this->assertEquals('<EMAIL>', $customer->email);
        $this->assertEquals(3000000, $customer->credit_limit);
    }

    public function test_customer_search_functionality(): void
    {
        $customer1 = $this->createTestRecord([
            'customer_name' => 'ABC Company',
            'email' => '<EMAIL>',
        ]);
        
        $customer2 = $this->createTestRecord([
            'customer_code' => 'CUST-002',
            'customer_name' => 'XYZ Corporation',
            'email' => '<EMAIL>',
        ]);
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class);
        
        $component->searchTable('ABC')
            ->assertCanSeeTableRecords([$customer1])
            ->assertCanNotSeeTableRecords([$customer2]);
            
        $component->searchTable('<EMAIL>')
            ->assertCanSeeTableRecords([$customer2])
            ->assertCanNotSeeTableRecords([$customer1]);
    }

    public function test_customer_status_filter(): void
    {
        $activeCustomer = $this->createTestRecord(['is_active' => true]);
        $inactiveCustomer = $this->createTestRecord([
            'customer_code' => 'CUST-002',
            'is_active' => false,
        ]);
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class);
        
        $component->filterTable('is_active', true)
            ->assertCanSeeTableRecords([$activeCustomer])
            ->assertCanNotSeeTableRecords([$inactiveCustomer]);
    }

    public function test_customer_credit_limit_display(): void
    {
        $customer = $this->createTestRecord(['credit_limit' => 5000000]);
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class)
            ->assertCanSeeTableRecords([$customer]);
        
        // Should display credit limit in currency format
        $component->assertSee('Rp5,000,000.00');
    }

    public function test_customer_deletion(): void
    {
        $customer = $this->createTestRecord();
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class);
        
        $component->callTableAction('delete', $customer);
        
        $this->assertSoftDeleted($customer);
    }

    public function test_customer_bulk_deletion(): void
    {
        $customer1 = $this->createTestRecord();
        $customer2 = $this->createTestRecord(['customer_code' => 'CUST-002']);
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class);
        
        $component->callTableBulkAction('delete', [$customer1, $customer2]);
        
        $this->assertSoftDeleted($customer1);
        $this->assertSoftDeleted($customer2);
    }

    public function test_customer_sorting(): void
    {
        $customerA = $this->createTestRecord(['customer_name' => 'A Customer']);
        $customerB = $this->createTestRecord([
            'customer_code' => 'CUST-002',
            'customer_name' => 'B Customer',
        ]);
        $customerC = $this->createTestRecord([
            'customer_code' => 'CUST-003',
            'customer_name' => 'C Customer',
        ]);
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class);
        
        // Default sort should be by customer_name ascending
        $component->assertCanSeeTableRecords([$customerA, $customerB, $customerC], inOrder: true);
    }

    public function test_customer_phone_validation(): void
    {
        $component = Livewire::test(CustomerResource\Pages\CreateCustomer::class);
        
        $customerData = [
            'customer_name' => 'Test Customer',
            'phone' => '123-456-789', // Valid phone format
        ];
        
        $component->fillForm($customerData)
            ->call('create')
            ->assertHasNoFormErrors();
    }

    public function test_customer_address_field(): void
    {
        $customer = $this->createTestRecord([
            'address' => 'Jl. Test No. 123, Jakarta',
        ]);
        
        $this->assertEquals('Jl. Test No. 123, Jakarta', $customer->address);
    }

    public function test_customer_actions_are_available(): void
    {
        $customer = $this->createTestRecord();
        
        $component = Livewire::test(CustomerResource\Pages\ListCustomers::class);
        
        $component->assertTableActionExists('view', $customer)
            ->assertTableActionExists('edit', $customer)
            ->assertTableActionExists('delete', $customer);
    }

    public function test_customer_view_page_displays_information(): void
    {
        $customer = $this->createTestRecord();
        
        $component = Livewire::test(CustomerResource\Pages\ViewCustomer::class, [
            'record' => $customer->id,
        ]);
        
        $component->assertSee($customer->customer_name)
            ->assertSee($customer->email)
            ->assertSee($customer->phone)
            ->assertSee($customer->address);
    }
}
