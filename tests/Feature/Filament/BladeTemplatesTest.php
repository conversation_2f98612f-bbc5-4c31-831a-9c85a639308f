<?php

namespace Tests\Feature\Filament;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\View;

class BladeTemplatesTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test client
        $this->client = Client::factory()->create();

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Set current tenant and authenticate
        Filament::setTenant($this->client);
        $this->actingAs($this->user);
    }

    public function test_general_ledger_blade_template_renders(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        $journalEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $entries = collect([$journalEntry->load(['journal', 'account'])]);

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check that essential elements are present
        $this->assertStringContainsString('General Ledger', $html);
        $this->assertStringContainsString($account->account_name, $html);
        $this->assertStringContainsString('100,000.00', $html);
        $this->assertStringContainsString($journalEntry->description, $html);
    }

    public function test_general_ledger_template_handles_empty_data(): void
    {
        $entries = collect([]);

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        $this->assertStringContainsString('No journal entries found', $html);
    }

    public function test_general_ledger_template_calculates_running_balance(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'normal_balance' => 'debit',
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'is_posted' => true,
        ]);

        // Create multiple entries to test running balance
        $entry1 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'First entry',
        ]);

        $entry2 = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 50000,
            'credit' => 0,
            'description' => 'Second entry',
        ]);

        $entries = collect([$entry1, $entry2])->each(function ($entry) {
            $entry->load(['journal', 'account']);
        });

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => $account,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check that running balance is calculated correctly
        $this->assertStringContainsString('100,000.00', $html); // First balance
        $this->assertStringContainsString('150,000.00', $html); // Running balance after second entry
    }

    public function test_general_ledger_template_formats_currency_correctly(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => true,
        ]);

        $journalEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 1234567.89,
            'credit' => 0,
            'description' => 'Large amount entry',
        ]);

        $entries = collect([$journalEntry->load(['journal', 'account'])]);

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check currency formatting
        $this->assertStringContainsString('1,234,567.89', $html);
    }

    public function test_general_ledger_template_displays_account_information(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash in Bank',
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => true,
        ]);

        $journalEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $entries = collect([$journalEntry->load(['journal', 'account'])]);

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => $account,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        $this->assertStringContainsString('1101', $html);
        $this->assertStringContainsString('Cash in Bank', $html);
    }

    public function test_general_ledger_template_handles_date_formatting(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $specificDate = now()->setDate(2024, 6, 15);
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => $specificDate,
            'is_posted' => true,
        ]);

        $journalEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Test entry',
        ]);

        $entries = collect([$journalEntry->load(['journal', 'account'])]);

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check date formatting (should be in d/m/Y format)
        $this->assertStringContainsString('15/06/2024', $html);
    }

    public function test_general_ledger_template_shows_debit_credit_correctly(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => true,
        ]);

        $debitEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Debit entry',
        ]);

        $creditEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => 0,
            'credit' => 50000,
            'description' => 'Credit entry',
        ]);

        $entries = collect([$debitEntry, $creditEntry])->each(function ($entry) {
            $entry->load(['journal', 'account']);
        });

        $view = View::make('filament.pages.general-ledger', [
            'entries' => $entries,
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check that debit and credit amounts are displayed correctly
        $this->assertStringContainsString('100,000.00', $html); // Debit amount
        $this->assertStringContainsString('50,000.00', $html);  // Credit amount
        
        // Check that zero amounts show as dash
        $this->assertStringContainsString('-', $html);
    }

    public function test_general_ledger_template_responsive_design(): void
    {
        $view = View::make('filament.pages.general-ledger', [
            'entries' => collect([]),
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check for responsive classes
        $this->assertStringContainsString('overflow-x-auto', $html);
        $this->assertStringContainsString('min-w-full', $html);
    }

    public function test_general_ledger_template_accessibility(): void
    {
        $view = View::make('filament.pages.general-ledger', [
            'entries' => collect([]),
            'selectedAccount' => null,
            'startDate' => now()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->endOfMonth()->format('Y-m-d'),
        ]);

        $html = $view->render();

        // Check for accessibility features
        $this->assertStringContainsString('<table', $html);
        $this->assertStringContainsString('<thead', $html);
        $this->assertStringContainsString('<tbody', $html);
        $this->assertStringContainsString('<th', $html);
    }
}
