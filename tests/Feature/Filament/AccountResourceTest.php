<?php

namespace Tests\Feature\Filament;

use App\Filament\Resources\AccountResource;
use App\Models\Account;
use Livewire\Livewire;

class AccountResourceTest extends BaseResourceTest
{
    protected string $resourceClass = AccountResource::class;
    protected string $modelClass = Account::class;

    protected function createTestRecord(array $attributes = []): Account
    {
        return Account::factory()->create(array_merge([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Test Account',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
        ], $attributes));
    }

    public function test_account_table_displays_correct_columns(): void
    {
        $account = $this->createTestRecord();

        $component = Livewire::test(AccountResource\Pages\ListAccounts::class)
            ->assertCanSeeTableRecords([$account]);

        $component->assertSee($account->account_code)
            ->assertSee($account->account_name)
            ->assertSee($account->account_type);
    }

    public function test_account_form_validation(): void
    {
        $component = Livewire::test(AccountResource\Pages\CreateAccount::class);
        
        // Test required fields
        $component->fillForm([
            'account_code' => '',
            'account_name' => '',
            'account_type' => '',
            'normal_balance' => '',
        ])->call('create')
            ->assertHasFormErrors(['account_code', 'account_name', 'account_type', 'normal_balance']);
    }

    public function test_account_code_uniqueness(): void
    {
        $existingAccount = $this->createTestRecord(['account_code' => '1101']);
        
        $component = Livewire::test(AccountResource\Pages\CreateAccount::class);
        
        $component->fillForm([
            'account_code' => '1101', // Same as existing
            'account_name' => 'Another Account',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ])->call('create')
            ->assertHasFormErrors(['account_code']);
    }

    public function test_account_types_are_valid(): void
    {
        $validTypes = ['asset', 'liability', 'equity', 'revenue', 'expense'];
        
        foreach ($validTypes as $type) {
            $account = $this->createTestRecord([
                'account_code' => '110' . rand(1, 9),
                'account_type' => $type,
            ]);
            
            $this->assertEquals($type, $account->account_type);
        }
    }

    public function test_normal_balance_types_are_valid(): void
    {
        $validBalances = ['debit', 'credit'];
        
        foreach ($validBalances as $balance) {
            $account = $this->createTestRecord([
                'account_code' => '110' . rand(1, 9),
                'normal_balance' => $balance,
            ]);
            
            $this->assertEquals($balance, $account->normal_balance);
        }
    }

    public function test_account_hierarchy_functionality(): void
    {
        $parentAccount = $this->createTestRecord([
            'account_code' => '1100',
            'account_name' => 'Current Assets',
            'parent_account_id' => null,
        ]);
        
        $childAccount = $this->createTestRecord([
            'account_code' => '1101',
            'account_name' => 'Cash',
            'parent_account_id' => $parentAccount->id,
        ]);
        
        $this->assertEquals($parentAccount->id, $childAccount->parent_account_id);
        $this->assertInstanceOf(Account::class, $childAccount->parentAccount);
        $this->assertTrue($parentAccount->childAccounts->contains($childAccount));
    }

    public function test_account_filters_work(): void
    {
        $assetAccount = $this->createTestRecord([
            'account_code' => '1101',
            'account_type' => 'asset',
        ]);
        
        $liabilityAccount = $this->createTestRecord([
            'account_code' => '2101',
            'account_type' => 'liability',
        ]);
        
        $component = Livewire::test(AccountResource\Pages\ListAccounts::class);
        
        // Test account type filter
        $component->filterTable('account_type', 'asset')
            ->assertCanSeeTableRecords([$assetAccount])
            ->assertCanNotSeeTableRecords([$liabilityAccount]);
    }

    public function test_account_search_functionality(): void
    {
        $account1 = $this->createTestRecord([
            'account_code' => '1101',
            'account_name' => 'Cash in Bank',
        ]);
        
        $account2 = $this->createTestRecord([
            'account_code' => '2101',
            'account_name' => 'Accounts Payable',
        ]);
        
        $component = Livewire::test(AccountResource\Pages\ListAccounts::class);
        
        $component->searchTable('Cash')
            ->assertCanSeeTableRecords([$account1])
            ->assertCanNotSeeTableRecords([$account2]);
            
        $component->searchTable('1101')
            ->assertCanSeeTableRecords([$account1])
            ->assertCanNotSeeTableRecords([$account2]);
    }

    public function test_account_status_filter(): void
    {
        $activeAccount = $this->createTestRecord(['is_active' => true]);
        $inactiveAccount = $this->createTestRecord([
            'account_code' => '1102',
            'is_active' => false,
        ]);
        
        $component = Livewire::test(AccountResource\Pages\ListAccounts::class);
        
        $component->filterTable('is_active', true)
            ->assertCanSeeTableRecords([$activeAccount])
            ->assertCanNotSeeTableRecords([$inactiveAccount]);
    }

    public function test_account_balance_calculation(): void
    {
        $account = $this->createTestRecord();
        
        // Test initial balance
        $this->assertEquals(0, $account->getBalanceAttribute());
        
        // This would require journal entries to test properly
        // We'll test this in integration tests
    }

    public function test_account_can_be_created_with_valid_data(): void
    {
        $component = Livewire::test(AccountResource\Pages\CreateAccount::class);
        
        $accountData = [
            'account_code' => '1105',
            'account_name' => 'Petty Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'description' => 'Small cash fund for daily expenses',
            'is_active' => true,
        ];
        
        $component->fillForm($accountData)
            ->call('create')
            ->assertHasNoFormErrors();
            
        $this->assertDatabaseHas('accounts', array_merge($accountData, [
            'client_id' => $this->client->id,
        ]));
    }

    public function test_account_can_be_updated(): void
    {
        $account = $this->createTestRecord();
        
        $component = Livewire::test(AccountResource\Pages\EditAccount::class, [
            'record' => $account->id,
        ]);
        
        $newData = [
            'account_name' => 'Updated Account Name',
            'description' => 'Updated description',
        ];
        
        $component->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();
            
        $account->refresh();
        $this->assertEquals('Updated Account Name', $account->account_name);
        $this->assertEquals('Updated description', $account->description);
    }

    public function test_account_deletion_restrictions(): void
    {
        $account = $this->createTestRecord();
        
        // Test that account can be soft deleted
        $account->delete();
        $this->assertSoftDeleted($account);
        
        // Test that account can be restored
        $account->restore();
        $this->assertDatabaseHas('accounts', [
            'id' => $account->id,
            'deleted_at' => null,
        ]);
    }

    public function test_account_sorting(): void
    {
        $account1 = $this->createTestRecord(['account_code' => '1101']);
        $account2 = $this->createTestRecord(['account_code' => '1102']);
        $account3 = $this->createTestRecord(['account_code' => '1103']);
        
        $component = Livewire::test(AccountResource\Pages\ListAccounts::class);
        
        // Default sort should be by account_code ascending
        $component->assertCanSeeTableRecords([$account1, $account2, $account3], inOrder: true);
    }
}
