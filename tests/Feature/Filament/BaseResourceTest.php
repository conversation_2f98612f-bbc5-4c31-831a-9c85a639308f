<?php

namespace Tests\Feature\Filament;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\User;
use App\Models\Client;
use Filament\Facades\Filament;


abstract class BaseResourceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Client $client;
    protected string $resourceClass;
    protected string $modelClass;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test client
        $this->client = Client::factory()->create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'address' => 'Test Address',
        ]);

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Authenticate user first
        $this->actingAs($this->user);

        // Then set current tenant
        Filament::setTenant($this->client);
    }

    /**
     * Test that resource can be accessed
     */
    public function test_resource_can_be_accessed(): void
    {
        $this->assertNotNull($this->resourceClass);
        $this->assertTrue(class_exists($this->resourceClass));

        if (method_exists($this->resourceClass, 'canViewAny')) {
            $this->assertTrue($this->resourceClass::canViewAny());
        }
    }

    /**
     * Test resource index page loads
     */
    public function test_resource_index_page_loads(): void
    {
        $indexPage = $this->resourceClass::getPages()['index'] ?? null;
        $this->assertNotNull($indexPage, 'Index page not found for resource');

        $response = $this->get($indexPage->getUrl());
        $response->assertSuccessful();
    }

    /**
     * Test resource create page loads
     */
    public function test_resource_create_page_loads(): void
    {
        $createPage = $this->resourceClass::getPages()['create'] ?? null;

        if ($createPage) {
            $response = $this->get($createPage->getUrl());
            $response->assertSuccessful();
        } else {
            $this->markTestSkipped('Create page not available for this resource');
        }
    }

    /**
     * Test resource edit page loads with existing record
     */
    public function test_resource_edit_page_loads(): void
    {
        $editPage = $this->resourceClass::getPages()['edit'] ?? null;

        if ($editPage && $this->modelClass) {
            $record = $this->createTestRecord();

            $url = str_replace('{record}', $record->id, $editPage->getUrl());
            $response = $this->get($url);
            $response->assertSuccessful();
        } else {
            $this->markTestSkipped('Edit page or model class not available for this resource');
        }
    }

    /**
     * Test multi-tenant isolation
     */
    public function test_multi_tenant_isolation(): void
    {
        if (!$this->modelClass) {
            $this->markTestSkipped('Model class not defined for multi-tenant test');
            return;
        }

        // Create another client and user
        $otherClient = Client::factory()->create();
        $otherUser = User::factory()->create(['client_id' => $otherClient->id]);

        // Create records for both clients
        $myRecord = $this->createTestRecord();

        // Switch to other client
        Filament::setTenant($otherClient);
        $this->actingAs($otherUser);

        $otherRecord = $this->createTestRecord(['client_id' => $otherClient->id]);

        // Switch back to original client
        Filament::setTenant($this->client);
        $this->actingAs($this->user);

        // Test that we can only see our own records
        $query = $this->resourceClass::getEloquentQuery();
        $records = $query->get();

        $this->assertTrue($records->contains('id', $myRecord->id));
        $this->assertFalse($records->contains('id', $otherRecord->id));
    }

    /**
     * Test resource table columns are defined
     */
    public function test_resource_table_has_columns(): void
    {
        // Skip this test for now due to Filament complexity
        $this->markTestSkipped('Table column test requires complex Filament setup');
    }

    /**
     * Test resource form has fields
     */
    public function test_resource_form_has_fields(): void
    {
        // Skip this test for now due to Filament complexity
        $this->markTestSkipped('Form field test requires complex Filament setup');
    }

    /**
     * Create a test record for the resource
     * Override this method in child classes
     */
    protected function createTestRecord(array $attributes = [])
    {
        if (!$this->modelClass) {
            throw new \Exception('Model class not defined');
        }

        $defaultAttributes = ['client_id' => $this->client->id];

        if (method_exists($this->modelClass, 'factory')) {
            return $this->modelClass::factory()->create(array_merge($defaultAttributes, $attributes));
        }

        throw new \Exception('Factory not available for model: ' . $this->modelClass);
    }

    /**
     * Test that resource respects soft deletes if applicable
     */
    public function test_resource_respects_soft_deletes(): void
    {
        if (!$this->modelClass) {
            $this->markTestSkipped('Model class not defined');
            return;
        }

        $model = new $this->modelClass;

        if (!in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses_recursive($model))) {
            $this->markTestSkipped('Model does not use soft deletes');
            return;
        }

        $record = $this->createTestRecord();
        $record->delete();

        // Test that soft deleted records are not shown by default
        $query = $this->resourceClass::getEloquentQuery();
        $records = $query->get();

        $this->assertFalse($records->contains('id', $record->id));
    }

    /**
     * Test resource actions are defined
     */
    public function test_resource_has_actions(): void
    {
        // Skip this test for now due to Filament complexity
        $this->markTestSkipped('Actions test requires complex Filament setup');
    }

    /**
     * Test resource bulk actions are defined
     */
    public function test_resource_has_bulk_actions(): void
    {
        // Skip this test for now due to Filament complexity
        $this->markTestSkipped('Bulk actions test requires complex Filament setup');
    }

    /**
     * Test resource navigation is properly configured
     */
    public function test_resource_navigation_configured(): void
    {
        $this->assertNotNull($this->resourceClass::getNavigationIcon());
        $this->assertNotNull($this->resourceClass::getNavigationLabel());

        if (method_exists($this->resourceClass, 'getNavigationGroup')) {
            $this->assertNotNull($this->resourceClass::getNavigationGroup());
        }
    }
}
