<?php

namespace Tests\Feature\Filament;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Client;
use Filament\Facades\Filament;

class AllResourcesTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test client
        $this->client = Client::factory()->create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
        ]);

        // Create test user
        $this->user = User::factory()->create([
            'client_id' => $this->client->id,
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Authenticate user first
        $this->actingAs($this->user);
        
        // Then set current tenant
        Filament::setTenant($this->client);
    }

    /**
     * Test that all core resources can be accessed
     */
    public function test_core_resources_can_be_accessed(): void
    {
        $coreResources = [
            'App\Filament\Resources\AccountResource',
            'App\Filament\Resources\JournalResource',
            'App\Filament\Resources\CustomerResource',
            'App\Filament\Resources\SupplierResource',
            'App\Filament\Resources\ProductResource',
            'App\Filament\Resources\TransactionResource',
        ];

        foreach ($coreResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
            $this->assertTrue(method_exists($resourceClass, 'getPages'), "Resource {$resourceClass} should have getPages method");
        }
    }

    /**
     * Test that all inventory resources can be accessed
     */
    public function test_inventory_resources_can_be_accessed(): void
    {
        $inventoryResources = [
            'App\Filament\Resources\InventoryResource',
            'App\Filament\Resources\StockMovementResource',
            'App\Filament\Resources\UnitOfMeasureResource',
            'App\Filament\Resources\ProductCategoryResource',
        ];

        foreach ($inventoryResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
        }
    }

    /**
     * Test that all financial resources can be accessed
     */
    public function test_financial_resources_can_be_accessed(): void
    {
        $financialResources = [
            'App\Filament\Resources\BankAccountResource',
            'App\Filament\Resources\BankTransactionResource',
            'App\Filament\Resources\CashFlowCategoryResource',
            'App\Filament\Resources\BudgetResource',
        ];

        foreach ($financialResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
        }
    }

    /**
     * Test that all manufacturing resources can be accessed
     */
    public function test_manufacturing_resources_can_be_accessed(): void
    {
        $manufacturingResources = [
            'App\Filament\Resources\BillOfMaterialResource',
            'App\Filament\Resources\ProductionOrderResource',
            'App\Filament\Resources\WorkOrderResource',
            'App\Filament\Resources\WorkCenterResource',
        ];

        foreach ($manufacturingResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
        }
    }

    /**
     * Test that all asset management resources can be accessed
     */
    public function test_asset_resources_can_be_accessed(): void
    {
        $assetResources = [
            'App\Filament\Resources\FixedAssetResource',
            'App\Filament\Resources\DepreciationResource',
            'App\Filament\Resources\AssetTransferResource',
        ];

        foreach ($assetResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
        }
    }

    /**
     * Test that all system resources can be accessed
     */
    public function test_system_resources_can_be_accessed(): void
    {
        $systemResources = [
            'App\Filament\Resources\UserResource',
            'App\Filament\Resources\ClientResource',
            'App\Filament\Resources\AuditLogResource',
            'App\Filament\Resources\SystemNotificationResource',
        ];

        foreach ($systemResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
        }
    }

    /**
     * Test that all workflow resources can be accessed
     */
    public function test_workflow_resources_can_be_accessed(): void
    {
        $workflowResources = [
            'App\Filament\Resources\ApprovalWorkflowResource',
            'App\Filament\Resources\ApprovalRequestResource',
            'App\Filament\Resources\AutomatedJournalEntryResource',
        ];

        foreach ($workflowResources as $resourceClass) {
            $this->assertTrue(class_exists($resourceClass), "Resource {$resourceClass} should exist");
            
            // Test that resource has required methods
            $this->assertTrue(method_exists($resourceClass, 'form'), "Resource {$resourceClass} should have form method");
            $this->assertTrue(method_exists($resourceClass, 'table'), "Resource {$resourceClass} should have table method");
        }
    }

    /**
     * Test that all resources have proper navigation configuration
     */
    public function test_all_resources_have_navigation_config(): void
    {
        $allResources = [
            'App\Filament\Resources\AccountResource',
            'App\Filament\Resources\JournalResource',
            'App\Filament\Resources\CustomerResource',
            'App\Filament\Resources\SupplierResource',
            'App\Filament\Resources\ProductResource',
            'App\Filament\Resources\TransactionResource',
            'App\Filament\Resources\InventoryResource',
            'App\Filament\Resources\BankAccountResource',
            'App\Filament\Resources\UserResource',
        ];

        foreach ($allResources as $resourceClass) {
            if (class_exists($resourceClass)) {
                // Test navigation properties
                $this->assertTrue(method_exists($resourceClass, 'getNavigationIcon'), 
                    "Resource {$resourceClass} should have getNavigationIcon method");
                $this->assertTrue(method_exists($resourceClass, 'getNavigationLabel'), 
                    "Resource {$resourceClass} should have getNavigationLabel method");
                
                // Test that navigation icon and label are not null
                $icon = $resourceClass::getNavigationIcon();
                $label = $resourceClass::getNavigationLabel();
                
                $this->assertNotNull($icon, "Resource {$resourceClass} should have navigation icon");
                $this->assertNotNull($label, "Resource {$resourceClass} should have navigation label");
            }
        }
    }

    /**
     * Test that resources have proper model associations
     */
    public function test_resources_have_model_associations(): void
    {
        $resourceModelMap = [
            'App\Filament\Resources\AccountResource' => 'App\Models\Account',
            'App\Filament\Resources\JournalResource' => 'App\Models\Journal',
            'App\Filament\Resources\CustomerResource' => 'App\Models\Customer',
            'App\Filament\Resources\SupplierResource' => 'App\Models\Supplier',
            'App\Filament\Resources\ProductResource' => 'App\Models\Product',
            'App\Filament\Resources\TransactionResource' => 'App\Models\Transaction',
            'App\Filament\Resources\UserResource' => 'App\Models\User',
        ];

        foreach ($resourceModelMap as $resourceClass => $modelClass) {
            if (class_exists($resourceClass)) {
                $this->assertTrue(method_exists($resourceClass, 'getModel'), 
                    "Resource {$resourceClass} should have getModel method");
                
                $model = $resourceClass::getModel();
                $this->assertEquals($modelClass, $model, 
                    "Resource {$resourceClass} should be associated with model {$modelClass}");
                
                // Test that model class exists
                $this->assertTrue(class_exists($modelClass), 
                    "Model {$modelClass} should exist");
            }
        }
    }

    /**
     * Test that resources can handle basic CRUD operations
     */
    public function test_resources_can_handle_crud_operations(): void
    {
        $crudResources = [
            'App\Filament\Resources\AccountResource',
            'App\Filament\Resources\CustomerResource',
            'App\Filament\Resources\SupplierResource',
        ];

        foreach ($crudResources as $resourceClass) {
            if (class_exists($resourceClass)) {
                $pages = $resourceClass::getPages();
                
                // Test that resource has basic pages
                $this->assertArrayHasKey('index', $pages, 
                    "Resource {$resourceClass} should have index page");
                
                // Most resources should have create and edit pages
                if (isset($pages['create'])) {
                    $this->assertNotNull($pages['create'], 
                        "Resource {$resourceClass} create page should not be null");
                }
                
                if (isset($pages['edit'])) {
                    $this->assertNotNull($pages['edit'], 
                        "Resource {$resourceClass} edit page should not be null");
                }
            }
        }
    }
}
