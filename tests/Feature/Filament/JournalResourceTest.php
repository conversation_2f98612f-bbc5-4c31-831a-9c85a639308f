<?php

namespace Tests\Feature\Filament;

use App\Filament\Resources\JournalResource;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use Livewire\Livewire;

class JournalResourceTest extends BaseResourceTest
{
    protected string $resourceClass = JournalResource::class;
    protected string $modelClass = Journal::class;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test accounts
        $this->createTestAccounts();
    }

    protected function createTestAccounts(): void
    {
        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Kas',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
        ]);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Pendapatan Penjualan',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
            'is_active' => true,
        ]);
    }

    protected function createTestRecord(array $attributes = []): Journal
    {
        $journal = Journal::factory()->create(array_merge([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Test Journal Entry',
            'is_posted' => false,
        ], $attributes));

        // Create journal entries
        $accounts = Account::where('client_id', $this->client->id)->get();
        
        if ($accounts->count() >= 2) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts->first()->id,
                'debit' => 100000,
                'credit' => 0,
                'description' => 'Test debit entry',
            ]);

            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts->last()->id,
                'debit' => 0,
                'credit' => 100000,
                'description' => 'Test credit entry',
            ]);
        }

        return $journal;
    }

    public function test_journal_table_displays_correct_columns(): void
    {
        $journal = $this->createTestRecord();

        $component = Livewire::test(JournalResource\Pages\ListJournals::class)
            ->assertCanSeeTableRecords([$journal]);

        // Test that key columns are visible
        $component->assertSee($journal->id)
            ->assertSee($journal->description)
            ->assertSee($journal->journal_date->format('d/m/Y'));
    }

    public function test_journal_entries_count_is_displayed(): void
    {
        $journal = $this->createTestRecord();
        
        $component = Livewire::test(JournalResource\Pages\ListJournals::class);
        
        // Should show the count of journal entries
        $entriesCount = $journal->journalEntries()->count();
        $component->assertSee((string) $entriesCount);
    }

    public function test_journal_total_amounts_are_calculated(): void
    {
        $journal = $this->createTestRecord();
        
        $totalDebit = $journal->journalEntries()->sum('debit');
        $totalCredit = $journal->journalEntries()->sum('credit');
        
        $this->assertEquals($totalDebit, $totalCredit, 'Journal should be balanced');
        $this->assertEquals(100000, $totalDebit);
        $this->assertEquals(100000, $totalCredit);
    }

    public function test_journal_posting_functionality(): void
    {
        $journal = $this->createTestRecord(['is_posted' => false]);
        
        $this->assertFalse($journal->is_posted);
        $this->assertNull($journal->posted_at);
        
        // Test posting
        $journal->update([
            'is_posted' => true,
            'posted_at' => now(),
        ]);
        
        $journal->refresh();
        $this->assertTrue($journal->is_posted);
        $this->assertNotNull($journal->posted_at);
    }

    public function test_journal_form_validation(): void
    {
        $component = Livewire::test(JournalResource\Pages\CreateJournal::class);
        
        // Test required fields
        $component->fillForm([
            'journal_date' => '',
            'description' => '',
        ])->call('create')
            ->assertHasFormErrors(['journal_date', 'description']);
    }

    public function test_journal_entries_relationship(): void
    {
        $journal = $this->createTestRecord();
        
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $journal->journalEntries);
        $this->assertGreaterThan(0, $journal->journalEntries->count());
        
        foreach ($journal->journalEntries as $entry) {
            $this->assertEquals($journal->id, $entry->journal_id);
            $this->assertInstanceOf(Account::class, $entry->account);
        }
    }

    public function test_journal_balance_validation(): void
    {
        $journal = $this->createTestRecord();
        
        $this->assertTrue($journal->isBalanced(), 'Journal should be balanced');
        
        // Create unbalanced journal
        $unbalancedJournal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Unbalanced Journal',
        ]);
        
        $account = Account::where('client_id', $this->client->id)->first();
        
        JournalEntry::create([
            'journal_id' => $unbalancedJournal->id,
            'account_id' => $account->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Unbalanced entry',
        ]);
        
        $this->assertFalse($unbalancedJournal->isBalanced(), 'Journal should be unbalanced');
    }

    public function test_journal_filters_work(): void
    {
        // Create posted and unposted journals
        $postedJournal = $this->createTestRecord(['is_posted' => true, 'posted_at' => now()]);
        $unpostedJournal = $this->createTestRecord(['is_posted' => false]);
        
        $component = Livewire::test(JournalResource\Pages\ListJournals::class);
        
        // Test status filter
        $component->filterTable('is_posted', '1')
            ->assertCanSeeTableRecords([$postedJournal])
            ->assertCanNotSeeTableRecords([$unpostedJournal]);
            
        $component->filterTable('is_posted', '0')
            ->assertCanSeeTableRecords([$unpostedJournal])
            ->assertCanNotSeeTableRecords([$postedJournal]);
    }

    public function test_journal_date_filter_works(): void
    {
        $oldJournal = $this->createTestRecord(['journal_date' => now()->subMonth()]);
        $newJournal = $this->createTestRecord(['journal_date' => now()]);
        
        $component = Livewire::test(JournalResource\Pages\ListJournals::class);
        
        // Test date range filter
        $component->filterTable('journal_date', [
            'from' => now()->startOfMonth()->format('Y-m-d'),
            'until' => now()->endOfMonth()->format('Y-m-d'),
        ])->assertCanSeeTableRecords([$newJournal])
          ->assertCanNotSeeTableRecords([$oldJournal]);
    }

    public function test_journal_search_functionality(): void
    {
        $journal1 = $this->createTestRecord(['description' => 'Penjualan produk A']);
        $journal2 = $this->createTestRecord(['description' => 'Pembelian bahan baku']);
        
        $component = Livewire::test(JournalResource\Pages\ListJournals::class);
        
        $component->searchTable('Penjualan')
            ->assertCanSeeTableRecords([$journal1])
            ->assertCanNotSeeTableRecords([$journal2]);
    }

    public function test_journal_actions_are_available(): void
    {
        $journal = $this->createTestRecord(['is_posted' => false]);
        
        $component = Livewire::test(JournalResource\Pages\ListJournals::class);
        
        // Test that actions are available for unposted journals
        $component->assertTableActionExists('edit', $journal)
            ->assertTableActionExists('post', $journal);
    }

    public function test_posted_journal_cannot_be_edited(): void
    {
        $journal = $this->createTestRecord(['is_posted' => true, 'posted_at' => now()]);
        
        $component = Livewire::test(JournalResource\Pages\ListJournals::class);
        
        // Edit action should not be visible for posted journals
        $component->assertTableActionDoesNotExist('edit', $journal);
    }
}
