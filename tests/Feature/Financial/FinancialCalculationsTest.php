<?php

namespace Tests\Feature\Financial;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;

class FinancialCalculationsTest extends TestCase
{
    use RefreshDatabase;

    protected Client $client;
    protected Account $cashAccount;
    protected Account $revenueAccount;
    protected Account $expenseAccount;
    protected Account $liabilityAccount;
    protected Account $equityAccount;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = Client::factory()->create();
        $this->createTestAccounts();
    }

    protected function createTestAccounts(): void
    {
        $this->cashAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Cash',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        $this->revenueAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '4101',
            'account_name' => 'Sales Revenue',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);

        $this->expenseAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '6101',
            'account_name' => 'Office Expense',
            'account_type' => 'expense',
            'normal_balance' => 'debit',
        ]);

        $this->liabilityAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '2101',
            'account_name' => 'Accounts Payable',
            'account_type' => 'liability',
            'normal_balance' => 'credit',
        ]);

        $this->equityAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '3101',
            'account_name' => 'Owner Equity',
            'account_type' => 'equity',
            'normal_balance' => 'credit',
        ]);
    }

    protected function createBalancedJournal(Account $debitAccount, Account $creditAccount, float $amount): Journal
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Test Journal',
            'is_posted' => true,
            'posted_at' => now(),
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $debitAccount->id,
            'debit' => $amount,
            'credit' => 0,
            'description' => 'Debit entry',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $creditAccount->id,
            'debit' => 0,
            'credit' => $amount,
            'description' => 'Credit entry',
        ]);

        return $journal;
    }

    // Journal Balance Tests
    public function test_journal_is_balanced_when_debits_equal_credits(): void
    {
        $journal = $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 100000);

        $this->assertTrue($journal->isBalanced());
        $this->assertEquals(100000, $journal->getTotalDebitAttribute());
        $this->assertEquals(100000, $journal->getTotalCreditAttribute());
    }

    public function test_journal_is_unbalanced_when_debits_not_equal_credits(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Unbalanced Journal',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->cashAccount->id,
            'debit' => 100000,
            'credit' => 0,
            'description' => 'Debit entry',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 50000, // Different amount
            'description' => 'Credit entry',
        ]);

        $this->assertFalse($journal->isBalanced());
        $this->assertEquals(100000, $journal->getTotalDebitAttribute());
        $this->assertEquals(50000, $journal->getTotalCreditAttribute());
    }

    public function test_complex_journal_with_multiple_entries_is_balanced(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Complex Journal',
        ]);

        // Multiple debit entries
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->cashAccount->id,
            'debit' => 50000,
            'credit' => 0,
            'description' => 'Cash debit',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->expenseAccount->id,
            'debit' => 30000,
            'credit' => 0,
            'description' => 'Expense debit',
        ]);

        // Multiple credit entries
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 60000,
            'description' => 'Revenue credit',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->liabilityAccount->id,
            'debit' => 0,
            'credit' => 20000,
            'description' => 'Liability credit',
        ]);

        $this->assertTrue($journal->isBalanced());
        $this->assertEquals(80000, $journal->getTotalDebitAttribute()); // 50000 + 30000
        $this->assertEquals(80000, $journal->getTotalCreditAttribute()); // 60000 + 20000
    }

    // Account Balance Tests
    public function test_asset_account_balance_calculation(): void
    {
        // Asset accounts have normal debit balance
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 100000);
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 50000);

        $balance = $this->cashAccount->getBalanceAttribute();
        $this->assertEquals(150000, $balance); // Total debits for asset account
    }

    public function test_revenue_account_balance_calculation(): void
    {
        // Revenue accounts have normal credit balance
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 100000);
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 75000);

        $balance = $this->revenueAccount->getBalanceAttribute();
        $this->assertEquals(175000, $balance); // Total credits for revenue account
    }

    public function test_expense_account_balance_calculation(): void
    {
        // Expense accounts have normal debit balance
        $this->createBalancedJournal($this->expenseAccount, $this->cashAccount, 50000);
        $this->createBalancedJournal($this->expenseAccount, $this->cashAccount, 25000);

        $balance = $this->expenseAccount->getBalanceAttribute();
        $this->assertEquals(75000, $balance); // Total debits for expense account
    }

    public function test_liability_account_balance_calculation(): void
    {
        // Liability accounts have normal credit balance
        $this->createBalancedJournal($this->cashAccount, $this->liabilityAccount, 80000);
        $this->createBalancedJournal($this->cashAccount, $this->liabilityAccount, 20000);

        $balance = $this->liabilityAccount->getBalanceAttribute();
        $this->assertEquals(100000, $balance); // Total credits for liability account
    }

    public function test_equity_account_balance_calculation(): void
    {
        // Equity accounts have normal credit balance
        $this->createBalancedJournal($this->cashAccount, $this->equityAccount, 200000);

        $balance = $this->equityAccount->getBalanceAttribute();
        $this->assertEquals(200000, $balance); // Total credits for equity account
    }

    // Accounting Equation Tests
    public function test_accounting_equation_assets_equal_liabilities_plus_equity(): void
    {
        // Create transactions that affect the accounting equation
        $this->createBalancedJournal($this->cashAccount, $this->equityAccount, 100000); // Initial investment
        $this->createBalancedJournal($this->cashAccount, $this->liabilityAccount, 50000); // Borrowed money

        $totalAssets = $this->cashAccount->getBalanceAttribute();
        $totalLiabilities = $this->liabilityAccount->getBalanceAttribute();
        $totalEquity = $this->equityAccount->getBalanceAttribute();

        // Assets = Liabilities + Equity
        $this->assertEquals($totalAssets, $totalLiabilities + $totalEquity);
        $this->assertEquals(150000, $totalAssets);
        $this->assertEquals(50000, $totalLiabilities);
        $this->assertEquals(100000, $totalEquity);
    }

    // Trial Balance Tests
    public function test_trial_balance_total_debits_equal_total_credits(): void
    {
        // Create multiple balanced transactions
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 100000);
        $this->createBalancedJournal($this->expenseAccount, $this->cashAccount, 30000);
        $this->createBalancedJournal($this->cashAccount, $this->liabilityAccount, 50000);

        // Calculate total debits and credits across all accounts
        $accounts = Account::where('client_id', $this->client->id)->get();
        
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $entries = $account->journalEntries()
                ->whereHas('journal', function ($query) {
                    $query->where('is_posted', true);
                })
                ->get();

            $accountDebits = $entries->sum('debit');
            $accountCredits = $entries->sum('credit');

            $totalDebits += $accountDebits;
            $totalCredits += $accountCredits;
        }

        $this->assertEquals($totalDebits, $totalCredits);
    }

    // Financial Statement Calculations
    public function test_income_statement_calculation(): void
    {
        // Create revenue transactions
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 200000);
        $this->createBalancedJournal($this->cashAccount, $this->revenueAccount, 150000);

        // Create expense transactions
        $this->createBalancedJournal($this->expenseAccount, $this->cashAccount, 80000);
        $this->createBalancedJournal($this->expenseAccount, $this->cashAccount, 45000);

        $totalRevenue = $this->revenueAccount->getBalanceAttribute();
        $totalExpenses = $this->expenseAccount->getBalanceAttribute();
        $netIncome = $totalRevenue - $totalExpenses;

        $this->assertEquals(350000, $totalRevenue);
        $this->assertEquals(125000, $totalExpenses);
        $this->assertEquals(225000, $netIncome);
    }

    public function test_balance_sheet_calculation(): void
    {
        // Create asset transactions
        $this->createBalancedJournal($this->cashAccount, $this->equityAccount, 100000);

        // Create liability transactions
        $this->createBalancedJournal($this->cashAccount, $this->liabilityAccount, 30000);

        $totalAssets = $this->cashAccount->getBalanceAttribute();
        $totalLiabilities = $this->liabilityAccount->getBalanceAttribute();
        $totalEquity = $this->equityAccount->getBalanceAttribute();

        // Verify balance sheet equation
        $this->assertEquals($totalAssets, $totalLiabilities + $totalEquity);
        $this->assertEquals(130000, $totalAssets);
        $this->assertEquals(30000, $totalLiabilities);
        $this->assertEquals(100000, $totalEquity);
    }

    // Precision and Rounding Tests
    public function test_financial_calculations_handle_decimal_precision(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Precision Test',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->cashAccount->id,
            'debit' => 100.33,
            'credit' => 0,
            'description' => 'Precise debit',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 100.33,
            'description' => 'Precise credit',
        ]);

        $this->assertTrue($journal->isBalanced());
        $this->assertEquals(100.33, $journal->getTotalDebitAttribute());
        $this->assertEquals(100.33, $journal->getTotalCreditAttribute());
    }

    public function test_zero_amount_entries_are_handled_correctly(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => now(),
            'description' => 'Zero Amount Test',
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $this->cashAccount->id,
            'debit' => 0,
            'credit' => 0,
            'description' => 'Zero entry',
        ]);

        $this->assertTrue($journal->isBalanced());
        $this->assertEquals(0, $journal->getTotalDebitAttribute());
        $this->assertEquals(0, $journal->getTotalCreditAttribute());
    }
}
