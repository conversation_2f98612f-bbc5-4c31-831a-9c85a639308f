<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductResourceCreateOptionTest extends TestCase
{
    use RefreshDatabase;

    protected $client;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->client = Client::factory()->create();
        $this->user = User::factory()->create(['client_id' => $this->client->id]);
    }

    public function test_can_create_category_via_create_option(): void
    {
        $this->actingAs($this->user);

        // Simulate creating category via createOptionUsing
        $categoryData = [
            'category_code' => 'NEWCAT',
            'category_name' => 'New Category',
        ];

        // This simulates what happens in createOptionUsing
        $data = $categoryData;
        $data['client_id'] = $this->user->client_id;
        $data['is_active'] = true;
        $data['sort_order'] = 0;

        $category = ProductCategory::create($data);

        $this->assertDatabaseHas('product_categories', [
            'client_id' => $this->client->id,
            'category_code' => 'NEWCAT',
            'category_name' => 'New Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        $this->assertEquals($this->client->id, $category->client_id);
    }

    public function test_can_create_unit_via_create_option(): void
    {
        $this->actingAs($this->user);

        // Simulate creating unit via createOptionUsing
        $unitData = [
            'unit_code' => 'NEWUNIT',
            'unit_name' => 'New Unit',
        ];

        // This simulates what happens in createOptionUsing
        $data = $unitData;
        $data['client_id'] = $this->user->client_id;
        $data['is_active'] = true;
        $data['conversion_factor'] = 1.0;
        $data['is_base_unit'] = false;

        $unit = UnitOfMeasure::create($data);

        $this->assertDatabaseHas('unit_of_measures', [
            'client_id' => $this->client->id,
            'unit_code' => 'NEWUNIT',
            'unit_name' => 'New Unit',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        $this->assertEquals($this->client->id, $unit->client_id);
    }

    public function test_can_create_product_with_new_category_and_unit(): void
    {
        $this->actingAs($this->user);

        // Create category via createOptionUsing simulation
        $categoryData = [
            'category_code' => 'TESTCAT',
            'category_name' => 'Test Category',
            'client_id' => $this->user->client_id,
            'is_active' => true,
            'sort_order' => 0,
        ];
        $category = ProductCategory::create($categoryData);

        // Create unit via createOptionUsing simulation
        $unitData = [
            'unit_code' => 'TESTUNIT',
            'unit_name' => 'Test Unit',
            'client_id' => $this->user->client_id,
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ];
        $unit = UnitOfMeasure::create($unitData);

        // Create product using the new category and unit
        $product = Product::create([
            'client_id' => $this->user->client_id,
            'category_id' => $category->id,
            'unit_id' => $unit->id,
            'product_code' => 'TESTPROD',
            'product_name' => 'Test Product',
            'product_type' => 'finished_good',
            'sku' => 'TESTPROD',
            'standard_cost' => 100000,
            'selling_price' => 150000,
            'minimum_stock' => 10,
            'maximum_stock' => 100,
            'reorder_point' => 20,
            'lead_time_days' => 7,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => false,
            'origin_country' => 'Indonesia',
        ]);

        $this->assertDatabaseHas('products', [
            'client_id' => $this->client->id,
            'category_id' => $category->id,
            'unit_id' => $unit->id,
            'product_code' => 'TESTPROD',
            'product_name' => 'Test Product',
        ]);

        // Verify relationships
        $this->assertEquals($category->id, $product->category_id);
        $this->assertEquals($unit->id, $product->unit_id);
        $this->assertEquals($this->client->id, $product->client_id);
        $this->assertEquals($this->client->id, $category->client_id);
        $this->assertEquals($this->client->id, $unit->client_id);
    }

    public function test_category_scoped_to_client(): void
    {
        $this->actingAs($this->user);

        // Create another client with category
        $anotherClient = Client::factory()->create();
        $anotherCategory = ProductCategory::create([
            'client_id' => $anotherClient->id,
            'category_code' => 'OTHER',
            'category_name' => 'Other Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        // Create category for current client
        $myCategory = ProductCategory::create([
            'client_id' => $this->client->id,
            'category_code' => 'MINE',
            'category_name' => 'My Category',
            'is_active' => true,
            'sort_order' => 0,
        ]);

        // Query should only return categories for current client
        $categories = ProductCategory::where('client_id', $this->user->client_id)->get();

        $this->assertCount(1, $categories);
        $this->assertEquals('MINE', $categories->first()->category_code);
        $this->assertNotContains($anotherCategory->id, $categories->pluck('id'));
    }

    public function test_unit_scoped_to_client(): void
    {
        $this->actingAs($this->user);

        // Create another client with unit
        $anotherClient = Client::factory()->create();
        $anotherUnit = UnitOfMeasure::create([
            'client_id' => $anotherClient->id,
            'unit_code' => 'OTHER',
            'unit_name' => 'Other Unit',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Create unit for current client
        $myUnit = UnitOfMeasure::create([
            'client_id' => $this->client->id,
            'unit_code' => 'MINE',
            'unit_name' => 'My Unit',
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ]);

        // Query should only return units for current client
        $units = UnitOfMeasure::where('client_id', $this->user->client_id)->get();

        $this->assertCount(1, $units);
        $this->assertEquals('MINE', $units->first()->unit_code);
        $this->assertNotContains($anotherUnit->id, $units->pluck('id'));
    }

    public function test_create_option_sets_correct_defaults(): void
    {
        $this->actingAs($this->user);

        // Test category defaults
        $categoryData = [
            'category_code' => 'DEFAULT',
            'category_name' => 'Default Category',
            'client_id' => $this->user->client_id,
            'is_active' => true,
            'sort_order' => 0,
        ];
        $category = ProductCategory::create($categoryData);

        $this->assertTrue($category->is_active);
        $this->assertEquals(0, $category->sort_order);
        $this->assertEquals($this->client->id, $category->client_id);

        // Test unit defaults
        $unitData = [
            'unit_code' => 'DEFAULT',
            'unit_name' => 'Default Unit',
            'client_id' => $this->user->client_id,
            'is_active' => true,
            'conversion_factor' => 1.0,
            'is_base_unit' => false,
        ];
        $unit = UnitOfMeasure::create($unitData);

        $this->assertTrue($unit->is_active);
        $this->assertEquals(1.0, $unit->conversion_factor);
        $this->assertFalse($unit->is_base_unit);
        $this->assertEquals($this->client->id, $unit->client_id);
    }
}
