<?php

/**
 * Comprehensive Test Runner for SaaS Akuntansi
 * 
 * This script runs all tests and generates a comprehensive report
 * for production readiness assessment.
 */

require_once __DIR__ . '/../vendor/autoload.php';

class ComprehensiveTestRunner
{
    private array $testSuites = [
        'Filament Resources' => [
            'tests/Feature/Filament/JournalResourceTest.php',
            'tests/Feature/Filament/AccountResourceTest.php',
            'tests/Feature/Filament/CustomerResourceTest.php',
        ],
        'Custom Pages' => [
            'tests/Feature/Filament/GeneralLedgerPageTest.php',
            'tests/Feature/Filament/FinancialReportsTest.php',
        ],
        'Blade Templates' => [
            'tests/Feature/Filament/BladeTemplatesTest.php',
        ],
        'Model Relationships' => [
            'tests/Feature/Models/ModelRelationshipsTest.php',
        ],
        'Financial Calculations' => [
            'tests/Feature/Financial/FinancialCalculationsTest.php',
        ],
    ];

    private array $results = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;
    private array $failures = [];

    public function runAllTests(): void
    {
        echo "🧪 Starting Comprehensive Test Suite for SaaS Akuntansi\n";
        echo "=" . str_repeat("=", 60) . "\n\n";

        foreach ($this->testSuites as $suiteName => $testFiles) {
            echo "📋 Running {$suiteName} Tests...\n";
            $this->runTestSuite($suiteName, $testFiles);
            echo "\n";
        }

        $this->generateReport();
    }

    private function runTestSuite(string $suiteName, array $testFiles): void
    {
        $suiteResults = [];
        
        foreach ($testFiles as $testFile) {
            if (!file_exists($testFile)) {
                echo "⚠️  Test file not found: {$testFile}\n";
                continue;
            }

            echo "  🔍 Running {$testFile}...\n";
            $result = $this->runSingleTest($testFile);
            $suiteResults[] = $result;
            
            if ($result['passed']) {
                echo "  ✅ {$result['name']} - {$result['tests']} tests passed\n";
            } else {
                echo "  ❌ {$result['name']} - {$result['failures']} failures\n";
                foreach ($result['failure_details'] as $failure) {
                    echo "     💥 {$failure}\n";
                }
            }
        }

        $this->results[$suiteName] = $suiteResults;
    }

    private function runSingleTest(string $testFile): array
    {
        $command = "php artisan test {$testFile} --stop-on-failure";
        $output = [];
        $returnCode = 0;
        
        exec($command . " 2>&1", $output, $returnCode);
        
        $outputString = implode("\n", $output);
        
        // Parse PHPUnit output
        $tests = 0;
        $failures = 0;
        $failureDetails = [];
        
        if (preg_match('/Tests: (\d+)/', $outputString, $matches)) {
            $tests = (int) $matches[1];
        }
        
        if (preg_match('/Failures: (\d+)/', $outputString, $matches)) {
            $failures = (int) $matches[1];
        }
        
        if (preg_match('/Errors: (\d+)/', $outputString, $matches)) {
            $failures += (int) $matches[1];
        }

        // Extract failure details
        if ($failures > 0) {
            $lines = explode("\n", $outputString);
            foreach ($lines as $line) {
                if (strpos($line, 'FAIL') !== false || strpos($line, 'ERROR') !== false) {
                    $failureDetails[] = trim($line);
                }
            }
        }

        $this->totalTests += $tests;
        if ($failures === 0) {
            $this->passedTests += $tests;
        } else {
            $this->failedTests += $failures;
            $this->failures = array_merge($this->failures, $failureDetails);
        }

        return [
            'name' => basename($testFile, '.php'),
            'tests' => $tests,
            'failures' => $failures,
            'passed' => $failures === 0,
            'failure_details' => $failureDetails,
            'output' => $outputString,
        ];
    }

    private function generateReport(): void
    {
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "📊 COMPREHENSIVE TEST REPORT\n";
        echo str_repeat("=", 80) . "\n\n";

        // Summary
        echo "📈 SUMMARY:\n";
        echo "  Total Tests: {$this->totalTests}\n";
        echo "  Passed: {$this->passedTests}\n";
        echo "  Failed: {$this->failedTests}\n";
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        echo "  Success Rate: {$successRate}%\n\n";

        // Detailed Results
        echo "📋 DETAILED RESULTS:\n";
        foreach ($this->results as $suiteName => $suiteResults) {
            echo "\n🔸 {$suiteName}:\n";
            foreach ($suiteResults as $result) {
                $status = $result['passed'] ? '✅' : '❌';
                echo "  {$status} {$result['name']}: {$result['tests']} tests";
                if (!$result['passed']) {
                    echo " ({$result['failures']} failures)";
                }
                echo "\n";
            }
        }

        // Failures
        if (!empty($this->failures)) {
            echo "\n❌ FAILURES:\n";
            foreach ($this->failures as $failure) {
                echo "  💥 {$failure}\n";
            }
        }

        // Production Readiness Assessment
        echo "\n" . str_repeat("-", 80) . "\n";
        echo "🚀 PRODUCTION READINESS ASSESSMENT:\n";
        echo str_repeat("-", 80) . "\n";

        $this->assessProductionReadiness();

        // Recommendations
        echo "\n💡 RECOMMENDATIONS:\n";
        $this->generateRecommendations();

        // Save report to file
        $this->saveReportToFile();
    }

    private function assessProductionReadiness(): void
    {
        $criteria = [
            'All Tests Pass' => $this->failedTests === 0,
            'High Test Coverage' => $this->totalTests >= 50,
            'Resource Tests Complete' => isset($this->results['Filament Resources']),
            'Financial Tests Complete' => isset($this->results['Financial Calculations']),
            'Multi-tenant Tests Complete' => $this->checkMultiTenantTests(),
            'UI Tests Complete' => isset($this->results['Blade Templates']),
        ];

        foreach ($criteria as $criterion => $passed) {
            $status = $passed ? '✅' : '❌';
            echo "  {$status} {$criterion}\n";
        }

        $passedCriteria = array_sum($criteria);
        $totalCriteria = count($criteria);
        $readinessScore = round(($passedCriteria / $totalCriteria) * 100, 2);

        echo "\n🎯 Production Readiness Score: {$readinessScore}%\n";

        if ($readinessScore >= 90) {
            echo "🟢 READY FOR PRODUCTION\n";
        } elseif ($readinessScore >= 70) {
            echo "🟡 NEEDS MINOR FIXES BEFORE PRODUCTION\n";
        } else {
            echo "🔴 NOT READY FOR PRODUCTION - MAJOR ISSUES NEED FIXING\n";
        }
    }

    private function checkMultiTenantTests(): bool
    {
        // Check if multi-tenant tests are present in the results
        foreach ($this->results as $suiteResults) {
            foreach ($suiteResults as $result) {
                if (strpos($result['output'], 'multi_tenant') !== false || 
                    strpos($result['output'], 'tenant') !== false) {
                    return true;
                }
            }
        }
        return false;
    }

    private function generateRecommendations(): void
    {
        $recommendations = [];

        if ($this->failedTests > 0) {
            $recommendations[] = "Fix all failing tests before deploying to production";
        }

        if ($this->totalTests < 50) {
            $recommendations[] = "Increase test coverage by adding more unit and integration tests";
        }

        if (!isset($this->results['Filament Resources'])) {
            $recommendations[] = "Add comprehensive tests for all Filament resources";
        }

        if (!isset($this->results['Financial Calculations'])) {
            $recommendations[] = "Add tests for financial calculations and accounting principles";
        }

        if (empty($recommendations)) {
            $recommendations[] = "All major test categories are covered - consider adding edge case tests";
            $recommendations[] = "Add performance tests for large datasets";
            $recommendations[] = "Add security tests for authentication and authorization";
        }

        foreach ($recommendations as $recommendation) {
            echo "  • {$recommendation}\n";
        }
    }

    private function saveReportToFile(): void
    {
        $reportContent = $this->generateTextReport();
        $filename = 'test-report-' . date('Y-m-d-H-i-s') . '.txt';
        $filepath = __DIR__ . '/reports/' . $filename;
        
        // Create reports directory if it doesn't exist
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        file_put_contents($filepath, $reportContent);
        echo "\n📄 Report saved to: {$filepath}\n";
    }

    private function generateTextReport(): string
    {
        $report = "SaaS Akuntansi - Comprehensive Test Report\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $report .= str_repeat("=", 80) . "\n\n";
        
        $report .= "SUMMARY:\n";
        $report .= "Total Tests: {$this->totalTests}\n";
        $report .= "Passed: {$this->passedTests}\n";
        $report .= "Failed: {$this->failedTests}\n";
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        $report .= "Success Rate: {$successRate}%\n\n";
        
        // Add detailed results
        foreach ($this->results as $suiteName => $suiteResults) {
            $report .= "{$suiteName}:\n";
            foreach ($suiteResults as $result) {
                $status = $result['passed'] ? 'PASS' : 'FAIL';
                $report .= "  [{$status}] {$result['name']}: {$result['tests']} tests\n";
            }
            $report .= "\n";
        }
        
        return $report;
    }
}

// Run the comprehensive test suite
$runner = new ComprehensiveTestRunner();
$runner->runAllTests();
