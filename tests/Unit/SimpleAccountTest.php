<?php

namespace Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SimpleAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_accounts_are_created_correctly(): void
    {
        // Create a client
        $clientId = DB::table('clients')->insertGetId([
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'phone' => '*********',
            'address' => 'Test Address',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create an account
        $accountId = DB::table('accounts')->insertGetId([
            'client_id' => $clientId,
            'account_code' => '1101',
            'account_name' => 'Kas',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Verify account exists
        $account = DB::table('accounts')->where('id', $accountId)->first();
        
        $this->assertNotNull($account);
        $this->assertEquals('1101', $account->account_code);
        $this->assertEquals('Kas', $account->account_name);
        $this->assertEquals('asset', $account->account_type);
        $this->assertEquals('debit', $account->normal_balance);
        $this->assertEquals($clientId, $account->client_id);
    }

    public function test_journal_entries_are_created_correctly(): void
    {
        // Create client and account
        $clientId = DB::table('clients')->insertGetId([
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'phone' => '*********',
            'address' => 'Test Address',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $accountId = DB::table('accounts')->insertGetId([
            'client_id' => $clientId,
            'account_code' => '1101',
            'account_name' => 'Kas',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create journal
        $journalId = DB::table('journals')->insertGetId([
            'client_id' => $clientId,
            'journal_date' => now(),
            'description' => 'Test journal',
            'is_posted' => true,
            'posted_at' => now(),
            'created_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create journal entry
        $entryId = DB::table('journal_entries')->insertGetId([
            'journal_id' => $journalId,
            'account_id' => $accountId,
            'debit' => 1000000,
            'credit' => 0,
            'description' => 'Test entry',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Verify journal entry exists
        $entry = DB::table('journal_entries')->where('id', $entryId)->first();
        
        $this->assertNotNull($entry);
        $this->assertEquals($journalId, $entry->journal_id);
        $this->assertEquals($accountId, $entry->account_id);
        $this->assertEquals(1000000, $entry->debit);
        $this->assertEquals(0, $entry->credit);
    }

    public function test_financial_report_data_calculation(): void
    {
        // Create test data
        $clientId = DB::table('clients')->insertGetId([
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'phone' => '*********',
            'address' => 'Test Address',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create accounts
        $cashAccountId = DB::table('accounts')->insertGetId([
            'client_id' => $clientId,
            'account_code' => '1101',
            'account_name' => 'Kas',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $revenueAccountId = DB::table('accounts')->insertGetId([
            'client_id' => $clientId,
            'account_code' => '4101',
            'account_name' => 'Penjualan',
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create journal
        $journalId = DB::table('journals')->insertGetId([
            'client_id' => $clientId,
            'journal_date' => now(),
            'description' => 'Sales transaction',
            'is_posted' => true,
            'posted_at' => now(),
            'created_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create journal entries
        DB::table('journal_entries')->insert([
            [
                'journal_id' => $journalId,
                'account_id' => $cashAccountId,
                'debit' => 5000000,
                'credit' => 0,
                'description' => 'Cash received',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'journal_id' => $journalId,
                'account_id' => $revenueAccountId,
                'debit' => 0,
                'credit' => 5000000,
                'description' => 'Sales revenue',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Test revenue calculation
        $revenueTotal = DB::table('journal_entries')
            ->join('accounts', 'journal_entries.account_id', '=', 'accounts.id')
            ->join('journals', 'journal_entries.journal_id', '=', 'journals.id')
            ->where('accounts.client_id', $clientId)
            ->where('accounts.account_code', 'like', '4%')
            ->where('journals.is_posted', true)
            ->sum('journal_entries.credit');

        $this->assertEquals(5000000, $revenueTotal);

        // Test cash balance calculation
        $cashBalance = DB::table('journal_entries')
            ->join('accounts', 'journal_entries.account_id', '=', 'accounts.id')
            ->join('journals', 'journal_entries.journal_id', '=', 'journals.id')
            ->where('accounts.client_id', $clientId)
            ->where('accounts.account_code', '1101')
            ->where('journals.is_posted', true)
            ->selectRaw('SUM(debit) - SUM(credit) as balance')
            ->value('balance');

        $this->assertEquals(5000000, $cashBalance);
    }

    public function test_multi_tenant_isolation(): void
    {
        // Create two clients
        $client1Id = DB::table('clients')->insertGetId([
            'name' => 'Company 1',
            'email' => '<EMAIL>',
            'phone' => '*********',
            'address' => 'Address 1',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $client2Id = DB::table('clients')->insertGetId([
            'name' => 'Company 2',
            'email' => '<EMAIL>',
            'phone' => '*********',
            'address' => 'Address 2',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create accounts for both clients with same account code
        DB::table('accounts')->insert([
            [
                'client_id' => $client1Id,
                'account_code' => '1101',
                'account_name' => 'Kas Client 1',
                'account_type' => 'asset',
                'normal_balance' => 'debit',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'client_id' => $client2Id,
                'account_code' => '1101',
                'account_name' => 'Kas Client 2',
                'account_type' => 'asset',
                'normal_balance' => 'debit',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Verify each client only sees their own accounts
        $client1Accounts = DB::table('accounts')->where('client_id', $client1Id)->get();
        $client2Accounts = DB::table('accounts')->where('client_id', $client2Id)->get();

        $this->assertCount(1, $client1Accounts);
        $this->assertCount(1, $client2Accounts);
        $this->assertEquals('Kas Client 1', $client1Accounts->first()->account_name);
        $this->assertEquals('Kas Client 2', $client2Accounts->first()->account_name);
    }

    public function test_seeded_data_integrity(): void
    {
        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'MinimalDataSeeder']);

        // Verify clients were created
        $clients = DB::table('clients')->get();
        $this->assertGreaterThanOrEqual(2, $clients->count());

        // Verify accounts were created for each client
        foreach ($clients as $client) {
            $accounts = DB::table('accounts')->where('client_id', $client->id)->get();
            $this->assertGreaterThan(0, $accounts->count());

            // Verify we have different account types
            $accountTypes = $accounts->pluck('account_type')->unique();
            $this->assertContains('asset', $accountTypes);
            $this->assertContains('revenue', $accountTypes);
            $this->assertContains('expense', $accountTypes);
        }

        // Verify journal entries were created
        $journalEntries = DB::table('journal_entries')->get();
        $this->assertGreaterThan(0, $journalEntries->count());

        // Verify journals are balanced
        $journals = DB::table('journals')->where('is_posted', true)->get();
        foreach ($journals as $journal) {
            $totalDebits = DB::table('journal_entries')
                ->where('journal_id', $journal->id)
                ->sum('debit');
            
            $totalCredits = DB::table('journal_entries')
                ->where('journal_id', $journal->id)
                ->sum('credit');

            $this->assertEquals($totalDebits, $totalCredits, "Journal {$journal->id} is not balanced");
        }
    }
}
