<?php

namespace Tests\Unit\Models;

use App\Models\Account;
use App\Models\Client;
use App\Models\JournalEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AccountTest extends TestCase
{
    use RefreshDatabase;

    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = Client::factory()->create();
    }

    public function test_account_can_be_created(): void
    {
        $account = Account::create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
            'account_name' => 'Kas',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('accounts', [
            'account_code' => '1101',
            'account_name' => 'Kas',
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);

        $this->assertEquals('1101', $account->account_code);
        $this->assertEquals('Kas', $account->account_name);
        $this->assertTrue($account->is_active);
    }

    public function test_account_belongs_to_client(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $this->assertInstanceOf(Client::class, $account->client);
        $this->assertEquals($this->client->id, $account->client->id);
    }

    public function test_account_has_many_journal_entries(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $journalEntry = JournalEntry::factory()->create([
            'account_id' => $account->id,
        ]);

        $this->assertInstanceOf(JournalEntry::class, $account->journalEntries->first());
        $this->assertEquals($journalEntry->id, $account->journalEntries->first()->id);
    }

    public function test_account_code_is_unique_per_client(): void
    {
        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
        ]);
    }

    public function test_account_code_can_be_same_for_different_clients(): void
    {
        $client2 = Client::factory()->create();

        $account1 = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_code' => '1101',
        ]);

        $account2 = Account::factory()->create([
            'client_id' => $client2->id,
            'account_code' => '1101',
        ]);

        $this->assertEquals('1101', $account1->account_code);
        $this->assertEquals('1101', $account2->account_code);
        $this->assertNotEquals($account1->client_id, $account2->client_id);
    }

    public function test_account_scope_active(): void
    {
        Account::factory()->create([
            'client_id' => $this->client->id,
            'is_active' => true,
        ]);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'is_active' => false,
        ]);

        $activeAccounts = Account::active()->get();
        
        $this->assertCount(1, $activeAccounts);
        $this->assertTrue($activeAccounts->first()->is_active);
    }

    public function test_account_scope_by_type(): void
    {
        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_type' => 'asset',
        ]);

        Account::factory()->create([
            'client_id' => $this->client->id,
            'account_type' => 'liability',
        ]);

        $assetAccounts = Account::byType('asset')->get();
        
        $this->assertCount(1, $assetAccounts);
        $this->assertEquals('asset', $assetAccounts->first()->account_type);
    }

    public function test_account_balance_calculation(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
            'normal_balance' => 'debit',
        ]);

        // Create journal entries
        JournalEntry::factory()->create([
            'account_id' => $account->id,
            'debit' => 1000,
            'credit' => 0,
        ]);

        JournalEntry::factory()->create([
            'account_id' => $account->id,
            'debit' => 0,
            'credit' => 300,
        ]);

        $balance = $account->getBalance();
        
        $this->assertEquals(700, $balance); // 1000 - 300 = 700
    }

    public function test_account_validation_rules(): void
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        // Test required fields
        Account::create([
            'client_id' => $this->client->id,
            // Missing required fields
        ]);
    }

    public function test_account_soft_deletes(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $account->delete();

        $this->assertSoftDeleted('accounts', [
            'id' => $account->id,
        ]);

        // Test that soft deleted accounts are not included in normal queries
        $this->assertCount(0, Account::all());
        
        // Test that soft deleted accounts can be retrieved with withTrashed
        $this->assertCount(1, Account::withTrashed()->get());
    }

    public function test_account_can_be_restored(): void
    {
        $account = Account::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $account->delete();
        $account->restore();

        $this->assertCount(1, Account::all());
        $this->assertNull($account->fresh()->deleted_at);
    }
}
