<?php

namespace Tests\Unit\Models;

use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use App\Models\Client;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class JournalTest extends TestCase
{
    use RefreshDatabase;

    private Client $client;
    private Account $assetAccount;
    private Account $revenueAccount;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = Client::factory()->create();
        
        $this->assetAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_type' => 'asset',
            'normal_balance' => 'debit',
        ]);
        
        $this->revenueAccount = Account::factory()->create([
            'client_id' => $this->client->id,
            'account_type' => 'revenue',
            'normal_balance' => 'credit',
        ]);
    }

    public function test_journal_can_be_created(): void
    {
        $journal = Journal::create([
            'client_id' => $this->client->id,
            'journal_number' => 'JE-001',
            'journal_date' => Carbon::today(),
            'description' => 'Test journal entry',
            'total_amount' => 1000,
            'is_posted' => false,
            'created_by' => 1,
        ]);

        $this->assertDatabaseHas('journals', [
            'journal_number' => 'JE-001',
            'description' => 'Test journal entry',
            'total_amount' => 1000,
            'is_posted' => false,
        ]);

        $this->assertEquals('JE-001', $journal->journal_number);
        $this->assertFalse($journal->is_posted);
    }

    public function test_journal_belongs_to_client(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $this->assertInstanceOf(Client::class, $journal->client);
        $this->assertEquals($this->client->id, $journal->client->id);
    }

    public function test_journal_has_many_entries(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $entry1 = JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->assetAccount->id,
        ]);

        $entry2 = JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
        ]);

        $this->assertCount(2, $journal->entries);
        $this->assertTrue($journal->entries->contains($entry1));
        $this->assertTrue($journal->entries->contains($entry2));
    }

    public function test_journal_can_be_posted(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => false,
        ]);

        $journal->post();

        $this->assertTrue($journal->fresh()->is_posted);
        $this->assertNotNull($journal->fresh()->posted_at);
    }

    public function test_journal_can_be_unposted(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => true,
            'posted_at' => Carbon::now(),
        ]);

        $journal->unpost();

        $this->assertFalse($journal->fresh()->is_posted);
        $this->assertNull($journal->fresh()->posted_at);
    }

    public function test_journal_scope_posted(): void
    {
        Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => true,
        ]);

        Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => false,
        ]);

        $postedJournals = Journal::posted()->get();
        
        $this->assertCount(1, $postedJournals);
        $this->assertTrue($postedJournals->first()->is_posted);
    }

    public function test_journal_scope_date_range(): void
    {
        $startDate = Carbon::today()->subDays(10);
        $endDate = Carbon::today()->subDays(5);

        Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => $startDate->copy()->addDays(2),
        ]);

        Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => Carbon::today(),
        ]);

        $journalsInRange = Journal::dateRange($startDate, $endDate)->get();
        
        $this->assertCount(1, $journalsInRange);
    }

    public function test_journal_validates_balanced_entries(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Create balanced entries
        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->assetAccount->id,
            'debit' => 1000,
            'credit' => 0,
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 1000,
        ]);

        $this->assertTrue($journal->isBalanced());
    }

    public function test_journal_detects_unbalanced_entries(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
        ]);

        // Create unbalanced entries
        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->assetAccount->id,
            'debit' => 1000,
            'credit' => 0,
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 500,
        ]);

        $this->assertFalse($journal->isBalanced());
    }

    public function test_journal_calculates_total_amount(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'total_amount' => 0,
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->assetAccount->id,
            'debit' => 1000,
            'credit' => 0,
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $this->revenueAccount->id,
            'debit' => 0,
            'credit' => 1000,
        ]);

        $journal->calculateTotalAmount();

        $this->assertEquals(1000, $journal->fresh()->total_amount);
    }

    public function test_journal_number_is_unique_per_client(): void
    {
        Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_number' => 'JE-001',
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_number' => 'JE-001',
        ]);
    }

    public function test_journal_number_can_be_same_for_different_clients(): void
    {
        $client2 = Client::factory()->create();

        $journal1 = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_number' => 'JE-001',
        ]);

        $journal2 = Journal::factory()->create([
            'client_id' => $client2->id,
            'journal_number' => 'JE-001',
        ]);

        $this->assertEquals('JE-001', $journal1->journal_number);
        $this->assertEquals('JE-001', $journal2->journal_number);
        $this->assertNotEquals($journal1->client_id, $journal2->client_id);
    }

    public function test_journal_soft_deletes(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
        ]);

        $journal->delete();

        $this->assertSoftDeleted('journals', [
            'id' => $journal->id,
        ]);
    }

    public function test_posted_journal_cannot_be_deleted(): void
    {
        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'is_posted' => true,
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Cannot delete posted journal');

        $journal->delete();
    }
}
