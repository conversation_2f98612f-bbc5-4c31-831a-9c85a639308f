<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\FinancialRatioAnalysisService;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Inventory;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FinancialRatioAnalysisServiceTest extends TestCase
{
    use RefreshDatabase;

    protected FinancialRatioAnalysisService $service;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new FinancialRatioAnalysisService();
        $this->client = Client::factory()->create();
        $this->createTestAccounts();
    }

    protected function createTestAccounts(): void
    {
        // Create chart of accounts
        $accounts = [
            ['account_code' => '1111', 'account_name' => 'Kas', 'account_type' => 'asset'],
            ['account_code' => '1121', 'account_name' => 'Piutang Usaha', 'account_type' => 'asset'],
            ['account_code' => '1131', 'account_name' => 'Persediaan', 'account_type' => 'asset'],
            ['account_code' => '1141', 'account_name' => 'Aset Tetap', 'account_type' => 'asset'],
            ['account_code' => '2111', 'account_name' => 'Hutang Usaha', 'account_type' => 'liability'],
            ['account_code' => '2121', 'account_name' => 'Hutang Bank', 'account_type' => 'liability'],
            ['account_code' => '3111', 'account_name' => 'Modal', 'account_type' => 'equity'],
            ['account_code' => '4111', 'account_name' => 'Pendapatan', 'account_type' => 'revenue'],
            ['account_code' => '5111', 'account_name' => 'HPP', 'account_type' => 'expense'],
            ['account_code' => '6111', 'account_name' => 'Beban Operasional', 'account_type' => 'expense'],
        ];

        foreach ($accounts as $accountData) {
            Account::factory()->create([
                'client_id' => $this->client->id,
                'account_code' => $accountData['account_code'],
                'account_name' => $accountData['account_name'],
                'account_type' => $accountData['account_type'],
            ]);
        }
    }

    protected function createJournalEntry(string $accountCode, float $debit, float $credit, Carbon $date): void
    {
        $account = Account::where('client_id', $this->client->id)
            ->where('account_code', $accountCode)
            ->first();

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => $date,
            'is_posted' => true,
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => $debit,
            'credit' => $credit,
        ]);
    }

    public function test_get_liquidity_ratios_with_sample_data(): void
    {
        $asOfDate = Carbon::now();

        // Create sample balance sheet data
        $this->createJournalEntry('1111', *********, 0, $asOfDate); // Cash
        $this->createJournalEntry('1121', ********, 0, $asOfDate);  // Accounts Receivable
        $this->createJournalEntry('1131', ********, 0, $asOfDate);  // Inventory
        $this->createJournalEntry('2111', 0, ********, $asOfDate);  // Accounts Payable
        $this->createJournalEntry('2121', 0, ********, $asOfDate);  // Bank Loan

        $ratios = $this->service->getLiquidityRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertArrayHasKey('current_ratio', $ratios);
        $this->assertArrayHasKey('quick_ratio', $ratios);
        $this->assertArrayHasKey('cash_ratio', $ratios);

        // Current Ratio = Current Assets / Current Liabilities
        // (100M + 50M + 30M) / (80M + 20M) = 180M / 100M = 1.8
        $this->assertEquals(1.8, $ratios['current_ratio']);

        // Quick Ratio = (Current Assets - Inventory) / Current Liabilities
        // Since getInventoryValue returns 0 (no Inventory records), quick ratio = current ratio
        // (180M - 0) / 100M = 180M / 100M = 1.8
        $this->assertEquals(1.8, $ratios['quick_ratio']);

        // Cash Ratio = Cash / Current Liabilities
        // 100M / 100M = 1.0
        $this->assertEquals(1.0, $ratios['cash_ratio']);
    }

    public function test_get_profitability_ratios_with_sample_data(): void
    {
        $asOfDate = Carbon::now();
        $startDate = $asOfDate->copy()->subYear();

        // Create sample income statement data
        $this->createJournalEntry('4111', 0, ********0, $asOfDate); // Revenue
        $this->createJournalEntry('5111', ********0, 0, $asOfDate); // COGS
        $this->createJournalEntry('6111', *********, 0, $asOfDate); // Operating Expenses

        // Create sample balance sheet data for ROA/ROE calculation
        $this->createJournalEntry('1141', ********0, 0, $asOfDate); // Total Assets
        $this->createJournalEntry('3111', 0, 1********, $asOfDate);  // Equity

        $ratios = $this->service->getProfitabilityRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertArrayHasKey('gross_profit_margin', $ratios);
        $this->assertArrayHasKey('net_profit_margin', $ratios);
        $this->assertArrayHasKey('return_on_assets', $ratios);
        $this->assertArrayHasKey('return_on_equity', $ratios);

        // Gross Profit Margin = (Revenue - COGS) / Revenue * 100
        // (500M - 300M) / 500M * 100 = 40%
        $this->assertEquals(40.0, $ratios['gross_profit_margin']);

        // Net Profit Margin = (Revenue - COGS - Operating Expenses) / Revenue * 100
        // (500M - 300M - 100M) / 500M * 100 = 20%
        $this->assertEquals(20.0, $ratios['net_profit_margin']);
    }

    public function test_get_efficiency_ratios(): void
    {
        $asOfDate = Carbon::now();

        $ratios = $this->service->getEfficiencyRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertArrayHasKey('asset_turnover', $ratios);
        $this->assertArrayHasKey('inventory_turnover', $ratios);
        $this->assertArrayHasKey('receivables_turnover', $ratios);
    }

    public function test_get_leverage_ratios(): void
    {
        $asOfDate = Carbon::now();

        $ratios = $this->service->getLeverageRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertArrayHasKey('debt_to_assets', $ratios);
        $this->assertArrayHasKey('debt_to_equity', $ratios);
        $this->assertArrayHasKey('equity_ratio', $ratios);
        $this->assertArrayHasKey('times_interest_earned', $ratios);
    }

    public function test_get_market_ratios(): void
    {
        $asOfDate = Carbon::now();

        $ratios = $this->service->getMarketRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertArrayHasKey('book_value_per_share', $ratios);
        $this->assertArrayHasKey('earnings_per_share', $ratios);
        $this->assertArrayHasKey('note', $ratios);
        $this->assertEquals('Market ratios are not applicable for private companies', $ratios['note']);
    }

    public function test_get_ratio_trends_returns_six_months(): void
    {
        $asOfDate = Carbon::now();

        $trends = $this->service->getRatioTrends($this->client->id, $asOfDate);

        $this->assertIsArray($trends);
        $this->assertCount(6, $trends); // Should return 6 months of data

        foreach ($trends as $trend) {
            $this->assertArrayHasKey('period', $trend);
            $this->assertArrayHasKey('current_ratio', $trend);
            $this->assertArrayHasKey('quick_ratio', $trend);
            $this->assertArrayHasKey('roa', $trend);
            $this->assertArrayHasKey('roe', $trend);
        }
    }

    public function test_analyze_financial_ratios_comprehensive(): void
    {
        $asOfDate = Carbon::now();

        $analysis = $this->service->analyzeFinancialRatios($this->client->id, $asOfDate);

        $this->assertIsArray($analysis);
        $this->assertArrayHasKey('analysis_date', $analysis);
        $this->assertArrayHasKey('current_period', $analysis);
        $this->assertArrayHasKey('ratio_categories', $analysis);
        $this->assertArrayHasKey('overall_assessment', $analysis);
        $this->assertArrayHasKey('industry_benchmarks', $analysis);
        $this->assertArrayHasKey('recommendations', $analysis);

        // Check ratio categories
        $categories = $analysis['ratio_categories'];
        $this->assertArrayHasKey('liquidity_ratios', $categories);
        $this->assertArrayHasKey('profitability_ratios', $categories);
        $this->assertArrayHasKey('efficiency_ratios', $categories);
        $this->assertArrayHasKey('leverage_ratios', $categories);
        $this->assertArrayHasKey('market_ratios', $categories);
    }

    public function test_zero_division_handling(): void
    {
        $asOfDate = Carbon::now();

        // Test with no data (should handle zero division gracefully)
        $ratios = $this->service->getLiquidityRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertEquals(0, $ratios['current_ratio']);
        $this->assertEquals(0, $ratios['quick_ratio']);
        $this->assertEquals(0, $ratios['cash_ratio']);
    }

    public function test_negative_values_handling(): void
    {
        $asOfDate = Carbon::now();

        // Create negative equity scenario
        $this->createJournalEntry('1111', *********, 0, $asOfDate); // Assets
        $this->createJournalEntry('2111', 0, 1********, $asOfDate);  // Liabilities > Assets

        $ratios = $this->service->getLeverageRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        // Should handle negative equity gracefully
        $this->assertIsNumeric($ratios['debt_to_equity']);
    }
}
