<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\WorkingCapitalAnalysisService;
use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WorkingCapitalAnalysisServiceTest extends TestCase
{
    use RefreshDatabase;

    protected WorkingCapitalAnalysisService $service;
    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new WorkingCapitalAnalysisService();
        $this->client = Client::factory()->create();
        $this->createTestAccounts();
        $this->createTestInventory();
    }

    protected function createTestAccounts(): void
    {
        $accounts = [
            ['account_code' => '1111', 'account_name' => 'Kas', 'account_type' => 'asset'],
            ['account_code' => '1121', 'account_name' => 'Piutang Usaha', 'account_type' => 'asset'],
            ['account_code' => '1131', 'account_name' => 'Persediaan', 'account_type' => 'asset'],
            ['account_code' => '1141', 'account_name' => 'Aset Tetap', 'account_type' => 'asset'],
            ['account_code' => '2111', 'account_name' => 'Hutang Usaha', 'account_type' => 'liability'],
            ['account_code' => '2121', 'account_name' => 'Hutang Bank Jangka Pendek', 'account_type' => 'liability'],
            ['account_code' => '2211', 'account_name' => 'Hutang Bank Jangka Panjang', 'account_type' => 'liability'],
            ['account_code' => '4111', 'account_name' => 'Pendapatan', 'account_type' => 'revenue'],
            ['account_code' => '5111', 'account_name' => 'HPP', 'account_type' => 'expense'],
        ];

        foreach ($accounts as $accountData) {
            Account::factory()->create([
                'client_id' => $this->client->id,
                'account_code' => $accountData['account_code'],
                'account_name' => $accountData['account_name'],
                'account_type' => $accountData['account_type'],
            ]);
        }
    }

    protected function createTestInventory(): void
    {
        $category = Category::factory()->create([
            'client_id' => $this->client->id,
            'category_name' => 'Electronics',
        ]);

        $product = Product::factory()->create([
            'client_id' => $this->client->id,
            'category_id' => $category->id,
            'product_name' => 'Laptop',
        ]);

        Inventory::factory()->create([
            'client_id' => $this->client->id,
            'product_id' => $product->id,
            'current_stock' => 100,
            'average_cost' => ********,
        ]);
    }

    protected function createJournalEntry(string $accountCode, float $debit, float $credit, Carbon $date): void
    {
        $account = Account::where('client_id', $this->client->id)
            ->where('account_code', $accountCode)
            ->first();

        $journal = Journal::factory()->create([
            'client_id' => $this->client->id,
            'journal_date' => $date,
            'is_posted' => true,
        ]);

        JournalEntry::factory()->create([
            'journal_id' => $journal->id,
            'account_id' => $account->id,
            'debit' => $debit,
            'credit' => $credit,
        ]);
    }

    public function test_get_working_capital_analysis(): void
    {
        $asOfDate = Carbon::now();

        // Create sample balance sheet data
        $this->createJournalEntry('1111', *********, 0, $asOfDate); // Cash
        $this->createJournalEntry('1121', *********, 0, $asOfDate); // Accounts Receivable
        $this->createJournalEntry('1131', ********0, 0, $asOfDate); // Inventory
        $this->createJournalEntry('2111', 0, *********, $asOfDate);  // Accounts Payable
        $this->createJournalEntry('2121', 0, ********, $asOfDate);   // Short-term Bank Loan

        $analysis = $this->service->getWorkingCapitalAnalysis($this->client->id, $asOfDate);

        $this->assertIsArray($analysis);
        $this->assertArrayHasKey('current_assets', $analysis);
        $this->assertArrayHasKey('current_liabilities', $analysis);
        $this->assertArrayHasKey('net_working_capital', $analysis);
        $this->assertArrayHasKey('working_capital_ratio', $analysis);

        // Current Assets = Cash + AR + Inventory = 200M + 150M + 100M = 450M
        $this->assertEquals(*********, $analysis['current_assets']);

        // Current Liabilities = AP + Short-term Loan = 180M + 70M = 250M
        $this->assertEquals(*********, $analysis['current_liabilities']);

        // Net Working Capital = Current Assets - Current Liabilities = 450M - 250M = 200M
        $this->assertEquals(*********, $analysis['net_working_capital']);

        // Working Capital Ratio = Current Assets / Current Liabilities = 450M / 250M = 1.8
        $this->assertEquals(1.8, $analysis['working_capital_ratio']);
    }

    public function test_get_cash_conversion_cycle(): void
    {
        $asOfDate = Carbon::now();
        $startDate = $asOfDate->copy()->subYear();

        // Create sample data for cash conversion cycle calculation
        $this->createJournalEntry('1121', *********, 0, $asOfDate); // Accounts Receivable
        $this->createJournalEntry('1131', ********0, 0, $asOfDate); // Inventory
        $this->createJournalEntry('2111', 0, *********, $asOfDate);  // Accounts Payable

        // Create revenue and COGS data
        $this->createJournalEntry('4111', 0, 1*********, $asOfDate); // Revenue
        $this->createJournalEntry('5111', *********, 0, $asOfDate);  // COGS

        $cycle = $this->service->getCashConversionCycle($this->client->id, $asOfDate);

        $this->assertIsArray($cycle);
        $this->assertArrayHasKey('days_sales_outstanding', $cycle);
        $this->assertArrayHasKey('days_inventory_outstanding', $cycle);
        $this->assertArrayHasKey('days_payable_outstanding', $cycle);
        $this->assertArrayHasKey('cash_conversion_cycle', $cycle);

        // All values should be numeric
        $this->assertIsNumeric($cycle['days_sales_outstanding']);
        $this->assertIsNumeric($cycle['days_inventory_outstanding']);
        $this->assertIsNumeric($cycle['days_payable_outstanding']);
        $this->assertIsNumeric($cycle['cash_conversion_cycle']);
    }

    public function test_get_liquidity_ratios(): void
    {
        $asOfDate = Carbon::now();

        // Create sample balance sheet data
        $this->createJournalEntry('1111', ********0, 0, $asOfDate); // Cash
        $this->createJournalEntry('1121', ********, 0, $asOfDate);  // Accounts Receivable
        $this->createJournalEntry('1131', ********, 0, $asOfDate);  // Inventory
        $this->createJournalEntry('2111', 0, *********, $asOfDate); // Accounts Payable
        $this->createJournalEntry('2121', 0, ********, $asOfDate);  // Short-term Loan

        $ratios = $this->service->getLiquidityRatios($this->client->id, $asOfDate);

        $this->assertIsArray($ratios);
        $this->assertArrayHasKey('current_ratio', $ratios);
        $this->assertArrayHasKey('quick_ratio', $ratios);
        $this->assertArrayHasKey('cash_ratio', $ratios);

        // Current Ratio = (100M + 80M + 60M) / (120M + 40M) = 240M / 160M = 1.5
        $this->assertEquals(1.5, $ratios['current_ratio']);

        // Quick Ratio = (100M + 80M) / 160M = 180M / 160M = 1.125
        $this->assertEquals(1.125, $ratios['quick_ratio']);

        // Cash Ratio = 100M / 160M = 0.625
        $this->assertEquals(0.625, $ratios['cash_ratio']);
    }

    public function test_get_working_capital_trends(): void
    {
        $asOfDate = Carbon::now();

        $trends = $this->service->getWorkingCapitalTrends($this->client->id, $asOfDate);

        $this->assertIsArray($trends);
        $this->assertCount(12, $trends); // Should return 12 months of data

        foreach ($trends as $trend) {
            $this->assertArrayHasKey('period', $trend);
            $this->assertArrayHasKey('current_assets', $trend);
            $this->assertArrayHasKey('current_liabilities', $trend);
            $this->assertArrayHasKey('working_capital', $trend);
            $this->assertArrayHasKey('working_capital_ratio', $trend);

            // All values should be numeric
            $this->assertIsNumeric($trend['current_assets']);
            $this->assertIsNumeric($trend['current_liabilities']);
            $this->assertIsNumeric($trend['working_capital']);
            $this->assertIsNumeric($trend['working_capital_ratio']);
        }
    }

    public function test_get_working_capital_components(): void
    {
        $asOfDate = Carbon::now();

        // Create sample balance sheet data
        $this->createJournalEntry('1111', *********, 0, $asOfDate); // Cash
        $this->createJournalEntry('1121', ********0, 0, $asOfDate); // Accounts Receivable
        $this->createJournalEntry('1131', ********, 0, $asOfDate);  // Inventory
        $this->createJournalEntry('2111', 0, 1********, $asOfDate); // Accounts Payable
        $this->createJournalEntry('2121', 0, ********, $asOfDate);  // Short-term Loan

        $components = $this->service->getWorkingCapitalComponents($this->client->id, $asOfDate);

        $this->assertIsArray($components);
        $this->assertArrayHasKey('current_assets', $components);
        $this->assertArrayHasKey('current_liabilities', $components);
        $this->assertArrayHasKey('net_working_capital', $components);

        // Check current assets structure
        $currentAssets = $components['current_assets'];
        $this->assertArrayHasKey('total', $currentAssets);
        $this->assertArrayHasKey('breakdown', $currentAssets);

        // Check current liabilities structure
        $currentLiabilities = $components['current_liabilities'];
        $this->assertArrayHasKey('total', $currentLiabilities);
        $this->assertArrayHasKey('breakdown', $currentLiabilities);

        // Verify totals
        $this->assertEquals(*********, $currentAssets['total']); // 150M + 100M + 80M
        $this->assertEquals(*********, $currentLiabilities['total']); // 140M + 60M
        $this->assertEquals(*********, $components['net_working_capital']); // 330M - 200M
    }

    public function test_analyze_working_capital_comprehensive(): void
    {
        $asOfDate = Carbon::now();

        // Create comprehensive sample data
        $this->createJournalEntry('1111', *********, 0, $asOfDate); // Cash
        $this->createJournalEntry('1121', *********, 0, $asOfDate); // Accounts Receivable
        $this->createJournalEntry('1131', ********0, 0, $asOfDate); // Inventory
        $this->createJournalEntry('2111', 0, *********, $asOfDate);  // Accounts Payable
        $this->createJournalEntry('2121', 0, ********, $asOfDate);   // Short-term Loan

        $analysis = $this->service->analyzeWorkingCapital($this->client->id, $asOfDate);

        $this->assertIsArray($analysis);
        $this->assertArrayHasKey('analysis_date', $analysis);
        $this->assertArrayHasKey('working_capital_summary', $analysis);
        $this->assertArrayHasKey('liquidity_analysis', $analysis);
        $this->assertArrayHasKey('cash_conversion_cycle', $analysis);
        $this->assertArrayHasKey('component_analysis', $analysis);
        $this->assertArrayHasKey('trend_analysis', $analysis);
        $this->assertArrayHasKey('recommendations', $analysis);

        // Check working capital summary
        $summary = $analysis['working_capital_summary'];
        $this->assertArrayHasKey('current_assets', $summary);
        $this->assertArrayHasKey('current_liabilities', $summary);
        $this->assertArrayHasKey('net_working_capital', $summary);
        $this->assertArrayHasKey('working_capital_ratio', $summary);
    }

    public function test_zero_division_handling(): void
    {
        $asOfDate = Carbon::now();

        // Test with no current liabilities (should handle zero division)
        $this->createJournalEntry('1111', ********0, 0, $asOfDate); // Cash only

        $analysis = $this->service->getWorkingCapitalAnalysis($this->client->id, $asOfDate);

        $this->assertIsArray($analysis);
        $this->assertEquals(0, $analysis['working_capital_ratio']); // Should be 0 when no current liabilities
    }

    public function test_negative_working_capital(): void
    {
        $asOfDate = Carbon::now();

        // Create scenario with negative working capital
        $this->createJournalEntry('1111', ********, 0, $asOfDate);  // Cash
        $this->createJournalEntry('2111', 0, ********0, $asOfDate); // Accounts Payable
        $this->createJournalEntry('2121', 0, ********, $asOfDate);  // Short-term Loan

        $analysis = $this->service->getWorkingCapitalAnalysis($this->client->id, $asOfDate);

        $this->assertIsArray($analysis);
        $this->assertEquals(********, $analysis['current_assets']);
        $this->assertEquals(*********, $analysis['current_liabilities']);
        $this->assertEquals(-*********, $analysis['net_working_capital']); // Negative working capital
        $this->assertLessThan(1, $analysis['working_capital_ratio']); // Ratio less than 1
    }

    public function test_empty_data_handling(): void
    {
        $asOfDate = Carbon::now();

        // Test with no journal entries
        $analysis = $this->service->getWorkingCapitalAnalysis($this->client->id, $asOfDate);

        $this->assertIsArray($analysis);
        $this->assertEquals(0, $analysis['current_assets']);
        $this->assertEquals(0, $analysis['current_liabilities']);
        $this->assertEquals(0, $analysis['net_working_capital']);
        $this->assertEquals(0, $analysis['working_capital_ratio']);
    }
}
