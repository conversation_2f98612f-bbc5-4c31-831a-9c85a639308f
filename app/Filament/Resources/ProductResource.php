<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Produk';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Basic Information Section
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('product_code')
                                    ->label('Kode Produk')
                                    ->required()
                                    ->maxLength(50)
                                    ->unique(ignoreRecord: true),
                                Forms\Components\TextInput::make('product_name')
                                    ->label('Nama Produk')
                                    ->required()
                                    ->maxLength(200),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('category_id')
                                    ->label('Kategori')
                                    ->relationship(
                                        name: 'category',
                                        titleAttribute: 'category_name',
                                        modifyQueryUsing: fn($query) => $query
                                            ->where('client_id', Auth::user()?->client_id)
                                            ->where('is_active', true)
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->native(false)
                                    ->createOptionForm([
                                        Forms\Components\Hidden::make('client_id')
                                            ->default(fn() => Auth::user()?->client_id),
                                        Forms\Components\TextInput::make('category_code')
                                            ->label('Kode Kategori')
                                            ->required()
                                            ->maxLength(20),
                                        Forms\Components\TextInput::make('category_name')
                                            ->label('Nama Kategori')
                                            ->required()
                                            ->maxLength(100),
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        $data['client_id'] = Auth::user()?->client_id;
                                        $data['is_active'] = true;
                                        $data['sort_order'] = 0;
                                        $category = \App\Models\ProductCategory::create($data);
                                        return $category->id;
                                    })
                                    ->createOptionAction(function ($action) {
                                        return $action
                                            ->modalHeading('Buat Kategori Baru')
                                            ->modalSubmitActionLabel('Buat Kategori')
                                            ->modalWidth('md');
                                    })
                                    ->required(),
                                Forms\Components\Select::make('unit_id')
                                    ->label('Satuan')
                                    ->relationship(
                                        name: 'unit',
                                        titleAttribute: 'unit_name',
                                        modifyQueryUsing: fn($query) => $query
                                            ->where('client_id', Auth::user()?->client_id)
                                            ->where('is_active', true)
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->native(false)
                                    ->createOptionForm([
                                        Forms\Components\Hidden::make('client_id')
                                            ->default(fn() => Auth::user()?->client_id),
                                        Forms\Components\TextInput::make('unit_code')
                                            ->label('Kode Satuan')
                                            ->required()
                                            ->maxLength(10),
                                        Forms\Components\TextInput::make('unit_name')
                                            ->label('Nama Satuan')
                                            ->required()
                                            ->maxLength(50),
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        $data['client_id'] = Auth::user()?->client_id;
                                        $data['is_active'] = true;
                                        $data['conversion_factor'] = 1.0;
                                        $data['is_base_unit'] = false;
                                        $unit = \App\Models\UnitOfMeasure::create($data);
                                        return $unit->id;
                                    })
                                    ->createOptionAction(function ($action) {
                                        return $action
                                            ->modalHeading('Buat Satuan Baru')
                                            ->modalSubmitActionLabel('Buat Satuan')
                                            ->modalWidth('md');
                                    })
                                    ->required(),
                            ]),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                // Pricing Section
                Forms\Components\Section::make('Harga')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('standard_cost')
                                    ->label('Harga Pokok')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),
                                Forms\Components\TextInput::make('selling_price')
                                    ->label('Harga Jual')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),
                                Forms\Components\TextInput::make('purchase_price')
                                    ->label('Harga Beli')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),
                            ]),
                    ]),

                // Inventory Section
                Forms\Components\Section::make('Stok & Inventory')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('minimum_stock')
                                    ->label('Stok Minimum')
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('maximum_stock')
                                    ->label('Stok Maksimum')
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('reorder_point')
                                    ->label('Titik Reorder')
                                    ->numeric()
                                    ->default(0),
                            ]),
                    ]),

                // Additional Information Section
                Forms\Components\Section::make('Informasi Tambahan')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('barcode')
                                    ->label('Barcode')
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('brand')
                                    ->label('Merek')
                                    ->maxLength(100),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('weight')
                                    ->label('Berat (kg)')
                                    ->numeric()
                                    ->step(0.01),
                                Forms\Components\TextInput::make('dimensions')
                                    ->label('Dimensi')
                                    ->placeholder('contoh: 30x20x10 cm')
                                    ->maxLength(100),
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed(),

                // Status Section
                Forms\Components\Section::make('Status')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Aktif')
                                    ->default(true),
                                Forms\Components\Toggle::make('is_saleable')
                                    ->label('Dapat Dijual')
                                    ->default(true),
                                Forms\Components\Toggle::make('is_purchasable')
                                    ->label('Dapat Dibeli')
                                    ->default(true),
                                Forms\Components\Toggle::make('is_manufactured')
                                    ->label('Dapat Dimanufaktur')
                                    ->default(false)
                                    ->helperText('Centang jika produk ini bisa diproduksi/dirakit'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn($query) => $query->where('client_id', Auth::user()?->client_id))
            ->columns([
                Tables\Columns\TextColumn::make('category.category_name')
                    ->label('Kategori')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('unit.unit_name')
                    ->label('Satuan')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('product_code')
                    ->label('Kode Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product_name')
                    ->label('Nama Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product_type')
                    ->label('Jenis Produk')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'raw_material' => 'info',
                        'finished_good' => 'success',
                        'semi_finished' => 'warning',
                        'service' => 'gray',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('standard_cost')
                    ->label('Harga Pokok')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->label('Harga Jual')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_stock')
                    ->label('Stok Min')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('maximum_stock')
                    ->label('Stok Max')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('reorder_point')
                    ->label('Reorder Point')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\IconColumn::make('is_stockable')
                    ->label('Stok')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('is_saleable')
                    ->label('Dijual')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
