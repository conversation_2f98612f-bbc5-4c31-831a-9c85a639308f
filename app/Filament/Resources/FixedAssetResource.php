<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FixedAssetResource\Pages;
use App\Filament\Resources\FixedAssetResource\RelationManagers;
use App\Models\FixedAsset;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FixedAssetResource extends Resource
{
    protected static ?string $model = FixedAsset::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Aset';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Aset Tetap';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\TextInput::make('asset_code')
                    ->label('Kode Aset')
                    ->required()
                    ->maxLength(50),
                Forms\Components\TextInput::make('asset_name')
                    ->label('Nama Aset')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('asset_category')
                    ->label('Kategori Aset')
                    ->required(),
                Forms\Components\TextInput::make('asset_type')
                    ->label('Tipe Aset')
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('manufacturer')
                    ->label('Produsen')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('model')
                    ->label('Model')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('serial_number')
                    ->label('Nomor Seri')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('barcode')
                    ->label('Barcode')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('purchase_date')
                    ->label('Tanggal Pembelian')
                    ->required(),
                Forms\Components\TextInput::make('acquisition_cost')
                    ->label('Biaya Akuisisi')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('installation_cost')
                    ->label('Biaya Instalasi')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('other_costs')
                    ->label('Biaya Lainnya')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('total_cost')
                    ->label('Total Biaya')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('supplier_id')
                    ->relationship('supplier', 'supplier_name')
                    ->label('Pemasok')
                    ->default(null),
                Forms\Components\Select::make('purchase_order_id')
                    ->relationship('purchaseOrder', 'po_number')
                    ->label('Pesanan Pembelian')
                    ->default(null),
                Forms\Components\TextInput::make('invoice_number')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('warranty_start_date'),
                Forms\Components\DatePicker::make('warranty_end_date'),
                Forms\Components\Select::make('location_id')
                    ->relationship('location', 'id')
                    ->required(),
                Forms\Components\Select::make('department_id')
                    ->relationship('department', 'id')
                    ->required(),
                Forms\Components\Select::make('cost_center_id')
                    ->relationship('costCenter', 'id')
                    ->default(null),
                Forms\Components\TextInput::make('responsible_person')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('condition')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('depreciation_method')
                    ->required(),
                Forms\Components\TextInput::make('useful_life_years')
                    ->label('Masa Manfaat (Tahun)')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('useful_life_units')
                    ->label('Masa Manfaat (Unit)')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('salvage_value')
                    ->label('Nilai Sisa')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('depreciation_rate')
                    ->label('Tingkat Depresiasi')
                    ->required()
                    ->numeric()
                    ->default(0.000000),
                Forms\Components\TextInput::make('accumulated_depreciation')
                    ->label('Akumulasi Depresiasi')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('book_value')
                    ->label('Nilai Buku')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('fair_value')
                    ->label('Nilai Wajar')
                    ->numeric()
                    ->default(null),
                Forms\Components\DatePicker::make('last_revaluation_date')
                    ->label('Tanggal Revaluasi Terakhir'),
                Forms\Components\TextInput::make('insurance_policy')
                    ->label('Polis Asuransi')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('insurance_value')
                    ->label('Nilai Asuransi')
                    ->numeric()
                    ->default(null),
                Forms\Components\DatePicker::make('insurance_expiry')
                    ->label('Kedaluwarsa Asuransi'),
                Forms\Components\TextInput::make('maintenance_schedule')
                    ->label('Jadwal Pemeliharaan')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('last_maintenance_date')
                    ->label('Tanggal Pemeliharaan Terakhir'),
                Forms\Components\DatePicker::make('next_maintenance_date')
                    ->label('Tanggal Pemeliharaan Berikutnya'),
                Forms\Components\DatePicker::make('disposal_date')
                    ->label('Tanggal Pelepasan'),
                Forms\Components\TextInput::make('disposal_method')
                    ->label('Metode Pelepasan'),
                Forms\Components\TextInput::make('disposal_value')
                    ->label('Nilai Pelepasan')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('disposal_gain_loss')
                    ->label('Keuntungan/Kerugian Pelepasan')
                    ->numeric()
                    ->default(null),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->required(),
                Forms\Components\TextInput::make('created_by')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('asset_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('asset_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('asset_category'),
                Tables\Columns\TextColumn::make('asset_type'),
                Tables\Columns\TextColumn::make('manufacturer')
                    ->searchable(),
                Tables\Columns\TextColumn::make('model')
                    ->searchable(),
                Tables\Columns\TextColumn::make('serial_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('barcode')
                    ->searchable(),
                Tables\Columns\TextColumn::make('purchase_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('acquisition_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('installation_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('other_costs')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('supplier.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('purchaseOrder.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('warranty_start_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warranty_end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('location.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('department.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('costCenter.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('responsible_person')
                    ->searchable(),
                Tables\Columns\TextColumn::make('condition'),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('depreciation_method'),
                Tables\Columns\TextColumn::make('useful_life_years')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('useful_life_units')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('salvage_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('depreciation_rate')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('accumulated_depreciation')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('book_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fair_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_revaluation_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('insurance_policy')
                    ->searchable(),
                Tables\Columns\TextColumn::make('insurance_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('insurance_expiry')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('maintenance_schedule')
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_maintenance_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('next_maintenance_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('disposal_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('disposal_method'),
                Tables\Columns\TextColumn::make('disposal_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('disposal_gain_loss')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFixedAssets::route('/'),
            'create' => Pages\CreateFixedAsset::route('/create'),
            'edit' => Pages\EditFixedAsset::route('/{record}/edit'),
        ];
    }
}
