<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionResource\Pages;
use App\Filament\Resources\TransactionResource\RelationManagers;
use App\Models\Transaction;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;

use Filament\Tables\Columns\TextColumn;

use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;
use Filament\Tables;

class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';

    protected static ?string $navigationGroup = 'Keuangan';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Transaksi';

    protected static ?string $modelLabel = 'Transaksi';

    protected static ?string $pluralModelLabel = 'Transaksi';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Dasar')
                    ->description('Informasi umum transaksi')
                    ->schema([
                        Select::make('type')
                            ->options(Transaction::getTransactionTypes())
                            ->required()
                            ->live()
                            ->label('Jenis Transaksi')
                            ->helperText('Pilih jenis transaksi yang akan dibuat'),

                        DatePicker::make('transaction_date')
                            ->required()
                            ->default(now())
                            ->label('Tanggal Transaksi'),

                        TextInput::make('reference_number')
                            ->maxLength(255)
                            ->label('Nomor Referensi')
                            ->helperText('Nomor invoice, kwitansi, atau referensi lainnya'),

                        Textarea::make('description')
                            ->required()
                            ->rows(2)
                            ->label('Deskripsi')
                            ->helperText('Jelaskan tujuan atau detail transaksi'),

                        Select::make('status')
                            ->options(Transaction::getTransactionStatuses())
                            ->default('pending')
                            ->required()
                            ->label('Status')
                            ->disabled(fn() => Auth::user()?->user_type === 'user'),
                    ])
                    ->columns(2),

                // Customer section for sales only
                Section::make('Informasi Pelanggan')
                    ->description('Data pelanggan untuk transaksi penjualan')
                    ->schema([
                        Select::make('customer_id')
                            ->relationship('customer', 'customer_name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Pelanggan')
                            ->helperText('Pilih pelanggan untuk transaksi penjualan'),
                    ])
                    ->visible(fn($get) => $get('type') === 'sales')
                    ->columns(1),

                // Product details for sales transactions
                Section::make('Detail Produk')
                    ->description('Daftar produk yang dijual')
                    ->schema([
                        Repeater::make('items')
                            ->relationship()
                            ->live()
                            ->afterStateUpdated(function (callable $get, callable $set) {
                                // Update subtotal when items are added/removed
                                self::updateSubtotal($get, $set);
                            })
                            ->schema([
                                Select::make('product_id')
                                    ->relationship('product', 'product_name')
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $product = \App\Models\Product::find($state);
                                            if ($product) {
                                                $set('unit_id', $product->unit_id);
                                                $set('unit_price', $product->selling_price);
                                                $set('description', $product->product_name);
                                            }
                                        }
                                    })
                                    ->label('Produk')
                                    ->columnSpan(2),

                                TextInput::make('description')
                                    ->label('Deskripsi')
                                    ->columnSpan(2),

                                TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->default(1)
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        $unitPrice = $get('unit_price') ?? 0;
                                        $lineTotal = $state * $unitPrice;
                                        $set('line_total', $lineTotal);

                                        // Update subtotal
                                        self::updateSubtotal($get, $set);
                                    })
                                    ->label('Jumlah')
                                    ->columnSpan(1),

                                Select::make('unit_id')
                                    ->relationship('unit', 'unit_name')
                                    ->required()
                                    ->label('Satuan')
                                    ->columnSpan(1),

                                TextInput::make('unit_price')
                                    ->required()
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                        $quantity = $get('quantity') ?? 0;
                                        $lineTotal = $quantity * $state;
                                        $set('line_total', $lineTotal);

                                        // Update subtotal
                                        self::updateSubtotal($get, $set);
                                    })
                                    ->label('Harga Satuan')
                                    ->columnSpan(1),

                                TextInput::make('line_total')
                                    ->required()
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated()
                                    ->label('Total Baris')
                                    ->columnSpan(1),
                            ])
                            ->columns(6)
                            ->defaultItems(1)
                            ->addActionLabel('Tambah Produk')
                            ->label('Item Produk')
                            ->reorderable(false)
                            ->collapsible(),
                    ])
                    ->visible(fn($get) => $get('type') === 'sales'),

                // Payment details for sales transactions
                Section::make('Detail Pembayaran')
                    ->description('Informasi pembayaran dan total transaksi')
                    ->schema([
                        TextInput::make('subtotal')
                            ->numeric()
                            ->prefix('Rp')
                            ->label('Subtotal')
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Otomatis dihitung dari total item'),

                        TextInput::make('discount_amount')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0)
                            ->live()
                            ->afterStateUpdated(function (callable $get, callable $set) {
                                // Update total when discount changes
                                self::updateSubtotal($get, $set);
                            })
                            ->label('Diskon')
                            ->helperText('Masukkan jumlah diskon jika ada'),

                        TextInput::make('tax_amount')
                            ->numeric()
                            ->prefix('Rp')
                            ->label('Pajak (PPN)')
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Otomatis dihitung berdasarkan subtotal'),

                        TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->prefix('Rp')
                            ->label('Total Akhir')
                            ->disabled()
                            ->dehydrated()
                            ->helperText('Total yang harus dibayar'),

                        Select::make('payment_method')
                            ->options(Transaction::getPaymentMethods())
                            ->label('Metode Pembayaran')
                            ->helperText('Cara pembayaran yang digunakan'),

                        Select::make('payment_status')
                            ->options(Transaction::getPaymentStatuses())
                            ->default('unpaid')
                            ->label('Status Pembayaran')
                            ->helperText('Status pembayaran saat ini'),

                        Textarea::make('notes')
                            ->rows(2)
                            ->label('Catatan Pembayaran')
                            ->helperText('Catatan tambahan tentang pembayaran')
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->visible(fn($get) => $get('type') === 'sales'),

                // For non-sales transactions, show simple amount field
                Section::make('Detail Transaksi')
                    ->description('Jumlah dan detail transaksi')
                    ->schema([
                        TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->prefix('Rp')
                            ->label('Jumlah Transaksi')
                            ->helperText('Masukkan total jumlah transaksi'),

                        Textarea::make('notes')
                            ->rows(3)
                            ->label('Catatan')
                            ->helperText('Catatan tambahan untuk transaksi ini')
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->visible(fn($get) => $get('type') !== 'sales'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('transaction_date')
                    ->date('d/m/Y')
                    ->sortable()
                    ->label('Tanggal'),

                TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'sales' => 'success',
                        'purchase' => 'warning',
                        'expense' => 'danger',
                        'asset_acquisition' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => Transaction::getTransactionTypes()[$state] ?? $state)
                    ->label('Jenis'),

                TextColumn::make('customer.customer_name')
                    ->label('Pelanggan')
                    ->searchable()
                    ->toggleable()
                    ->visible(fn($record) => $record?->type === 'sales'),

                TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    })
                    ->label('Deskripsi'),

                TextColumn::make('payment_method')
                    ->badge()
                    ->formatStateUsing(fn(?string $state): string => $state ? (Transaction::getPaymentMethods()[$state] ?? $state) : '-')
                    ->label('Metode Bayar')
                    ->toggleable()
                    ->visible(fn($record) => $record?->type === 'sales'),

                TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'paid' => 'success',
                        'partial' => 'warning',
                        'unpaid' => 'danger',
                        'overpaid' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => Transaction::getPaymentStatuses()[$state] ?? $state)
                    ->label('Status Bayar')
                    ->toggleable()
                    ->visible(fn($record) => $record?->type === 'sales'),

                TextColumn::make('amount')
                    ->money('IDR')
                    ->sortable()
                    ->label('Total'),

                TextColumn::make('reference_number')
                    ->searchable()
                    ->label('Referensi'),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'processed' => 'success',
                        'rejected' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => Transaction::getTransactionStatuses()[$state] ?? $state)
                    ->label('Status'),



                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Dibuat Pada'),
            ])
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('type')
                    ->options(Transaction::getTransactionTypes())
                    ->label('Jenis Transaksi'),
                SelectFilter::make('status')
                    ->options(Transaction::getTransactionStatuses())
                    ->label('Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('process')
                    ->label('Proses')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(fn(Transaction $record) => $record->update(['status' => 'processed']))
                    ->requiresConfirmation()
                    ->visible(fn(Transaction $record) => $record->status === 'pending' && Auth::user()?->user_type !== 'user'),
                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->action(fn(Transaction $record) => $record->update(['status' => 'rejected']))
                    ->requiresConfirmation()
                    ->visible(fn(Transaction $record) => $record->status === 'pending' && Auth::user()?->user_type !== 'user'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canViewAny(): bool
    {
        return Auth::check();
    }

    /**
     * Update subtotal, tax, and total amount based on items
     */
    protected static function updateSubtotal(callable $get, callable $set): void
    {
        // Get all items
        $items = $get('items') ?? [];

        // Calculate subtotal from all line totals
        $subtotal = 0;
        foreach ($items as $item) {
            $lineTotal = $item['line_total'] ?? 0;
            $subtotal += $lineTotal;
        }

        // Set subtotal
        $set('subtotal', $subtotal);

        // Get discount amount
        $discountAmount = $get('discount_amount') ?? 0;

        // Calculate tax (PPN 11% from subtotal after discount)
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * 0.11; // 11% PPN
        $set('tax_amount', $taxAmount);

        // Calculate final total
        $finalTotal = $subtotal - $discountAmount + $taxAmount;
        $set('amount', $finalTotal);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            'create' => Pages\CreateTransaction::route('/create'),
            'edit' => Pages\EditTransaction::route('/{record}/edit'),
        ];
    }
}
