<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JournalResource\Pages;
use App\Models\Journal;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;
use Filament\Tables;

class JournalResource extends Resource
{
    protected static ?string $model = Journal::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Keuangan';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Jurnal Entry';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        $user = Auth::user();
        if ($user && $user->client_id) {
            $query->where('client_id', $user->client_id);
        }

        return $query;
    }



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('journal_date')
                    ->required()
                    ->default(now())
                    ->label('Tanggal Jurnal'),

                Textarea::make('description')
                    ->required()
                    ->rows(3)
                    ->label('Deskripsi'),

                Select::make('reference_transaction_id')
                    ->relationship('referenceTransaction', 'description', function ($query) {
                        $user = Auth::user();
                        if ($user && $user->client_id) {
                            $query->where('client_id', $user->client_id);
                        }
                        return $query->where('status', 'processed');
                    })
                    ->searchable()
                    ->preload()
                    ->label('Transaksi Referensi')
                    ->placeholder('Pilih transaksi (opsional)'),

                Select::make('client_id')
                    ->relationship('client', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Klien')
                    ->visible(fn() => false) // Hide for now
                    ->required(fn() => false),

                Repeater::make('journalEntries')
                    ->relationship()
                    ->schema([
                        Select::make('account_id')
                            ->relationship('account', 'account_name', function ($query) {
                                $user = Auth::user();
                                if ($user && $user->client_id) {
                                    $query->where('client_id', $user->client_id);
                                }
                                return $query->where('is_active', true)->orderBy('account_code');
                            })
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Akun'),

                        TextInput::make('debit')
                            ->numeric()
                            ->default(0)
                            ->prefix('Rp')
                            ->label('Debit'),

                        TextInput::make('credit')
                            ->numeric()
                            ->default(0)
                            ->prefix('Rp')
                            ->label('Kredit'),

                        Textarea::make('description')
                            ->rows(2)
                            ->label('Deskripsi Entry'),
                    ])
                    ->columns(2)
                    ->defaultItems(2)
                    ->addActionLabel('Tambah Jurnal Entry')
                    ->label('Jurnal Entries'),

                Toggle::make('is_posted')
                    ->label('Diposting')
                    ->disabled(fn($record) => $record && $record->is_posted)
                    ->helperText('Setelah diposting, jurnal tidak dapat dimodifikasi'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                TextColumn::make('journal_date')
                    ->label('Tanggal')
                    ->date('d/m/Y')
                    ->sortable(),

                TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->searchable(),

                TextColumn::make('journal_entries_count')
                    ->label('Jumlah Entry')
                    ->counts('journalEntries')
                    ->badge(),

                TextColumn::make('total_debit')
                    ->label('Total Debit')
                    ->getStateUsing(function ($record) {
                        return $record->journalEntries()->sum('debit');
                    })
                    ->money('IDR')
                    ->alignEnd(),

                TextColumn::make('total_credit')
                    ->label('Total Kredit')
                    ->getStateUsing(function ($record) {
                        return $record->journalEntries()->sum('credit');
                    })
                    ->money('IDR')
                    ->alignEnd(),

                IconColumn::make('is_posted')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                TextColumn::make('posted_at')
                    ->label('Tanggal Posting')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('is_posted')
                    ->label('Status')
                    ->options([
                        1 => 'Posted',
                        0 => 'Draft',
                    ]),

                Tables\Filters\Filter::make('journal_date')
                    ->form([
                        DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('journal_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('journal_date', '<=', $date),
                            );
                    }),

                TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => !$record->is_posted),
                Tables\Actions\Action::make('post')
                    ->label('Post')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn($record) => !$record->is_posted)
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update([
                            'is_posted' => true,
                            'posted_at' => now(),
                        ]);
                    }),
                Tables\Actions\Action::make('unpost')
                    ->label('Unpost')
                    ->icon('heroicon-o-x-mark')
                    ->color('warning')
                    ->visible(fn($record) => $record->is_posted)
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update([
                            'is_posted' => false,
                            'posted_at' => null,
                        ]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('journal_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJournals::route('/'),
            'create' => Pages\CreateJournal::route('/create'),
            'edit' => Pages\EditJournal::route('/{record}/edit'),
        ];
    }
}
