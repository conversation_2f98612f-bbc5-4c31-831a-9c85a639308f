<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SystemNotificationResource\Pages;
use App\Models\SystemNotification;
use App\Services\NotificationService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class SystemNotificationResource extends Resource
{
    protected static ?string $model = SystemNotification::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell';

    protected static ?string $navigationLabel = 'Notifikasi';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Notifikasi')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Tipe')
                            ->options(SystemNotification::getTypes())
                            ->required(),

                        Forms\Components\TextInput::make('title')
                            ->label('Judul')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('message')
                            ->label('Pesan')
                            ->required()
                            ->rows(3),

                        Forms\Components\Select::make('priority')
                            ->label('Prioritas')
                            ->options(SystemNotification::getPriorities())
                            ->default(SystemNotification::PRIORITY_NORMAL)
                            ->required(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(SystemNotification::getStatuses())
                            ->default(SystemNotification::STATUS_UNREAD)
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Target & Referensi')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('User Spesifik')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->placeholder('Kosongkan untuk semua user'),

                        Forms\Components\TextInput::make('reference_type')
                            ->label('Tipe Referensi')
                            ->placeholder('Contoh: App\\Models\\PurchaseOrder'),

                        Forms\Components\TextInput::make('reference_id')
                            ->label('ID Referensi')
                            ->numeric(),

                        Forms\Components\DateTimePicker::make('expires_at')
                            ->label('Kadaluarsa Pada'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\IconColumn::make('type')
                    ->label('')
                    ->icon(fn(SystemNotification $record): string => $record->getTypeIcon())
                    ->color(fn(SystemNotification $record): string => $record->getPriorityColor())
                    ->size('lg'),

                Tables\Columns\TextColumn::make('title')
                    ->label('Judul')
                    ->searchable()
                    ->weight('bold')
                    ->color(fn(SystemNotification $record): string => $record->isUnread() ? 'primary' : 'gray'),

                Tables\Columns\TextColumn::make('message')
                    ->label('Pesan')
                    ->limit(50)
                    ->tooltip(function (SystemNotification $record): string {
                        return $record->message;
                    }),

                Tables\Columns\TextColumn::make('type')
                    ->label('Tipe')
                    ->formatStateUsing(fn(string $state): string => SystemNotification::getTypes()[$state] ?? $state)
                    ->badge(),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Prioritas')
                    ->formatStateUsing(fn(string $state): string => SystemNotification::getPriorities()[$state] ?? $state)
                    ->badge()
                    ->color(fn(SystemNotification $record): string => $record->getPriorityColor()),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn(string $state): string => SystemNotification::getStatuses()[$state] ?? $state)
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'unread' => 'warning',
                        'read' => 'success',
                        'dismissed' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->placeholder('Semua User'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('expires_at')
                    ->label('Kadaluarsa')
                    ->dateTime()
                    ->placeholder('-')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Tipe')
                    ->options(SystemNotification::getTypes()),

                Tables\Filters\SelectFilter::make('priority')
                    ->label('Prioritas')
                    ->options(SystemNotification::getPriorities()),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(SystemNotification::getStatuses()),

                Tables\Filters\Filter::make('unread_only')
                    ->label('Hanya Belum Dibaca')
                    ->query(fn(Builder $query): Builder => $query->unread()),

                Tables\Filters\Filter::make('not_expired')
                    ->label('Belum Kadaluarsa')
                    ->query(fn(Builder $query): Builder => $query->notExpired()),
            ])
            ->actions([
                Tables\Actions\Action::make('mark_as_read')
                    ->label('Tandai Dibaca')
                    ->icon('heroicon-o-eye')
                    ->color('success')
                    ->visible(fn(SystemNotification $record): bool => $record->isUnread())
                    ->action(function (SystemNotification $record) {
                        $record->markAsRead();
                        \Filament\Notifications\Notification::make()
                            ->title('Notifikasi ditandai sebagai dibaca')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('dismiss')
                    ->label('Abaikan')
                    ->icon('heroicon-o-x-mark')
                    ->color('gray')
                    ->visible(fn(SystemNotification $record): bool => !$record->isDismissed())
                    ->action(function (SystemNotification $record) {
                        $record->markAsDismissed();
                        \Filament\Notifications\Notification::make()
                            ->title('Notifikasi diabaikan')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('mark_all_read')
                        ->label('Tandai Semua Dibaca')
                        ->icon('heroicon-o-eye')
                        ->color('success')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            $records->each(fn(SystemNotification $record) => $record->markAsRead());
                            \Filament\Notifications\Notification::make()
                                ->title('Semua notifikasi ditandai sebagai dibaca')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('dismiss_all')
                        ->label('Abaikan Semua')
                        ->icon('heroicon-o-x-mark')
                        ->color('gray')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            $records->each(fn(SystemNotification $record) => $record->markAsDismissed());
                            \Filament\Notifications\Notification::make()
                                ->title('Semua notifikasi diabaikan')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSystemNotifications::route('/'),
            'create' => Pages\CreateSystemNotification::route('/create'),
            'edit' => Pages\EditSystemNotification::route('/{record}/edit'),
        ];
    }
}
