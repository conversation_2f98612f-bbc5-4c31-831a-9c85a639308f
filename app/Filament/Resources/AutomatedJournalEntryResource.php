<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AutomatedJournalEntryResource\Pages;
use App\Models\AutomatedJournalEntry;
use App\Models\Account;
use App\Models\FixedAsset;
use App\Services\AutomatedJournalService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class AutomatedJournalEntryResource extends Resource
{
    protected static ?string $model = AutomatedJournalEntry::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Jurnal Otomatis';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\Select::make('entry_type')
                            ->label('Tipe Jurnal')
                            ->options(AutomatedJournalEntry::getEntryTypes())
                            ->required()
                            ->live()
                            ->afterStateUpdated(fn(Forms\Set $set) => $set('configuration', [])),

                        Forms\Components\TextInput::make('name')
                            ->label('Nama')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                    ])->columns(2),

                Forms\Components\Section::make('Jadwal Eksekusi')
                    ->schema([
                        Forms\Components\Select::make('frequency')
                            ->label('Frekuensi')
                            ->options(AutomatedJournalEntry::getFrequencies())
                            ->required(),

                        Forms\Components\DatePicker::make('start_date')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->default(now()),

                        Forms\Components\DatePicker::make('end_date')
                            ->label('Tanggal Berakhir')
                            ->after('start_date'),

                        Forms\Components\DatePicker::make('next_run_date')
                            ->label('Tanggal Eksekusi Berikutnya')
                            ->default(now()),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('entry_type')
                    ->label('Tipe')
                    ->formatStateUsing(fn(string $state): string => AutomatedJournalEntry::getEntryTypes()[$state] ?? $state)
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'depreciation' => 'warning',
                        'accrual' => 'info',
                        'allocation' => 'success',
                        'recurring' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('frequency')
                    ->label('Frekuensi')
                    ->formatStateUsing(fn(?string $state): string => $state ? AutomatedJournalEntry::getFrequencies()[$state] : '-'),

                Tables\Columns\TextColumn::make('next_run_date')
                    ->label('Eksekusi Berikutnya')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('last_run_date')
                    ->label('Eksekusi Terakhir')
                    ->date()
                    ->placeholder('-')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('entry_type')
                    ->label('Tipe Jurnal')
                    ->options(AutomatedJournalEntry::getEntryTypes()),

                Tables\Filters\SelectFilter::make('frequency')
                    ->label('Frekuensi')
                    ->options(AutomatedJournalEntry::getFrequencies()),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
            ])
            ->actions([
                Tables\Actions\Action::make('execute')
                    ->label('Eksekusi')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn(AutomatedJournalEntry $record): bool => $record->isDueForExecution())
                    ->requiresConfirmation()
                    ->action(function (AutomatedJournalEntry $record) {
                        $service = app(AutomatedJournalService::class);
                        try {
                            $journal = $service->executeEntry($record);
                            \Filament\Notifications\Notification::make()
                                ->title('Jurnal berhasil dibuat')
                                ->body("Jurnal {$journal->journal_number} telah dibuat.")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Gagal membuat jurnal')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAutomatedJournalEntries::route('/'),
            'create' => Pages\CreateAutomatedJournalEntry::route('/create'),
            'edit' => Pages\EditAutomatedJournalEntry::route('/{record}/edit'),
        ];
    }
}
