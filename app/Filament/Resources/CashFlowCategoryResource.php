<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CashFlowCategoryResource\Pages;
use App\Filament\Resources\CashFlowCategoryResource\RelationManagers;
use App\Models\CashFlowCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CashFlowCategoryResource extends Resource
{
    protected static ?string $model = CashFlowCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = 'Financial';

    protected static ?int $navigationSort = 6;

    protected static ?string $navigationLabel = 'Cash Flow Categories';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('parent_id')
                    ->relationship('parent', 'category_name')
                    ->label('Kategori Induk')
                    ->default(null),
                Forms\Components\TextInput::make('category_code')
                    ->label('Kode Kategori')
                    ->required()
                    ->maxLength(20),
                Forms\Components\TextInput::make('category_name')
                    ->label('Nama Kategori')
                    ->required()
                    ->maxLength(100),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('activity_type')
                    ->label('Tipe Aktivitas')
                    ->required(),
                Forms\Components\TextInput::make('flow_type')
                    ->label('Tipe Aliran'),
                Forms\Components\TextInput::make('sort_order')
                    ->label('Urutan Tampil')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->label('Klien')
                    ->sortable(),
                Tables\Columns\TextColumn::make('parent.category_name')
                    ->label('Kategori Induk')
                    ->sortable(),
                Tables\Columns\TextColumn::make('category_code')
                    ->label('Kode Kategori')
                    ->searchable(),
                Tables\Columns\TextColumn::make('category_name')
                    ->label('Nama Kategori')
                    ->searchable(),
                Tables\Columns\TextColumn::make('activity_type')
                    ->label('Tipe Aktivitas'),
                Tables\Columns\TextColumn::make('flow_type')
                    ->label('Tipe Aliran'),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Urutan Tampil')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCashFlowCategories::route('/'),
            'create' => Pages\CreateCashFlowCategory::route('/create'),
            'edit' => Pages\EditCashFlowCategory::route('/{record}/edit'),
        ];
    }
}
