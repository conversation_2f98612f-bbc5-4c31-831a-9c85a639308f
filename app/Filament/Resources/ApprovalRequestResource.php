<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ApprovalRequestResource\Pages;
use App\Models\ApprovalRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ApprovalRequestResource extends Resource
{
    protected static ?string $model = ApprovalRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationLabel = 'Request Persetujuan';

    protected static ?string $modelLabel = 'Request Persetujuan';

    protected static ?string $pluralModelLabel = 'Request Persetujuan';

    protected static ?string $navigationGroup = 'Sistem';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Request')
                    ->schema([
                        Forms\Components\TextInput::make('approvalWorkflow.workflow_name')
                            ->label('Workflow')
                            ->disabled(),
                        Forms\Components\TextInput::make('approvable_type')
                            ->label('Tipe Model')
                            ->formatStateUsing(fn(string $state): string => match ($state) {
                                'App\\Models\\PurchaseOrder' => 'Pesanan Pembelian',
                                // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
                                'App\\Models\\Journal' => 'Jurnal Entry',
                                'App\\Models\\Expense' => 'Beban',
                                'App\\Models\\Invoice' => 'Invoice',
                                default => $state
                            })
                            ->disabled(),
                        Forms\Components\TextInput::make('requester.name')
                            ->label('Diminta Oleh')
                            ->disabled(),
                        Forms\Components\TextInput::make('status')
                            ->label('Status')
                            ->formatStateUsing(fn(string $state): string => match ($state) {
                                'pending' => 'Menunggu',
                                'approved' => 'Disetujui',
                                'rejected' => 'Ditolak',
                                'cancelled' => 'Dibatalkan',
                                default => $state
                            })
                            ->disabled(),
                        Forms\Components\TextInput::make('current_step')
                            ->label('Step Saat Ini')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('requested_at')
                            ->label('Tanggal Request')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('completed_at')
                            ->label('Tanggal Selesai')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Detail')
                    ->schema([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->disabled()
                            ->rows(3),
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->disabled()
                            ->rows(3),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('approvalWorkflow.workflow_name')
                    ->label('Workflow')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approvable_type')
                    ->label('Tipe Model')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'App\\Models\\PurchaseOrder' => 'Purchase Order',
                        // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
                        'App\\Models\\Journal' => 'Journal Entry',
                        'App\\Models\\Expense' => 'Expense',
                        'App\\Models\\Invoice' => 'Invoice',
                        default => $state
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('requester.name')
                    ->label('Diminta Oleh')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'cancelled' => 'Dibatalkan',
                        default => $state
                    })
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'cancelled' => 'gray',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('current_step')
                    ->label('Step')
                    ->sortable(),
                Tables\Columns\TextColumn::make('requested_at')
                    ->label('Tanggal Request')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->label('Tanggal Selesai')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('-'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'cancelled' => 'Dibatalkan',
                    ]),
                Tables\Filters\SelectFilter::make('approvable_type')
                    ->label('Tipe Model')
                    ->options([
                        'App\\Models\\PurchaseOrder' => 'Purchase Order',
                        // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
                        'App\\Models\\Journal' => 'Journal Entry',
                        'App\\Models\\Expense' => 'Expense',
                        'App\\Models\\Invoice' => 'Invoice',
                    ]),
                Tables\Filters\Filter::make('requested_at')
                    ->form([
                        Forms\Components\DatePicker::make('requested_from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('requested_until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['requested_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('requested_at', '>=', $date),
                            )
                            ->when(
                                $data['requested_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('requested_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn(ApprovalRequest $record): bool => $record->isPending())
                    ->requiresConfirmation()
                    ->action(function (ApprovalRequest $record) {
                        // Logic untuk approve akan ditambahkan nanti
                    }),
                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn(ApprovalRequest $record): bool => $record->isPending())
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (ApprovalRequest $record, array $data) {
                        // Logic untuk reject akan ditambahkan nanti
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApprovalRequests::route('/'),
            'create' => Pages\CreateApprovalRequest::route('/create'),
            'edit' => Pages\EditApprovalRequest::route('/{record}/edit'),
        ];
    }
}
