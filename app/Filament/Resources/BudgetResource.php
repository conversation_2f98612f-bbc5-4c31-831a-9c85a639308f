<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BudgetResource\Pages;
use App\Models\Budget;
use App\Services\BudgetService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class BudgetResource extends Resource
{
    protected static ?string $model = Budget::class;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?string $navigationLabel = 'Budget';

    protected static ?string $navigationGroup = 'Keuangan';

    protected static ?int $navigationSort = 8;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Budget')
                    ->schema([
                        Forms\Components\TextInput::make('budget_name')
                            ->label('Nama Budget')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),

                        Forms\Components\Select::make('budget_year')
                            ->label('Tahun Budget')
                            ->options(function () {
                                $currentYear = now()->year;
                                $years = [];
                                for ($i = $currentYear - 1; $i <= $currentYear + 3; $i++) {
                                    $years[$i] = $i;
                                }
                                return $years;
                            })
                            ->required()
                            ->default(now()->year),

                        Forms\Components\Select::make('budget_type')
                            ->label('Tipe Budget')
                            ->options(Budget::getTypes())
                            ->default(Budget::TYPE_ANNUAL)
                            ->required(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(Budget::getStatuses())
                            ->default(Budget::STATUS_DRAFT)
                            ->disabled(fn(?Budget $record) => $record && !$record->canBeEdited()),
                    ])->columns(2),

                Forms\Components\Section::make('Periode Budget')
                    ->schema([
                        Forms\Components\DatePicker::make('period_start')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->default(now()->startOfYear()),

                        Forms\Components\DatePicker::make('period_end')
                            ->label('Tanggal Berakhir')
                            ->required()
                            ->default(now()->endOfYear())
                            ->after('period_start'),
                    ])->columns(2),

                Forms\Components\Section::make('Ringkasan Budget')
                    ->schema([
                        Forms\Components\TextInput::make('total_revenue_budget')
                            ->label('Total Budget Pendapatan')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled(),

                        Forms\Components\TextInput::make('total_expense_budget')
                            ->label('Total Budget Beban')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled(),

                        Forms\Components\TextInput::make('net_income_budget')
                            ->label('Net Income Budget')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled(),
                    ])->columns(3)
                    ->visible(fn(?Budget $record) => $record && $record->exists),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('budget_name')
                    ->label('Nama Budget')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('budget_year')
                    ->label('Tahun')
                    ->sortable(),

                Tables\Columns\TextColumn::make('budget_type')
                    ->label('Tipe')
                    ->formatStateUsing(fn(string $state): string => Budget::getTypes()[$state] ?? $state)
                    ->badge(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn(string $state): string => Budget::getStatuses()[$state] ?? $state)
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'submitted' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'active' => 'primary',
                        'closed' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_revenue_budget')
                    ->label('Budget Pendapatan')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_expense_budget')
                    ->label('Budget Beban')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('net_income_budget')
                    ->label('Net Income')
                    ->money('IDR')
                    ->sortable()
                    ->color(fn($state): string => $state >= 0 ? 'success' : 'danger'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('budget_year')
                    ->label('Tahun')
                    ->options(function () {
                        $currentYear = now()->year;
                        $years = [];
                        for ($i = $currentYear - 5; $i <= $currentYear + 3; $i++) {
                            $years[$i] = $i;
                        }
                        return $years;
                    }),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(Budget::getStatuses()),

                Tables\Filters\SelectFilter::make('budget_type')
                    ->label('Tipe Budget')
                    ->options(Budget::getTypes()),
            ])
            ->actions([
                Tables\Actions\Action::make('submit')
                    ->label('Ajukan')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('warning')
                    ->visible(fn(Budget $record): bool => $record->canBeSubmitted())
                    ->requiresConfirmation()
                    ->action(function (Budget $record) {
                        $service = app(BudgetService::class);
                        $service->submitBudget($record);

                        \Filament\Notifications\Notification::make()
                            ->title('Budget berhasil diajukan')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn(Budget $record): bool => $record->canBeApproved())
                    ->form([
                        Forms\Components\Textarea::make('approval_notes')
                            ->label('Catatan Persetujuan')
                            ->rows(3),
                    ])
                    ->action(function (Budget $record, array $data) {
                        $service = app(BudgetService::class);
                        $service->approveBudget($record, Auth::id(), $data['approval_notes'] ?? null);

                        \Filament\Notifications\Notification::make()
                            ->title('Budget berhasil disetujui')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn(Budget $record): bool => $record->canBeApproved())
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (Budget $record, array $data) {
                        $service = app(BudgetService::class);
                        $service->rejectBudget($record, Auth::id(), $data['rejection_reason']);

                        \Filament\Notifications\Notification::make()
                            ->title('Budget ditolak')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('activate')
                    ->label('Aktifkan')
                    ->icon('heroicon-o-play')
                    ->color('primary')
                    ->visible(fn(Budget $record): bool => $record->canBeActivated())
                    ->requiresConfirmation()
                    ->modalHeading('Aktifkan Budget')
                    ->modalDescription('Budget yang diaktifkan akan menjadi budget aktif untuk tahun ini. Budget aktif lainnya akan ditutup.')
                    ->action(function (Budget $record) {
                        $service = app(BudgetService::class);
                        $service->activateBudget($record);

                        \Filament\Notifications\Notification::make()
                            ->title('Budget berhasil diaktifkan')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn(Budget $record): bool => $record->canBeEdited()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn(Budget $record): bool => $record->canBeEdited()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => false), // Disable bulk delete for budgets
                ]),
            ])
            ->defaultSort('budget_year', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBudgets::route('/'),
            'create' => Pages\CreateBudget::route('/create'),
            'edit' => Pages\EditBudget::route('/{record}/edit'),
        ];
    }
}
