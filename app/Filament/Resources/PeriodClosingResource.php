<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PeriodClosingResource\Pages;
use App\Models\PeriodClosing;
use App\Services\PeriodClosingService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class PeriodClosingResource extends Resource
{
    protected static ?string $model = PeriodClosing::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'Tutup Buku';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Periode')
                    ->schema([
                        Forms\Components\Select::make('year')
                            ->label('Tahun')
                            ->options(function () {
                                $currentYear = now()->year;
                                $years = [];
                                for ($i = $currentYear - 2; $i <= $currentYear + 1; $i++) {
                                    $years[$i] = $i;
                                }
                                return $years;
                            })
                            ->required()
                            ->default(now()->year),

                        Forms\Components\Select::make('month')
                            ->label('Bulan')
                            ->options([
                                1 => 'Januari',
                                2 => 'Februari',
                                3 => 'Maret',
                                4 => 'April',
                                5 => 'Mei',
                                6 => 'Juni',
                                7 => 'Juli',
                                8 => 'Agustus',
                                9 => 'September',
                                10 => 'Oktober',
                                11 => 'November',
                                12 => 'Desember'
                            ])
                            ->required()
                            ->default(now()->month),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options(PeriodClosing::getStatuses())
                            ->default(PeriodClosing::STATUS_OPEN)
                            ->disabled(fn(?PeriodClosing $record) => $record && $record->isClosed()),
                    ])->columns(3),

                Forms\Components\Section::make('Catatan')
                    ->schema([
                        Forms\Components\Textarea::make('closing_notes')
                            ->label('Catatan Tutup Buku')
                            ->rows(3),
                    ])
                    ->visible(fn(?PeriodClosing $record) => $record && $record->isClosed()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('year')
                    ->label('Tahun')
                    ->sortable(),

                Tables\Columns\TextColumn::make('month')
                    ->label('Bulan')
                    ->formatStateUsing(fn(int $state): string => [
                        1 => 'Januari',
                        2 => 'Februari',
                        3 => 'Maret',
                        4 => 'April',
                        5 => 'Mei',
                        6 => 'Juni',
                        7 => 'Juli',
                        8 => 'Agustus',
                        9 => 'September',
                        10 => 'Oktober',
                        11 => 'November',
                        12 => 'Desember'
                    ][$state])
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn(string $state): string => PeriodClosing::getStatuses()[$state] ?? $state)
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'open' => 'success',
                        'in_progress' => 'warning',
                        'closed' => 'danger',
                        'reopened' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('checklist_progress')
                    ->label('Progress Checklist')
                    ->getStateUsing(function (PeriodClosing $record): string {
                        $progress = $record->getChecklistProgress();
                        return "{$progress['completed']}/{$progress['total']} ({$progress['percentage']}%)";
                    }),

                Tables\Columns\IconColumn::make('is_balanced')
                    ->label('Seimbang')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('closed_at')
                    ->label('Ditutup Pada')
                    ->dateTime()
                    ->placeholder('-')
                    ->sortable(),

                Tables\Columns\TextColumn::make('closedBy.name')
                    ->label('Ditutup Oleh')
                    ->placeholder('-'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(PeriodClosing::getStatuses()),

                Tables\Filters\SelectFilter::make('year')
                    ->label('Tahun')
                    ->options(function () {
                        $currentYear = now()->year;
                        $years = [];
                        for ($i = $currentYear - 5; $i <= $currentYear + 1; $i++) {
                            $years[$i] = $i;
                        }
                        return $years;
                    }),

                Tables\Filters\TernaryFilter::make('is_balanced')
                    ->label('Status Neraca')
                    ->placeholder('Semua')
                    ->trueLabel('Seimbang')
                    ->falseLabel('Tidak Seimbang'),
            ])
            ->actions([
                Tables\Actions\Action::make('run_validations')
                    ->label('Validasi')
                    ->icon('heroicon-o-check-badge')
                    ->color('info')
                    ->visible(fn(PeriodClosing $record): bool => !$record->isClosed())
                    ->action(function (PeriodClosing $record) {
                        $service = app(PeriodClosingService::class);
                        $validations = $service->runValidations($record);

                        \Filament\Notifications\Notification::make()
                            ->title('Validasi selesai')
                            ->body('Hasil validasi telah diperbarui')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('close_period')
                    ->label('Tutup Buku')
                    ->icon('heroicon-o-lock-closed')
                    ->color('danger')
                    ->visible(fn(PeriodClosing $record): bool => $record->canBeClosed())
                    ->requiresConfirmation()
                    ->modalHeading('Tutup Buku Periode')
                    ->modalDescription('Apakah Anda yakin ingin menutup buku periode ini? Tindakan ini tidak dapat dibatalkan.')
                    ->form([
                        Forms\Components\Textarea::make('closing_notes')
                            ->label('Catatan Tutup Buku')
                            ->rows(3),
                    ])
                    ->action(function (PeriodClosing $record, array $data) {
                        $service = app(PeriodClosingService::class);
                        try {
                            $service->closePeriod($record, Auth::id(), $data['closing_notes'] ?? null);

                            \Filament\Notifications\Notification::make()
                                ->title('Periode berhasil ditutup')
                                ->body("Periode {$record->getPeriodName()} telah ditutup")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Gagal menutup periode')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('reopen_period')
                    ->label('Buka Kembali')
                    ->icon('heroicon-o-lock-open')
                    ->color('warning')
                    ->visible(fn(PeriodClosing $record): bool => $record->canBeReopened())
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Textarea::make('reopen_reason')
                            ->label('Alasan Pembukaan Kembali')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (PeriodClosing $record, array $data) {
                        $service = app(PeriodClosingService::class);
                        $service->reopenPeriod($record, Auth::id(), $data['reopen_reason']);

                        \Filament\Notifications\Notification::make()
                            ->title('Periode berhasil dibuka kembali')
                            ->body("Periode {$record->getPeriodName()} telah dibuka kembali")
                            ->success()
                            ->send();
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn(PeriodClosing $record): bool => !$record->isClosed()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => false), // Disable bulk delete for period closings
                ]),
            ])
            ->defaultSort('year', 'desc')
            ->defaultSort('month', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPeriodClosings::route('/'),
            'create' => Pages\CreatePeriodClosing::route('/create'),
            'edit' => Pages\EditPeriodClosing::route('/{record}/edit'),
        ];
    }
}
