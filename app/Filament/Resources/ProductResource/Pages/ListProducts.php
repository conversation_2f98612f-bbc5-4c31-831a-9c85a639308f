<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Exports\ProductTemplateExport;
use App\Filament\Resources\ProductResource;
use App\Imports\ProductImport;
use Filament\Actions;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Maatwebsite\Excel\Facades\Excel;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            // Download Template Action
            Actions\Action::make('downloadTemplate')
                ->label('Download Template')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('info')
                ->action(function () {
                    return Excel::download(new ProductTemplateExport(), 'template-import-produk.xlsx');
                }),

            // Import Product Action
            Actions\Action::make('importProducts')
                ->label('Import Produk')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('success')
                ->form([
                    FileUpload::make('file')
                        ->label('File Excel/CSV')
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv'])
                        ->required()
                        ->helperText('Upload file Excel (.xlsx) atau CSV dengan format sesuai template')
                ])
                ->action(function (array $data) {
                    try {
                        $import = new ProductImport();
                        Excel::import($import, $data['file']);

                        $importedCount = $import->getImportedCount();
                        $updatedCount = $import->getUpdatedCount();
                        $skippedCount = $import->getSkippedCount();
                        $errors = $import->getErrors();

                        if ($importedCount > 0 || $updatedCount > 0) {
                            $message = "Berhasil import {$importedCount} produk baru";
                            if ($updatedCount > 0) {
                                $message .= " dan update {$updatedCount} produk existing";
                            }
                            if ($skippedCount > 0) {
                                $message .= ", {$skippedCount} baris dilewati";
                            }

                            Notification::make()
                                ->title('Import Berhasil!')
                                ->body($message)
                                ->success()
                                ->send();
                        }

                        if (!empty($errors)) {
                            $errorMessage = "Errors:\n" . implode("\n", array_slice($errors, 0, 10));
                            if (count($errors) > 10) {
                                $errorMessage .= "\n... dan " . (count($errors) - 10) . " error lainnya";
                            }

                            Notification::make()
                                ->title('Ada Error dalam Import')
                                ->body($errorMessage)
                                ->warning()
                                ->persistent()
                                ->send();
                        }

                        if ($importedCount === 0 && $updatedCount === 0 && !empty($errors)) {
                            Notification::make()
                                ->title('Import Gagal!')
                                ->body('Tidak ada data yang berhasil diimport. Periksa format file dan data.')
                                ->danger()
                                ->send();
                        }
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Error Import!')
                            ->body('Terjadi kesalahan: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->modalHeading('Import Produk')
                ->modalDescription('Upload file Excel/CSV untuk import data produk secara bulk. Pastikan format sesuai dengan template.')
                ->modalSubmitActionLabel('Import')
                ->modalWidth('lg'),
        ];
    }
}
