<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WorkOrderResource\Pages;
use App\Filament\Resources\WorkOrderResource\RelationManagers;
use App\Models\WorkOrder;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WorkOrderResource extends Resource
{
    protected static ?string $model = WorkOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench';

    protected static ?string $navigationGroup = 'Manufacturing';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationLabel = 'Work Order';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('production_order_id')
                    ->relationship('productionOrder', 'po_number')
                    ->label('Order Produksi')
                    ->required(),
                Forms\Components\TextInput::make('bom_id')
                    ->label('BOM ID')
                    ->numeric()
                    ->default(null),
                Forms\Components\Select::make('work_center_id')
                    ->relationship('workCenter', 'work_center_name')
                    ->label('Pusat Kerja')
                    ->default(null),
                Forms\Components\TextInput::make('wo_number')
                    ->label('Nomor WO')
                    ->required()
                    ->maxLength(50),
                Forms\Components\TextInput::make('operation_sequence')
                    ->label('Urutan Operasi')
                    ->required()
                    ->numeric()
                    ->default(1),
                Forms\Components\TextInput::make('operation_name')
                    ->label('Nama Operasi')
                    ->required()
                    ->maxLength(100),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('quantity_to_process')
                    ->label('Kuantitas untuk Diproses')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('quantity_completed')
                    ->label('Kuantitas Selesai')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('quantity_scrapped')
                    ->label('Kuantitas Rusak')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Select::make('unit_id')
                    ->relationship('unit', 'unit_name')
                    ->label('Satuan')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->label('Status')
                    ->required(),
                Forms\Components\DateTimePicker::make('planned_start_date')
                    ->label('Tanggal Mulai Rencana'),
                Forms\Components\DateTimePicker::make('planned_end_date')
                    ->label('Tanggal Selesai Rencana'),
                Forms\Components\DateTimePicker::make('actual_start_date')
                    ->label('Tanggal Mulai Aktual'),
                Forms\Components\DateTimePicker::make('actual_end_date')
                    ->label('Tanggal Selesai Aktual'),
                Forms\Components\TextInput::make('setup_time_planned')
                    ->label('Waktu Setup Rencana')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('setup_time_actual')
                    ->label('Waktu Setup Aktual')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('run_time_planned')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('run_time_actual')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('labor_hours_planned')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('labor_hours_actual')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('machine_hours_planned')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('machine_hours_actual')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('labor_cost')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('machine_cost')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('overhead_cost')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('total_cost')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('assigned_to')
                    ->numeric()
                    ->default(null),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('productionOrder.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('bom_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('workCenter.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('wo_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('operation_sequence')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('operation_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity_to_process')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity_completed')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity_scrapped')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('planned_start_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('planned_end_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('actual_start_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('actual_end_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('setup_time_planned')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('setup_time_actual')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('run_time_planned')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('run_time_actual')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('labor_hours_planned')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('labor_hours_actual')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('machine_hours_planned')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('machine_hours_actual')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('labor_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('machine_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overhead_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('assigned_to')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWorkOrders::route('/'),
            'create' => Pages\CreateWorkOrder::route('/create'),
            'edit' => Pages\EditWorkOrder::route('/{record}/edit'),
        ];
    }
}
