<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DepreciationResource\Pages;
use App\Filament\Resources\DepreciationResource\RelationManagers;
use App\Models\Depreciation;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DepreciationResource extends Resource
{
    protected static ?string $model = Depreciation::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-pie';

    protected static ?string $navigationGroup = 'Asset Management';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Depreciation';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('fixed_asset_id')
                    ->relationship('fixedAsset', 'asset_name')
                    ->label('Aset Tetap')
                    ->required(),
                Forms\Components\DatePicker::make('depreciation_date')
                    ->label('Tanggal Depresiasi')
                    ->required(),
                Forms\Components\TextInput::make('depreciation_method')
                    ->label('Metode Depresiasi')
                    ->required()
                    ->maxLength(50),
                Forms\Components\TextInput::make('depreciation_amount')
                    ->label('Jumlah Depresiasi')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('accumulated_depreciation')
                    ->label('Akumulasi Depresiasi')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('book_value_before')
                    ->label('Nilai Buku Sebelum')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('book_value_after')
                    ->label('Nilai Buku Sesudah')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('useful_life_remaining')
                    ->label('Sisa Masa Manfaat')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('period_year')
                    ->label('Tahun Periode')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('period_month')
                    ->label('Bulan Periode')
                    ->required()
                    ->numeric(),
                Forms\Components\Toggle::make('is_manual')
                    ->label('Manual')
                    ->required(),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\Select::make('journal_id')
                    ->relationship('journal', 'description')
                    ->label('Jurnal')
                    ->default(null),
                Forms\Components\TextInput::make('created_by')
                    ->label('Dibuat Oleh')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fixedAsset.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('depreciation_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('depreciation_method')
                    ->searchable(),
                Tables\Columns\TextColumn::make('depreciation_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('accumulated_depreciation')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('book_value_before')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('book_value_after')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('useful_life_remaining')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('period_year')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('period_month')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_manual')
                    ->boolean(),
                Tables\Columns\TextColumn::make('journal.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDepreciations::route('/'),
            'create' => Pages\CreateDepreciation::route('/create'),
            'edit' => Pages\EditDepreciation::route('/{record}/edit'),
        ];
    }
}
