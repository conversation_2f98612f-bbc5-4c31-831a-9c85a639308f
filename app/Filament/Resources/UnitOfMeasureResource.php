<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UnitOfMeasureResource\Pages;
use App\Models\UnitOfMeasure;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class UnitOfMeasureResource extends Resource
{
    protected static ?string $model = UnitOfMeasure::class;

    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Satuan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('client_id')
                    ->default(fn() => Auth::user()?->client_id),
                Forms\Components\Select::make('base_unit_id')
                    ->relationship('baseUnit', 'unit_name', fn(Builder $query) => $query->where('client_id', Auth::user()?->client_id))
                    ->label('Satuan Dasar')
                    ->placeholder('Pilih satuan dasar (opsional)')
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('unit_code')
                    ->label('Kode Satuan')
                    ->required()
                    ->maxLength(10)
                    ->unique('unit_of_measures', 'unit_code', ignoreRecord: true, modifyRuleUsing: function ($rule) {
                        return $rule->where('client_id', Auth::user()?->client_id);
                    })
                    ->placeholder('Contoh: PCS, KG, M'),
                Forms\Components\TextInput::make('unit_name')
                    ->label('Nama Satuan')
                    ->required()
                    ->maxLength(50)
                    ->placeholder('Contoh: Pieces, Kilogram, Meter'),
                Forms\Components\TextInput::make('unit_symbol')
                    ->label('Simbol')
                    ->maxLength(10)
                    ->placeholder('Contoh: pcs, kg, m'),
                Forms\Components\TextInput::make('conversion_factor')
                    ->label('Faktor Konversi')
                    ->numeric()
                    ->default(1.000000)
                    ->step(0.000001)
                    ->helperText('Faktor konversi ke satuan dasar (1 = sama dengan satuan dasar)'),
                Forms\Components\Toggle::make('is_base_unit')
                    ->label('Satuan Dasar')
                    ->default(false)
                    ->helperText('Centang jika ini adalah satuan dasar'),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->default(true),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->placeholder('Deskripsi satuan (opsional)')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn(Builder $query) => $query->where('client_id', Auth::user()?->client_id))
            ->columns([
                Tables\Columns\TextColumn::make('unit_code')
                    ->label('Kode Satuan')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_name')
                    ->label('Nama Satuan')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_symbol')
                    ->label('Simbol')
                    ->placeholder('Tidak ada')
                    ->searchable(),
                Tables\Columns\TextColumn::make('baseUnit.unit_name')
                    ->label('Satuan Dasar')
                    ->placeholder('Tidak ada')
                    ->sortable(),
                Tables\Columns\TextColumn::make('conversion_factor')
                    ->label('Faktor Konversi')
                    ->numeric()
                    ->formatStateUsing(fn($state) => number_format($state, 6))
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_base_unit')
                    ->label('Satuan Dasar')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray'),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->placeholder('Tidak ada deskripsi'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),
                Tables\Filters\TernaryFilter::make('is_base_unit')
                    ->label('Satuan Dasar')
                    ->placeholder('Semua')
                    ->trueLabel('Satuan Dasar')
                    ->falseLabel('Bukan Satuan Dasar'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('unit_code');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUnitOfMeasures::route('/'),
            'create' => Pages\CreateUnitOfMeasure::route('/create'),
            'edit' => Pages\EditUnitOfMeasure::route('/{record}/edit'),
        ];
    }
}
