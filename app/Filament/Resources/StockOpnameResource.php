<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockOpnameResource\Pages;
use App\Filament\Resources\StockOpnameResource\RelationManagers;
use App\Models\StockOpname;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockOpnameResource extends Resource
{
    protected static ?string $model = StockOpname::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Stock Opname';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('location_id')
                    ->relationship('location', 'location_name')
                    ->label('Lokasi')
                    ->required(),
                Forms\Components\TextInput::make('opname_number')
                    ->label('Nomor Stock Opname')
                    ->required()
                    ->maxLength(50),
                Forms\Components\DatePicker::make('opname_date')
                    ->label('Tanggal Stock Opname')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->label('Status')
                    ->required(),
                Forms\Components\TextInput::make('count_type')
                    ->label('Tipe Perhitungan')
                    ->required(),
                Forms\Components\DatePicker::make('scheduled_date')
                    ->label('Tanggal Terjadwal'),
                Forms\Components\DateTimePicker::make('started_at')
                    ->label('Dimulai Pada'),
                Forms\Components\DateTimePicker::make('completed_at')
                    ->label('Selesai Pada'),
                Forms\Components\TextInput::make('total_items')
                    ->label('Total Item')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('counted_items')
                    ->label('Item Terhitung')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('variance_items')
                    ->label('Item Varians')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('total_variance_value')
                    ->label('Total Nilai Varians')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->label('Dibuat Oleh')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('approved_by')
                    ->label('Disetujui Oleh')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('approved_at')
                    ->label('Disetujui Pada'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('location.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('opname_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('opname_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('count_type'),
                Tables\Columns\TextColumn::make('scheduled_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_items')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('counted_items')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('variance_items')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_variance_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockOpnames::route('/'),
            'create' => Pages\CreateStockOpname::route('/create'),
            'edit' => Pages\EditStockOpname::route('/{record}/edit'),
        ];
    }
}
