<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CostVarianceReportResource\Pages;
use App\Filament\Resources\CostVarianceReportResource\RelationManagers;
use App\Models\CostVarianceReport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CostVarianceReportResource extends Resource
{
    protected static ?string $model = CostVarianceReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Cost Variance Reports';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('production_order_id')
                    ->relationship('productionOrder', 'po_number')
                    ->label('Order Produksi')
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'product_name')
                    ->label('Produk')
                    ->required(),
                Forms\Components\TextInput::make('report_period')
                    ->label('Periode Laporan')
                    ->required()
                    ->maxLength(20),
                Forms\Components\DatePicker::make('report_date')
                    ->label('Tanggal Laporan')
                    ->required(),
                Forms\Components\TextInput::make('total_standard_cost')
                    ->label('Total Biaya Standar')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('total_actual_cost')
                    ->label('Total Biaya Aktual')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('total_variance')
                    ->label('Total Varians')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('material_variance')
                    ->label('Varians Material')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('labor_variance')
                    ->label('Varians Tenaga Kerja')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('overhead_variance')
                    ->label('Varians Overhead')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('variance_percentage')
                    ->label('Persentase Varians')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('performance_rating')
                    ->label('Rating Performa')
                    ->required()
                    ->maxLength(20),
                Forms\Components\TextInput::make('significant_variances_count')
                    ->label('Jumlah Varians Signifikan')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\Textarea::make('recommendations')
                    ->label('Rekomendasi')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('action_items')
                    ->label('Item Tindakan')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('reviewed_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('reviewed_at'),
                Forms\Components\TextInput::make('approved_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('approved_at'),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('productionOrder.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('report_period')
                    ->searchable(),
                Tables\Columns\TextColumn::make('report_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_standard_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_actual_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_variance')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('material_variance')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('labor_variance')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overhead_variance')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('variance_percentage')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('performance_rating')
                    ->searchable(),
                Tables\Columns\TextColumn::make('significant_variances_count')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('reviewed_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reviewed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCostVarianceReports::route('/'),
            'create' => Pages\CreateCostVarianceReport::route('/create'),
            'edit' => Pages\EditCostVarianceReport::route('/{record}/edit'),
        ];
    }
}
