<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WorkCenterResource\Pages;
use App\Filament\Resources\WorkCenterResource\RelationManagers;
use App\Models\WorkCenter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WorkCenterResource extends Resource
{
    protected static ?string $model = WorkCenter::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Produksi';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Pusat Kerja';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('location_id')
                    ->relationship('location', 'location_name')
                    ->label('Lokasi')
                    ->default(null),
                Forms\Components\TextInput::make('work_center_code')
                    ->label('Kode Pusat Kerja')
                    ->required()
                    ->maxLength(20),
                Forms\Components\TextInput::make('work_center_name')
                    ->label('Nama Pusat Kerja')
                    ->required()
                    ->maxLength(100),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('work_center_type')
                    ->label('Tipe Pusat Kerja')
                    ->required(),
                Forms\Components\TextInput::make('capacity_per_hour')
                    ->label('Kapasitas per Jam')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Select::make('capacity_unit_id')
                    ->relationship('capacityUnit', 'unit_name')
                    ->label('Satuan Kapasitas')
                    ->default(null),
                Forms\Components\TextInput::make('labor_rate')
                    ->label('Tarif Tenaga Kerja')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('machine_rate')
                    ->label('Tarif Mesin')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('overhead_rate')
                    ->label('Tarif Overhead')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('setup_time_default')
                    ->label('Waktu Setup Default')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('efficiency_percentage')
                    ->label('Persentase Efisiensi')
                    ->required()
                    ->numeric()
                    ->default(100.00),
                Forms\Components\TextInput::make('utilization_percentage')
                    ->label('Persentase Utilisasi')
                    ->required()
                    ->numeric()
                    ->default(100.00),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->required(),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('location.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('work_center_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('work_center_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('work_center_type'),
                Tables\Columns\TextColumn::make('capacity_per_hour')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('capacityUnit.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('labor_rate')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('machine_rate')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overhead_rate')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('setup_time_default')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('efficiency_percentage')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('utilization_percentage')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWorkCenters::route('/'),
            'create' => Pages\CreateWorkCenter::route('/create'),
            'edit' => Pages\EditWorkCenter::route('/{record}/edit'),
        ];
    }
}
