<?php

namespace App\Filament\Resources\AutomatedJournalEntryResource\Pages;

use App\Filament\Resources\AutomatedJournalEntryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAutomatedJournalEntry extends EditRecord
{
    protected static string $resource = AutomatedJournalEntryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
