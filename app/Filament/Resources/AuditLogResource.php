<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AuditLogResource\Pages;
use App\Models\AuditLog;
use App\Services\AuditService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AuditLogResource extends Resource
{
    protected static ?string $model = AuditLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Audit Trail';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Audit')
                    ->schema([
                        Forms\Components\TextInput::make('event')
                            ->label('Event')
                            ->disabled(),

                        Forms\Components\TextInput::make('auditable_type')
                            ->label('Tipe Model')
                            ->disabled(),

                        Forms\Components\TextInput::make('auditable_id')
                            ->label('Model ID')
                            ->disabled(),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->disabled()
                            ->rows(2),
                    ])->columns(2),

                Forms\Components\Section::make('Detail Perubahan')
                    ->schema([
                        Forms\Components\KeyValue::make('old_values')
                            ->label('Nilai Lama')
                            ->disabled(),

                        Forms\Components\KeyValue::make('new_values')
                            ->label('Nilai Baru')
                            ->disabled(),
                    ])->columns(2)
                    ->visible(fn(?AuditLog $record) => $record && ($record->old_values || $record->new_values)),

                Forms\Components\Section::make('Informasi Request')
                    ->schema([
                        Forms\Components\TextInput::make('ip_address')
                            ->label('IP Address')
                            ->disabled(),

                        Forms\Components\TextInput::make('method')
                            ->label('HTTP Method')
                            ->disabled(),

                        Forms\Components\TextInput::make('url')
                            ->label('URL')
                            ->disabled(),

                        Forms\Components\Textarea::make('user_agent')
                            ->label('User Agent')
                            ->disabled()
                            ->rows(2),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Waktu')
                    ->dateTime()
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->placeholder('System'),

                Tables\Columns\TextColumn::make('event')
                    ->label('Event')
                    ->formatStateUsing(fn(AuditLog $record): string => $record->getEventLabel())
                    ->badge()
                    ->color(fn(AuditLog $record): string => $record->getEventColor())
                    ->searchable(),

                Tables\Columns\TextColumn::make('auditable_type')
                    ->label('Model')
                    ->formatStateUsing(fn(AuditLog $record): string => $record->getModelName())
                    ->searchable(),

                Tables\Columns\TextColumn::make('auditable_id')
                    ->label('ID')
                    ->searchable(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->tooltip(function (AuditLog $record): string {
                        return $record->description;
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('method')
                    ->label('Method')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'GET' => 'success',
                        'POST' => 'primary',
                        'PUT', 'PATCH' => 'warning',
                        'DELETE' => 'danger',
                        default => 'gray',
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('changed_fields')
                    ->label('Field yang Berubah')
                    ->formatStateUsing(function (?array $state): string {
                        return $state ? implode(', ', $state) : '-';
                    })
                    ->limit(30)
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('event')
                    ->label('Event')
                    ->options(AuditLog::getEvents()),

                Tables\Filters\SelectFilter::make('auditable_type')
                    ->label('Model Type')
                    ->options([
                        'App\\Models\\Account' => 'Akun',
                        'App\\Models\\Transaction' => 'Transaksi',
                        'App\\Models\\Journal' => 'Jurnal',
                        'App\\Models\\Product' => 'Produk',
                        'App\\Models\\Customer' => 'Pelanggan',
                        'App\\Models\\Supplier' => 'Supplier',
                        'App\\Models\\Budget' => 'Budget',
                        'App\\Models\\User' => 'User',
                    ]),

                Tables\Filters\SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable(),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\Action::make('view_changes')
                    ->label('Lihat Perubahan')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn(AuditLog $record): bool => !empty($record->changed_fields))
                    ->modalHeading('Detail Perubahan')
                    ->modalContent(function (AuditLog $record) {
                        $changes = $record->getChangedFieldsFormatted();

                        if (empty($changes)) {
                            return view('filament::components.modal.content', [
                                'content' => 'Tidak ada perubahan data.'
                            ]);
                        }

                        $html = '<div class="space-y-4">';
                        foreach ($changes as $change) {
                            $html .= '<div class="border rounded p-3">';
                            $html .= '<h4 class="font-semibold text-gray-900">' . $change['field'] . '</h4>';
                            $html .= '<div class="grid grid-cols-2 gap-4 mt-2">';
                            $html .= '<div><span class="text-sm text-gray-500">Nilai Lama:</span><br><span class="text-red-600">' . $change['old_value'] . '</span></div>';
                            $html .= '<div><span class="text-sm text-gray-500">Nilai Baru:</span><br><span class="text-green-600">' . $change['new_value'] . '</span></div>';
                            $html .= '</div></div>';
                        }
                        $html .= '</div>';

                        return new \Illuminate\Support\HtmlString($html);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('export')
                        ->label('Ekspor')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            $service = app(AuditService::class);
                            $data = $records->map(function ($record) {
                                return [
                                    'Waktu' => $record->created_at->format('Y-m-d H:i:s'),
                                    'User' => $record->user->name ?? 'System',
                                    'Event' => $record->getEventLabel(),
                                    'Model' => $record->getModelName(),
                                    'ID' => $record->auditable_id,
                                    'Deskripsi' => $record->description,
                                    'IP Address' => $record->ip_address,
                                    'Method' => $record->method,
                                ];
                            })->toArray();

                            // Here you would implement actual export functionality
                            \Filament\Notifications\Notification::make()
                                ->title('Export berhasil')
                                ->body('Data audit log telah diekspor')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAuditLogs::route('/'),
            'create' => Pages\CreateAuditLog::route('/create'),
            'edit' => Pages\EditAuditLog::route('/{record}/edit'),
        ];
    }
}
