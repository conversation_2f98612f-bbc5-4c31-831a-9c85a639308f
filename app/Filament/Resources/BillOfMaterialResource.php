<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BillOfMaterialResource\Pages;
use App\Filament\Resources\BillOfMaterialResource\RelationManagers;
use App\Models\BillOfMaterial;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BillOfMaterialResource extends Resource
{
    protected static ?string $model = BillOfMaterial::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Produksi';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Daftar Bahan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'product_name')
                    ->label('Produk')
                    ->required(),
                Forms\Components\TextInput::make('bom_code')
                    ->label('Kode BOM')
                    ->required()
                    ->maxLength(50),
                Forms\Components\TextInput::make('version')
                    ->label('Versi')
                    ->required()
                    ->maxLength(20)
                    ->default(1.0),
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('quantity_produced')
                    ->label('Kuantitas Diproduksi')
                    ->required()
                    ->numeric()
                    ->default(1.00),
                Forms\Components\TextInput::make('labor_cost_per_unit')
                    ->label('Biaya Tenaga Kerja per Unit')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('overhead_cost_per_unit')
                    ->label('Biaya Overhead per Unit')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('setup_time_minutes')
                    ->label('Waktu Setup (Menit)')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('production_time_minutes')
                    ->label('Waktu Produksi (Menit)')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->required(),
                Forms\Components\DatePicker::make('effective_date')
                    ->label('Tanggal Efektif'),
                Forms\Components\DatePicker::make('expiry_date')
                    ->label('Tanggal Kedaluwarsa'),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('bom_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('version')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity_produced')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('labor_cost_per_unit')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overhead_cost_per_unit')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('setup_time_minutes')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('production_time_minutes')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('effective_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('expiry_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBillOfMaterials::route('/'),
            'create' => Pages\CreateBillOfMaterial::route('/create'),
            'edit' => Pages\EditBillOfMaterial::route('/{record}/edit'),
        ];
    }
}
