<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ApprovalWorkflowResource\Pages;
use App\Filament\Resources\ApprovalWorkflowResource\RelationManagers;
use App\Models\ApprovalWorkflow;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ApprovalWorkflowResource extends Resource
{
    protected static ?string $model = ApprovalWorkflow::class;

    protected static ?string $navigationIcon = 'heroicon-o-check-circle';

    protected static ?string $navigationLabel = 'Workflow Persetujuan';

    protected static ?string $modelLabel = 'Workflow Persetujuan';

    protected static ?string $pluralModelLabel = 'Workflow Persetujuan';

    protected static ?string $navigationGroup = 'Sistem';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\TextInput::make('workflow_name')
                            ->label('Nama Workflow')
                            ->required()
                            ->maxLength(100),
                        Forms\Components\TextInput::make('workflow_code')
                            ->label('Kode Workflow')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(50),
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),
                        Forms\Components\Select::make('approvable_type')
                            ->label('Tipe Model')
                            ->required()
                            ->options([
                                'App\\Models\\PurchaseOrder' => 'Purchase Order',
                                // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
                                'App\\Models\\Journal' => 'Journal Entry',
                                'App\\Models\\Expense' => 'Expense',
                                'App\\Models\\Invoice' => 'Invoice',
                            ]),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),
                    ])->columns(2),

                Forms\Components\Section::make('Pengaturan Persetujuan')
                    ->schema([
                        Forms\Components\TextInput::make('max_amount')
                            ->label('Jumlah Maksimal')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\Toggle::make('is_sequential')
                            ->label('Persetujuan Berurutan')
                            ->default(true)
                            ->helperText('Jika diaktifkan, persetujuan harus dilakukan secara berurutan'),
                        Forms\Components\Toggle::make('allow_self_approval')
                            ->label('Izinkan Self Approval')
                            ->default(false)
                            ->helperText('Izinkan pembuat request untuk menyetujui sendiri'),
                        Forms\Components\Toggle::make('auto_approve_below_limit')
                            ->label('Auto Approve di Bawah Limit')
                            ->default(false)
                            ->reactive(),
                        Forms\Components\TextInput::make('auto_approve_limit')
                            ->label('Limit Auto Approve')
                            ->numeric()
                            ->prefix('Rp')
                            ->visible(fn($get) => $get('auto_approve_below_limit')),
                    ])->columns(2),

                Forms\Components\Section::make('Catatan')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->rows(3),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('workflow_name')
                    ->label('Nama Workflow')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('workflow_code')
                    ->label('Kode')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approvable_type')
                    ->label('Tipe Model')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'App\\Models\\PurchaseOrder' => 'Purchase Order',
                        // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
                        'App\\Models\\Journal' => 'Journal Entry',
                        'App\\Models\\Expense' => 'Expense',
                        'App\\Models\\Invoice' => 'Invoice',
                        default => $state
                    })
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean(),
                Tables\Columns\TextColumn::make('max_amount')
                    ->label('Jumlah Maks')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_sequential')
                    ->label('Berurutan')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('allow_self_approval')
                    ->label('Self Approval')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),
                Tables\Filters\SelectFilter::make('approvable_type')
                    ->label('Tipe Model')
                    ->options([
                        'App\\Models\\PurchaseOrder' => 'Pesanan Pembelian',
                        // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
                        'App\\Models\\Journal' => 'Jurnal Entry',
                        'App\\Models\\Expense' => 'Beban',
                        'App\\Models\\Invoice' => 'Invoice',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\StepsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApprovalWorkflows::route('/'),
            'create' => Pages\CreateApprovalWorkflow::route('/create'),
            'edit' => Pages\EditApprovalWorkflow::route('/{record}/edit'),
        ];
    }
}
