<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductionOrderResource\Pages;
use App\Filament\Resources\ProductionOrderResource\RelationManagers;
use App\Models\ProductionOrder;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductionOrderResource extends Resource
{
    protected static ?string $model = ProductionOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Manufacturing';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Production Order';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'product_name')
                    ->label('Produk')
                    ->required(),
                Forms\Components\TextInput::make('bom_id')
                    ->label('BOM ID')
                    ->numeric()
                    ->default(null),
                Forms\Components\Select::make('location_id')
                    ->relationship('location', 'location_name')
                    ->label('Lokasi')
                    ->default(null),
                Forms\Components\TextInput::make('po_number')
                    ->label('Nomor PO')
                    ->required()
                    ->maxLength(50),
                Forms\Components\TextInput::make('reference_number')
                    ->label('Nomor Referensi')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('quantity_to_produce')
                    ->label('Kuantitas untuk Diproduksi')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('quantity_produced')
                    ->label('Kuantitas Diproduksi')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('quantity_remaining')
                    ->label('Kuantitas Tersisa')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Select::make('unit_id')
                    ->relationship('unit', 'unit_name')
                    ->label('Satuan')
                    ->required(),
                Forms\Components\TextInput::make('priority')
                    ->label('Prioritas')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->label('Status')
                    ->required(),
                Forms\Components\DateTimePicker::make('planned_start_date')
                    ->label('Tanggal Mulai Rencana'),
                Forms\Components\DateTimePicker::make('planned_end_date')
                    ->label('Tanggal Selesai Rencana'),
                Forms\Components\DateTimePicker::make('actual_start_date')
                    ->label('Tanggal Mulai Aktual'),
                Forms\Components\DateTimePicker::make('actual_end_date')
                    ->label('Tanggal Selesai Aktual'),
                Forms\Components\TextInput::make('estimated_cost')
                    ->label('Biaya Estimasi')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('actual_cost')
                    ->label('Biaya Aktual')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('approved_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('approved_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('bom_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('location.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('po_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('reference_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity_to_produce')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity_produced')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity_remaining')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('priority'),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('planned_start_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('planned_end_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('actual_start_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('actual_end_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimated_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('actual_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductionOrders::route('/'),
            'create' => Pages\CreateProductionOrder::route('/create'),
            'edit' => Pages\EditProductionOrder::route('/{record}/edit'),
        ];
    }
}
