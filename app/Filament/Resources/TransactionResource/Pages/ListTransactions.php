<?php

namespace App\Filament\Resources\TransactionResource\Pages;

use App\Exports\SalesTransactionTemplateExport;
use App\Filament\Resources\TransactionResource;
use App\Imports\SalesTransactionImport;
use Filament\Actions;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Maatwebsite\Excel\Facades\Excel;

class ListTransactions extends ListRecords
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            // Download Template Action
            Actions\Action::make('downloadTemplate')
                ->label('Download Template')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('info')
                ->action(function () {
                    return Excel::download(new SalesTransactionTemplateExport(), 'template-import-transaksi-penjualan.xlsx');
                }),

            // Import Sales Transaction Action
            Actions\Action::make('importSales')
                ->label('Import Penjualan')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('success')
                ->form([
                    FileUpload::make('file')
                        ->label('File Excel/CSV')
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv'])
                        ->required()
                        ->helperText('Upload file Excel (.xlsx) atau CSV dengan format sesuai template')
                ])
                ->action(function (array $data) {
                    try {
                        $import = new SalesTransactionImport();
                        Excel::import($import, $data['file']);

                        $importedCount = $import->getImportedCount();
                        $skippedCount = $import->getSkippedCount();
                        $errors = $import->getErrors();

                        if ($importedCount > 0) {
                            Notification::make()
                                ->title('Import Berhasil!')
                                ->body("Berhasil import {$importedCount} transaksi" . ($skippedCount > 0 ? ", {$skippedCount} baris dilewati" : ""))
                                ->success()
                                ->send();
                        }

                        if (!empty($errors)) {
                            $errorMessage = "Errors:\n" . implode("\n", array_slice($errors, 0, 10));
                            if (count($errors) > 10) {
                                $errorMessage .= "\n... dan " . (count($errors) - 10) . " error lainnya";
                            }

                            Notification::make()
                                ->title('Ada Error dalam Import')
                                ->body($errorMessage)
                                ->warning()
                                ->persistent()
                                ->send();
                        }

                        if ($importedCount === 0 && !empty($errors)) {
                            Notification::make()
                                ->title('Import Gagal!')
                                ->body('Tidak ada data yang berhasil diimport. Periksa format file dan data.')
                                ->danger()
                                ->send();
                        }
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Error Import!')
                            ->body('Terjadi kesalahan: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->modalHeading('Import Transaksi Penjualan')
                ->modalDescription('Upload file Excel/CSV untuk import data transaksi penjualan secara bulk. Pastikan format sesuai dengan template.')
                ->modalSubmitActionLabel('Import')
                ->modalWidth('lg'),
        ];
    }
}
