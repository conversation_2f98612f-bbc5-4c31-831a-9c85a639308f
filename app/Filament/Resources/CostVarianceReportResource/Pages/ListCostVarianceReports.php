<?php

namespace App\Filament\Resources\CostVarianceReportResource\Pages;

use App\Filament\Resources\CostVarianceReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCostVarianceReports extends ListRecords
{
    protected static string $resource = CostVarianceReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
