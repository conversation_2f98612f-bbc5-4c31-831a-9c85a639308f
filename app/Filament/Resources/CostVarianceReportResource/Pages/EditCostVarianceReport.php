<?php

namespace App\Filament\Resources\CostVarianceReportResource\Pages;

use App\Filament\Resources\CostVarianceReportResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCostVarianceReport extends EditRecord
{
    protected static string $resource = CostVarianceReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
