<?php

namespace App\Filament\Resources\ApprovalWorkflowResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StepsRelationManager extends RelationManager
{
    protected static string $relationship = 'steps';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Step')
                    ->schema([
                        Forms\Components\TextInput::make('step_order')
                            ->label('Urutan Step')
                            ->required()
                            ->numeric()
                            ->minValue(1),
                        Forms\Components\TextInput::make('step_name')
                            ->label('Nama Step')
                            ->required()
                            ->maxLength(100),
                        Forms\Components\Textarea::make('step_description')
                            ->label('Deskripsi')
                            ->rows(3),
                    ])->columns(2),

                Forms\Components\Section::make('Pengaturan Approver')
                    ->schema([
                        Forms\Components\Select::make('approver_type')
                            ->label('Tipe Approver')
                            ->required()
                            ->options([
                                'user' => 'User Spesifik',
                                'role' => 'Role',
                                'department' => 'Department',
                                'dynamic' => 'Dynamic',
                            ])
                            ->reactive(),
                        Forms\Components\Select::make('approver_user_id')
                            ->label('User Approver')
                            ->relationship('approverUser', 'name')
                            ->visible(fn($get) => $get('approver_type') === 'user'),
                        Forms\Components\TextInput::make('approver_role')
                            ->label('Role Approver')
                            ->visible(fn($get) => $get('approver_type') === 'role'),
                        Forms\Components\Select::make('approver_department_id')
                            ->label('Department Approver')
                            ->relationship('approverDepartment', 'name')
                            ->visible(fn($get) => $get('approver_type') === 'department'),
                        Forms\Components\Select::make('dynamic_approver_field')
                            ->label('Field Dynamic')
                            ->options([
                                'requester_manager' => 'Manager Pembuat Request',
                                'cost_center_head' => 'Kepala Cost Center',
                                'department_head' => 'Kepala Department',
                            ])
                            ->visible(fn($get) => $get('approver_type') === 'dynamic'),
                    ])->columns(2),

                Forms\Components\Section::make('Pengaturan Lanjutan')
                    ->schema([
                        Forms\Components\Toggle::make('is_required')
                            ->label('Wajib')
                            ->default(true),
                        Forms\Components\Toggle::make('can_delegate')
                            ->label('Bisa Didelegasikan')
                            ->default(false),
                        Forms\Components\Toggle::make('can_skip')
                            ->label('Bisa Dilewati')
                            ->default(false),
                        Forms\Components\TextInput::make('timeout_hours')
                            ->label('Timeout (Jam)')
                            ->numeric()
                            ->helperText('Auto-approve setelah timeout'),
                        Forms\Components\TextInput::make('min_amount')
                            ->label('Jumlah Minimal')
                            ->numeric()
                            ->prefix('Rp'),
                        Forms\Components\TextInput::make('max_amount')
                            ->label('Jumlah Maksimal')
                            ->numeric()
                            ->prefix('Rp'),
                    ])->columns(3),

                Forms\Components\Section::make('Catatan')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->rows(3),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('step_name')
            ->columns([
                Tables\Columns\TextColumn::make('step_order')
                    ->label('Urutan')
                    ->sortable(),
                Tables\Columns\TextColumn::make('step_name')
                    ->label('Nama Step')
                    ->searchable(),
                Tables\Columns\TextColumn::make('approver_type')
                    ->label('Tipe Approver')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'user' => 'User Spesifik',
                        'role' => 'Role',
                        'department' => 'Department',
                        'dynamic' => 'Dynamic',
                        default => $state
                    }),
                Tables\Columns\TextColumn::make('approverUser.name')
                    ->label('User Approver')
                    ->placeholder('-'),
                Tables\Columns\TextColumn::make('approver_role')
                    ->label('Role')
                    ->placeholder('-'),
                Tables\Columns\IconColumn::make('is_required')
                    ->label('Wajib')
                    ->boolean(),
                Tables\Columns\IconColumn::make('can_delegate')
                    ->label('Delegasi')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('timeout_hours')
                    ->label('Timeout (Jam)')
                    ->placeholder('-')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
