<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockMovementResource\Pages;
use App\Filament\Resources\StockMovementResource\RelationManagers;
use App\Models\StockMovement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockMovementResource extends Resource
{
    protected static ?string $model = StockMovement::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';

    protected static ?string $navigationGroup = 'Inventory';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Stock Movements';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'product_name')
                    ->label('Produk')
                    ->required(),
                Forms\Components\Select::make('inventory_id')
                    ->relationship('inventory', 'id')
                    ->label('Inventori')
                    ->required(),
                Forms\Components\Select::make('location_id')
                    ->relationship('location', 'location_name')
                    ->label('Lokasi')
                    ->default(null),
                Forms\Components\Select::make('from_location_id')
                    ->relationship('fromLocation', 'location_name')
                    ->label('Dari Lokasi')
                    ->default(null),
                Forms\Components\Select::make('to_location_id')
                    ->relationship('toLocation', 'location_name')
                    ->label('Ke Lokasi')
                    ->default(null),
                Forms\Components\Select::make('unit_id')
                    ->relationship('unit', 'unit_name')
                    ->label('Satuan')
                    ->required(),
                Forms\Components\TextInput::make('reference_type')
                    ->label('Tipe Referensi')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('reference_id')
                    ->label('ID Referensi')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('movement_type')
                    ->label('Tipe Pergerakan')
                    ->required(),
                Forms\Components\TextInput::make('transaction_type')
                    ->label('Tipe Transaksi')
                    ->required(),
                Forms\Components\TextInput::make('reason_code')
                    ->label('Kode Alasan')
                    ->maxLength(50)
                    ->default(null),
                Forms\Components\TextInput::make('quantity')
                    ->label('Kuantitas')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('unit_cost')
                    ->label('Biaya per Unit')
                    ->required()
                    ->numeric()
                    ->default(0.0000),
                Forms\Components\TextInput::make('total_cost')
                    ->label('Total Biaya')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('running_balance')
                    ->label('Saldo Berjalan')
                    ->required()
                    ->numeric()
                    ->default(0.000000),
                Forms\Components\TextInput::make('running_value')
                    ->label('Nilai Berjalan')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\DateTimePicker::make('movement_date')
                    ->label('Tanggal Pergerakan')
                    ->required(),
                Forms\Components\TextInput::make('reference_number')
                    ->label('Nomor Referensi')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('batch_number')
                    ->label('Nomor Batch')
                    ->maxLength(50)
                    ->default(null),
                Forms\Components\TextInput::make('serial_number')
                    ->label('Nomor Seri')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\DatePicker::make('expiry_date')
                    ->label('Tanggal Kedaluwarsa'),
                Forms\Components\TextInput::make('lot_number')
                    ->label('Nomor Lot')
                    ->maxLength(50)
                    ->default(null),
                Forms\Components\Select::make('work_order_id')
                    ->relationship('workOrder', 'wo_number')
                    ->label('Order Kerja')
                    ->default(null),
                Forms\Components\Select::make('production_order_id')
                    ->relationship('productionOrder', 'po_number')
                    ->label('Order Produksi')
                    ->default(null),
                Forms\Components\Select::make('purchase_order_id')
                    ->relationship('purchaseOrder', 'id')
                    ->default(null),
                Forms\Components\TextInput::make('sales_order_id')
                    ->numeric()
                    ->default(null),
                Forms\Components\Toggle::make('is_reversed')
                    ->required(),
                Forms\Components\Select::make('reversed_by_id')
                    ->relationship('reversedBy', 'name')
                    ->default(null),
                Forms\Components\DateTimePicker::make('reversed_at'),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('approved_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('approved_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('inventory.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('location.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromLocation.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toLocation.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('reference_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('movement_type'),
                Tables\Columns\TextColumn::make('transaction_type'),
                Tables\Columns\TextColumn::make('reason_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('running_balance')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('running_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('movement_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reference_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('batch_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('serial_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('expiry_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('lot_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('workOrder.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('productionOrder.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('purchaseOrder.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sales_order_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_reversed')
                    ->boolean(),
                Tables\Columns\TextColumn::make('reversedBy.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reversed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockMovements::route('/'),
            'create' => Pages\CreateStockMovement::route('/create'),
            'edit' => Pages\EditStockMovement::route('/{record}/edit'),
        ];
    }
}
