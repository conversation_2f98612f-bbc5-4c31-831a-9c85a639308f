<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AccountResource\Pages;
use App\Models\Account;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;
use Filament\Tables;

class AccountResource extends Resource
{
    protected static ?string $model = Account::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationGroup = 'Keuangan';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Bagan Akun';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('account_code')
                    ->required()
                    ->maxLength(255)
                    ->label('Kode Akun')
                    ->unique(ignoreRecord: true, modifyRuleUsing: function ($rule, $get) {
                        return $rule->where('client_id', Auth::user()->client_id ?? $get('client_id'));
                    }),

                TextInput::make('account_name')
                    ->required()
                    ->maxLength(255)
                    ->label('Nama Akun'),

                Select::make('account_type')
                    ->options(Account::getAccountTypes())
                    ->required()
                    ->label('Tipe Akun'),

                Select::make('normal_balance')
                    ->options(Account::getNormalBalanceTypes())
                    ->required()
                    ->label('Saldo Normal'),

                Select::make('parent_account_id')
                    ->relationship('parentAccount', 'account_name')
                    ->searchable()
                    ->preload()
                    ->label('Akun Induk')
                    ->placeholder('Pilih akun induk (opsional)'),

                Toggle::make('is_active')
                    ->default(true)
                    ->label('Status Aktif'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('account_code')
                    ->searchable()
                    ->sortable()
                    ->label('Kode'),

                TextColumn::make('account_name')
                    ->searchable()
                    ->sortable()
                    ->label('Nama Akun'),

                TextColumn::make('account_type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'asset' => 'success',
                        'liability' => 'warning',
                        'equity' => 'info',
                        'revenue' => 'primary',
                        'expense' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => Account::getAccountTypes()[$state] ?? $state)
                    ->label('Tipe'),

                TextColumn::make('normal_balance')
                    ->badge()
                    ->color(fn(string $state): string => $state === 'debit' ? 'success' : 'warning')
                    ->formatStateUsing(fn(string $state): string => Account::getNormalBalanceTypes()[$state] ?? $state)
                    ->label('Saldo Normal'),

                TextColumn::make('parentAccount.account_name')
                    ->label('Akun Induk')
                    ->placeholder('Tidak Ada'),

                IconColumn::make('is_active')
                    ->boolean()
                    ->label('Aktif'),


            ])
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('account_type')
                    ->options(Account::getAccountTypes())
                    ->label('Tipe Akun'),
                SelectFilter::make('normal_balance')
                    ->options(Account::getNormalBalanceTypes())
                    ->label('Saldo Normal'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('account_code', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAccounts::route('/'),
            'create' => Pages\CreateAccount::route('/create'),
            'edit' => Pages\EditAccount::route('/{record}/edit'),
        ];
    }
}
