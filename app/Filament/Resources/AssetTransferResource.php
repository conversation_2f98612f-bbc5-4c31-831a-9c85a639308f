<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetTransferResource\Pages;
use App\Filament\Resources\AssetTransferResource\RelationManagers;
use App\Models\AssetTransfer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AssetTransferResource extends Resource
{
    protected static ?string $model = AssetTransfer::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path';

    protected static ?string $navigationGroup = 'Asset Management';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Asset Transfers';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('fixed_asset_id')
                    ->relationship('fixedAsset', 'asset_name')
                    ->label('Aset Tetap')
                    ->required(),
                Forms\Components\TextInput::make('transfer_number')
                    ->label('Nomor Transfer')
                    ->required()
                    ->maxLength(50),
                Forms\Components\DatePicker::make('transfer_date')
                    ->label('Tanggal Transfer')
                    ->required(),
                Forms\Components\Select::make('from_location_id')
                    ->relationship('fromLocation', 'location_name')
                    ->label('Dari Lokasi')
                    ->required(),
                Forms\Components\Select::make('to_location_id')
                    ->relationship('toLocation', 'location_name')
                    ->label('Ke Lokasi')
                    ->required(),
                Forms\Components\Select::make('from_department_id')
                    ->relationship('fromDepartment', 'department_name')
                    ->label('Dari Departemen')
                    ->required(),
                Forms\Components\Select::make('to_department_id')
                    ->relationship('toDepartment', 'department_name')
                    ->label('Ke Departemen')
                    ->required(),
                Forms\Components\Select::make('from_cost_center_id')
                    ->relationship('fromCostCenter', 'cost_center_name')
                    ->label('Dari Pusat Biaya')
                    ->default(null),
                Forms\Components\Select::make('to_cost_center_id')
                    ->relationship('toCostCenter', 'cost_center_name')
                    ->label('Ke Pusat Biaya')
                    ->default(null),
                Forms\Components\TextInput::make('from_responsible_person')
                    ->label('Penanggung Jawab Asal')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('to_responsible_person')
                    ->label('Penanggung Jawab Tujuan')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('transfer_type')
                    ->label('Tipe Transfer')
                    ->required(),
                Forms\Components\Textarea::make('reason')
                    ->label('Alasan')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('condition_before')
                    ->label('Kondisi Sebelum')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('condition_after')
                    ->label('Kondisi Sesudah')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('book_value_at_transfer')
                    ->label('Nilai Buku saat Transfer')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('transfer_cost')
                    ->label('Biaya Transfer')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Toggle::make('approval_required')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('transferred_by')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('received_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\TextInput::make('approved_by')
                    ->numeric()
                    ->default(null),
                Forms\Components\DateTimePicker::make('approved_at'),
                Forms\Components\DateTimePicker::make('completed_at'),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('created_by')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fixedAsset.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transfer_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('transfer_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromLocation.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toLocation.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromDepartment.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toDepartment.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fromCostCenter.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('toCostCenter.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('from_responsible_person')
                    ->searchable(),
                Tables\Columns\TextColumn::make('to_responsible_person')
                    ->searchable(),
                Tables\Columns\TextColumn::make('transfer_type'),
                Tables\Columns\TextColumn::make('condition_before')
                    ->searchable(),
                Tables\Columns\TextColumn::make('condition_after')
                    ->searchable(),
                Tables\Columns\TextColumn::make('book_value_at_transfer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transfer_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('approval_required')
                    ->boolean(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('transferred_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('received_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssetTransfers::route('/'),
            'create' => Pages\CreateAssetTransfer::route('/create'),
            'edit' => Pages\EditAssetTransfer::route('/{record}/edit'),
        ];
    }
}
