<?php

namespace App\Filament\Resources\CashFlowCategoryResource\Pages;

use App\Filament\Resources\CashFlowCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCashFlowCategories extends ListRecords
{
    protected static string $resource = CashFlowCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
