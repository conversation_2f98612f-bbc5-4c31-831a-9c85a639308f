<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InventoryResource\Pages;
use App\Filament\Resources\InventoryResource\RelationManagers;
use App\Models\Inventory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InventoryResource extends Resource
{
    protected static ?string $model = Inventory::class;

    protected static ?string $navigationIcon = 'heroicon-o-archive-box';

    protected static ?string $navigationGroup = 'Inventori';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Inventori';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'product_name')
                    ->label('Produk')
                    ->required(),
                Forms\Components\Select::make('location_id')
                    ->relationship('location', 'location_name')
                    ->label('Lokasi')
                    ->default(null),
                Forms\Components\TextInput::make('current_stock')
                    ->label('Stok Saat Ini')
                    ->required()
                    ->numeric()
                    ->default(0.000000),
                Forms\Components\TextInput::make('reserved_stock')
                    ->label('Stok Dipesan')
                    ->required()
                    ->numeric()
                    ->default(0.000000),
                Forms\Components\TextInput::make('available_stock')
                    ->label('Stok Tersedia')
                    ->required()
                    ->numeric()
                    ->default(0.000000),
                Forms\Components\TextInput::make('average_cost')
                    ->label('Biaya Rata-rata')
                    ->required()
                    ->numeric()
                    ->default(0.0000),
                Forms\Components\TextInput::make('last_cost')
                    ->label('Biaya Terakhir')
                    ->required()
                    ->numeric()
                    ->default(0.0000),
                Forms\Components\TextInput::make('total_value')
                    ->label('Total Nilai')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\DateTimePicker::make('last_movement_date')
                    ->label('Tanggal Pergerakan Terakhir'),
                Forms\Components\DateTimePicker::make('last_count_date')
                    ->label('Tanggal Hitung Terakhir'),
                Forms\Components\DatePicker::make('cycle_count_due_date')
                    ->label('Tanggal Jatuh Tempo Hitung Siklus'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->label('Klien')
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.product_name')
                    ->label('Produk')
                    ->sortable(),
                Tables\Columns\TextColumn::make('location.location_name')
                    ->label('Lokasi')
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_stock')
                    ->label('Stok Saat Ini')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reserved_stock')
                    ->label('Stok Dipesan')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('available_stock')
                    ->label('Stok Tersedia')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('average_cost')
                    ->label('Biaya Rata-rata')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_cost')
                    ->label('Biaya Terakhir')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Nilai')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_movement_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_count_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('cycle_count_due_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInventories::route('/'),
            'create' => Pages\CreateInventory::route('/create'),
            'edit' => Pages\EditInventory::route('/{record}/edit'),
        ];
    }
}
