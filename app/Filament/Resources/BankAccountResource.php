<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BankAccountResource\Pages;
use App\Filament\Resources\BankAccountResource\RelationManagers;
use App\Models\BankAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BankAccountResource extends Resource
{
    protected static ?string $model = BankAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?string $navigationGroup = 'Keuangan';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationLabel = 'Rekening Bank';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'name')
                    ->label('Klien')
                    ->required(),
                Forms\Components\Select::make('account_id')
                    ->relationship('account', 'account_name')
                    ->label('Akun')
                    ->default(null),
                Forms\Components\TextInput::make('bank_name')
                    ->label('Nama Bank')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('account_number')
                    ->label('Nomor Rekening')
                    ->required()
                    ->maxLength(50),
                Forms\Components\TextInput::make('account_name')
                    ->label('Nama Rekening')
                    ->required()
                    ->maxLength(100),
                Forms\Components\TextInput::make('account_type')
                    ->label('Tipe Rekening')
                    ->required(),
                Forms\Components\TextInput::make('currency_code')
                    ->label('Kode Mata Uang')
                    ->required()
                    ->maxLength(3)
                    ->default('IDR'),
                Forms\Components\TextInput::make('opening_balance')
                    ->label('Saldo Awal')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('current_balance')
                    ->label('Saldo Saat Ini')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('available_balance')
                    ->label('Saldo Tersedia')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('credit_limit')
                    ->label('Limit Kredit')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('interest_rate')
                    ->label('Tingkat Bunga')
                    ->required()
                    ->numeric()
                    ->default(0.0000),
                Forms\Components\TextInput::make('bank_code')
                    ->label('Kode Bank')
                    ->maxLength(10)
                    ->default(null),
                Forms\Components\TextInput::make('swift_code')
                    ->label('Kode SWIFT')
                    ->maxLength(20)
                    ->default(null),
                Forms\Components\TextInput::make('branch_name')
                    ->label('Nama Cabang')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\Textarea::make('branch_address')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('contact_person')
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\TextInput::make('contact_phone')
                    ->label('Telepon Kontak')
                    ->tel()
                    ->maxLength(20)
                    ->default(null),
                Forms\Components\TextInput::make('contact_email')
                    ->label('Email Kontak')
                    ->email()
                    ->maxLength(100)
                    ->default(null),
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->required(),
                Forms\Components\Toggle::make('is_default')
                    ->label('Default')
                    ->required(),
                Forms\Components\DatePicker::make('last_reconciled_date')
                    ->label('Tanggal Rekonsiliasi Terakhir'),
                Forms\Components\DatePicker::make('last_statement_date')
                    ->label('Tanggal Statement Terakhir'),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name')
                    ->label('Klien')
                    ->sortable(),
                Tables\Columns\TextColumn::make('account.account_name')
                    ->label('Akun')
                    ->sortable(),
                Tables\Columns\TextColumn::make('bank_name')
                    ->label('Nama Bank')
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_number')
                    ->label('Nomor Rekening')
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_name')
                    ->label('Nama Rekening')
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_type')
                    ->label('Tipe Rekening'),
                Tables\Columns\TextColumn::make('currency_code')
                    ->label('Mata Uang')
                    ->searchable(),
                Tables\Columns\TextColumn::make('opening_balance')
                    ->label('Saldo Awal')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_balance')
                    ->label('Saldo Saat Ini')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('available_balance')
                    ->label('Saldo Tersedia')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('credit_limit')
                    ->label('Limit Kredit')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('interest_rate')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('bank_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('swift_code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('branch_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact_person')
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact_phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact_email')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_default')
                    ->boolean(),
                Tables\Columns\TextColumn::make('last_reconciled_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_statement_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccounts::route('/'),
            'create' => Pages\CreateBankAccount::route('/create'),
            'edit' => Pages\EditBankAccount::route('/{record}/edit'),
        ];
    }
}
