<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;

class QuickActionsWidget extends Widget
{
    protected static string $view = 'filament.widgets.quick-actions';

    protected int | string | array $columnSpan = 'full';

    public function getDisplayName(): string
    {
        return 'Aksi Cepat';
    }

    public function getQuickActions(): array
    {
        $tenant = \Filament\Facades\Filament::getTenant();

        // Helper function to safely generate routes
        $safeRoute = function ($routeName, $params = []) use ($tenant) {
            try {
                return route($routeName, array_merge(['tenant' => $tenant], $params));
            } catch (\Exception $e) {
                // Fallback to dashboard if route doesn't exist
                return route('filament.admin.pages.main-dashboard', ['tenant' => $tenant]);
            }
        };

        return [
            // === AKSI CEPAT ===
            'quick_actions' => [
                'title' => 'Aksi Cepat',
                'description' => 'Buat data baru dengan cepat',
                'items' => [
                    [
                        'label' => 'Buat Transaksi',
                        'description' => 'Transaksi baru',
                        'icon' => '💳',
                        'url' => $safeRoute('filament.admin.resources.transactions.create'),
                    ],
                    [
                        'label' => 'Buat Journal',
                        'description' => 'Journal entry baru',
                        'icon' => '📝',
                        'url' => $safeRoute('filament.admin.resources.journals.create'),
                    ],
                    [
                        'label' => 'Tambah Pelanggan',
                        'description' => 'Pelanggan baru',
                        'icon' => '👥',
                        'url' => $safeRoute('filament.admin.resources.customers.create'),
                    ],
                    [
                        'label' => 'Tambah Produk',
                        'description' => 'Produk baru',
                        'icon' => '📦',
                        'url' => $safeRoute('filament.admin.resources.products.create'),
                    ],
                    [
                        'label' => 'Order Pembelian',
                        'description' => 'Purchase order baru',
                        'icon' => '📋',
                        'url' => $safeRoute('filament.admin.resources.purchase-orders.create'),
                    ],
                    [
                        'label' => 'Order Produksi',
                        'description' => 'Production order baru',
                        'icon' => '⚙️',
                        'url' => $safeRoute('filament.admin.resources.production-orders.create'),
                    ],
                ]
            ],

            // === DASHBOARD ===
            'dashboards' => [
                'title' => 'Dashboard',
                'description' => 'Analisis dan monitoring sistem',
                'items' => [
                    [
                        'label' => 'Dashboard Keuangan',
                        'description' => 'Analisis keuangan',
                        'icon' => '💰',
                        'url' => route('filament.admin.pages.financial-dashboard', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Dashboard Operasional',
                        'description' => 'Monitoring operasional',
                        'icon' => '⚙️',
                        'url' => route('filament.admin.pages.operational-dashboard', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Dashboard Persetujuan',
                        'description' => 'Approval dashboard',
                        'icon' => '✅',
                        'url' => route('filament.admin.pages.approval-dashboard', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Analisis Produk',
                        'description' => 'Product engineering',
                        'icon' => '📊',
                        'url' => route('filament.admin.pages.product-engineering-dashboard', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === DATA MASTER ===
            'data_master' => [
                'title' => 'Data Master',
                'description' => 'Kelola data dasar sistem',
                'items' => [
                    [
                        'label' => 'Pengguna',
                        'description' => 'Kelola pengguna sistem',
                        'icon' => '👤',
                        'url' => route('filament.admin.resources.users.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Pelanggan',
                        'description' => 'Kelola data pelanggan',
                        'icon' => '👥',
                        'url' => route('filament.admin.resources.customers.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Supplier',
                        'description' => 'Kelola data supplier',
                        'icon' => '🏢',
                        'url' => route('filament.admin.resources.suppliers.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Produk',
                        'description' => 'Kelola produk dan layanan',
                        'icon' => '📦',
                        'url' => route('filament.admin.resources.products.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Kategori',
                        'description' => 'Kategori produk',
                        'icon' => '🏷️',
                        'url' => route('filament.admin.resources.product-categories.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Mata Uang',
                        'description' => 'Satuan pengukuran',
                        'icon' => '📏',
                        'url' => route('filament.admin.resources.unit-of-measures.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Lokasi',
                        'description' => 'Lokasi gudang/cabang',
                        'icon' => '📍',
                        'url' => route('filament.admin.resources.locations.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Departemen',
                        'description' => 'Departemen perusahaan',
                        'icon' => '🏛️',
                        'url' => route('filament.admin.resources.departments.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Pusat Biaya',
                        'description' => 'Cost center',
                        'icon' => '💰',
                        'url' => route('filament.admin.resources.cost-centers.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === SALES & CRM ===
            'sales_crm' => [
                'title' => 'Sales & CRM',
                'description' => 'Kelola penjualan dan hubungan pelanggan',
                'items' => [
                    [
                        'label' => 'Transaksi',
                        'description' => 'Kelola transaksi penjualan',
                        'icon' => '💳',
                        'url' => route('filament.admin.resources.transactions.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Pelanggan',
                        'description' => 'Database pelanggan',
                        'icon' => '👥',
                        'url' => route('filament.admin.resources.customers.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Produk',
                        'description' => 'Katalog produk',
                        'icon' => '📦',
                        'url' => route('filament.admin.resources.products.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Inventori',
                        'description' => 'Stok produk',
                        'icon' => '📊',
                        'url' => route('filament.admin.resources.inventories.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === PEMBELIAN ===
            'purchasing' => [
                'title' => 'Pembelian',
                'description' => 'Kelola pembelian dan supplier',
                'items' => [
                    [
                        'label' => 'Pesanan Pembelian',
                        'description' => 'Purchase orders',
                        'icon' => '📋',
                        'url' => route('filament.admin.resources.purchase-orders.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Supplier',
                        'description' => 'Database supplier',
                        'icon' => '🏢',
                        'url' => route('filament.admin.resources.suppliers.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Pergerakan Stok',
                        'description' => 'Stock movements',
                        'icon' => '📈',
                        'url' => route('filament.admin.resources.stock-movements.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Stock Opname',
                        'description' => 'Audit inventori',
                        'icon' => '🔍',
                        'url' => route('filament.admin.resources.stock-opnames.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === KEUANGAN ===
            'financial' => [
                'title' => 'Keuangan',
                'description' => 'Kelola keuangan dan akuntansi',
                'items' => [
                    [
                        'label' => 'Bagan Akun',
                        'description' => 'Chart of accounts',
                        'icon' => '🏦',
                        'url' => route('filament.admin.resources.accounts.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Jurnal',
                        'description' => 'Journal entries',
                        'icon' => '📝',
                        'url' => route('filament.admin.resources.journals.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Rekening Bank',
                        'description' => 'Bank accounts',
                        'icon' => '🏧',
                        'url' => route('filament.admin.resources.bank-accounts.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Transaksi Bank',
                        'description' => 'Bank transactions',
                        'icon' => '💰',
                        'url' => route('filament.admin.resources.bank-transactions.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Budget',
                        'description' => 'Anggaran keuangan',
                        'icon' => '📊',
                        'url' => route('filament.admin.resources.budgets.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Kategori Arus Kas',
                        'description' => 'Cash flow categories',
                        'icon' => '💸',
                        'url' => route('filament.admin.resources.cash-flow-categories.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Penutupan Periode',
                        'description' => 'Period closing',
                        'icon' => '🔒',
                        'url' => route('filament.admin.resources.period-closings.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Jurnal Otomatis',
                        'description' => 'Automated journal entries',
                        'icon' => '🤖',
                        'url' => route('filament.admin.resources.automated-journal-entries.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === LAPORAN ===
            'reports' => [
                'title' => 'Laporan',
                'description' => 'Laporan keuangan dan operasional',
                'items' => [
                    [
                        'label' => 'Laporan Keuangan',
                        'description' => 'Financial reports',
                        'icon' => '📊',
                        'url' => route('filament.admin.pages.financial-report-dashboard', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Laba Rugi',
                        'description' => 'Income statement',
                        'icon' => '💹',
                        'url' => route('filament.admin.pages.income-statement', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Neraca',
                        'description' => 'Balance sheet',
                        'icon' => '⚖️',
                        'url' => route('filament.admin.pages.balance-sheet', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Arus Kas',
                        'description' => 'Cash flow statement',
                        'icon' => '💸',
                        'url' => route('filament.admin.pages.cash-flow-statement', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Buku Besar',
                        'description' => 'General ledger',
                        'icon' => '📚',
                        'url' => route('filament.admin.pages.general-ledger', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Analisis Rasio',
                        'description' => 'Financial ratio analysis',
                        'icon' => '📈',
                        'url' => route('filament.admin.pages.financial-ratio-analysis', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Analisis Profitabilitas',
                        'description' => 'Profitability analysis',
                        'icon' => '💰',
                        'url' => route('filament.admin.pages.profitability-analysis', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Modal Kerja',
                        'description' => 'Working capital analysis',
                        'icon' => '🔄',
                        'url' => route('filament.admin.pages.working-capital-analysis', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Varians Biaya',
                        'description' => 'Cost variance reports',
                        'icon' => '📊',
                        'url' => route('filament.admin.resources.cost-variance-reports.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Target Penjualan',
                        'description' => 'Sales target analysis',
                        'icon' => '🎯',
                        'url' => route('filament.admin.pages.sales-target-analysis', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === ASET TETAP ===
            'fixed_assets' => [
                'title' => 'Aset Tetap',
                'description' => 'Kelola aset tetap dan depresiasi',
                'items' => [
                    [
                        'label' => 'Aset Tetap',
                        'description' => 'Fixed assets',
                        'icon' => '🏢',
                        'url' => route('filament.admin.resources.fixed-assets.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Depresiasi',
                        'description' => 'Depreciation',
                        'icon' => '📉',
                        'url' => route('filament.admin.resources.depreciations.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Transfer Aset',
                        'description' => 'Asset transfers',
                        'icon' => '🔄',
                        'url' => route('filament.admin.resources.asset-transfers.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === MANAJEMEN PROYEK ===
            'project_management' => [
                'title' => 'Manajemen Proyek',
                'description' => 'Kelola proyek dan produksi',
                'items' => [
                    [
                        'label' => 'Order Produksi',
                        'description' => 'Production orders',
                        'icon' => '⚙️',
                        'url' => route('filament.admin.resources.production-orders.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Work Order',
                        'description' => 'Work orders',
                        'icon' => '🔧',
                        'url' => route('filament.admin.resources.work-orders.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Work Center',
                        'description' => 'Pusat kerja',
                        'icon' => '🏭',
                        'url' => route('filament.admin.resources.work-centers.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Bill of Material',
                        'description' => 'BOM produk',
                        'icon' => '📋',
                        'url' => route('filament.admin.resources.bill-of-materials.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === SDM ===
            'human_resources' => [
                'title' => 'SDM',
                'description' => 'Kelola sumber daya manusia',
                'items' => [
                    [
                        'label' => 'Karyawan',
                        'description' => 'Data karyawan',
                        'icon' => '👨‍💼',
                        'url' => route('filament.admin.resources.users.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Departemen',
                        'description' => 'Departemen perusahaan',
                        'icon' => '🏛️',
                        'url' => route('filament.admin.resources.departments.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Pusat Biaya',
                        'description' => 'Cost centers',
                        'icon' => '💰',
                        'url' => route('filament.admin.resources.cost-centers.index', ['tenant' => $tenant]),
                    ],
                ]
            ],

            // === SISTEM ===
            'system' => [
                'title' => 'Sistem',
                'description' => 'Pengaturan dan administrasi sistem',
                'items' => [
                    [
                        'label' => 'Pengguna',
                        'description' => 'Manajemen pengguna',
                        'icon' => '👤',
                        'url' => route('filament.admin.resources.users.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Workflow Persetujuan',
                        'description' => 'Approval workflows',
                        'icon' => '✅',
                        'url' => route('filament.admin.resources.approval-workflows.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Permintaan Persetujuan',
                        'description' => 'Approval requests',
                        'icon' => '📋',
                        'url' => route('filament.admin.resources.approval-requests.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Log Audit',
                        'description' => 'Audit trail',
                        'icon' => '📜',
                        'url' => route('filament.admin.resources.audit-logs.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Notifikasi',
                        'description' => 'System notifications',
                        'icon' => '🔔',
                        'url' => route('filament.admin.resources.system-notifications.index', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Export/Import',
                        'description' => 'Export dan import data',
                        'icon' => '📤',
                        'url' => route('filament.admin.pages.export-import', ['tenant' => $tenant]),
                    ],
                    [
                        'label' => 'Backup',
                        'description' => 'Backup management',
                        'icon' => '💾',
                        'url' => route('filament.admin.pages.backup-management', ['tenant' => $tenant]),
                    ],
                ]
            ],
        ];
    }
}
