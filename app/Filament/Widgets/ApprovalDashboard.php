<?php

namespace App\Filament\Widgets;

use App\Models\ApprovalRequest;
use App\Services\ApprovalWorkflowService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class ApprovalDashboard extends BaseWidget
{


    protected function getStats(): array
    {
        $user = Auth::user();
        $service = app(ApprovalWorkflowService::class);

        // Get pending approvals for current user
        $pendingApprovals = $service->getPendingApprovals($user);
        $pendingCount = $pendingApprovals->count();

        // Get total approval requests in system
        $totalRequests = ApprovalRequest::where('client_id', $user->client_id)->count();

        // Get approved requests this month
        $approvedThisMonth = ApprovalRequest::where('client_id', $user->client_id)
            ->where('status', 'approved')
            ->whereMonth('completed_at', now()->month)
            ->count();

        // Get rejected requests this month
        $rejectedThisMonth = ApprovalRequest::where('client_id', $user->client_id)
            ->where('status', 'rejected')
            ->whereMonth('completed_at', now()->month)
            ->count();

        return [
            Stat::make('Menunggu Persetujuan Saya', $pendingCount)
                ->description('Permintaan yang perlu saya setujui')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Total Permintaan', $totalRequests)
                ->description('Total permintaan persetujuan')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('Disetujui Bulan Ini', $approvedThisMonth)
                ->description('Permintaan yang disetujui bulan ini')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Ditolak Bulan Ini', $rejectedThisMonth)
                ->description('Permintaan yang ditolak bulan ini')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }

    public function getDisplayName(): string
    {
        return 'Dashboard Persetujuan';
    }
}
