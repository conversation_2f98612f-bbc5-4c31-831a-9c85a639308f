<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Facades\Filament;
use App\Models\Transaction;
use App\Models\PurchaseOrder;

class FinancialDashboardWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    // Tidak tampil di dashboard utama, hanya di FinancialDashboard
    protected static bool $isDiscoverable = false;

    protected static ?int $sort = 5;

    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        // Accounts Receivable Aging (unpaid sales transactions older than 30 days)
        $overdueReceivables = Transaction::where('client_id', $clientId)
            ->where('type', 'sales')
            ->where('payment_status', '!=', 'paid')
            ->where('transaction_date', '<', now()->subDays(30))
            ->sum('amount');

        // Accounts Payable Aging (unpaid purchase orders older than 30 days)
        $overduePayables = PurchaseOrder::where('client_id', $clientId)
            ->where('payment_status', '!=', 'paid')
            ->where('invoice_date', '<', now()->subDays(30))
            ->sum('total_amount');

        // Current month collections (paid sales transactions)
        $currentCollections = Transaction::where('client_id', $clientId)
            ->where('type', 'sales')
            ->where('payment_status', 'paid')
            ->whereBetween('transaction_date', [now()->startOfMonth(), now()->endOfMonth()])
            ->sum('amount');

        // Current month payments (paid purchase orders)
        $currentPayments = PurchaseOrder::where('client_id', $clientId)
            ->where('payment_status', 'paid')
            ->whereBetween('invoice_date', [now()->startOfMonth(), now()->endOfMonth()])
            ->sum('paid_amount');

        return [
            Stat::make('Piutang Overdue', 'Rp ' . number_format($overdueReceivables, 0, ',', '.'))
                ->description('Piutang lewat jatuh tempo (>30 hari)')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($overdueReceivables > 0 ? 'danger' : 'success')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->extraAttributes([
                    'class' => 'cursor-pointer transition-all hover:scale-105 hover:shadow-lg',
                ]),

            Stat::make('Hutang Overdue', 'Rp ' . number_format($overduePayables, 0, ',', '.'))
                ->description('Hutang lewat jatuh tempo (>30 hari)')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($overduePayables > 0 ? 'warning' : 'success')
                ->chart([17, 16, 14, 15, 14, 13, 12])
                ->extraAttributes([
                    'class' => 'cursor-pointer transition-all hover:scale-105 hover:shadow-lg',
                ]),

            Stat::make('Penerimaan Bulan Ini', 'Rp ' . number_format($currentCollections, 0, ',', '.'))
                ->description('Total penerimaan dari pelanggan')
                ->descriptionIcon('heroicon-m-arrow-down-circle')
                ->color('success')
                ->chart([5, 10, 15, 20, 25, 30, $currentCollections / 1000000])
                ->extraAttributes([
                    'class' => 'cursor-pointer transition-all hover:scale-105 hover:shadow-lg',
                ]),

            Stat::make('Pembayaran Bulan Ini', 'Rp ' . number_format($currentPayments, 0, ',', '.'))
                ->description('Total pembayaran ke supplier')
                ->descriptionIcon('heroicon-m-arrow-up-circle')
                ->color('info')
                ->chart([10, 15, 12, 18, 20, 25, $currentPayments / 1000000])
                ->extraAttributes([
                    'class' => 'cursor-pointer transition-all hover:scale-105 hover:shadow-lg',
                ]),
        ];
    }
}
