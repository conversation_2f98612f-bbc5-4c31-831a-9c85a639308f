<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Facades\Filament;
use App\Models\Transaction;
use App\Models\ProductionOrder;
use App\Models\Inventory;

class OperationalDashboardWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';

    // Tidak tampil di dashboard utama, hanya di OperationalDashboard
    protected static bool $isDiscoverable = false;

    protected static ?int $sort = 3;

    protected function getStats(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        // Sales transactions this month
        $salesOrdersCount = Transaction::where('client_id', $clientId)
            ->where('type', 'sales')
            ->whereBetween('transaction_date', [now()->startOfMonth(), now()->endOfMonth()])
            ->count();

        // Production orders
        $totalProductionOrders = ProductionOrder::where('client_id', $clientId)
            ->count();

        $completedProductionOrders = ProductionOrder::where('client_id', $clientId)
            ->where('status', 'completed')
            ->count();

        $completionRate = $totalProductionOrders > 0 ? ($completedProductionOrders / $totalProductionOrders) * 100 : 0;

        // Low stock items (current stock <= reorder point)
        $lowStockItems = Inventory::where('client_id', $clientId)
            ->whereColumn('current_stock', '<=', 'reorder_point')
            ->count();

        $salesPerformance = [
            'total_orders' => ['value' => $salesOrdersCount, 'icon' => 'heroicon-o-shopping-cart', 'color' => 'primary'],
        ];

        $productionPerformance = [
            'total_production_orders' => ['value' => $totalProductionOrders, 'icon' => 'heroicon-o-cog-6-tooth', 'color' => 'primary'],
            'completion_rate' => ['value' => round($completionRate, 1), 'icon' => 'heroicon-o-check-circle', 'color' => $completionRate >= 80 ? 'success' : 'warning'],
        ];

        $inventoryStatus = [
            'low_stock' => ['value' => $lowStockItems, 'icon' => 'heroicon-o-exclamation-triangle', 'color' => $lowStockItems > 0 ? 'warning' : 'success'],
        ];

        return [
            Stat::make('Sales Orders', $salesPerformance['total_orders']['value'])
                ->description('Total pesanan bulan ini')
                ->descriptionIcon($salesPerformance['total_orders']['icon'])
                ->color($salesPerformance['total_orders']['color']),

            Stat::make('Production Orders', $productionPerformance['total_production_orders']['value'])
                ->description('Order produksi aktif')
                ->descriptionIcon($productionPerformance['total_production_orders']['icon'])
                ->color($productionPerformance['total_production_orders']['color']),

            Stat::make('Completion Rate', number_format($productionPerformance['completion_rate']['value'], 1) . '%')
                ->description('Tingkat penyelesaian produksi')
                ->descriptionIcon($productionPerformance['completion_rate']['icon'])
                ->color($productionPerformance['completion_rate']['color']),

            Stat::make('Low Stock Items', $inventoryStatus['low_stock']['value'])
                ->description('Item dengan stok rendah')
                ->descriptionIcon($inventoryStatus['low_stock']['icon'])
                ->color($inventoryStatus['low_stock']['color']),
        ];
    }
}
