<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use Filament\Facades\Filament;
use App\Models\JournalEntry;
use Carbon\Carbon;

class RevenueTrendChart extends ChartWidget
{
    protected static ?string $heading = 'Trend Pendapatan (12 Bulan)';

    protected static ?int $sort = 2;

    // Tidak tampil di dashboard utama
    protected static bool $isDiscoverable = false;

    protected function getData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        $labels = [];
        $data = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            // Get revenue for this month (account code 4xxx)
            $monthlyRevenue = $this->getRevenueForPeriod($clientId, $startOfMonth, $endOfMonth);

            $labels[] = $date->format('M Y');
            $data[] = $monthlyRevenue;
        }

        $trendData = [
            'labels' => $labels,
            'data' => $data,
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Pendapatan',
                    'data' => $trendData['data'],
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
            ],
            'labels' => $trendData['labels'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                    'titleColor' => '#fff',
                    'bodyColor' => '#fff',
                    'borderColor' => '#3b82f6',
                    'borderWidth' => 1,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'grid' => [
                        'color' => 'rgba(0, 0, 0, 0.1)',
                    ],
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + value.toLocaleString("id-ID"); }',
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
            'animation' => [
                'duration' => 2000,
                'easing' => 'easeInOutQuart',
            ],
            'elements' => [
                'point' => [
                    'radius' => 4,
                    'hoverRadius' => 8,
                ],
                'line' => [
                    'tension' => 0.4,
                ],
            ],
        ];
    }

    private function getRevenueForPeriod(int $clientId, Carbon $startDate, Carbon $endDate): float
    {
        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', '4%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', '4%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        // Revenue accounts have credit balance
        return $totalCredits - $totalDebits;
    }
}
