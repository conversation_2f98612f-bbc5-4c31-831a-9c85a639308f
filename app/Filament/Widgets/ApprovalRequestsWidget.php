<?php

namespace App\Filament\Widgets;

use App\Models\ApprovalRequest;
use App\Services\ApprovalWorkflowService;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;

class ApprovalRequestsWidget extends BaseWidget
{
    protected static ?string $heading = 'Permintaan Persetujuan Menunggu';

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;



    public function table(Table $table): Table
    {
        $user = Auth::user();

        return $table
            ->query(
                ApprovalRequest::query()
                    ->where('client_id', $user->client_id)
                    ->where('status', 'pending')
                    ->forApprover($user)
                    ->with(['approvable', 'approvalWorkflow', 'requester'])
                    ->latest('requested_at')
            )
            ->columns([
                Tables\Columns\TextColumn::make('approvalWorkflow.workflow_name')
                    ->label('Workflow')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approvable_type')
                    ->label('Tipe')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'App\\Models\\PurchaseOrder' => 'Pesanan Pembelian',
                        'App\\Models\\Journal' => 'Jurnal Entry',
                        'App\\Models\\Expense' => 'Beban',
                        'App\\Models\\Invoice' => 'Invoice',
                        default => $state
                    }),
                Tables\Columns\TextColumn::make('approvable_id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Jumlah')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('requester.name')
                    ->label('Diminta Oleh')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_step')
                    ->label('Step')
                    ->sortable(),
                Tables\Columns\TextColumn::make('requested_at')
                    ->label('Tanggal Request')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Setujui Permintaan')
                    ->modalDescription('Apakah Anda yakin ingin menyetujui permintaan ini?')
                    ->action(function (ApprovalRequest $record) {
                        $service = app(ApprovalWorkflowService::class);
                        $result = $service->approve($record, Auth::user());

                        if ($result) {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Permintaan berhasil disetujui!'
                            ]);
                        } else {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Gagal menyetujui permintaan!'
                            ]);
                        }
                    }),
                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->form([
                        \Filament\Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function (ApprovalRequest $record, array $data) {
                        $service = app(ApprovalWorkflowService::class);
                        $result = $service->reject(
                            $record,
                            Auth::user(),
                            $data['rejection_reason']
                        );

                        if ($result) {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Permintaan berhasil ditolak!'
                            ]);
                        } else {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Gagal menolak permintaan!'
                            ]);
                        }
                    }),
            ])
            ->emptyStateHeading('Tidak ada permintaan persetujuan')
            ->emptyStateDescription('Saat ini tidak ada permintaan yang menunggu persetujuan Anda.')
            ->emptyStateIcon('heroicon-o-check-circle')
            ->defaultPaginationPageOption(5)
            ->poll('30s'); // Auto refresh every 30 seconds
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [5, 10, 25];
    }
}
