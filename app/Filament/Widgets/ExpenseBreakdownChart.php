<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use Filament\Facades\Filament;
use App\Models\JournalEntry;

class ExpenseBreakdownChart extends ChartWidget
{
    protected static ?string $heading = 'Breakdown Biaya Bulan Ini';

    protected static ?int $sort = 8;

    // Tidak tampil di dashboard utama
    protected static bool $isDiscoverable = false;

    protected function getData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        // Get expense breakdown by account subcategories
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $expenseCategories = [
            '61' => 'Biaya Bahan Baku',
            '62' => 'Biaya Tenaga Kerja',
            '63' => 'Biaya Overhead',
            '64' => 'Biaya Pemasaran',
            '65' => 'Biaya Administrasi',
        ];

        $labels = [];
        $data = [];

        foreach ($expenseCategories as $code => $label) {
            $amount = $this->getExpenseByCategory($clientId, $code, $currentMonth, $endOfMonth);
            $labels[] = $label;
            $data[] = $amount;
        }

        $expenseBreakdown = [
            'labels' => $labels,
            'data' => $data,
        ];

        $colors = [
            'rgba(239, 68, 68, 0.8)', // red
            'rgba(249, 115, 22, 0.8)', // orange
            'rgba(234, 179, 8, 0.8)', // yellow
            'rgba(34, 197, 94, 0.8)', // green
            'rgba(59, 130, 246, 0.8)', // blue
            'rgba(139, 92, 246, 0.8)', // purple
            'rgba(236, 72, 153, 0.8)', // pink
        ];

        $hoverColors = [
            'rgba(239, 68, 68, 1)', // red
            'rgba(249, 115, 22, 1)', // orange
            'rgba(234, 179, 8, 1)', // yellow
            'rgba(34, 197, 94, 1)', // green
            'rgba(59, 130, 246, 1)', // blue
            'rgba(139, 92, 246, 1)', // purple
            'rgba(236, 72, 153, 1)', // pink
        ];

        return [
            'datasets' => [
                [
                    'data' => $expenseBreakdown['data'],
                    'backgroundColor' => array_slice($colors, 0, count($expenseBreakdown['data'])),
                    'hoverBackgroundColor' => array_slice($hoverColors, 0, count($expenseBreakdown['data'])),
                    'borderWidth' => 2,
                    'borderColor' => '#ffffff',
                    'hoverBorderWidth' => 3,
                ],
            ],
            'labels' => $expenseBreakdown['labels'],
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                    'labels' => [
                        'usePointStyle' => true,
                        'padding' => 20,
                    ],
                ],
                'tooltip' => [
                    'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                    'titleColor' => '#fff',
                    'bodyColor' => '#fff',
                    'borderColor' => '#3b82f6',
                    'borderWidth' => 1,
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": Rp " + context.parsed.toLocaleString("id-ID");
                        }',
                    ],
                ],
            ],
            'animation' => [
                'animateRotate' => true,
                'animateScale' => true,
                'duration' => 2000,
            ],
            'hover' => [
                'animationDuration' => 400,
            ],
        ];
    }

    private function getExpenseByCategory(int $clientId, string $categoryCode, $startDate, $endDate): float
    {
        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $categoryCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $categoryCode . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $categoryCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $categoryCode . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        // For expense accounts, return debit - credit
        return $totalDebits - $totalCredits;
    }
}
