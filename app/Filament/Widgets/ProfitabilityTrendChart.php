<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use Filament\Facades\Filament;
use App\Models\JournalEntry;
use Carbon\Carbon;

class ProfitabilityTrendChart extends ChartWidget
{
    protected static ?string $heading = 'Trend Profitabilitas (12 Bulan)';

    protected static ?int $sort = 7;

    // Tidak tampil di dashboard utama
    protected static bool $isDiscoverable = false;

    protected function getData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        $profitTrend = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            // Calculate revenue and expenses for this month
            $revenue = $this->getAccountGroupTotal($clientId, '4', $startOfMonth, $endOfMonth);
            $expenses = $this->getAccountGroupTotal($clientId, '6', $startOfMonth, $endOfMonth);
            $profit = $revenue - $expenses;
            $margin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;

            $profitTrend[] = [
                'period' => $date->format('M Y'),
                'profit' => $profit,
                'margin' => $margin,
            ];
        }

        $labels = [];
        $profits = [];
        $margins = [];

        foreach ($profitTrend as $data) {
            $labels[] = $data['period'];
            $profits[] = $data['profit'];
            $margins[] = $data['margin'];
        }

        return [
            'datasets' => [
                [
                    'label' => 'Laba (Rp)',
                    'data' => $profits,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Margin (%)',
                    'data' => $margins,
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'borderColor' => 'rgb(16, 185, 129)',
                    'borderWidth' => 2,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + value.toLocaleString("id-ID"); }',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'beginAtZero' => true,
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'ticks' => [
                        'callback' => 'function(value) { return value + "%"; }',
                    ],
                ],
            ],
        ];
    }

    private function getAccountGroupTotal(int $clientId, string $accountPrefix, Carbon $startDate, Carbon $endDate): float
    {
        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        // For revenue accounts (4xxx), return credit - debit
        // For expense accounts (6xxx), return debit - credit
        if ($accountPrefix === '4') {
            return $totalCredits - $totalDebits;
        } else {
            return $totalDebits - $totalCredits;
        }
    }
}
