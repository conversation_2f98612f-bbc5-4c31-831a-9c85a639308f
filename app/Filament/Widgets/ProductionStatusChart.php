<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use Filament\Facades\Filament;
use App\Models\ProductionOrder;

class ProductionStatusChart extends ChartWidget
{
    protected static ?string $heading = 'Status Produksi';

    protected static ?int $sort = 4;

    // Tidak tampil di dashboard utama
    protected static bool $isDiscoverable = false;

    protected function getData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        $productionOrders = ProductionOrder::where('client_id', $clientId)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $statusLabels = [
            'planned' => 'Direncanakan',
            'in_progress' => 'Dalam Proses',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
        ];

        $labels = [];
        $data = [];
        $colors = [
            'planned' => '#f59e0b',
            'in_progress' => '#3b82f6',
            'completed' => '#10b981',
            'cancelled' => '#ef4444',
        ];

        foreach ($productionOrders as $status => $count) {
            $labels[] = $statusLabels[$status] ?? $status;
            $data[] = $count;
        }

        return [
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => array_values($colors),
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
        ];
    }
}
