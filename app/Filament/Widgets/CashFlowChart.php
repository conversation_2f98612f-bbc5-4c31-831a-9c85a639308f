<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use Filament\Facades\Filament;
use App\Models\JournalEntry;
use Carbon\Carbon;

class CashFlowChart extends ChartWidget
{
    protected static ?string $heading = 'Arus Kas (6 Bulan)';

    protected static ?int $sort = 6;

    // Tidak tampil di dashboard utama
    protected static bool $isDiscoverable = false;

    protected function getData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        $months = [];
        $inflows = [];
        $outflows = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            // Cash inflows (revenue accounts 4xxx)
            $monthlyInflow = $this->getCashFlowForPeriod($clientId, '4', $startOfMonth, $endOfMonth);

            // Cash outflows (expense accounts 6xxx)
            $monthlyOutflow = $this->getCashFlowForPeriod($clientId, '6', $startOfMonth, $endOfMonth);

            $months[] = $date->format('M Y');
            $inflows[] = $monthlyInflow;
            $outflows[] = $monthlyOutflow;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Arus Masuk',
                    'data' => $inflows,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.8)',
                    'borderColor' => 'rgb(34, 197, 94)',
                ],
                [
                    'label' => 'Arus Keluar',
                    'data' => $outflows,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.8)',
                    'borderColor' => 'rgb(239, 68, 68)',
                ],
            ],
            'labels' => $months,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + value.toLocaleString("id-ID"); }',
                    ],
                ],
            ],
        ];
    }

    private function getCashFlowForPeriod(int $clientId, string $accountPrefix, Carbon $startDate, Carbon $endDate): float
    {
        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        // For revenue accounts (4xxx), return credit - debit (inflows)
        // For expense accounts (6xxx), return debit - credit (outflows)
        if ($accountPrefix === '4') {
            return $totalCredits - $totalDebits;
        } else {
            return $totalDebits - $totalCredits;
        }
    }
}
