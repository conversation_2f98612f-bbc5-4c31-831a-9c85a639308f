<?php

namespace App\Filament\Widgets;

use App\Models\Product;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ProductEngineeringWidget extends Widget
{
    protected static string $view = 'filament.widgets.product-engineering';

    // Tidak tampil di dashboard utama, hanya di ProductEngineeringDashboard
    protected static bool $isDiscoverable = false;

    protected int | string | array $columnSpan = 'full';

    public function getData(): array
    {
        $clientId = Auth::user()->client_id;

        // Get products with their sales data from transactions
        $products = Product::where('client_id', $clientId)
            ->where('is_active', true)
            ->with(['transactionItems' => function ($query) {
                $query->whereHas('transaction', function ($q) {
                    $q->where('type', 'sales')
                        ->where('status', 'completed')
                        ->whereBetween('transaction_date', [
                            Carbon::now()->subMonths(6),
                            Carbon::now()
                        ]);
                });
            }])
            ->get();

        $productData = [];

        foreach ($products as $product) {
            // Calculate sales metrics from transaction items
            $salesData = $product->transactionItems;

            $totalQuantitySold = $salesData->sum('quantity');
            $totalRevenue = $salesData->sum('line_total');
            $salesFrequency = $salesData->count();

            // Calculate margin
            $averageSellingPrice = $totalQuantitySold > 0 ? $totalRevenue / $totalQuantitySold : 0;
            $margin = 0;
            if ($averageSellingPrice > 0 && $product->standard_cost > 0) {
                $margin = (($averageSellingPrice - $product->standard_cost) / $averageSellingPrice) * 100;
            }

            // Categorize products into quadrants
            $isHighMargin = $margin > 20; // Above 20% margin
            $isHighFrequency = $salesFrequency > 5; // More than 5 sales in 6 months

            $quadrant = '';
            $color = '';

            if ($isHighMargin && $isHighFrequency) {
                $quadrant = 'Star Products';
                $color = 'success';
            } elseif ($isHighMargin && !$isHighFrequency) {
                $quadrant = 'Cash Cows';
                $color = 'warning';
            } elseif (!$isHighMargin && $isHighFrequency) {
                $quadrant = 'Question Marks';
                $color = 'info';
            } else {
                $quadrant = 'Dogs';
                $color = 'danger';
            }

            // Calculate additional metrics
            $avgOrderValue = $salesFrequency > 0 ? $totalRevenue / $salesFrequency : 0;
            $profitPerUnit = $averageSellingPrice - $product->standard_cost;
            $totalProfit = $profitPerUnit * $totalQuantitySold;
            $lastSaleDate = $salesData->isNotEmpty() ?
                $salesData->first()->transaction->transaction_date : null;

            // Calculate velocity (sales per month)
            $velocity = $totalQuantitySold / 6; // 6 months period

            // Calculate stock turnover if we have inventory data
            $stockTurnover = $product->current_stock > 0 ?
                $totalQuantitySold / $product->current_stock : 0;

            $productData[] = [
                'id' => $product->id,
                'code' => $product->product_code,
                'name' => $product->product_name,
                'category' => $product->category->category_name ?? 'N/A',
                'margin' => round($margin, 2),
                'frequency' => $salesFrequency,
                'revenue' => $totalRevenue,
                'quantity_sold' => $totalQuantitySold,
                'avg_order_value' => $avgOrderValue,
                'profit_per_unit' => $profitPerUnit,
                'total_profit' => $totalProfit,
                'velocity' => round($velocity, 2),
                'stock_turnover' => round($stockTurnover, 2),
                'last_sale_date' => $lastSaleDate,
                'selling_price' => $product->selling_price,
                'standard_cost' => $product->standard_cost,
                'current_stock' => $product->current_stock ?? 0,
                'quadrant' => $quadrant,
                'color' => $color,
                'x' => $salesFrequency, // For chart plotting
                'y' => $margin, // For chart plotting
                'sales_history' => $this->getSalesHistory($product->id, $clientId),
            ];
        }

        // Sort by revenue descending
        usort($productData, function ($a, $b) {
            return $b['revenue'] <=> $a['revenue'];
        });

        return [
            'products' => $productData,
            'summary' => $this->getSummaryData($productData),
            'chartData' => $this->getChartData($productData),
            'quadrant_data' => $this->getQuadrantData($productData),
            'summary_stats' => $this->getSummaryStats($productData),
            'trend_data' => $this->getTrendData($clientId),
            'performance_analysis' => $this->getPerformanceAnalysis($productData),
            'recommendations' => $this->getRecommendations($productData),
            'period' => [
                'start' => Carbon::now()->subMonths(6)->format('M Y'),
                'end' => Carbon::now()->format('M Y'),
            ],
        ];
    }

    private function getSummaryData(array $products): array
    {
        $total = count($products);

        $quadrantCounts = [
            'Star Products' => 0,
            'Cash Cows' => 0,
            'Question Marks' => 0,
            'Dogs' => 0,
        ];

        foreach ($products as $product) {
            $quadrantCounts[$product['quadrant']]++;
        }

        return [
            'total_products' => $total,
            'star_products' => $quadrantCounts['Star Products'],
            'cash_cows' => $quadrantCounts['Cash Cows'],
            'question_marks' => $quadrantCounts['Question Marks'],
            'dogs' => $quadrantCounts['Dogs'],
            'star_percentage' => $total > 0 ? round(($quadrantCounts['Star Products'] / $total) * 100, 1) : 0,
        ];
    }

    private function getChartData(array $products): array
    {
        $chartData = [
            'datasets' => [
                [
                    'label' => 'Star Products',
                    'data' => [],
                    'backgroundColor' => 'rgba(34, 197, 94, 0.6)',
                    'borderColor' => 'rgba(34, 197, 94, 1)',
                ],
                [
                    'label' => 'Cash Cows',
                    'data' => [],
                    'backgroundColor' => 'rgba(251, 191, 36, 0.6)',
                    'borderColor' => 'rgba(251, 191, 36, 1)',
                ],
                [
                    'label' => 'Question Marks',
                    'data' => [],
                    'backgroundColor' => 'rgba(59, 130, 246, 0.6)',
                    'borderColor' => 'rgba(59, 130, 246, 1)',
                ],
                [
                    'label' => 'Dogs',
                    'data' => [],
                    'backgroundColor' => 'rgba(239, 68, 68, 0.6)',
                    'borderColor' => 'rgba(239, 68, 68, 1)',
                ],
            ],
        ];

        foreach ($products as $product) {
            $dataPoint = [
                'x' => $product['frequency'],
                'y' => $product['margin'],
                'label' => $product['name'],
                'revenue' => $product['revenue'],
            ];

            switch ($product['quadrant']) {
                case 'Star Products':
                    $chartData['datasets'][0]['data'][] = $dataPoint;
                    break;
                case 'Cash Cows':
                    $chartData['datasets'][1]['data'][] = $dataPoint;
                    break;
                case 'Question Marks':
                    $chartData['datasets'][2]['data'][] = $dataPoint;
                    break;
                case 'Dogs':
                    $chartData['datasets'][3]['data'][] = $dataPoint;
                    break;
            }
        }

        return $chartData;
    }

    private function getQuadrantData(array $products): array
    {
        $quadrants = [
            'high_margin_high_frequency' => [],
            'high_margin_low_frequency' => [],
            'low_margin_high_frequency' => [],
            'low_margin_low_frequency' => [],
        ];

        foreach ($products as $product) {
            $productData = [
                'id' => $product['id'],
                'product_code' => $product['code'],
                'product_name' => $product['name'],
                'category' => $product['category'],
                'margin_percentage' => $product['margin'],
                'sales_frequency' => $product['frequency'],
                'revenue' => $product['revenue'],
                'total_profit' => $product['total_profit'],
                'velocity' => $product['velocity'],
                'last_sale_date' => $product['last_sale_date'],
                'sales_history' => $product['sales_history'],
            ];

            $isHighMargin = $product['margin'] > 20;
            $isHighFrequency = $product['frequency'] > 5;

            if ($isHighMargin && $isHighFrequency) {
                $quadrants['high_margin_high_frequency'][] = $productData;
            } elseif ($isHighMargin && !$isHighFrequency) {
                $quadrants['high_margin_low_frequency'][] = $productData;
            } elseif (!$isHighMargin && $isHighFrequency) {
                $quadrants['low_margin_high_frequency'][] = $productData;
            } else {
                $quadrants['low_margin_low_frequency'][] = $productData;
            }
        }

        return $quadrants;
    }

    private function getSummaryStats(array $products): array
    {
        $totalProducts = count($products);
        $totalRevenue = array_sum(array_column($products, 'revenue'));
        $avgMargin = $totalProducts > 0 ? array_sum(array_column($products, 'margin')) / $totalProducts : 0;
        $avgFrequency = $totalProducts > 0 ? array_sum(array_column($products, 'frequency')) / $totalProducts : 0;

        // Find top performer
        $topPerformer = null;
        if (!empty($products)) {
            $topPerformer = $products[0]; // Already sorted by revenue
        }

        return [
            'total_products' => $totalProducts,
            'total_revenue' => $totalRevenue,
            'avg_margin' => $avgMargin,
            'avg_frequency' => $avgFrequency,
            'top_performer' => $topPerformer ? [
                'product_code' => $topPerformer['code'],
                'product_name' => $topPerformer['name'],
                'revenue' => $topPerformer['revenue'],
            ] : null,
        ];
    }

    private function getTrendData(int $clientId): array
    {
        $trends = [];

        for ($i = 11; $i >= 0; $i--) {
            $startDate = Carbon::now()->subMonths($i)->startOfMonth();
            $endDate = Carbon::now()->subMonths($i)->endOfMonth();

            // Get products for this month
            $monthlyProducts = Product::where('client_id', $clientId)
                ->where('is_active', true)
                ->with(['transactionItems' => function ($query) use ($startDate, $endDate) {
                    $query->whereHas('transaction', function ($q) use ($startDate, $endDate) {
                        $q->where('type', 'sales')
                            ->where('status', 'completed')
                            ->whereBetween('transaction_date', [$startDate, $endDate]);
                    });
                }])
                ->get();

            $quadrantCounts = [
                'high_margin_high_frequency_count' => 0,
                'high_margin_low_frequency_count' => 0,
                'low_margin_high_frequency_count' => 0,
                'low_margin_low_frequency_count' => 0,
            ];

            $monthlyRevenue = 0;

            foreach ($monthlyProducts as $product) {
                $salesData = $product->transactionItems;
                $totalQuantitySold = $salesData->sum('quantity');
                $totalRevenue = $salesData->sum('line_total');
                $salesFrequency = $salesData->count();

                $monthlyRevenue += $totalRevenue;

                if ($totalQuantitySold > 0) {
                    $averageSellingPrice = $totalRevenue / $totalQuantitySold;
                    $margin = 0;
                    if ($averageSellingPrice > 0 && $product->standard_cost > 0) {
                        $margin = (($averageSellingPrice - $product->standard_cost) / $averageSellingPrice) * 100;
                    }

                    $isHighMargin = $margin > 20;
                    $isHighFrequency = $salesFrequency > 2; // Lower threshold for monthly data

                    if ($isHighMargin && $isHighFrequency) {
                        $quadrantCounts['high_margin_high_frequency_count']++;
                    } elseif ($isHighMargin && !$isHighFrequency) {
                        $quadrantCounts['high_margin_low_frequency_count']++;
                    } elseif (!$isHighMargin && $isHighFrequency) {
                        $quadrantCounts['low_margin_high_frequency_count']++;
                    } else {
                        $quadrantCounts['low_margin_low_frequency_count']++;
                    }
                }
            }

            $trends[] = array_merge([
                'month' => $startDate->format('M Y'),
                'total_revenue' => $monthlyRevenue,
            ], $quadrantCounts);
        }

        return $trends;
    }

    private function getSalesHistory(int $productId, int $clientId): array
    {
        $history = [];

        // Get last 10 sales transactions for this product
        $transactionItems = \App\Models\TransactionItem::where('product_id', $productId)
            ->where('client_id', $clientId)
            ->whereHas('transaction', function ($query) {
                $query->where('type', 'sales')
                    ->where('status', 'completed');
            })
            ->with(['transaction.customer'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        foreach ($transactionItems as $item) {
            $history[] = [
                'date' => $item->transaction->transaction_date->format('d M Y'),
                'customer' => $item->transaction->customer->customer_name ?? 'N/A',
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'total' => $item->line_total,
                'transaction_id' => $item->transaction_id,
            ];
        }

        return $history;
    }

    private function getPerformanceAnalysis(array $products): array
    {
        if (empty($products)) {
            return [
                'top_revenue_products' => [],
                'top_profit_products' => [],
                'fastest_moving_products' => [],
                'slowest_moving_products' => [],
                'high_margin_low_sales' => [],
                'declining_products' => [],
            ];
        }

        // Sort products by different metrics
        $byRevenue = $products;
        usort($byRevenue, fn($a, $b) => $b['revenue'] <=> $a['revenue']);

        $byProfit = $products;
        usort($byProfit, fn($a, $b) => $b['total_profit'] <=> $a['total_profit']);

        $byVelocity = $products;
        usort($byVelocity, fn($a, $b) => $b['velocity'] <=> $a['velocity']);

        // Find products with high margin but low sales
        $highMarginLowSales = array_filter($products, function ($product) {
            return $product['margin'] > 25 && $product['frequency'] < 3;
        });

        // Find products that haven't sold recently
        $decliningProducts = array_filter($products, function ($product) {
            if (!$product['last_sale_date']) return true;
            $lastSale = Carbon::parse($product['last_sale_date']);
            return $lastSale->diffInDays(now()) > 60; // No sales in 60 days
        });

        return [
            'top_revenue_products' => array_slice($byRevenue, 0, 5),
            'top_profit_products' => array_slice($byProfit, 0, 5),
            'fastest_moving_products' => array_slice($byVelocity, 0, 5),
            'slowest_moving_products' => array_slice(array_reverse($byVelocity), 0, 5),
            'high_margin_low_sales' => array_slice($highMarginLowSales, 0, 5),
            'declining_products' => array_slice($decliningProducts, 0, 5),
        ];
    }

    private function getRecommendations(array $products): array
    {
        $recommendations = [];

        foreach ($products as $product) {
            $productRecommendations = [];

            // Based on quadrant
            switch ($product['quadrant']) {
                case 'Star Products':
                    $productRecommendations[] = 'Tingkatkan stok dan kapasitas produksi';
                    $productRecommendations[] = 'Ekspansi ke pasar baru';
                    $productRecommendations[] = 'Pertahankan kualitas dan inovasi';
                    break;

                case 'Cash Cows':
                    $productRecommendations[] = 'Optimalisasi biaya produksi';
                    $productRecommendations[] = 'Leverage economies of scale';
                    $productRecommendations[] = 'Fokus pada efisiensi operasional';
                    break;

                case 'Question Marks':
                    $productRecommendations[] = 'Tingkatkan aktivitas marketing';
                    $productRecommendations[] = 'Analisis target market yang tepat';
                    $productRecommendations[] = 'Evaluasi pricing strategy';
                    break;

                case 'Dogs':
                    $productRecommendations[] = 'Pertimbangkan untuk discontinue';
                    $productRecommendations[] = 'Reposisi ke niche market';
                    $productRecommendations[] = 'Bundle dengan produk lain';
                    break;
            }

            // Additional recommendations based on metrics
            if ($product['velocity'] < 1) {
                $productRecommendations[] = 'Produk bergerak lambat - review strategi penjualan';
            }

            if ($product['stock_turnover'] < 2) {
                $productRecommendations[] = 'Stock turnover rendah - optimalisasi inventory';
            }

            if ($product['last_sale_date'] && Carbon::parse($product['last_sale_date'])->diffInDays(now()) > 30) {
                $productRecommendations[] = 'Tidak ada penjualan dalam 30 hari - perlu promosi khusus';
            }

            $recommendations[$product['id']] = [
                'product_name' => $product['name'],
                'recommendations' => $productRecommendations,
                'priority' => $this->calculatePriority($product),
            ];
        }

        // Sort by priority
        uasort($recommendations, fn($a, $b) => $b['priority'] <=> $a['priority']);

        return $recommendations;
    }

    private function calculatePriority(array $product): int
    {
        $priority = 0;

        // High revenue products get higher priority
        if ($product['revenue'] > 1000000) $priority += 3;
        elseif ($product['revenue'] > 500000) $priority += 2;
        elseif ($product['revenue'] > 100000) $priority += 1;

        // High margin products get priority
        if ($product['margin'] > 30) $priority += 2;
        elseif ($product['margin'] > 20) $priority += 1;

        // Fast moving products get priority
        if ($product['velocity'] > 10) $priority += 2;
        elseif ($product['velocity'] > 5) $priority += 1;

        // Declining products get urgent priority
        if ($product['last_sale_date'] && Carbon::parse($product['last_sale_date'])->diffInDays(now()) > 60) {
            $priority += 3;
        }

        return $priority;
    }

    public static function canView(): bool
    {
        return Auth::check() && Auth::user()->client_id;
    }
}
