<?php

namespace App\Filament\Widgets;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\Transaction;
use App\Models\PurchaseOrder;
use Filament\Facades\Filament;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class AdvancedFinancialWidget extends Widget
{
    protected static string $view = 'filament.widgets.advanced-financial';



    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '30s';

    protected static bool $isLazy = false;

    public $selectedPeriod = 'month';
    public $comparisonPeriod = 'previous_month';

    public function getViewData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        $periods = $this->getPeriodDates();
        $currentPeriod = $periods['current'];
        $comparisonPeriod = $periods['comparison'];

        return [
            'financial_health' => $this->getFinancialHealthScore($clientId, $currentPeriod),
            'cash_flow_forecast' => $this->getCashFlowForecast($clientId),
            'profitability_analysis' => $this->getProfitabilityAnalysis($clientId, $currentPeriod, $comparisonPeriod),
            'profitability_monthly' => $this->getMonthlyProfitability($clientId),
            'working_capital' => $this->getWorkingCapitalAnalysis($clientId, $currentPeriod),
            'top_customers' => $this->getTopCustomers($clientId, $currentPeriod),
            'expense_trends' => $this->getExpenseTrends($clientId),
            'budget_variance' => $this->getBudgetVariance($clientId, $currentPeriod),
            'financial_ratios' => $this->getFinancialRatios($clientId, $currentPeriod),
            'alerts' => $this->getAdvancedAlerts($clientId),
            'period_selector' => [
                'current' => $this->selectedPeriod,
                'options' => [
                    'week' => 'Minggu Ini',
                    'month' => 'Bulan Ini',
                    'quarter' => 'Kuartal Ini',
                    'year' => 'Tahun Ini',
                ],
            ],
        ];
    }

    private function getPeriodDates(): array
    {
        $now = now();

        switch ($this->selectedPeriod) {
            case 'week':
                return [
                    'current' => [$now->startOfWeek(), $now->endOfWeek()],
                    'comparison' => [$now->subWeek()->startOfWeek(), $now->subWeek()->endOfWeek()],
                ];
            case 'quarter':
                return [
                    'current' => [$now->startOfQuarter(), $now->endOfQuarter()],
                    'comparison' => [$now->subQuarter()->startOfQuarter(), $now->subQuarter()->endOfQuarter()],
                ];
            case 'year':
                return [
                    'current' => [$now->startOfYear(), $now->endOfYear()],
                    'comparison' => [$now->subYear()->startOfYear(), $now->subYear()->endOfYear()],
                ];
            default: // month
                return [
                    'current' => [$now->startOfMonth(), $now->endOfMonth()],
                    'comparison' => [$now->subMonth()->startOfMonth(), $now->subMonth()->endOfMonth()],
                ];
        }
    }

    private function getFinancialHealthScore(int $clientId, array $period): array
    {
        $revenue = $this->getAccountGroupTotal($clientId, '4', $period[0], $period[1]);
        $expenses = $this->getAccountGroupTotal($clientId, '6', $period[0], $period[1]);
        $currentAssets = $this->getAccountGroupTotal($clientId, '11', $period[0], $period[1]);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', $period[0], $period[1]);
        $cash = $this->getAccountGroupTotal($clientId, '1101', $period[0], $period[1]);

        $profitMargin = $revenue > 0 ? (($revenue - $expenses) / $revenue) * 100 : 0;
        $currentRatio = $currentLiabilities > 0 ? $currentAssets / $currentLiabilities : 0;
        $cashRatio = $currentLiabilities > 0 ? $cash / $currentLiabilities : 0;

        // Calculate health score (0-100)
        $score = 0;
        $score += min(30, max(0, $profitMargin * 3)); // Max 30 points for profit margin
        $score += min(25, max(0, ($currentRatio - 1) * 25)); // Max 25 points for current ratio
        $score += min(20, max(0, $cashRatio * 100)); // Max 20 points for cash ratio
        $score += min(25, max(0, ($revenue > 0 ? 25 : 0))); // 25 points for having revenue

        return [
            'score' => round($score),
            'grade' => $this->getHealthGrade($score),
            'metrics' => [
                'profit_margin' => $profitMargin,
                'current_ratio' => $currentRatio,
                'cash_ratio' => $cashRatio,
                'revenue' => $revenue,
            ],
        ];
    }

    private function getHealthGrade(float $score): string
    {
        if ($score >= 90) return 'A+';
        if ($score >= 80) return 'A';
        if ($score >= 70) return 'B+';
        if ($score >= 60) return 'B';
        if ($score >= 50) return 'C+';
        if ($score >= 40) return 'C';
        return 'D';
    }

    private function getCashFlowForecast(int $clientId): array
    {
        // Simple 3-month forecast based on historical data
        $forecast = [];

        for ($i = 0; $i < 3; $i++) {
            $month = now()->addMonths($i);
            $startDate = $month->copy()->startOfMonth();
            $endDate = $month->copy()->endOfMonth();

            // Get historical average for same month in previous years
            $historicalInflow = $this->getAccountGroupTotal($clientId, '4', $startDate->copy()->subYear(), $endDate->copy()->subYear());
            $historicalOutflow = $this->getAccountGroupTotal($clientId, '6', $startDate->copy()->subYear(), $endDate->copy()->subYear());

            $forecast[] = [
                'month' => $month->format('M Y'),
                'projected_inflow' => $historicalInflow * 1.05, // 5% growth assumption
                'projected_outflow' => $historicalOutflow * 1.03, // 3% inflation assumption
                'net_flow' => ($historicalInflow * 1.05) - ($historicalOutflow * 1.03),
            ];
        }

        return $forecast;
    }

    private function getProfitabilityAnalysis(int $clientId, array $current, array $comparison): array
    {
        $currentRevenue = $this->getAccountGroupTotal($clientId, '4', $current[0], $current[1]);
        $currentExpenses = $this->getAccountGroupTotal($clientId, '6', $current[0], $current[1]);
        $currentProfit = $currentRevenue - $currentExpenses;

        $comparisonRevenue = $this->getAccountGroupTotal($clientId, '4', $comparison[0], $comparison[1]);
        $comparisonExpenses = $this->getAccountGroupTotal($clientId, '6', $comparison[0], $comparison[1]);
        $comparisonProfit = $comparisonRevenue - $comparisonExpenses;

        return [
            'current' => [
                'revenue' => $currentRevenue,
                'expenses' => $currentExpenses,
                'profit' => $currentProfit,
                'margin' => $currentRevenue > 0 ? ($currentProfit / $currentRevenue) * 100 : 0,
            ],
            'comparison' => [
                'revenue' => $comparisonRevenue,
                'expenses' => $comparisonExpenses,
                'profit' => $comparisonProfit,
                'margin' => $comparisonRevenue > 0 ? ($comparisonProfit / $comparisonRevenue) * 100 : 0,
            ],
            'growth' => [
                'revenue' => $comparisonRevenue > 0 ? (($currentRevenue - $comparisonRevenue) / $comparisonRevenue) * 100 : 0,
                'expenses' => $comparisonExpenses > 0 ? (($currentExpenses - $comparisonExpenses) / $comparisonExpenses) * 100 : 0,
                'profit' => $comparisonProfit != 0 ? (($currentProfit - $comparisonProfit) / abs($comparisonProfit)) * 100 : 0,
            ],
        ];
    }

    private function getMonthlyProfitability(int $clientId): array
    {
        $monthlyData = [];

        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $startDate = $month->copy()->startOfMonth();
            $endDate = $month->copy()->endOfMonth();

            $revenue = $this->getAccountGroupTotal($clientId, '4', $startDate, $endDate);
            $expenses = $this->getAccountGroupTotal($clientId, '6', $startDate, $endDate);
            $profit = $revenue - $expenses;

            $monthlyData[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue,
                'expenses' => $expenses,
                'profit' => $profit,
                'margin' => $revenue > 0 ? ($profit / $revenue) * 100 : 0,
            ];
        }

        return $monthlyData;
    }

    private function getWorkingCapitalAnalysis(int $clientId, array $period): array
    {
        $currentAssets = $this->getAccountGroupTotal($clientId, '11', $period[0], $period[1]);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', $period[0], $period[1]);
        $inventory = $this->getAccountGroupTotal($clientId, '1201', $period[0], $period[1]);
        $receivables = $this->getAccountGroupTotal($clientId, '1101', $period[0], $period[1]);
        $payables = $this->getAccountGroupTotal($clientId, '2101', $period[0], $period[1]);

        return [
            'working_capital' => $currentAssets - $currentLiabilities,
            'current_ratio' => $currentLiabilities > 0 ? $currentAssets / $currentLiabilities : 0,
            'quick_ratio' => $currentLiabilities > 0 ? ($currentAssets - $inventory) / $currentLiabilities : 0,
            'components' => [
                'current_assets' => $currentAssets,
                'current_liabilities' => $currentLiabilities,
                'inventory' => $inventory,
                'receivables' => $receivables,
                'payables' => $payables,
            ],
        ];
    }

    private function getTopCustomers(int $clientId, array $period): array
    {
        // Get top customers from sales transactions
        $topCustomers = Transaction::where('client_id', $clientId)
            ->where('type', 'sales')
            ->whereBetween('transaction_date', [$period[0], $period[1]])
            ->with('customer')
            ->select('customer_id', DB::raw('SUM(amount) as total_revenue'))
            ->groupBy('customer_id')
            ->orderBy('total_revenue', 'desc')
            ->limit(5)
            ->get();

        $result = [];
        foreach ($topCustomers as $transaction) {
            if ($transaction->customer) {
                // Calculate growth by comparing with previous period
                $previousRevenue = Transaction::where('client_id', $clientId)
                    ->where('type', 'sales')
                    ->where('customer_id', $transaction->customer_id)
                    ->whereBetween('transaction_date', [
                        $period[0]->copy()->subMonth(),
                        $period[1]->copy()->subMonth()
                    ])
                    ->sum('amount');

                $growth = $previousRevenue > 0 ?
                    (($transaction->total_revenue - $previousRevenue) / $previousRevenue) * 100 : 0;

                $result[] = [
                    'name' => $transaction->customer->customer_name,
                    'revenue' => $transaction->total_revenue,
                    'growth' => round($growth, 1),
                ];
            }
        }

        return $result;
    }

    private function getExpenseTrends(int $clientId): array
    {
        $trends = [];

        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $startDate = $month->copy()->startOfMonth();
            $endDate = $month->copy()->endOfMonth();

            $trends[] = [
                'month' => $month->format('M'),
                'operational' => $this->getAccountGroupTotal($clientId, '61', $startDate, $endDate),
                'administrative' => $this->getAccountGroupTotal($clientId, '62', $startDate, $endDate),
                'marketing' => $this->getAccountGroupTotal($clientId, '63', $startDate, $endDate),
            ];
        }

        return $trends;
    }

    private function getBudgetVariance(int $clientId, array $period): array
    {
        // Get actual financial data for the period
        $actualRevenue = $this->getAccountGroupTotal($clientId, '4', $period[0], $period[1]);
        $actualExpenses = $this->getAccountGroupTotal($clientId, '6', $period[0], $period[1]);
        $actualProfit = $actualRevenue - $actualExpenses;

        // Get budget data from Budget model if exists, otherwise use previous year as baseline
        $previousYearRevenue = $this->getAccountGroupTotal(
            $clientId,
            '4',
            $period[0]->copy()->subYear(),
            $period[1]->copy()->subYear()
        );
        $previousYearExpenses = $this->getAccountGroupTotal(
            $clientId,
            '6',
            $period[0]->copy()->subYear(),
            $period[1]->copy()->subYear()
        );
        $previousYearProfit = $previousYearRevenue - $previousYearExpenses;

        // Use previous year + 10% growth as budget baseline
        $budgetRevenue = $previousYearRevenue * 1.1;
        $budgetExpenses = $previousYearExpenses * 1.05;
        $budgetProfit = $budgetRevenue - $budgetExpenses;

        return [
            'revenue' => [
                'budget' => $budgetRevenue,
                'actual' => $actualRevenue,
                'variance' => $budgetRevenue > 0 ? (($actualRevenue - $budgetRevenue) / $budgetRevenue) * 100 : 0,
            ],
            'expenses' => [
                'budget' => $budgetExpenses,
                'actual' => $actualExpenses,
                'variance' => $budgetExpenses > 0 ? (($actualExpenses - $budgetExpenses) / $budgetExpenses) * 100 : 0,
            ],
            'profit' => [
                'budget' => $budgetProfit,
                'actual' => $actualProfit,
                'variance' => $budgetProfit != 0 ? (($actualProfit - $budgetProfit) / abs($budgetProfit)) * 100 : 0,
            ],
        ];
    }

    private function getFinancialRatios(int $clientId, array $period): array
    {
        $revenue = $this->getAccountGroupTotal($clientId, '4', $period[0], $period[1]);
        $expenses = $this->getAccountGroupTotal($clientId, '6', $period[0], $period[1]);
        $assets = $this->getAccountGroupTotal($clientId, '1', $period[0], $period[1]);
        $liabilities = $this->getAccountGroupTotal($clientId, '2', $period[0], $period[1]);
        $equity = $this->getAccountGroupTotal($clientId, '3', $period[0], $period[1]);

        return [
            'profitability' => [
                'gross_margin' => $revenue > 0 ? (($revenue - $expenses) / $revenue) * 100 : 0,
                'roa' => $assets > 0 ? (($revenue - $expenses) / $assets) * 100 : 0,
                'roe' => $equity > 0 ? (($revenue - $expenses) / $equity) * 100 : 0,
            ],
            'liquidity' => [
                'current_ratio' => $this->getAccountGroupTotal($clientId, '21', $period[0], $period[1]) > 0 ?
                    $this->getAccountGroupTotal($clientId, '11', $period[0], $period[1]) / $this->getAccountGroupTotal($clientId, '21', $period[0], $period[1]) : 0,
                'quick_ratio' => $this->calculateQuickRatio($clientId, $period),
                'cash_ratio' => $this->calculateCashRatio($clientId, $period),
            ],
            'leverage' => [
                'debt_to_equity' => $equity > 0 ? $liabilities / $equity : 0,
                'debt_to_assets' => $assets > 0 ? $liabilities / $assets : 0,
                'equity_ratio' => $assets > 0 ? $equity / $assets : 0,
            ],
        ];
    }

    private function getAdvancedAlerts(int $clientId): array
    {
        $alerts = [];

        // Cash flow alert
        $cashPosition = $this->getAccountGroupTotal($clientId, '1101', now()->startOfMonth(), now());
        if ($cashPosition < 5000000) {
            $alerts[] = [
                'type' => 'critical',
                'title' => 'Posisi Kas Kritis',
                'message' => 'Kas tersedia hanya Rp ' . number_format($cashPosition, 0, ',', '.'),
                'action' => 'Segera review cash flow dan piutang',
                'icon' => 'heroicon-m-exclamation-triangle',
            ];
        }

        // Profit margin alert
        $revenue = $this->getAccountGroupTotal($clientId, '4', now()->startOfMonth(), now());
        $expenses = $this->getAccountGroupTotal($clientId, '6', now()->startOfMonth(), now());
        $margin = $revenue > 0 ? (($revenue - $expenses) / $revenue) * 100 : 0;

        if ($margin < 5) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Margin Keuntungan Rendah',
                'message' => 'Margin profit hanya ' . number_format($margin, 1) . '%',
                'action' => 'Review struktur biaya dan pricing',
                'icon' => 'heroicon-m-arrow-trending-down',
            ];
        }

        return $alerts;
    }

    private function calculateQuickRatio(int $clientId, array $period): float
    {
        $currentAssets = $this->getAccountGroupTotal($clientId, '11', $period[0], $period[1]);
        $inventory = $this->getAccountGroupTotal($clientId, '1201', $period[0], $period[1]);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', $period[0], $period[1]);

        return $currentLiabilities > 0 ? ($currentAssets - $inventory) / $currentLiabilities : 0;
    }

    private function calculateCashRatio(int $clientId, array $period): float
    {
        $cash = $this->getAccountGroupTotal($clientId, '1101', $period[0], $period[1]);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', $period[0], $period[1]);

        return $currentLiabilities > 0 ? $cash / $currentLiabilities : 0;
    }

    private function getAccountGroupTotal(int $clientId, string $accountCode, $startDate, $endDate): float
    {
        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountCode . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountCode . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        // Return based on account type normal balance
        if (in_array(substr($accountCode, 0, 1), ['1', '6'])) {
            // Assets and Expenses: Debit balance
            return $totalDebits - $totalCredits;
        } else {
            // Liabilities, Equity, Revenue: Credit balance
            return $totalCredits - $totalDebits;
        }
    }
}
