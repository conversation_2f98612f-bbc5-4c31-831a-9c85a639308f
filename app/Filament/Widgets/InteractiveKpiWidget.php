<?php

namespace App\Filament\Widgets;

use App\Models\Account;
use App\Models\JournalEntry;
use Filament\Facades\Filament;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class InteractiveKpiWidget extends Widget
{
    protected static string $view = 'filament.widgets.interactive-kpi';

    // Tidak tampil di dashboard utama, hanya di FinancialDashboard
    protected static bool $isDiscoverable = false;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '30s';

    protected static bool $isLazy = false;

    public function getViewData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;

        // Current month data
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        // Previous month for comparison
        $previousMonth = now()->subMonth()->startOfMonth();
        $endOfPreviousMonth = now()->subMonth()->endOfMonth();

        // Revenue comparison
        $currentRevenue = $this->getAccountGroupTotal($clientId, '4', $currentMonth, $endOfMonth);
        $previousRevenue = $this->getAccountGroupTotal($clientId, '4', $previousMonth, $endOfPreviousMonth);
        $revenueGrowth = $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        // Expense comparison
        $currentExpenses = $this->getAccountGroupTotal($clientId, '6', $currentMonth, $endOfMonth);
        $previousExpenses = $this->getAccountGroupTotal($clientId, '6', $previousMonth, $endOfPreviousMonth);
        $expenseGrowth = $previousExpenses > 0 ? (($currentExpenses - $previousExpenses) / $previousExpenses) * 100 : 0;

        // Profit calculation
        $currentProfit = $currentRevenue - $currentExpenses;
        $previousProfit = $previousRevenue - $previousExpenses;
        $profitGrowth = $previousProfit != 0 ? (($currentProfit - $previousProfit) / abs($previousProfit)) * 100 : 0;

        // Cash position
        $cashAccounts = $this->getAccountGroupTotal($clientId, '1101', $currentMonth, $endOfMonth);

        // Quick ratios
        $currentAssets = $this->getAccountGroupTotal($clientId, '11', $currentMonth, $endOfMonth);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', $currentMonth, $endOfMonth);
        $currentRatio = $currentLiabilities > 0 ? $currentAssets / $currentLiabilities : 0;

        return [
            'revenue' => [
                'current' => $currentRevenue,
                'growth' => $revenueGrowth,
                'trend' => $this->getMonthlyTrend($clientId, '4', 6),
            ],
            'expenses' => [
                'current' => $currentExpenses,
                'growth' => $expenseGrowth,
                'trend' => $this->getMonthlyTrend($clientId, '6', 6),
            ],
            'profit' => [
                'current' => $currentProfit,
                'growth' => $profitGrowth,
                'margin' => $currentRevenue > 0 ? ($currentProfit / $currentRevenue) * 100 : 0,
            ],
            'cash' => [
                'current' => $cashAccounts,
                'ratio' => $currentRatio,
            ],
            'alerts' => $this->getAlerts($clientId),
        ];
    }

    private function getAccountGroupTotal(int $clientId, string $accountCode, $startDate, $endDate): float
    {
        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountCode . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountCode . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        // Return based on account type normal balance
        if (in_array(substr($accountCode, 0, 1), ['1', '6'])) {
            // Assets and Expenses: Debit balance
            return $totalDebits - $totalCredits;
        } else {
            // Liabilities, Equity, Revenue: Credit balance
            return $totalCredits - $totalDebits;
        }
    }

    private function getMonthlyTrend(int $clientId, string $accountCode, int $months): array
    {
        $trend = [];

        for ($i = $months - 1; $i >= 0; $i--) {
            $startDate = now()->subMonths($i)->startOfMonth();
            $endDate = now()->subMonths($i)->endOfMonth();

            $amount = $this->getAccountGroupTotal($clientId, $accountCode, $startDate, $endDate);
            $trend[] = $amount;
        }

        return $trend;
    }

    private function getAlerts(int $clientId): array
    {
        $alerts = [];

        // Check for overdue receivables
        // This would need proper receivables tracking

        // Check for low cash
        $cashPosition = $this->getAccountGroupTotal($clientId, '1101', now()->startOfMonth(), now());
        if ($cashPosition < ********) { // Less than 10M
            $alerts[] = [
                'type' => 'warning',
                'message' => 'Posisi kas rendah: Rp ' . number_format($cashPosition, 0, ',', '.'),
                'icon' => 'heroicon-m-exclamation-triangle',
            ];
        }

        // Check profit margin
        $revenue = $this->getAccountGroupTotal($clientId, '4', now()->startOfMonth(), now());
        $expenses = $this->getAccountGroupTotal($clientId, '6', now()->startOfMonth(), now());
        $profit = $revenue - $expenses;
        $margin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;

        if ($margin < 10) {
            $alerts[] = [
                'type' => 'danger',
                'message' => 'Margin keuntungan rendah: ' . number_format($margin, 1) . '%',
                'icon' => 'heroicon-m-arrow-trending-down',
            ];
        }

        return $alerts;
    }
}
