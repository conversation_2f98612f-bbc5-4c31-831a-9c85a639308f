<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\Account;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class SystemInfoWidget extends BaseWidget
{
    // Tidak tampil di dashboard utama
    protected static bool $isDiscoverable = false;

    protected function getStats(): array
    {
        $user = Auth::user();
        $clientId = $user->client_id;

        // Get system statistics
        $totalUsers = User::where('client_id', $clientId)->count();
        $totalAccounts = Account::where('client_id', $clientId)->count();
        $totalProducts = Product::where('client_id', $clientId)->count();
        $totalCustomers = Customer::where('client_id', $clientId)->count();
        $totalSuppliers = Supplier::where('client_id', $clientId)->count();



        return [
            Stat::make('Pengguna Sistem', $totalUsers)
                ->description('Total pengguna sistem')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Bagan Akun', $totalAccounts)
                ->description('Akun keuangan tersedia')
                ->descriptionIcon('heroicon-m-list-bullet')
                ->color('blue'),

            Stat::make('Data Master', $totalProducts + $totalCustomers + $totalSuppliers)
                ->description("Data: {$totalProducts} produk, {$totalCustomers} pelanggan, {$totalSuppliers} pemasok")
                ->descriptionIcon('heroicon-m-cube')
                ->color('green'),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }

    public function getDisplayName(): string
    {
        return 'Informasi Sistem';
    }
}
