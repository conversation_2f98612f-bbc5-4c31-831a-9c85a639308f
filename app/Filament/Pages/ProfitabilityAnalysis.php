<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;

use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Services\ProfitabilityAnalysisService;
use Filament\Facades\Filament;
use Carbon\Carbon;

class ProfitabilityAnalysis extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static string $view = 'filament.pages.profitability-analysis';
    protected static ?string $navigationGroup = 'Laporan';
    protected static ?string $title = 'Analisis Profitabilitas';
    protected static ?string $navigationLabel = 'Analisis Profitabilitas';
    protected static ?int $navigationSort = 5;

    public ?array $data = [];
    public $startDate;
    public $endDate;
    public $analysisType = 'product';

    public function mount(): void
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->endOfMonth()->format('Y-m-d');

        $this->form->fill([
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'analysis_type' => $this->analysisType,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Tanggal Mulai')
                    ->required()
                    ->default(now()->startOfMonth()),

                DatePicker::make('end_date')
                    ->label('Tanggal Akhir')
                    ->required()
                    ->default(now()->endOfMonth()),

                Select::make('analysis_type')
                    ->label('Jenis Analisis')
                    ->options([
                        'product' => 'Per Produk',
                        'customer' => 'Per Pelanggan',
                        'sales_person' => 'Per Sales Person',
                        'region' => 'Per Wilayah',
                    ])
                    ->default('product')
                    ->required(),
            ])
            ->columns(3)
            ->statePath('data');
    }

    public function updateAnalysis(): void
    {
        // Validate form data
        $this->form->getState();

        // Refresh the page data
        $this->dispatch('$refresh');
        $this->dispatch('refresh-charts');
    }

    // Listen for form field updates
    public function updatedData($value, $key): void
    {
        // Auto-refresh when any form field changes
        $this->updateAnalysis();
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action('updateAnalysis'),

            \Filament\Actions\Action::make('export')
                ->label('Export Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportToExcel'),
        ];
    }

    public function exportToExcel(): void
    {
        // Implementation for Excel export
        $this->notify('success', 'Export Excel akan segera tersedia');
    }

    protected function getViewData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;
        $service = app(ProfitabilityAnalysisService::class);

        $startDate = Carbon::parse($this->data['start_date'] ?? $this->startDate);
        $endDate = Carbon::parse($this->data['end_date'] ?? $this->endDate);
        $analysisType = $this->data['analysis_type'] ?? $this->analysisType;

        return [
            'summary' => $service->getProfitabilitySummary($clientId, $startDate, $endDate),
            'trends' => $service->getProfitabilityTrends($clientId, $startDate, $endDate),
            'tableData' => $service->getProfitabilityQuery($clientId, $startDate, $endDate, $analysisType),
            'analysisType' => $analysisType,
        ];
    }
}
