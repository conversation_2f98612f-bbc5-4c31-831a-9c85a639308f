<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\JournalEntry;
use App\Models\Account;
use Carbon\Carbon;
use Filament\Facades\Filament;

class CashFlowStatement extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';

    protected static ?string $navigationLabel = 'Laporan Arus Kas';

    protected static ?string $navigationGroup = 'Laporan';

    protected static ?int $navigationSort = 4;

    protected static string $view = 'filament.pages.cash-flow-statement';

    public function getCashFlowData()
    {
        $tenant = Filament::getTenant();
        $currentYear = Carbon::now()->year;

        // Get cash accounts (assuming account codes starting with 1101 for cash)
        $cashAccountIds = Account::where('account_type', 'asset')
            ->where(function ($q) {
                $q->where('account_name', 'like', '%kas%')
                    ->orWhere('account_name', 'like', '%cash%')
                    ->orWhere('account_code', 'like', '1101%');
            });

        if ($tenant) {
            $cashAccountIds->where('client_id', $tenant->id);
        }

        $cashAccountIds = $cashAccountIds->pluck('id');

        // Operating Activities (simplified - revenue and expenses)
        $operatingCashFlow = JournalEntry::whereHas('account', function ($q) {
            $q->whereIn('account_type', ['revenue', 'expense']);
        })
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->selectRaw('SUM(credit - debit) as flow')
            ->value('flow') ?? 0;

        // Cash from operations (cash account movements)
        $cashFromOperations = JournalEntry::whereIn('account_id', $cashAccountIds)
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->selectRaw('SUM(debit - credit) as flow')
            ->value('flow') ?? 0;

        // Beginning cash balance
        $beginningCash = JournalEntry::whereIn('account_id', $cashAccountIds)
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', '<', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->selectRaw('SUM(debit - credit) as balance')
            ->value('balance') ?? 0;

        // Ending cash balance
        $endingCash = $beginningCash + $cashFromOperations;

        return [
            'operating_cash_flow' => $operatingCashFlow,
            'cash_from_operations' => $cashFromOperations,
            'beginning_cash' => $beginningCash,
            'ending_cash' => $endingCash,
            'net_change' => $cashFromOperations,
            'year' => $currentYear,
        ];
    }

    public static function canAccess(): bool
    {
        return true; // All authenticated users can access
    }
}
