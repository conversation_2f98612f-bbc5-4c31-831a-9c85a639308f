<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\AdvancedFinancialWidget;
use App\Filament\Widgets\FinancialDashboardWidget;
use App\Filament\Widgets\InteractiveKpiWidget;
use Filament\Pages\Dashboard;

class FinancialDashboard extends Dashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Dashboard Keuangan';

    protected static ?string $title = 'Dashboard Keuangan';

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = 2;

    protected static string $routePath = '/financial-dashboard';

    public function getWidgets(): array
    {
        return [
            AdvancedFinancialWidget::class,
            InteractiveKpiWidget::class,
            FinancialDashboardWidget::class,
        ];
    }

    public function getColumns(): int | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 4,
        ];
    }
}
