<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use App\Models\Account;
use App\Models\JournalEntry;

use Carbon\Carbon;
use Filament\Facades\Filament;

class GeneralLedger extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Buku Besar';

    protected static ?string $navigationGroup = 'Laporan';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.general-ledger';

    public ?string $start_date = null;
    public ?string $end_date = null;
    public ?int $account_id = null;

    public function mount(): void
    {
        $this->start_date = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->end_date = Carbon::now()->endOfMonth()->format('Y-m-d');

        $this->form->fill([
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'account_id' => $this->account_id,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Start Date')
                    ->required()
                    ->reactive(),

                DatePicker::make('end_date')
                    ->label('End Date')
                    ->required()
                    ->reactive(),

                Select::make('account_id')
                    ->label('Account')
                    ->options(function () {
                        $tenant = Filament::getTenant();
                        $query = Account::query();

                        if ($tenant) {
                            $query->where('client_id', $tenant->id);
                        }

                        return $query->orderBy('account_code')
                            ->pluck('account_name', 'id');
                    })
                    ->searchable()
                    ->placeholder('All Accounts')
                    ->reactive(),
            ])
            ->statePath('data');
    }

    public function getGeneralLedgerData()
    {
        $query = JournalEntry::with(['journal', 'account'])
            ->whereHas('journal', function ($q) {
                $q->where('is_posted', true);

                if ($this->start_date) {
                    $q->where('journal_date', '>=', $this->start_date);
                }

                if ($this->end_date) {
                    $q->where('journal_date', '<=', $this->end_date);
                }

                // Apply tenant filtering
                $tenant = Filament::getTenant();
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            });

        if ($this->account_id) {
            $query->where('account_id', $this->account_id);
        }

        return $query->orderBy('created_at')->get();
    }

    public static function canAccess(): bool
    {
        return true; // All authenticated users can access
    }
}
