<?php

namespace App\Filament\Pages;

use App\Services\ExportImportService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Exceptions\Halt;


class ExportImport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-arrow-down-tray';

    protected static ?string $navigationLabel = 'Export/Import';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 7;

    protected static string $view = 'filament.pages.export-import';

    public ?array $exportData = [];
    public ?array $importData = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('export_import_tabs')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Export')
                            ->schema([
                                Forms\Components\Section::make('Export Data')
                                    ->description('Pilih data yang ingin diekspor dan format file')
                                    ->schema([
                                        Forms\Components\Select::make('export_type')
                                            ->label('Tipe Data')
                                            ->options(function () {
                                                $service = app(ExportImportService::class);
                                                return $service->getAvailableExportTypes();
                                            })
                                            ->required()
                                            ->live(),

                                        Forms\Components\Select::make('export_format')
                                            ->label('Format File')
                                            ->options(function () {
                                                $service = app(ExportImportService::class);
                                                return $service->getAvailableFormats();
                                            })
                                            ->default('xlsx')
                                            ->required(),

                                        Forms\Components\Group::make([
                                            Forms\Components\DatePicker::make('date_from')
                                                ->label('Dari Tanggal')
                                                ->visible(fn(Forms\Get $get) => in_array($get('export_type'), ['transactions', 'journals', 'audit_logs'])),

                                            Forms\Components\DatePicker::make('date_to')
                                                ->label('Sampai Tanggal')
                                                ->visible(fn(Forms\Get $get) => in_array($get('export_type'), ['transactions', 'journals', 'audit_logs'])),
                                        ])->columns(2),

                                        Forms\Components\Select::make('account_id')
                                            ->label('Filter Akun')
                                            ->relationship('account', 'name')
                                            ->searchable()
                                            ->visible(fn(Forms\Get $get) => $get('export_type') === 'transactions'),

                                        Forms\Components\Select::make('transaction_type')
                                            ->label('Tipe Transaksi')
                                            ->options([
                                                'income' => 'Pemasukan',
                                                'expense' => 'Pengeluaran',
                                                'transfer' => 'Transfer',
                                            ])
                                            ->visible(fn(Forms\Get $get) => $get('export_type') === 'transactions'),
                                    ])->columns(2),
                            ]),

                        Forms\Components\Tabs\Tab::make('Import')
                            ->schema([
                                Forms\Components\Section::make('Import Data')
                                    ->description('Upload file untuk mengimpor data ke sistem')
                                    ->schema([
                                        Forms\Components\Select::make('import_type')
                                            ->label('Tipe Data')
                                            ->options([
                                                'accounts' => 'Chart of Accounts',
                                                'products' => 'Produk',
                                                'customers' => 'Pelanggan',
                                                'suppliers' => 'Supplier',
                                            ])
                                            ->required(),

                                        Forms\Components\FileUpload::make('import_file')
                                            ->label('File Import')
                                            ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'])
                                            ->required()
                                            ->directory('imports')
                                            ->visibility('private'),

                                        Forms\Components\Checkbox::make('update_existing')
                                            ->label('Update data yang sudah ada')
                                            ->helperText('Jika dicentang, data yang sudah ada akan diupdate. Jika tidak, data duplikat akan diabaikan.'),

                                        Forms\Components\Textarea::make('import_notes')
                                            ->label('Catatan Import')
                                            ->rows(3)
                                            ->placeholder('Catatan tambahan untuk proses import ini...'),
                                    ]),
                            ]),
                    ]),
            ])
            ->statePath('exportData');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('export')
                ->label('Export Data')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action('exportData'),

            Action::make('import')
                ->label('Import Data')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('primary')
                ->action('importData'),

            Action::make('download_template')
                ->label('Download Template')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->action('downloadTemplate'),
        ];
    }

    public function exportData()
    {
        try {
            $data = $this->form->getState();
            $service = app(ExportImportService::class);
            $clientId = filament()->getTenant()->id;

            $filters = [];
            if (isset($data['date_from'])) $filters['date_from'] = $data['date_from'];
            if (isset($data['date_to'])) $filters['date_to'] = $data['date_to'];
            if (isset($data['account_id'])) $filters['account_id'] = $data['account_id'];
            if (isset($data['transaction_type'])) $filters['type'] = $data['transaction_type'];

            return match ($data['export_type']) {
                'accounts' => $service->exportAccounts($clientId, $data['export_format']),
                'transactions' => $service->exportTransactions($clientId, $filters, $data['export_format']),
                'journals' => $service->exportJournals($clientId, $filters, $data['export_format']),
                'products' => $service->exportProducts($clientId, $data['export_format']),
                'customers' => $service->exportCustomers($clientId, $data['export_format']),
                'suppliers' => $service->exportSuppliers($clientId, $data['export_format']),
                'audit_logs' => $service->exportAuditLogs($clientId, $filters, $data['export_format']),
                default => throw new \Exception('Tipe export tidak valid'),
            };
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Export Gagal')
                ->body($e->getMessage())
                ->danger()
                ->send();

            throw new Halt();
        }
    }

    public function importData(): void
    {
        // Implementation for import functionality
        \Filament\Notifications\Notification::make()
            ->title('Import Berhasil')
            ->body('Data berhasil diimpor ke sistem')
            ->success()
            ->send();
    }

    public function downloadTemplate(): void
    {
        // Implementation for downloading import templates
        \Filament\Notifications\Notification::make()
            ->title('Template Downloaded')
            ->body('Template file berhasil didownload')
            ->success()
            ->send();
    }

    public function quickExport(string $type)
    {
        try {
            $service = app(ExportImportService::class);
            $clientId = filament()->getTenant()->id;

            $result = match ($type) {
                'accounts' => $service->exportAccounts($clientId, 'xlsx'),
                'transactions' => $service->exportTransactions($clientId, [], 'xlsx'),
                'products' => $service->exportProducts($clientId, 'xlsx'),
                'customers' => $service->exportCustomers($clientId, 'xlsx'),
                'suppliers' => $service->exportSuppliers($clientId, 'xlsx'),
                default => throw new \Exception('Tipe export tidak valid'),
            };

            \Filament\Notifications\Notification::make()
                ->title('Export Berhasil')
                ->body("Data {$type} berhasil diekspor")
                ->success()
                ->send();

            return $result;
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Export Gagal')
                ->body($e->getMessage())
                ->danger()
                ->send();

            throw new Halt();
        }
    }
}
