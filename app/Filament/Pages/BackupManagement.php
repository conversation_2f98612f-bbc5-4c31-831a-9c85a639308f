<?php

namespace App\Filament\Pages;

use App\Services\BackupService;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Pages\Page;

class BackupManagement extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationLabel = 'Backup & Recovery';

    protected static ?string $navigationGroup = 'Sistem';

    protected static ?int $navigationSort = 8;

    protected static string $view = 'filament.pages.backup-management';

    public function getBackups(): array
    {
        $service = app(BackupService::class);
        return $service->listBackups();
    }

    public function createBackup(array $data): void
    {
        $service = app(BackupService::class);
        $clientId = $data['backup_type'] === 'client' ? ($data['client_id'] ?? null) : null;

        try {
            $result = match ($data['backup_type']) {
                'full' => $service->createFullBackup(null),
                'client' => $service->createFullBackup($clientId),
                'database' => $service->createFullBackup(null), // Database only backup
                default => throw new \Exception('Tipe backup tidak valid'),
            };

            if ($result['status'] === 'success') {
                \Filament\Notifications\Notification::make()
                    ->title('Backup Berhasil')
                    ->body("Backup {$result['backup_name']} berhasil dibuat dalam {$result['duration']} detik")
                    ->success()
                    ->send();
            } else {
                \Filament\Notifications\Notification::make()
                    ->title('Backup Gagal')
                    ->body($result['error'])
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Backup Gagal')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function downloadBackup(string $filename)
    {
        try {
            $service = app(BackupService::class);
            return $service->downloadBackup($filename);
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Download Gagal')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function deleteBackup(string $filename): void
    {
        try {
            $filepath = "backups/{$filename}";

            if (\Illuminate\Support\Facades\Storage::disk('local')->exists($filepath)) {
                \Illuminate\Support\Facades\Storage::disk('local')->delete($filepath);

                \Filament\Notifications\Notification::make()
                    ->title('Backup Dihapus')
                    ->body('File backup berhasil dihapus')
                    ->success()
                    ->send();

                // Refresh the page
                $this->dispatch('refresh-backups');
            } else {
                \Filament\Notifications\Notification::make()
                    ->title('Hapus Gagal')
                    ->body('File backup tidak ditemukan')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Hapus Gagal')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_backup')
                ->label('Buat Backup')
                ->icon('heroicon-o-plus')
                ->color('success')
                ->form([
                    Forms\Components\Select::make('backup_type')
                        ->label('Tipe Backup')
                        ->options([
                            'full' => 'Full System Backup',
                            'client' => 'Client Data Only',
                            'database' => 'Database Only',
                        ])
                        ->default('full')
                        ->required()
                        ->live(),

                    Forms\Components\Select::make('client_id')
                        ->label('Pilih Client')
                        ->options(\App\Models\Client::pluck('name', 'id'))
                        ->visible(fn(Forms\Get $get) => $get('backup_type') === 'client')
                        ->required(fn(Forms\Get $get) => $get('backup_type') === 'client'),

                    Forms\Components\TextInput::make('backup_name')
                        ->label('Nama Backup')
                        ->placeholder('Opsional - akan dibuat otomatis jika kosong'),

                    Forms\Components\Textarea::make('description')
                        ->label('Deskripsi')
                        ->rows(3)
                        ->placeholder('Catatan untuk backup ini...'),
                ])
                ->action(function (array $data) {
                    $this->createBackup($data);
                }),

            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(fn() => $this->dispatch('refresh-backups')),
        ];
    }
}
