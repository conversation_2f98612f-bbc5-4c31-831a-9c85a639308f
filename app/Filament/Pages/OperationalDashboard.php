<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\AdvancedOperationalWidget;
use App\Filament\Widgets\OperationalDashboardWidget;
use Filament\Pages\Dashboard;

class OperationalDashboard extends Dashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Dashboard Operasional';

    protected static ?string $title = 'Dashboard Operasional';

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = 3;

    protected static string $routePath = '/operational-dashboard';

    public function getWidgets(): array
    {
        return [
            AdvancedOperationalWidget::class,
            OperationalDashboardWidget::class,
        ];
    }

    public function getColumns(): int | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 4,
        ];
    }
}
