<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\JournalEntry;
use Carbon\Carbon;
use Filament\Facades\Filament;

class BalanceSheet extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static ?string $navigationLabel = 'Neraca';

    protected static ?string $navigationGroup = 'Laporan';

    protected static ?int $navigationSort = 3;

    protected static string $view = 'filament.pages.balance-sheet';

    public function getBalanceSheetData()
    {
        $tenant = Filament::getTenant();
        $currentYear = Carbon::now()->year;

        // Assets
        $assets = JournalEntry::whereHas('account', function ($q) {
            $q->where('account_type', 'asset');
        })
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', '<=', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->selectRaw('SUM(debit - credit) as balance')
            ->value('balance') ?? 0;

        // Liabilities
        $liabilities = JournalEntry::whereHas('account', function ($q) {
            $q->where('account_type', 'liability');
        })
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', '<=', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->selectRaw('SUM(credit - debit) as balance')
            ->value('balance') ?? 0;

        // Equity
        $equity = JournalEntry::whereHas('account', function ($q) {
            $q->where('account_type', 'equity');
        })
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', '<=', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->selectRaw('SUM(credit - debit) as balance')
            ->value('balance') ?? 0;

        return [
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'total_liabilities_equity' => $liabilities + $equity,
            'year' => $currentYear,
        ];
    }

    public static function canAccess(): bool
    {
        return true; // All authenticated users can access
    }
}
