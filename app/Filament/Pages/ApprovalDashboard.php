<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\ApprovalDashboard as ApprovalDashboardWidget;
use App\Filament\Widgets\ApprovalRequestsWidget;
use Filament\Pages\Dashboard;

class ApprovalDashboard extends Dashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationLabel = 'Dashboard Persetujuan';

    protected static ?string $title = 'Dashboard Persetujuan';

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = 4;

    protected static string $routePath = '/approval-dashboard';

    public function getWidgets(): array
    {
        return [
            ApprovalDashboardWidget::class,
            ApprovalRequestsWidget::class,
        ];
    }

    public function getColumns(): int | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 4,
        ];
    }
}
