<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Services\FinancialRatioAnalysisService;
use Filament\Facades\Filament;
use Carbon\Carbon;

class FinancialRatioAnalysis extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-pie';
    protected static string $view = 'filament.pages.financial-ratio-analysis';
    protected static ?string $navigationGroup = 'Laporan';
    protected static ?string $title = 'Analisis Rasio Keuangan';
    protected static ?string $navigationLabel = 'Rasio Keuangan';
    protected static ?int $navigationSort = 8;

    public ?array $data = [];
    public $asOfDate;

    public function mount(): void
    {
        $this->asOfDate = now()->format('Y-m-d');

        $this->form->fill([
            'as_of_date' => $this->asOfDate,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('as_of_date')
                    ->label('Per Tanggal')
                    ->required()
                    ->default(now()),
            ])
            ->columns(1)
            ->statePath('data');
    }

    public function updateAnalysis(): void
    {
        // Trigger view refresh
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action('updateAnalysis'),

            \Filament\Actions\Action::make('export')
                ->label('Export Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportToExcel'),
        ];
    }

    public function exportToExcel(): void
    {
        $this->notify('success', 'Export Excel akan segera tersedia');
    }

    protected function getViewData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;
        $service = app(FinancialRatioAnalysisService::class);

        $asOfDate = Carbon::parse($this->data['as_of_date'] ?? $this->asOfDate);

        return [
            'liquidity_ratios' => $service->getLiquidityRatios($clientId, $asOfDate),
            'profitability_ratios' => $service->getProfitabilityRatios($clientId, $asOfDate),
            'efficiency_ratios' => $service->getEfficiencyRatios($clientId, $asOfDate),
            'leverage_ratios' => $service->getLeverageRatios($clientId, $asOfDate),
            'market_ratios' => $service->getMarketRatios($clientId, $asOfDate),
            'ratio_trends' => $service->getRatioTrends($clientId, $asOfDate),
        ];
    }
}
