<?php

namespace App\Filament\Pages;

use App\Models\Account;
use App\Models\JournalEntry;
use Filament\Facades\Filament;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;

class FinancialReportDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static ?string $navigationLabel = 'Laporan Keuangan';

    protected static ?string $title = 'Laporan Keuangan';

    protected static ?string $navigationGroup = 'Laporan';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.financial-report-dashboard';

    public $selectedPeriod = 'current_month';
    public $selectedReport = 'profit_loss';
    public $startDate;
    public $endDate;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->endOfMonth()->format('Y-m-d');
    }

    public function updatedSelectedPeriod()
    {
        switch ($this->selectedPeriod) {
            case 'current_month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_month':
                $this->startDate = now()->subMonth()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->subMonth()->endOfMonth()->format('Y-m-d');
                break;
            case 'current_year':
                $this->startDate = now()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->endOfYear()->format('Y-m-d');
                break;
            case 'last_year':
                $this->startDate = now()->subYear()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->subYear()->endOfYear()->format('Y-m-d');
                break;
        }
    }

    public function getFinancialReportData()
    {
        $clientId = Filament::getTenant()?->id ?? 1;
        $startDate = $this->startDate;
        $endDate = $this->endDate;

        return [
            'profit_loss' => $this->getProfitLossStatement($clientId, $startDate, $endDate),
            'balance_sheet' => $this->getBalanceSheet($clientId, $startDate, $endDate),
            'cash_flow' => $this->getCashFlowStatement($clientId, $startDate, $endDate),
            'trial_balance' => $this->getTrialBalance($clientId, $startDate, $endDate),
            'period_info' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'company_name' => Filament::getTenant()?->name ?? 'Perusahaan',
            ],
        ];
    }

    private function getProfitLossStatement($clientId, $startDate, $endDate)
    {
        // Revenue (Account Code 4xxx)
        $revenue = $this->getAccountGroupBalance($clientId, '4', $startDate, $endDate, 'credit');

        // Cost of Goods Sold (Account Code 5xxx)
        $cogs = $this->getAccountGroupBalance($clientId, '5', $startDate, $endDate, 'debit');

        // Operating Expenses (Account Code 6xxx)
        $operatingExpenses = $this->getAccountGroupBalance($clientId, '6', $startDate, $endDate, 'debit');

        // Other Income (Account Code 7xxx)
        $otherIncome = $this->getAccountGroupBalance($clientId, '7', $startDate, $endDate, 'credit');

        // Other Expenses (Account Code 8xxx)
        $otherExpenses = $this->getAccountGroupBalance($clientId, '8', $startDate, $endDate, 'debit');

        $grossProfit = $revenue - $cogs;
        $operatingProfit = $grossProfit - $operatingExpenses;
        $netProfit = $operatingProfit + $otherIncome - $otherExpenses;

        return [
            'revenue' => [
                'total' => $revenue,
                'details' => $this->getAccountDetails($clientId, '4', $startDate, $endDate, 'credit'),
            ],
            'cogs' => [
                'total' => $cogs,
                'details' => $this->getAccountDetails($clientId, '5', $startDate, $endDate, 'debit'),
            ],
            'gross_profit' => $grossProfit,
            'operating_expenses' => [
                'total' => $operatingExpenses,
                'details' => $this->getAccountDetails($clientId, '6', $startDate, $endDate, 'debit'),
            ],
            'operating_profit' => $operatingProfit,
            'other_income' => [
                'total' => $otherIncome,
                'details' => $this->getAccountDetails($clientId, '7', $startDate, $endDate, 'credit'),
            ],
            'other_expenses' => [
                'total' => $otherExpenses,
                'details' => $this->getAccountDetails($clientId, '8', $startDate, $endDate, 'debit'),
            ],
            'net_profit' => $netProfit,
        ];
    }

    private function getBalanceSheet($clientId, $startDate, $endDate)
    {
        // Assets
        $currentAssets = $this->getAccountGroupBalance($clientId, '11', $startDate, $endDate, 'debit');
        $fixedAssets = $this->getAccountGroupBalance($clientId, '12', $startDate, $endDate, 'debit');
        $otherAssets = $this->getAccountGroupBalance($clientId, '13', $startDate, $endDate, 'debit');
        $totalAssets = $currentAssets + $fixedAssets + $otherAssets;

        // Liabilities
        $currentLiabilities = $this->getAccountGroupBalance($clientId, '21', $startDate, $endDate, 'credit');
        $longTermLiabilities = $this->getAccountGroupBalance($clientId, '22', $startDate, $endDate, 'credit');
        $totalLiabilities = $currentLiabilities + $longTermLiabilities;

        // Equity
        $equity = $this->getAccountGroupBalance($clientId, '3', $startDate, $endDate, 'credit');

        // Add current period profit to equity
        $profitLoss = $this->getProfitLossStatement($clientId, $startDate, $endDate);
        $totalEquity = $equity + $profitLoss['net_profit'];

        return [
            'assets' => [
                'current_assets' => [
                    'total' => $currentAssets,
                    'details' => $this->getAccountDetails($clientId, '11', $startDate, $endDate, 'debit'),
                ],
                'fixed_assets' => [
                    'total' => $fixedAssets,
                    'details' => $this->getAccountDetails($clientId, '12', $startDate, $endDate, 'debit'),
                ],
                'other_assets' => [
                    'total' => $otherAssets,
                    'details' => $this->getAccountDetails($clientId, '13', $startDate, $endDate, 'debit'),
                ],
                'total' => $totalAssets,
            ],
            'liabilities' => [
                'current_liabilities' => [
                    'total' => $currentLiabilities,
                    'details' => $this->getAccountDetails($clientId, '21', $startDate, $endDate, 'credit'),
                ],
                'long_term_liabilities' => [
                    'total' => $longTermLiabilities,
                    'details' => $this->getAccountDetails($clientId, '22', $startDate, $endDate, 'credit'),
                ],
                'total' => $totalLiabilities,
            ],
            'equity' => [
                'total' => $totalEquity,
                'details' => array_merge(
                    $this->getAccountDetails($clientId, '3', $startDate, $endDate, 'credit'),
                    [['account_name' => 'Laba Rugi Periode Berjalan', 'balance' => $profitLoss['net_profit']]]
                ),
            ],
            'total_liabilities_equity' => $totalLiabilities + $totalEquity,
        ];
    }

    private function getCashFlowStatement($clientId, $startDate, $endDate)
    {
        // Simplified cash flow - would need more detailed implementation
        $profitLoss = $this->getProfitLossStatement($clientId, $startDate, $endDate);

        return [
            'operating_activities' => [
                'net_profit' => $profitLoss['net_profit'],
                'depreciation' => 0, // Would need to calculate from fixed assets
                'working_capital_changes' => 0, // Would need to calculate changes
                'total' => $profitLoss['net_profit'],
            ],
            'investing_activities' => [
                'asset_purchases' => 0,
                'asset_sales' => 0,
                'total' => 0,
            ],
            'financing_activities' => [
                'loan_proceeds' => 0,
                'loan_payments' => 0,
                'equity_changes' => 0,
                'total' => 0,
            ],
            'net_cash_flow' => $profitLoss['net_profit'],
        ];
    }

    private function getTrialBalance($clientId, $startDate, $endDate)
    {
        $accounts = Account::where('client_id', $clientId)
            ->orderBy('account_code')
            ->get();

        $trialBalance = [];
        $totalDebit = 0;
        $totalCredit = 0;

        foreach ($accounts as $account) {
            $balance = $this->getAccountBalance($clientId, $account->account_code, $startDate, $endDate);

            if ($balance != 0) {
                $debit = $balance > 0 ? $balance : 0;
                $credit = $balance < 0 ? abs($balance) : 0;

                $trialBalance[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'debit' => $debit,
                    'credit' => $credit,
                ];

                $totalDebit += $debit;
                $totalCredit += $credit;
            }
        }

        return [
            'accounts' => $trialBalance,
            'total_debit' => $totalDebit,
            'total_credit' => $totalCredit,
        ];
    }

    private function getAccountGroupBalance($clientId, $accountCodePrefix, $startDate, $endDate, $normalBalance)
    {
        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCodePrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountCodePrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCodePrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountCodePrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        return $normalBalance === 'credit' ? $totalCredits - $totalDebits : $totalDebits - $totalCredits;
    }

    private function getAccountDetails($clientId, $accountCodePrefix, $startDate, $endDate, $normalBalance)
    {
        $accounts = Account::where('client_id', $clientId)
            ->where('account_code', 'like', $accountCodePrefix . '%')
            ->get();

        $details = [];
        foreach ($accounts as $account) {
            $balance = $this->getAccountBalance($clientId, $account->account_code, $startDate, $endDate);
            if ($balance != 0) {
                $details[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'balance' => $balance,
                ];
            }
        }

        return $details;
    }

    private function getAccountBalance($clientId, $accountCode, $startDate, $endDate)
    {
        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', $accountCode);
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountCode) {
            $q->where('client_id', $clientId)
                ->where('account_code', $accountCode);
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        return $totalDebits - $totalCredits;
    }
}
