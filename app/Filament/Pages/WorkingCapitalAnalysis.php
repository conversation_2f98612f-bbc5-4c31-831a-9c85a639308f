<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Services\WorkingCapitalAnalysisService;
use Filament\Facades\Filament;
use Carbon\Carbon;

class WorkingCapitalAnalysis extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static string $view = 'filament.pages.working-capital-analysis';
    protected static ?string $navigationGroup = 'Laporan';
    protected static ?string $title = 'Analisis Modal Kerja';
    protected static ?string $navigationLabel = 'Modal Kerja';
    protected static ?int $navigationSort = 7;

    public ?array $data = [];
    public $asOfDate;

    public function mount(): void
    {
        $this->asOfDate = now()->format('Y-m-d');

        $this->form->fill([
            'as_of_date' => $this->asOfDate,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('as_of_date')
                    ->label('Per Tanggal')
                    ->required()
                    ->default(now()),
            ])
            ->columns(1)
            ->statePath('data');
    }

    public function updateAnalysis(): void
    {
        // Reset and refresh data
        $this->resetPage();
        $this->dispatch('refresh-charts');
    }

    public function updatedData(): void
    {
        // Auto-refresh when form data changes
        $this->updateAnalysis();
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action('updateAnalysis'),

            \Filament\Actions\Action::make('export')
                ->label('Export Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportToExcel'),
        ];
    }

    public function exportToExcel(): void
    {
        $this->notify('success', 'Export Excel akan segera tersedia');
    }

    protected function getViewData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;
        $service = app(WorkingCapitalAnalysisService::class);

        $asOfDate = Carbon::parse($this->data['as_of_date'] ?? $this->asOfDate);

        return [
            'working_capital' => $service->getWorkingCapitalAnalysis($clientId, $asOfDate),
            'cash_conversion' => $service->getCashConversionCycle($clientId, $asOfDate),
            'liquidity_ratios' => $service->getLiquidityRatios($clientId, $asOfDate),
            'trends' => $service->getWorkingCapitalTrends($clientId, $asOfDate),
            'components' => $service->getWorkingCapitalComponents($clientId, $asOfDate),
        ];
    }
}
