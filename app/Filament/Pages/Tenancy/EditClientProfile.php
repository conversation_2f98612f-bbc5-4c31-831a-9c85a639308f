<?php

namespace App\Filament\Pages\Tenancy;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Pages\Tenancy\EditTenantProfile;
use Illuminate\Support\Facades\Auth;

class EditClientProfile extends EditTenantProfile
{
    public static function getLabel(): string
    {
        return 'Client Profile';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->label('Company Name'),
                    
                TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true)
                    ->label('Email'),
                    
                TextInput::make('phone')
                    ->tel()
                    ->maxLength(255)
                    ->label('Phone'),
                    
                Textarea::make('address')
                    ->rows(3)
                    ->label('Address'),
                    
                Toggle::make('is_active')
                    ->label('Active Status')
                    ->visible(fn () => Auth::user()?->isSuperAdmin() ?? false),
            ]);
    }
}
