<?php

namespace App\Filament\Pages\Tenancy;

use App\Models\Client;
use App\Models\Account;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Pages\Tenancy\RegisterTenant;
use Illuminate\Support\Facades\Auth;

class RegisterClient extends RegisterTenant
{
    public static function getLabel(): string
    {
        return 'Register New Client';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->label('Company Name'),
                    
                TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255)
                    ->unique(Client::class)
                    ->label('Email'),
                    
                TextInput::make('phone')
                    ->tel()
                    ->maxLength(255)
                    ->label('Phone'),
                    
                Textarea::make('address')
                    ->rows(3)
                    ->label('Address'),
            ]);
    }

    protected function handleRegistration(array $data): Client
    {
        // Create the client
        $client = Client::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'is_active' => true,
            'created_by' => Auth::id(),
        ]);

        // Associate current user with the client if they don't have one
        $user = Auth::user();
        if (!$user->client_id) {
            $user->update(['client_id' => $client->id]);
        }

        // Create standard chart of accounts for the new client
        $this->createStandardChartOfAccounts($client);

        return $client;
    }

    /**
     * Create standard chart of accounts for new client
     */
    private function createStandardChartOfAccounts(Client $client): void
    {
        $standardAccounts = [
            // ASSETS
            ['code' => '1000', 'name' => 'ASET', 'type' => 'asset', 'balance' => 'debit', 'parent' => null],
            ['code' => '1100', 'name' => 'Aset Lancar', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1000'],
            ['code' => '1101', 'name' => 'Kas', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1100'],
            ['code' => '1102', 'name' => 'Bank', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1100'],
            ['code' => '1103', 'name' => 'Piutang Usaha', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1100'],
            ['code' => '1104', 'name' => 'Persediaan', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1100'],
            ['code' => '1200', 'name' => 'Aset Tetap', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1000'],
            ['code' => '1201', 'name' => 'Peralatan', 'type' => 'asset', 'balance' => 'debit', 'parent' => '1200'],
            ['code' => '1202', 'name' => 'Akumulasi Penyusutan Peralatan', 'type' => 'asset', 'balance' => 'credit', 'parent' => '1200'],

            // LIABILITIES
            ['code' => '2000', 'name' => 'KEWAJIBAN', 'type' => 'liability', 'balance' => 'credit', 'parent' => null],
            ['code' => '2100', 'name' => 'Kewajiban Lancar', 'type' => 'liability', 'balance' => 'credit', 'parent' => '2000'],
            ['code' => '2101', 'name' => 'Hutang Usaha', 'type' => 'liability', 'balance' => 'credit', 'parent' => '2100'],
            ['code' => '2102', 'name' => 'Hutang Pajak', 'type' => 'liability', 'balance' => 'credit', 'parent' => '2100'],
            ['code' => '2103', 'name' => 'Hutang Gaji', 'type' => 'liability', 'balance' => 'credit', 'parent' => '2100'],

            // EQUITY
            ['code' => '3000', 'name' => 'EKUITAS', 'type' => 'equity', 'balance' => 'credit', 'parent' => null],
            ['code' => '3101', 'name' => 'Modal', 'type' => 'equity', 'balance' => 'credit', 'parent' => '3000'],
            ['code' => '3102', 'name' => 'Laba Ditahan', 'type' => 'equity', 'balance' => 'credit', 'parent' => '3000'],

            // REVENUE
            ['code' => '4000', 'name' => 'PENDAPATAN', 'type' => 'revenue', 'balance' => 'credit', 'parent' => null],
            ['code' => '4101', 'name' => 'Pendapatan Jasa', 'type' => 'revenue', 'balance' => 'credit', 'parent' => '4000'],
            ['code' => '4102', 'name' => 'Pendapatan Lain-lain', 'type' => 'revenue', 'balance' => 'credit', 'parent' => '4000'],

            // EXPENSES
            ['code' => '5000', 'name' => 'BEBAN', 'type' => 'expense', 'balance' => 'debit', 'parent' => null],
            ['code' => '5101', 'name' => 'Beban Gaji', 'type' => 'expense', 'balance' => 'debit', 'parent' => '5000'],
            ['code' => '5102', 'name' => 'Beban Sewa', 'type' => 'expense', 'balance' => 'debit', 'parent' => '5000'],
            ['code' => '5103', 'name' => 'Beban Listrik', 'type' => 'expense', 'balance' => 'debit', 'parent' => '5000'],
            ['code' => '5104', 'name' => 'Beban Telepon', 'type' => 'expense', 'balance' => 'debit', 'parent' => '5000'],
            ['code' => '5105', 'name' => 'Beban Penyusutan', 'type' => 'expense', 'balance' => 'debit', 'parent' => '5000'],
            ['code' => '5106', 'name' => 'Beban Lain-lain', 'type' => 'expense', 'balance' => 'debit', 'parent' => '5000'],
        ];

        $accountMap = [];
        
        foreach ($standardAccounts as $accountData) {
            $parentId = null;
            if ($accountData['parent']) {
                $parentId = $accountMap[$accountData['parent']] ?? null;
            }

            $account = Account::create([
                'client_id' => $client->id,
                'account_code' => $accountData['code'],
                'account_name' => $accountData['name'],
                'account_type' => $accountData['type'],
                'normal_balance' => $accountData['balance'],
                'parent_account_id' => $parentId,
                'is_active' => true,
                'created_by' => Auth::id(),
            ]);

            $accountMap[$accountData['code']] = $account->id;
        }
    }
}
