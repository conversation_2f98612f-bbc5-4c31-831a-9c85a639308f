<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;

use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Services\SalesTargetAnalysisService;
use Filament\Facades\Filament;
use Carbon\Carbon;

class SalesTargetAnalysis extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-flag';
    protected static string $view = 'filament.pages.sales-target-analysis';
    protected static ?string $navigationGroup = 'Laporan';
    protected static ?string $title = 'Analisis Target Penjualan';
    protected static ?string $navigationLabel = 'Target Penjualan';
    protected static ?int $navigationSort = 6;

    public ?array $data = [];
    public $startDate;
    public $endDate;
    public $analysisType = 'sales_person';

    public function mount(): void
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->endOfMonth()->format('Y-m-d');

        $this->form->fill([
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'analysis_type' => $this->analysisType,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Tanggal Mulai')
                    ->required()
                    ->default(now()->startOfMonth()),

                DatePicker::make('end_date')
                    ->label('Tanggal Akhir')
                    ->required()
                    ->default(now()->endOfMonth()),

                Select::make('analysis_type')
                    ->label('Jenis Analisis')
                    ->options([
                        'sales_person' => 'Per Sales Person',
                        'product' => 'Per Produk',
                        'region' => 'Per Wilayah',
                        'customer' => 'Per Pelanggan',
                    ])
                    ->default('sales_person')
                    ->required(),
            ])
            ->columns(3)
            ->statePath('data');
    }



    public function updateAnalysis(): void
    {
        // Reset and refresh data
        $this->resetPage();
        $this->dispatch('refresh-charts');
    }

    public function updatedData(): void
    {
        // Auto-refresh when form data changes
        $this->updateAnalysis();
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action('updateAnalysis'),

            \Filament\Actions\Action::make('export')
                ->label('Export Excel')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportToExcel'),
        ];
    }

    public function exportToExcel(): void
    {
        $this->notify('success', 'Export Excel akan segera tersedia');
    }

    protected function getViewData(): array
    {
        $clientId = Filament::getTenant()?->id ?? 1;
        $service = app(SalesTargetAnalysisService::class);

        $startDate = Carbon::parse($this->data['start_date'] ?? $this->startDate);
        $endDate = Carbon::parse($this->data['end_date'] ?? $this->endDate);
        $analysisType = $this->data['analysis_type'] ?? $this->analysisType;

        return [
            'summary' => $service->getSalesTargetSummary($clientId, $startDate, $endDate),
            'trends' => $service->getSalesTargetTrends($clientId, $startDate, $endDate),
            'tableData' => $service->getSalesTargetQuery($clientId, $startDate, $endDate, $analysisType),
            'analysisType' => $analysisType,
        ];
    }
}
