<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard;

class MainDashboard extends Dashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?string $title = 'Dashboard Utama';

    protected static ?int $navigationSort = 1;

    protected static string $routePath = '/dashboard';

    public function getWidgets(): array
    {
        return [
            // Hanya widget yang diinginkan untuk dashboard utama
            \App\Filament\Widgets\QuickActionsWidget::class,
        ];
    }

    public function getColumns(): int | array
    {
        return [
            'sm' => 1,
            'md' => 1,
            'lg' => 1,
        ];
    }
}
