<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class FinancialReports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static ?string $navigationLabel = '<PERSON>por<PERSON> Keuangan';

    protected static ?string $title = '<PERSON>por<PERSON>uang<PERSON>';

    protected static ?string $navigationGroup = 'Laporan';

    protected static ?int $navigationSort = 2;

    protected static string $routePath = '/financial-reports';

    protected static string $view = 'filament.pages.financial-reports';
}
