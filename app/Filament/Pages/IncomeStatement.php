<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\JournalEntry;
use Carbon\Carbon;
use Filament\Facades\Filament;

class IncomeStatement extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Laporan Laba Rugi';

    protected static ?string $navigationGroup = 'Laporan';

    protected static ?int $navigationSort = 2;

    protected static string $view = 'filament.pages.income-statement';

    public function getIncomeStatementData()
    {
        $tenant = Filament::getTenant();
        $currentYear = Carbon::now()->year;

        // Revenue
        $revenue = JournalEntry::whereHas('account', function ($q) {
            $q->where('account_type', 'revenue');
        })
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->sum('credit');

        // Expenses
        $expenses = JournalEntry::whereHas('account', function ($q) {
            $q->where('account_type', 'expense');
        })
            ->whereHas('journal', function ($q) use ($currentYear, $tenant) {
                $q->where('is_posted', true)
                    ->whereYear('journal_date', $currentYear);
                if ($tenant) {
                    $q->where('client_id', $tenant->id);
                }
            })
            ->sum('debit');

        // Net Income
        $netIncome = $revenue - $expenses;

        return [
            'revenue' => $revenue,
            'expenses' => $expenses,
            'net_income' => $netIncome,
            'year' => $currentYear,
        ];
    }

    public static function canAccess(): bool
    {
        return true; // All authenticated users can access
    }
}
