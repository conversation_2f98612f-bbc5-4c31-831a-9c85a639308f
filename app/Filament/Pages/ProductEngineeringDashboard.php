<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\ProductEngineeringWidget;
use Filament\Pages\Dashboard;

class ProductEngineeringDashboard extends Dashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';

    protected static ?string $navigationLabel = 'Analisis Produk';

    protected static ?string $title = 'Analisis Rekayasa Produk';

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = 4;

    protected static string $routePath = '/product-engineering';

    public function getWidgets(): array
    {
        return [
            ProductEngineeringWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return 1;
    }
}
