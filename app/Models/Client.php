<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasCreator;
use Filament\Models\Contracts\HasName;
use Filament\Models\Contracts\HasAvatar;

class Client extends Model implements HasName, HasAvatar
{
    use HasFactory, SoftDeletes, HasCreator;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'is_active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Relationship: Client has many users
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Relationship: Client has many accounts
     */
    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class);
    }

    /**
     * Relationship: Client has many transactions
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Relationship: Client has many journals
     */
    public function journals(): HasMany
    {
        return $this->hasMany(Journal::class);
    }

    /**
     * Relationship: Client has many bank accounts
     */
    public function bankAccounts(): HasMany
    {
        return $this->hasMany(BankAccount::class);
    }

    /**
     * Relationship: Client has many unit of measures
     */
    public function unitOfMeasures(): HasMany
    {
        return $this->hasMany(UnitOfMeasure::class);
    }

    /**
     * Relationship: Client has many products
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Relationship: Client has many locations
     */
    public function locations(): HasMany
    {
        return $this->hasMany(Location::class);
    }

    /**
     * Relationship: Client has many work centers
     */
    public function workCenters(): HasMany
    {
        return $this->hasMany(WorkCenter::class);
    }

    /**
     * Relationship: User who created this client
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope: Only active clients
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the name for Filament display
     */
    public function getFilamentName(): string
    {
        return $this->name;
    }

    /**
     * Get the avatar URL for Filament display
     */
    public function getFilamentAvatarUrl(): ?string
    {
        // Return null to use default avatar generation
        return null;
    }
}
