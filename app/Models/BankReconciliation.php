<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankReconciliation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'bank_account_id',
        'reconciliation_date',
        'statement_date',
        'statement_balance',
        'book_balance',
        'adjusted_book_balance',
        'difference',
        'status', // draft, in_progress, completed, approved
        'reconciled_by',
        'approved_by',
        'approved_at',
        'notes',
    ];

    protected $casts = [
        'reconciliation_date' => 'date',
        'statement_date' => 'date',
        'statement_balance' => 'decimal:2',
        'book_balance' => 'decimal:2',
        'adjusted_book_balance' => 'decimal:2',
        'difference' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    public function reconciledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reconciled_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function reconciliationItems(): HasMany
    {
        return $this->hasMany(BankReconciliationItem::class);
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'in_progress']);
    }

    // Accessors
    public function getIsBalancedAttribute(): bool
    {
        return abs($this->difference) < 0.01; // Allow for rounding differences
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'in_progress' => 'blue',
            'completed' => 'green',
            'approved' => 'purple',
            default => 'gray'
        };
    }

    public function getOutstandingItemsCountAttribute(): int
    {
        return $this->reconciliationItems()
            ->where('status', 'outstanding')
            ->count();
    }

    public function getReconciledItemsCountAttribute(): int
    {
        return $this->reconciliationItems()
            ->where('status', 'reconciled')
            ->count();
    }

    // Helper Methods
    public function calculateBalances(): void
    {
        // Get book balance from journal entries
        $this->book_balance = $this->bankAccount->account->getBalance($this->reconciliation_date);
        
        // Calculate adjustments
        $adjustments = $this->reconciliationItems()
            ->where('item_type', 'adjustment')
            ->sum('amount');
            
        $this->adjusted_book_balance = $this->book_balance + $adjustments;
        
        // Calculate difference
        $this->difference = $this->statement_balance - $this->adjusted_book_balance;
        
        $this->save();
    }

    public function addOutstandingDeposit(float $amount, string $description, $date = null): BankReconciliationItem
    {
        return $this->reconciliationItems()->create([
            'client_id' => $this->client_id,
            'item_type' => 'outstanding_deposit',
            'description' => $description,
            'amount' => $amount,
            'transaction_date' => $date ?? $this->reconciliation_date,
            'status' => 'outstanding',
        ]);
    }

    public function addOutstandingCheck(float $amount, string $description, string $checkNumber = null, $date = null): BankReconciliationItem
    {
        return $this->reconciliationItems()->create([
            'client_id' => $this->client_id,
            'item_type' => 'outstanding_check',
            'description' => $description,
            'amount' => -$amount, // Negative for checks
            'check_number' => $checkNumber,
            'transaction_date' => $date ?? $this->reconciliation_date,
            'status' => 'outstanding',
        ]);
    }

    public function addBankError(float $amount, string $description): BankReconciliationItem
    {
        return $this->reconciliationItems()->create([
            'client_id' => $this->client_id,
            'item_type' => 'bank_error',
            'description' => $description,
            'amount' => $amount,
            'transaction_date' => $this->reconciliation_date,
            'status' => 'adjustment',
        ]);
    }

    public function addBookError(float $amount, string $description): BankReconciliationItem
    {
        return $this->reconciliationItems()->create([
            'client_id' => $this->client_id,
            'item_type' => 'book_error',
            'description' => $description,
            'amount' => $amount,
            'transaction_date' => $this->reconciliation_date,
            'status' => 'adjustment',
        ]);
    }

    public function complete(): bool
    {
        if (!$this->is_balanced) {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'reconciled_by' => auth()->id(),
        ]);

        // Update bank account last reconciled date
        $this->bankAccount->update([
            'last_reconciled_date' => $this->reconciliation_date,
        ]);

        // Mark bank transactions as reconciled
        $this->markTransactionsAsReconciled();

        return true;
    }

    public function approve(int $approvedBy): void
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);
    }

    private function markTransactionsAsReconciled(): void
    {
        // Mark all bank transactions up to reconciliation date as reconciled
        $this->bankAccount->bankTransactions()
            ->where('transaction_date', '<=', $this->reconciliation_date)
            ->where('is_reconciled', false)
            ->update([
                'is_reconciled' => true,
                'reconciled_at' => now(),
                'reconciled_by' => $this->reconciled_by,
            ]);
    }

    public function autoReconcile(): array
    {
        $results = [
            'matched' => 0,
            'unmatched_bank' => 0,
            'unmatched_book' => 0,
        ];

        // Get unreconciled bank transactions
        $bankTransactions = $this->bankAccount->bankTransactions()
            ->unreconciled()
            ->where('transaction_date', '<=', $this->reconciliation_date)
            ->get();

        foreach ($bankTransactions as $transaction) {
            if ($transaction->autoReconcile()) {
                $results['matched']++;
            } else {
                $results['unmatched_bank']++;
            }
        }

        // Get unmatched journal entries
        $unmatchedEntries = JournalEntry::where('client_id', $this->client_id)
            ->where('account_id', $this->bankAccount->account_id)
            ->whereNull('bank_transaction_id')
            ->whereDate('created_at', '<=', $this->reconciliation_date)
            ->count();

        $results['unmatched_book'] = $unmatchedEntries;

        return $results;
    }
}
