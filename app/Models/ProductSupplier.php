<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductSupplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'product_id',
        'supplier_id',
        'supplier_product_code',
        'supplier_product_name',
        'purchase_price',
        'minimum_order_quantity',
        'lead_time_days',
        'currency_code',
        'is_preferred',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'purchase_price' => 'decimal:2',
        'minimum_order_quantity' => 'decimal:2',
        'lead_time_days' => 'integer',
        'is_preferred' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePreferred($query)
    {
        return $query->where('is_preferred', true);
    }

    public function scopeBySupplier($query, int $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    // Helper Methods
    public function calculateTotalPrice(float $quantity): float
    {
        return $this->purchase_price * $quantity;
    }

    public function isMinimumOrderMet(float $quantity): bool
    {
        return $quantity >= $this->minimum_order_quantity;
    }

    public function getEstimatedDeliveryDate(): \Carbon\Carbon
    {
        return now()->addDays($this->lead_time_days);
    }
}
