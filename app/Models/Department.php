<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Department extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'parent_id',
        'department_code',
        'department_name',
        'description',
        'department_type', // production, sales, admin, support
        'head_of_department_id',
        'location_id',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Department::class, 'parent_id');
    }

    public function headOfDepartment(): BelongsTo
    {
        return $this->belongsTo(User::class, 'head_of_department_id');
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function costCenters(): HasMany
    {
        return $this->hasMany(CostCenter::class);
    }

    public function employees(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function workCenters(): HasMany
    {
        return $this->hasMany(WorkCenter::class, 'department_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('department_type', $type);
    }

    public function scopeRootDepartments($query)
    {
        return $query->whereNull('parent_id');
    }

    // Accessors
    public function getFullCodeAttribute(): string
    {
        $codes = collect([$this->department_code]);
        $parent = $this->parent;
        
        while ($parent) {
            $codes->prepend($parent->department_code);
            $parent = $parent->parent;
        }
        
        return $codes->implode('.');
    }

    public function getFullNameAttribute(): string
    {
        $names = collect([$this->department_name]);
        $parent = $this->parent;
        
        while ($parent) {
            $names->prepend($parent->department_name);
            $parent = $parent->parent;
        }
        
        return $names->implode(' > ');
    }

    public function getTypeColorAttribute(): string
    {
        return match($this->department_type) {
            'production' => 'blue',
            'sales' => 'green',
            'admin' => 'purple',
            'support' => 'orange',
            default => 'gray'
        };
    }

    public function getEmployeeCountAttribute(): int
    {
        return $this->employees()->count();
    }

    public function getCostCenterCountAttribute(): int
    {
        return $this->costCenters()->count();
    }

    // Helper Methods
    public function getAllChildren()
    {
        $children = collect();
        
        foreach ($this->children as $child) {
            $children->push($child);
            $children = $children->merge($child->getAllChildren());
        }
        
        return $children;
    }

    public function getTotalBudget($year = null): float
    {
        $year = $year ?? date('Y');
        
        return $this->costCenters()
            ->whereHas('budgets', function ($query) use ($year) {
                $query->where('budget_year', $year);
            })
            ->with(['budgets' => function ($query) use ($year) {
                $query->where('budget_year', $year);
            }])
            ->get()
            ->sum(function ($costCenter) {
                return $costCenter->budgets->sum('budgeted_amount');
            });
    }

    public function getTotalActualCost($startDate, $endDate): float
    {
        $totalCost = 0;
        
        foreach ($this->costCenters as $costCenter) {
            $totalCost += $costCenter->getActualCost($startDate, $endDate);
        }
        
        return $totalCost;
    }

    public function getDepartmentPerformance($startDate, $endDate): array
    {
        $totalRevenue = 0;
        $totalCost = 0;
        $totalBudget = 0;
        
        foreach ($this->costCenters as $costCenter) {
            $totalRevenue += $costCenter->getActualRevenue($startDate, $endDate);
            $totalCost += $costCenter->getActualCost($startDate, $endDate);
            $totalBudget += $costCenter->getBudgetedAmount($startDate, $endDate);
        }
        
        $profit = $totalRevenue - $totalCost;
        $budgetVariance = $totalBudget - $totalCost;
        $profitMargin = $totalRevenue > 0 ? ($profit / $totalRevenue) * 100 : 0;
        
        return [
            'revenue' => $totalRevenue,
            'cost' => $totalCost,
            'profit' => $profit,
            'budget' => $totalBudget,
            'budget_variance' => $budgetVariance,
            'profit_margin' => $profitMargin,
            'budget_utilization' => $totalBudget > 0 ? ($totalCost / $totalBudget) * 100 : 0,
        ];
    }

    public static function getDefaultDepartments(): array
    {
        return [
            [
                'department_code' => 'PROD',
                'department_name' => 'Produksi',
                'department_type' => 'production',
                'description' => 'Departemen Produksi',
                'children' => [
                    [
                        'department_code' => 'PROD-MFG',
                        'department_name' => 'Manufacturing',
                        'department_type' => 'production',
                        'description' => 'Unit Manufacturing',
                    ],
                    [
                        'department_code' => 'PROD-QC',
                        'department_name' => 'Quality Control',
                        'department_type' => 'production',
                        'description' => 'Unit Quality Control',
                    ],
                ]
            ],
            [
                'department_code' => 'SALES',
                'department_name' => 'Penjualan',
                'department_type' => 'sales',
                'description' => 'Departemen Penjualan',
                'children' => [
                    [
                        'department_code' => 'SALES-DOM',
                        'department_name' => 'Penjualan Domestik',
                        'department_type' => 'sales',
                        'description' => 'Unit Penjualan Domestik',
                    ],
                    [
                        'department_code' => 'SALES-EXP',
                        'department_name' => 'Penjualan Export',
                        'department_type' => 'sales',
                        'description' => 'Unit Penjualan Export',
                    ],
                ]
            ],
            [
                'department_code' => 'ADMIN',
                'department_name' => 'Administrasi',
                'department_type' => 'admin',
                'description' => 'Departemen Administrasi',
                'children' => [
                    [
                        'department_code' => 'ADMIN-FIN',
                        'department_name' => 'Keuangan',
                        'department_type' => 'admin',
                        'description' => 'Unit Keuangan',
                    ],
                    [
                        'department_code' => 'ADMIN-HR',
                        'department_name' => 'SDM',
                        'department_type' => 'admin',
                        'description' => 'Unit Sumber Daya Manusia',
                    ],
                ]
            ],
        ];
    }
}
