<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'production_order_id',
        'bom_id',
        'work_center_id',
        'wo_number',
        'operation_sequence',
        'operation_name',
        'description',
        'quantity_to_process',
        'quantity_completed',
        'quantity_scrapped',
        'unit_id',
        'status', // pending, in_progress, completed, cancelled
        'planned_start_date',
        'planned_end_date',
        'actual_start_date',
        'actual_end_date',
        'setup_time_planned',
        'setup_time_actual',
        'run_time_planned',
        'run_time_actual',
        'labor_hours_planned',
        'labor_hours_actual',
        'machine_hours_planned',
        'machine_hours_actual',
        'labor_cost',
        'machine_cost',
        'overhead_cost',
        'total_cost',
        'assigned_to',
        'notes',
    ];

    protected $casts = [
        'quantity_to_process' => 'decimal:2',
        'quantity_completed' => 'decimal:2',
        'quantity_scrapped' => 'decimal:2',
        'planned_start_date' => 'datetime',
        'planned_end_date' => 'datetime',
        'actual_start_date' => 'datetime',
        'actual_end_date' => 'datetime',
        'setup_time_planned' => 'decimal:2',
        'setup_time_actual' => 'decimal:2',
        'run_time_planned' => 'decimal:2',
        'run_time_actual' => 'decimal:2',
        'labor_hours_planned' => 'decimal:2',
        'labor_hours_actual' => 'decimal:2',
        'machine_hours_planned' => 'decimal:2',
        'machine_hours_actual' => 'decimal:2',
        'labor_cost' => 'decimal:2',
        'machine_cost' => 'decimal:2',
        'overhead_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function billOfMaterial(): BelongsTo
    {
        return $this->belongsTo(BillOfMaterial::class, 'bom_id');
    }

    public function workCenter(): BelongsTo
    {
        return $this->belongsTo(WorkCenter::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function materialUsages(): HasMany
    {
        return $this->hasMany(RawMaterialUsage::class);
    }

    public function timeEntries(): HasMany
    {
        return $this->hasMany(WorkOrderTimeEntry::class);
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeOverdue($query)
    {
        return $query->where('planned_end_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    // Accessors
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->quantity_to_process <= 0) {
            return 0;
        }
        
        return ($this->quantity_completed / $this->quantity_to_process) * 100;
    }

    public function getEfficiencyPercentageAttribute(): float
    {
        $totalPlannedTime = $this->setup_time_planned + $this->run_time_planned;
        $totalActualTime = $this->setup_time_actual + $this->run_time_actual;
        
        if ($totalActualTime <= 0) {
            return 0;
        }
        
        return ($totalPlannedTime / $totalActualTime) * 100;
    }

    public function getYieldPercentageAttribute(): float
    {
        $totalProcessed = $this->quantity_completed + $this->quantity_scrapped;
        
        if ($totalProcessed <= 0) {
            return 0;
        }
        
        return ($this->quantity_completed / $totalProcessed) * 100;
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'gray',
            'in_progress' => 'blue',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    // Helper Methods
    public function canBeStarted(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeCompleted(): bool
    {
        return $this->status === 'in_progress';
    }

    public function start(): bool
    {
        if (!$this->canBeStarted()) {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'actual_start_date' => now(),
        ]);

        return true;
    }

    public function complete(): bool
    {
        if (!$this->canBeCompleted()) {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'actual_end_date' => now(),
        ]);

        // Update production order quantity
        $this->productionOrder->updateQuantityProduced($this->quantity_completed);

        return true;
    }

    public function recordProduction(float $completedQty, float $scrappedQty = 0): void
    {
        $this->quantity_completed += $completedQty;
        $this->quantity_scrapped += $scrappedQty;
        
        // Auto-complete if all quantity processed
        $totalProcessed = $this->quantity_completed + $this->quantity_scrapped;
        if ($totalProcessed >= $this->quantity_to_process && $this->status === 'in_progress') {
            $this->status = 'completed';
            $this->actual_end_date = now();
        }
        
        $this->save();
    }

    public function calculateCosts(): void
    {
        // Calculate labor cost
        $this->labor_cost = $this->labor_hours_actual * ($this->workCenter->labor_rate ?? 0);
        
        // Calculate machine cost
        $this->machine_cost = $this->machine_hours_actual * ($this->workCenter->machine_rate ?? 0);
        
        // Calculate overhead cost (percentage of labor + machine)
        $overheadRate = $this->workCenter->overhead_rate ?? 0;
        $this->overhead_cost = ($this->labor_cost + $this->machine_cost) * ($overheadRate / 100);
        
        // Total cost
        $this->total_cost = $this->labor_cost + $this->machine_cost + $this->overhead_cost;
        
        $this->save();
    }
}
