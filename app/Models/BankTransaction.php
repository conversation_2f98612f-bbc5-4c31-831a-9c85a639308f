<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'bank_account_id',
        'journal_entry_id',
        'reference_type',
        'reference_id',
        'transaction_date',
        'value_date',
        'transaction_type', // debit, credit, transfer, fee, interest
        'transaction_code',
        'description',
        'reference_number',
        'amount',
        'running_balance',
        'currency_code',
        'exchange_rate',
        'original_amount',
        'original_currency',
        'bank_reference',
        'counterparty_name',
        'counterparty_account',
        'is_reconciled',
        'reconciled_at',
        'reconciled_by',
        'is_imported',
        'import_batch_id',
        'notes',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'value_date' => 'date',
        'amount' => 'decimal:2',
        'running_balance' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'original_amount' => 'decimal:2',
        'is_reconciled' => 'boolean',
        'reconciled_at' => 'datetime',
        'is_imported' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    public function journalEntry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    public function reconciledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reconciled_by');
    }

    // Scopes
    public function scopeReconciled($query)
    {
        return $query->where('is_reconciled', true);
    }

    public function scopeUnreconciled($query)
    {
        return $query->where('is_reconciled', false);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('transaction_type', $type);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeImported($query)
    {
        return $query->where('is_imported', true);
    }

    public function scopeManual($query)
    {
        return $query->where('is_imported', false);
    }

    // Accessors
    public function getSignedAmountAttribute(): float
    {
        return $this->transaction_type === 'debit' ? $this->amount : -$this->amount;
    }

    public function getFormattedAmountAttribute(): string
    {
        $sign = $this->transaction_type === 'debit' ? '+' : '-';
        return $sign . $this->currency_code . ' ' . number_format($this->amount, 2);
    }

    public function getTransactionTypeColorAttribute(): string
    {
        return match($this->transaction_type) {
            'debit' => 'green',
            'credit' => 'red',
            'transfer' => 'blue',
            'fee' => 'orange',
            'interest' => 'purple',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return $this->is_reconciled ? 'green' : 'yellow';
    }

    // Helper Methods
    public function reconcile(int $reconciledBy, int $journalEntryId = null): void
    {
        $this->update([
            'is_reconciled' => true,
            'reconciled_at' => now(),
            'reconciled_by' => $reconciledBy,
            'journal_entry_id' => $journalEntryId,
        ]);
    }

    public function unreconcile(): void
    {
        $this->update([
            'is_reconciled' => false,
            'reconciled_at' => null,
            'reconciled_by' => null,
            'journal_entry_id' => null,
        ]);
    }

    public function createJournalEntry(int $accountId, string $description = null): JournalEntry
    {
        $journal = Journal::create([
            'client_id' => $this->client_id,
            'journal_number' => Journal::generateJournalNumber($this->client_id),
            'journal_date' => $this->transaction_date,
            'description' => $description ?? $this->description,
            'reference_number' => $this->reference_number,
            'is_posted' => true,
            'created_by' => auth()->id(),
        ]);

        // Bank account entry
        $bankEntry = JournalEntry::create([
            'client_id' => $this->client_id,
            'journal_id' => $journal->id,
            'account_id' => $this->bankAccount->account_id,
            'description' => $this->description,
            'debit' => $this->transaction_type === 'debit' ? $this->amount : 0,
            'credit' => $this->transaction_type === 'credit' ? $this->amount : 0,
        ]);

        // Counterpart entry
        JournalEntry::create([
            'client_id' => $this->client_id,
            'journal_id' => $journal->id,
            'account_id' => $accountId,
            'description' => $this->description,
            'debit' => $this->transaction_type === 'credit' ? $this->amount : 0,
            'credit' => $this->transaction_type === 'debit' ? $this->amount : 0,
        ]);

        $this->update(['journal_entry_id' => $bankEntry->id]);

        return $bankEntry;
    }

    public static function importFromArray(array $data, int $bankAccountId, string $batchId = null): self
    {
        return self::create(array_merge($data, [
            'bank_account_id' => $bankAccountId,
            'is_imported' => true,
            'import_batch_id' => $batchId ?? uniqid(),
        ]));
    }

    public function findMatchingJournalEntry(): ?JournalEntry
    {
        // Try to find matching journal entry by amount and date
        return JournalEntry::where('client_id', $this->client_id)
            ->where('account_id', $this->bankAccount->account_id)
            ->whereDate('created_at', $this->transaction_date)
            ->where(function ($query) {
                if ($this->transaction_type === 'debit') {
                    $query->where('debit', $this->amount);
                } else {
                    $query->where('credit', $this->amount);
                }
            })
            ->whereNull('bank_transaction_id')
            ->first();
    }

    public function autoReconcile(): bool
    {
        $matchingEntry = $this->findMatchingJournalEntry();
        
        if ($matchingEntry) {
            $this->reconcile(auth()->id(), $matchingEntry->id);
            $matchingEntry->update(['bank_transaction_id' => $this->id]);
            return true;
        }
        
        return false;
    }
}
