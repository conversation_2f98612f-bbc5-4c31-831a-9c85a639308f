<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrderItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'purchase_order_id',
        'product_id',
        'description',
        'quantity_ordered',
        'quantity_received',
        'quantity_invoiced',
        'unit_price',
        'discount_percentage',
        'discount_amount',
        'tax_percentage',
        'tax_amount',
        'line_total',
        'unit_id',
        'delivery_date',
        'notes',
        'status', // pending, partial, completed, cancelled
    ];

    protected $casts = [
        'quantity_ordered' => 'decimal:6',
        'quantity_received' => 'decimal:6',
        'quantity_invoiced' => 'decimal:6',
        'unit_price' => 'decimal:4',
        'discount_percentage' => 'decimal:4',
        'discount_amount' => 'decimal:2',
        'tax_percentage' => 'decimal:4',
        'tax_amount' => 'decimal:2',
        'line_total' => 'decimal:2',
        'delivery_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePartiallyReceived($query)
    {
        return $query->where('quantity_received', '>', 0)
                    ->where('quantity_received', '<', 'quantity_ordered');
    }

    public function scopeFullyReceived($query)
    {
        return $query->whereColumn('quantity_received', '>=', 'quantity_ordered');
    }

    // Accessors
    public function getQuantityOutstandingAttribute(): float
    {
        return $this->quantity_ordered - $this->quantity_received;
    }

    public function getQuantityToInvoiceAttribute(): float
    {
        return $this->quantity_received - $this->quantity_invoiced;
    }

    public function getReceivePercentageAttribute(): float
    {
        if ($this->quantity_ordered <= 0) {
            return 0;
        }
        
        return ($this->quantity_received / $this->quantity_ordered) * 100;
    }

    public function getInvoicePercentageAttribute(): float
    {
        if ($this->quantity_received <= 0) {
            return 0;
        }
        
        return ($this->quantity_invoiced / $this->quantity_received) * 100;
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'gray',
            'partial' => 'yellow',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->delivery_date && 
               $this->delivery_date < now() && 
               $this->quantity_outstanding > 0;
    }

    // Helper Methods
    public function calculateLineTotal(): void
    {
        $subtotal = $this->quantity_ordered * $this->unit_price;
        $discountAmount = $this->discount_percentage > 0 
            ? $subtotal * ($this->discount_percentage / 100)
            : $this->discount_amount;
        $taxableAmount = $subtotal - $discountAmount;
        $taxAmount = $taxableAmount * ($this->tax_percentage / 100);
        
        $this->update([
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'line_total' => $taxableAmount + $taxAmount,
        ]);
    }

    public function receiveQuantity(float $quantity, ?string $notes = null): bool
    {
        if ($quantity <= 0 || $quantity > $this->quantity_outstanding) {
            return false;
        }

        $newQuantityReceived = $this->quantity_received + $quantity;
        $newStatus = $newQuantityReceived >= $this->quantity_ordered ? 'completed' : 'partial';

        $this->update([
            'quantity_received' => $newQuantityReceived,
            'status' => $newStatus,
            'notes' => $notes ? ($this->notes . "\n" . $notes) : $this->notes,
        ]);

        // Create stock movement
        StockMovement::createMovement([
            'client_id' => $this->client_id,
            'product_id' => $this->product_id,
            'location_id' => 1, // Default location - should be configurable
            'movement_type' => 'in',
            'transaction_type' => 'purchase',
            'reason_code' => 'receipt',
            'quantity' => $quantity,
            'unit_cost' => $this->unit_price,
            'reference_type' => PurchaseOrder::class,
            'reference_id' => $this->purchase_order_id,
            'reference_number' => $this->purchaseOrder->po_number,
            'movement_date' => now(),
            'notes' => "PO Receipt: {$this->purchaseOrder->po_number}",
        ]);

        return true;
    }

    public function invoiceQuantity(float $quantity, ?string $invoiceNumber = null): bool
    {
        if ($quantity <= 0 || $quantity > $this->quantity_to_invoice) {
            return false;
        }

        $this->update([
            'quantity_invoiced' => $this->quantity_invoiced + $quantity,
        ]);

        return true;
    }

    public function cancel(?string $reason = null): bool
    {
        if ($this->quantity_received > 0) {
            return false; // Cannot cancel if already received
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => ($this->notes ?? '') . ' | Cancelled: ' . ($reason ?? 'Manual cancellation'),
        ]);

        return true;
    }

    public function getVarianceAnalysis(): array
    {
        return [
            'quantity_variance' => $this->quantity_received - $this->quantity_ordered,
            'quantity_variance_percentage' => $this->quantity_ordered > 0 
                ? (($this->quantity_received - $this->quantity_ordered) / $this->quantity_ordered) * 100 
                : 0,
            'delivery_variance_days' => $this->delivery_date && $this->purchaseOrder->received_at
                ? $this->delivery_date->diffInDays($this->purchaseOrder->received_at, false)
                : null,
            'is_quantity_variance_significant' => abs($this->quantity_received - $this->quantity_ordered) > ($this->quantity_ordered * 0.05), // 5% threshold
            'is_delivery_late' => $this->is_overdue,
        ];
    }

    public function getSupplierPerformance(): array
    {
        $supplier = $this->purchaseOrder->supplier;
        
        // Get historical data for this supplier and product
        $historicalItems = self::whereHas('purchaseOrder', function ($query) use ($supplier) {
                $query->where('supplier_id', $supplier->id);
            })
            ->where('product_id', $this->product_id)
            ->where('status', 'completed')
            ->get();

        $onTimeDeliveries = $historicalItems->filter(function ($item) {
            return !$item->is_overdue;
        })->count();

        $totalDeliveries = $historicalItems->count();
        $onTimePercentage = $totalDeliveries > 0 ? ($onTimeDeliveries / $totalDeliveries) * 100 : 0;

        $averagePrice = $historicalItems->avg('unit_price');
        $priceVariance = $averagePrice > 0 ? (($this->unit_price - $averagePrice) / $averagePrice) * 100 : 0;

        return [
            'supplier_name' => $supplier->supplier_name,
            'total_orders' => $totalDeliveries,
            'on_time_deliveries' => $onTimeDeliveries,
            'on_time_percentage' => $onTimePercentage,
            'average_historical_price' => $averagePrice,
            'current_price' => $this->unit_price,
            'price_variance_percentage' => $priceVariance,
            'performance_rating' => $this->getPerformanceRating($onTimePercentage, $priceVariance),
        ];
    }

    private function getPerformanceRating(float $onTimePercentage, float $priceVariance): string
    {
        if ($onTimePercentage >= 95 && abs($priceVariance) <= 5) {
            return 'Excellent';
        } elseif ($onTimePercentage >= 85 && abs($priceVariance) <= 10) {
            return 'Good';
        } elseif ($onTimePercentage >= 70 && abs($priceVariance) <= 15) {
            return 'Fair';
        } else {
            return 'Poor';
        }
    }
}
