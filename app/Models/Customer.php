<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'customer_code',
        'customer_name',
        'contact_person',
        'email',
        'phone',
        'mobile',
        'fax',
        'website',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'tax_number',
        'business_license',
        'payment_terms',
        'payment_method', // transfer, check, cash, credit
        'credit_limit',
        'credit_days',
        'currency_code',
        'bank_name',
        'bank_account',
        'bank_account_name',
        'customer_type', // retail, wholesale, corporate
        'customer_category', // local, export, government
        'industry',
        'company_size', // small, medium, large, enterprise
        'price_level', // standard, discount, premium
        'discount_percentage',
        'sales_person_id',
        'territory',
        'lead_source', // referral, website, marketing, cold_call
        'customer_since',
        'last_order_date',
        'total_orders',
        'total_amount',
        'average_order_value',
        'loyalty_points',
        'credit_rating', // A, B, C, D
        'payment_behavior', // excellent, good, fair, poor
        'is_vip',
        'is_active',
        'registration_date',
        'notes',
        'internal_notes',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'credit_days' => 'integer',
        'discount_percentage' => 'decimal:4',
        'customer_since' => 'date',
        'last_order_date' => 'date',
        'total_orders' => 'integer',
        'total_amount' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'loyalty_points' => 'integer',
        'is_vip' => 'boolean',
        'is_active' => 'boolean',
        'registration_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function salesPerson(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sales_person_id');
    }

    public function salesOrders(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'contact_id')->where('contact_type', 'customer');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('customer_type', $type);
    }

    public function scopeBySalesPerson($query, int $salesPersonId)
    {
        return $query->where('sales_person_id', $salesPersonId);
    }

    // Accessors
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->transactions()
            ->where('transaction_type', 'receivable')
            ->sum('amount');
    }

    public function getCreditAvailableAttribute(): float
    {
        return $this->credit_limit - $this->outstanding_balance;
    }

    public function getFormattedAddressAttribute(): string
    {
        $address = collect([
            $this->address,
            $this->city,
            $this->postal_code,
            $this->country
        ])->filter()->implode(', ');

        return $address;
    }

    // Helper Methods
    public function getTotalSales($year = null): float
    {
        $query = $this->salesOrders();

        if ($year) {
            $query->whereYear('order_date', $year);
        }

        return $query->sum('total_amount');
    }

    public function getAverageOrderValue(): float
    {
        return $this->salesOrders()->avg('total_amount') ?? 0;
    }

    public function isWithinCreditLimit(float $amount): bool
    {
        return ($this->outstanding_balance + $amount) <= $this->credit_limit;
    }

    public function getAgingAnalysis(): array
    {
        $orders = $this->salesOrders()
            ->where('payment_status', '!=', 'paid')
            ->whereNotNull('invoice_date')
            ->get();

        $aging = [
            'current' => 0,      // 0-30 days
            'days_31_60' => 0,   // 31-60 days
            'days_61_90' => 0,   // 61-90 days
            'over_90' => 0,      // Over 90 days
            'total_outstanding' => 0,
        ];

        foreach ($orders as $order) {
            $daysPastDue = now()->diffInDays($order->invoice_date);
            $outstandingAmount = $order->outstanding_amount;

            if ($daysPastDue <= 30) {
                $aging['current'] += $outstandingAmount;
            } elseif ($daysPastDue <= 60) {
                $aging['days_31_60'] += $outstandingAmount;
            } elseif ($daysPastDue <= 90) {
                $aging['days_61_90'] += $outstandingAmount;
            } else {
                $aging['over_90'] += $outstandingAmount;
            }

            $aging['total_outstanding'] += $outstandingAmount;
        }

        return $aging;
    }

    public function getCreditAnalysis(): array
    {
        $outstandingAmount = $this->salesOrders()
            ->where('payment_status', '!=', 'paid')
            ->sum('invoice_amount') - $this->salesOrders()
            ->where('payment_status', '!=', 'paid')
            ->sum('paid_amount');

        $creditUtilization = $this->credit_limit > 0 ? ($outstandingAmount / $this->credit_limit) * 100 : 0;

        return [
            'credit_limit' => $this->credit_limit,
            'outstanding_amount' => $outstandingAmount,
            'available_credit' => max(0, $this->credit_limit - $outstandingAmount),
            'credit_utilization_percentage' => round($creditUtilization, 2),
            'credit_status' => $this->getCreditStatus($creditUtilization),
            'payment_behavior' => $this->payment_behavior,
            'credit_rating' => $this->credit_rating,
        ];
    }

    private function getCreditStatus(float $utilization): string
    {
        if ($utilization <= 50) {
            return 'Good';
        } elseif ($utilization <= 80) {
            return 'Caution';
        } else {
            return 'Critical';
        }
    }

    public static function generateCustomerCode(int $clientId): string
    {
        $lastCustomer = self::where('client_id', $clientId)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastCustomer ? (int)substr($lastCustomer->customer_code, -4) + 1 : 1;

        return 'CUST' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
