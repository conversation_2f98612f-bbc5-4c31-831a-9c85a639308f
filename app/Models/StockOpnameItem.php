<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockOpnameItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'stock_opname_id',
        'product_id',
        'inventory_id',
        'system_quantity',
        'physical_quantity',
        'variance_quantity',
        'unit_cost',
        'variance_value',
        'status', // pending, counted, adjusted
        'count_date',
        'counted_by',
        'notes',
        'batch_number',
        'serial_number',
        'expiry_date',
    ];

    protected $casts = [
        'system_quantity' => 'decimal:6',
        'physical_quantity' => 'decimal:6',
        'variance_quantity' => 'decimal:6',
        'unit_cost' => 'decimal:4',
        'variance_value' => 'decimal:2',
        'count_date' => 'datetime',
        'expiry_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function stockOpname(): BelongsTo
    {
        return $this->belongsTo(StockOpname::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function inventory(): BelongsTo
    {
        return $this->belongsTo(Inventory::class);
    }

    public function countedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'counted_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeWithVariance($query)
    {
        return $query->where('variance_quantity', '!=', 0);
    }

    public function scopeOverage($query)
    {
        return $query->where('variance_quantity', '>', 0);
    }

    public function scopeShortage($query)
    {
        return $query->where('variance_quantity', '<', 0);
    }

    // Accessors
    public function getVarianceTypeAttribute(): string
    {
        if ($this->variance_quantity > 0) {
            return 'overage';
        } elseif ($this->variance_quantity < 0) {
            return 'shortage';
        }
        return 'match';
    }

    public function getVariancePercentageAttribute(): float
    {
        if ($this->system_quantity <= 0) {
            return 0;
        }
        
        return ($this->variance_quantity / $this->system_quantity) * 100;
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'gray',
            'counted' => 'blue',
            'adjusted' => 'green',
            default => 'gray'
        };
    }

    public function getVarianceColorAttribute(): string
    {
        return match($this->variance_type) {
            'overage' => 'green',
            'shortage' => 'red',
            'match' => 'gray',
            default => 'gray'
        };
    }

    // Helper Methods
    public function recordCount(float $physicalQuantity, ?int $countedBy = null, ?string $notes = null): void
    {
        $this->update([
            'physical_quantity' => $physicalQuantity,
            'variance_quantity' => $physicalQuantity - $this->system_quantity,
            'variance_value' => ($physicalQuantity - $this->system_quantity) * $this->unit_cost,
            'status' => 'counted',
            'count_date' => now(),
            'counted_by' => $countedBy ?? auth()->id() ?? 1,
            'notes' => $notes,
        ]);
    }

    public function isSignificantVariance(float $threshold = 5.0): bool
    {
        return abs($this->variance_percentage) >= $threshold;
    }

    public function requiresRecount(): bool
    {
        // Require recount if variance is significant (>10%) or high value variance
        return $this->isSignificantVariance(10.0) || abs($this->variance_value) >= 1000000;
    }

    public function getVarianceAnalysis(): array
    {
        return [
            'variance_type' => $this->variance_type,
            'variance_quantity' => $this->variance_quantity,
            'variance_percentage' => round($this->variance_percentage, 2),
            'variance_value' => $this->variance_value,
            'is_significant' => $this->isSignificantVariance(),
            'requires_recount' => $this->requiresRecount(),
            'possible_causes' => $this->getPossibleCauses(),
        ];
    }

    private function getPossibleCauses(): array
    {
        $causes = [];
        
        if ($this->variance_quantity > 0) {
            $causes = [
                'Unrecorded receipts',
                'Counting error (overcount)',
                'System error in recording issues',
                'Returns not recorded in system',
            ];
        } elseif ($this->variance_quantity < 0) {
            $causes = [
                'Unrecorded issues/sales',
                'Counting error (undercount)',
                'Theft or shrinkage',
                'Damage or spoilage',
                'System error in recording receipts',
            ];
        }
        
        return $causes;
    }

    public function createRecountItem(): self
    {
        return self::create([
            'client_id' => $this->client_id,
            'stock_opname_id' => $this->stock_opname_id,
            'product_id' => $this->product_id,
            'inventory_id' => $this->inventory_id,
            'system_quantity' => $this->physical_quantity, // Use previous count as system
            'unit_cost' => $this->unit_cost,
            'status' => 'pending',
            'notes' => 'Recount of item #' . $this->id,
        ]);
    }

    public static function generateCountSheet(int $stockOpnameId): array
    {
        $items = self::where('stock_opname_id', $stockOpnameId)
            ->with(['product.category', 'product.unit'])
            ->orderBy('product_id')
            ->get();

        return $items->map(function ($item) {
            return [
                'product_code' => $item->product->product_code,
                'product_name' => $item->product->product_name,
                'category' => $item->product->category->category_name ?? '',
                'unit' => $item->product->unit->unit_name ?? '',
                'system_quantity' => $item->system_quantity,
                'physical_quantity' => '', // To be filled during count
                'notes' => '',
                'counted_by' => '',
                'count_date' => '',
            ];
        })->toArray();
    }
}
