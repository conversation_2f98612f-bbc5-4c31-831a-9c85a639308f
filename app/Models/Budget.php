<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Budget extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'client_id',
        'budget_name',
        'description',
        'budget_year',
        'budget_type',
        'status',
        'period_start',
        'period_end',
        'total_revenue_budget',
        'total_expense_budget',
        'net_income_budget',
        'created_by',
        'approved_by',
        'approved_at',
        'approval_notes',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'total_revenue_budget' => 'decimal:2',
        'total_expense_budget' => 'decimal:2',
        'net_income_budget' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_ACTIVE = 'active';
    const STATUS_CLOSED = 'closed';

    // Budget type constants
    const TYPE_ANNUAL = 'annual';
    const TYPE_QUARTERLY = 'quarterly';
    const TYPE_MONTHLY = 'monthly';

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function budgetItems(): HasMany
    {
        return $this->hasMany(BudgetItem::class);
    }

    // Scopes
    public function scopeForYear($query, int $year)
    {
        return $query->where('budget_year', $year);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    // Helper methods
    public function isDraft(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function isSubmitted(): bool
    {
        return $this->status === self::STATUS_SUBMITTED;
    }

    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isClosed(): bool
    {
        return $this->status === self::STATUS_CLOSED;
    }

    public function canBeEdited(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_REJECTED]);
    }

    public function canBeSubmitted(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function canBeApproved(): bool
    {
        return $this->status === self::STATUS_SUBMITTED;
    }

    public function canBeActivated(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    public function calculateTotals(): void
    {
        $revenueTotal = $this->budgetItems()
            ->whereHas('account', function ($query) {
                $query->where('account_type', 'revenue');
            })
            ->sum('total_annual');

        $expenseTotal = $this->budgetItems()
            ->whereHas('account', function ($query) {
                $query->where('account_type', 'expense');
            })
            ->sum('total_annual');

        $this->update([
            'total_revenue_budget' => $revenueTotal,
            'total_expense_budget' => $expenseTotal,
            'net_income_budget' => $revenueTotal - $expenseTotal,
        ]);
    }

    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_SUBMITTED => 'Diajukan',
            self::STATUS_APPROVED => 'Disetujui',
            self::STATUS_REJECTED => 'Ditolak',
            self::STATUS_ACTIVE => 'Aktif',
            self::STATUS_CLOSED => 'Ditutup',
        ];
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_ANNUAL => 'Tahunan',
            self::TYPE_QUARTERLY => 'Triwulanan',
            self::TYPE_MONTHLY => 'Bulanan',
        ];
    }
}
