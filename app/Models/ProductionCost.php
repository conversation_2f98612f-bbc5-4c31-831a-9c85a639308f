<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionCost extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'production_order_id',
        'work_order_id',
        'product_id',
        'cost_type', // material, labor, overhead, other
        'cost_category', // direct, indirect
        'cost_element', // raw_material, direct_labor, variable_overhead, fixed_overhead
        'account_id',
        'cost_center_id',
        'department_id',
        'description',
        'standard_cost',
        'actual_cost',
        'variance_amount',
        'variance_percentage',
        'variance_type', // favorable, unfavorable
        'quantity',
        'unit_cost',
        'allocation_base', // units, hours, machine_hours, direct_labor_cost
        'allocation_rate',
        'cost_date',
        'period_month',
        'period_year',
        'reference_number',
        'is_allocated',
        'allocated_at',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'standard_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'variance_amount' => 'decimal:2',
        'variance_percentage' => 'decimal:4',
        'quantity' => 'decimal:6',
        'unit_cost' => 'decimal:4',
        'allocation_rate' => 'decimal:6',
        'cost_date' => 'date',
        'period_month' => 'integer',
        'period_year' => 'integer',
        'is_allocated' => 'boolean',
        'allocated_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function costCenter(): BelongsTo
    {
        return $this->belongsTo(CostCenter::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    // Scopes
    public function scopeByCostType($query, string $type)
    {
        return $query->where('cost_type', $type);
    }

    public function scopeByCostCategory($query, string $category)
    {
        return $query->where('cost_category', $category);
    }

    public function scopeByVarianceType($query, string $type)
    {
        return $query->where('variance_type', $type);
    }

    public function scopeWithVariance($query)
    {
        return $query->where('variance_amount', '!=', 0);
    }

    // Accessors
    public function getVarianceColorAttribute(): string
    {
        if ($this->variance_amount == 0) {
            return 'gray';
        }

        return $this->variance_type === 'favorable' ? 'green' : 'red';
    }

    public function getCostTypeColorAttribute(): string
    {
        return match ($this->cost_type) {
            'material' => 'blue',
            'labor' => 'green',
            'overhead' => 'orange',
            'other' => 'gray',
            default => 'gray'
        };
    }

    // Helper Methods
    public function calculateVariance(): void
    {
        $this->variance_amount = $this->actual_cost - $this->standard_cost;

        if ($this->standard_cost != 0) {
            $this->variance_percentage = ($this->variance_amount / $this->standard_cost) * 100;
        }

        $this->variance_type = $this->variance_amount <= 0 ? 'favorable' : 'unfavorable';

        $this->save();
    }

    public static function createMaterialCost(array $data): self
    {
        $cost = self::create(array_merge($data, [
            'cost_type' => 'material',
            'cost_category' => 'direct',
        ]));

        $cost->calculateVariance();

        return $cost;
    }

    public static function createLaborCost(array $data): self
    {
        $cost = self::create(array_merge($data, [
            'cost_type' => 'labor',
            'cost_category' => 'direct',
        ]));

        $cost->calculateVariance();

        return $cost;
    }

    public static function createOverheadCost(array $data): self
    {
        $cost = self::create(array_merge($data, [
            'cost_type' => 'overhead',
            'cost_category' => 'indirect',
        ]));

        $cost->calculateVariance();

        return $cost;
    }
}
