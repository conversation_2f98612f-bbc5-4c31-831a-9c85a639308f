<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class RawMaterialUsage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'production_order_id',
        'work_order_id',
        'bom_item_id',
        'product_id',
        'inventory_id',
        'unit_id',
        'quantity_required',
        'quantity_issued',
        'quantity_consumed',
        'quantity_returned',
        'quantity_scrapped',
        'unit_cost',
        'total_cost',
        'issue_date',
        'consumption_date',
        'batch_number',
        'lot_number',
        'expiry_date',
        'notes',
        'issued_by',
        'consumed_by',
    ];

    protected $casts = [
        'quantity_required' => 'decimal:6',
        'quantity_issued' => 'decimal:6',
        'quantity_consumed' => 'decimal:6',
        'quantity_returned' => 'decimal:6',
        'quantity_scrapped' => 'decimal:6',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:2',
        'issue_date' => 'datetime',
        'consumption_date' => 'datetime',
        'expiry_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class);
    }

    public function bomItem(): BelongsTo
    {
        return $this->belongsTo(BillOfMaterialItem::class, 'bom_item_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function inventory(): BelongsTo
    {
        return $this->belongsTo(Inventory::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    public function issuedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'issued_by');
    }

    public function consumedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'consumed_by');
    }

    // Scopes
    public function scopeByProductionOrder($query, int $productionOrderId)
    {
        return $query->where('production_order_id', $productionOrderId);
    }

    public function scopeByWorkOrder($query, int $workOrderId)
    {
        return $query->where('work_order_id', $workOrderId);
    }

    public function scopeByProduct($query, int $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeIssued($query)
    {
        return $query->where('quantity_issued', '>', 0);
    }

    public function scopeConsumed($query)
    {
        return $query->where('quantity_consumed', '>', 0);
    }

    // Accessors
    public function getQuantityVarianceAttribute(): float
    {
        return $this->quantity_consumed - $this->quantity_required;
    }

    public function getVariancePercentageAttribute(): float
    {
        if ($this->quantity_required <= 0) {
            return 0;
        }
        
        return ($this->quantity_variance / $this->quantity_required) * 100;
    }

    public function getEfficiencyPercentageAttribute(): float
    {
        if ($this->quantity_consumed <= 0) {
            return 0;
        }
        
        return ($this->quantity_required / $this->quantity_consumed) * 100;
    }

    public function getQuantityPendingAttribute(): float
    {
        return $this->quantity_issued - $this->quantity_consumed - $this->quantity_returned;
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date < now()->toDateString();
    }

    // Helper Methods
    public function issueMaterial(float $quantity, int $issuedBy): bool
    {
        if (!$this->inventory->isStockAvailable($quantity)) {
            return false;
        }

        // Reserve stock
        if (!$this->inventory->reserveStock($quantity)) {
            return false;
        }

        $this->update([
            'quantity_issued' => $this->quantity_issued + $quantity,
            'issue_date' => now(),
            'issued_by' => $issuedBy,
        ]);

        // Create stock movement
        StockMovement::createMovement([
            'client_id' => $this->client_id,
            'product_id' => $this->product_id,
            'inventory_id' => $this->inventory_id,
            'unit_id' => $this->unit_id,
            'reference_type' => self::class,
            'reference_id' => $this->id,
            'movement_type' => 'out',
            'transaction_type' => 'production',
            'quantity' => $quantity,
            'unit_cost' => $this->unit_cost,
            'reference_number' => $this->productionOrder->po_number,
            'notes' => "Material issued for production order {$this->productionOrder->po_number}",
        ]);

        return true;
    }

    public function consumeMaterial(float $quantity, int $consumedBy): void
    {
        $this->update([
            'quantity_consumed' => $this->quantity_consumed + $quantity,
            'consumption_date' => now(),
            'consumed_by' => $consumedBy,
            'total_cost' => ($this->quantity_consumed + $quantity) * $this->unit_cost,
        ]);

        // Release reserved stock
        $this->inventory->releaseReservedStock($quantity);
    }

    public function returnMaterial(float $quantity): void
    {
        $returnQuantity = min($quantity, $this->quantity_pending);
        
        $this->update([
            'quantity_returned' => $this->quantity_returned + $returnQuantity,
        ]);

        // Return to inventory
        $this->inventory->updateStock($returnQuantity, 'in', $this->unit_cost);

        // Create stock movement
        StockMovement::createMovement([
            'client_id' => $this->client_id,
            'product_id' => $this->product_id,
            'inventory_id' => $this->inventory_id,
            'unit_id' => $this->unit_id,
            'reference_type' => self::class,
            'reference_id' => $this->id,
            'movement_type' => 'in',
            'transaction_type' => 'return',
            'quantity' => $returnQuantity,
            'unit_cost' => $this->unit_cost,
            'reference_number' => $this->productionOrder->po_number,
            'notes' => "Material returned from production order {$this->productionOrder->po_number}",
        ]);
    }
}
