<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ApprovalWorkflowStep extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'approval_workflow_id',
        'step_order',
        'step_name',
        'step_description',
        'approver_type',
        'approver_user_id',
        'approver_role',
        'approver_department_id',
        'dynamic_approver_field',
        'is_required',
        'can_delegate',
        'can_skip',
        'timeout_hours',
        'min_amount',
        'max_amount',
        'conditions',
        'notes',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'can_delegate' => 'boolean',
        'can_skip' => 'boolean',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'conditions' => 'array',
    ];

    /**
     * Get the approval workflow that owns this step.
     */
    public function approvalWorkflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class);
    }

    /**
     * Get the specific approver user.
     */
    public function approverUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    /**
     * Get the approver department.
     */
    public function approverDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'approver_department_id');
    }

    /**
     * Get the actual approver for this step.
     */
    public function getApprover(?Model $context = null): ?User
    {
        return match ($this->approver_type) {
            'user' => $this->approverUser,
            'role' => $this->getApproverByRole(),
            'department' => $this->approverDepartment?->head,
            'dynamic' => $this->getDynamicApprover($context),
            default => null,
        };
    }

    /**
     * Get approver by role.
     */
    private function getApproverByRole(): ?User
    {
        if (!$this->approver_role) {
            return null;
        }

        // Get first active user with the specified role
        return User::whereHas('roles', function ($query) {
            $query->where('name', $this->approver_role);
        })->where('is_active', true)->first();
    }

    /**
     * Get dynamic approver based on context.
     */
    private function getDynamicApprover(?Model $context = null): ?User
    {
        if (!$this->dynamic_approver_field || !$context) {
            return null;
        }

        return match ($this->dynamic_approver_field) {
            'requester_manager' => $this->getRequesterManager($context),
            'cost_center_head' => $this->getCostCenterHead($context),
            'department_head' => $this->getDepartmentHead($context),
            default => null,
        };
    }

    /**
     * Get the requester's manager.
     */
    private function getRequesterManager(Model $context): ?User
    {
        if (method_exists($context, 'requester')) {
            return $context->requester?->manager;
        }

        if (property_exists($context, 'created_by')) {
            $user = User::find($context->created_by);
            return $user?->manager;
        }

        return null;
    }

    /**
     * Get the cost center head.
     */
    private function getCostCenterHead(Model $context): ?User
    {
        if (method_exists($context, 'costCenter')) {
            return $context->costCenter?->head;
        }

        return null;
    }

    /**
     * Get the department head.
     */
    private function getDepartmentHead(Model $context): ?User
    {
        if (method_exists($context, 'department')) {
            return $context->department?->head;
        }

        return null;
    }

    /**
     * Check if this step has timed out.
     */
    public function hasTimedOut(\DateTime $requestDate): bool
    {
        if (!$this->timeout_hours) {
            return false;
        }

        $timeoutDate = (clone $requestDate)->modify("+{$this->timeout_hours} hours");
        return now() > $timeoutDate;
    }
}
