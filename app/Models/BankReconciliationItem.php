<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankReconciliationItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'bank_reconciliation_id',
        'bank_transaction_id',
        'journal_entry_id',
        'item_type', // outstanding_deposit, outstanding_check, bank_error, book_error, adjustment
        'description',
        'amount',
        'check_number',
        'transaction_date',
        'status', // outstanding, reconciled, adjustment, cleared
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function bankReconciliation(): BelongsTo
    {
        return $this->belongsTo(BankReconciliation::class);
    }

    public function bankTransaction(): BelongsTo
    {
        return $this->belongsTo(BankTransaction::class);
    }

    public function journalEntry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    // Scopes
    public function scopeByType($query, string $type)
    {
        return $query->where('item_type', $type);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeOutstanding($query)
    {
        return $query->where('status', 'outstanding');
    }

    public function scopeReconciled($query)
    {
        return $query->where('status', 'reconciled');
    }

    // Accessors
    public function getItemTypeColorAttribute(): string
    {
        return match($this->item_type) {
            'outstanding_deposit' => 'green',
            'outstanding_check' => 'red',
            'bank_error' => 'orange',
            'book_error' => 'purple',
            'adjustment' => 'blue',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'outstanding' => 'yellow',
            'reconciled' => 'green',
            'adjustment' => 'blue',
            'cleared' => 'gray',
            default => 'gray'
        };
    }

    public function getFormattedAmountAttribute(): string
    {
        $sign = $this->amount >= 0 ? '+' : '';
        return $sign . number_format($this->amount, 2);
    }

    // Helper Methods
    public function markAsReconciled(): void
    {
        $this->update(['status' => 'reconciled']);
    }

    public function markAsCleared(): void
    {
        $this->update(['status' => 'cleared']);
    }

    public function createAdjustmentEntry(int $accountId): JournalEntry
    {
        $journal = Journal::create([
            'client_id' => $this->client_id,
            'journal_number' => Journal::generateJournalNumber($this->client_id),
            'journal_date' => $this->transaction_date,
            'description' => "Bank reconciliation adjustment: {$this->description}",
            'reference_number' => "RECON-{$this->bankReconciliation->id}",
            'is_posted' => true,
            'created_by' => auth()->id(),
        ]);

        // Bank account entry
        $bankEntry = JournalEntry::create([
            'client_id' => $this->client_id,
            'journal_id' => $journal->id,
            'account_id' => $this->bankReconciliation->bankAccount->account_id,
            'description' => $this->description,
            'debit' => $this->amount > 0 ? $this->amount : 0,
            'credit' => $this->amount < 0 ? abs($this->amount) : 0,
        ]);

        // Adjustment account entry
        JournalEntry::create([
            'client_id' => $this->client_id,
            'journal_id' => $journal->id,
            'account_id' => $accountId,
            'description' => $this->description,
            'debit' => $this->amount < 0 ? abs($this->amount) : 0,
            'credit' => $this->amount > 0 ? $this->amount : 0,
        ]);

        $this->update(['journal_entry_id' => $bankEntry->id]);

        return $bankEntry;
    }
}
