<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankAccount extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'account_id',
        'bank_name',
        'account_number',
        'account_name',
        'account_type', // checking, savings, credit_line, investment
        'currency_code',
        'opening_balance',
        'current_balance',
        'available_balance',
        'credit_limit',
        'interest_rate',
        'bank_code',
        'swift_code',
        'branch_name',
        'branch_address',
        'contact_person',
        'contact_phone',
        'contact_email',
        'is_active',
        'is_default',
        'last_reconciled_date',
        'last_statement_date',
        'notes',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'credit_limit' => 'decimal:2',
        'interest_rate' => 'decimal:4',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_reconciled_date' => 'date',
        'last_statement_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function bankTransactions(): HasMany
    {
        return $this->hasMany(BankTransaction::class);
    }

    public function bankReconciliations(): HasMany
    {
        return $this->hasMany(BankReconciliation::class);
    }

    public function cashFlowForecasts(): HasMany
    {
        return $this->hasMany(CashFlowForecast::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('account_type', $type);
    }

    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency_code', $currency);
    }

    // Accessors
    public function getBalanceStatusAttribute(): string
    {
        if ($this->current_balance < 0) {
            return 'overdrawn';
        }
        
        if ($this->current_balance < ($this->credit_limit * 0.1)) {
            return 'low';
        }
        
        return 'normal';
    }

    public function getFormattedBalanceAttribute(): string
    {
        return $this->currency_code . ' ' . number_format($this->current_balance, 2);
    }

    public function getIsOverdrawnAttribute(): bool
    {
        return $this->current_balance < 0;
    }

    public function getAvailableCreditAttribute(): float
    {
        if ($this->account_type === 'credit_line') {
            return $this->credit_limit + $this->current_balance;
        }
        
        return $this->current_balance;
    }

    // Helper Methods
    public function updateBalance(float $amount, string $type = 'debit'): void
    {
        if ($type === 'debit') {
            $this->current_balance += $amount;
        } else {
            $this->current_balance -= $amount;
        }
        
        $this->available_balance = $this->current_balance;
        
        // Adjust for credit accounts
        if ($this->account_type === 'credit_line') {
            $this->available_balance = $this->credit_limit + $this->current_balance;
        }
        
        $this->save();
    }

    public function getTransactionHistory(int $days = 30): array
    {
        $transactions = $this->bankTransactions()
            ->where('transaction_date', '>=', now()->subDays($days))
            ->orderBy('transaction_date', 'desc')
            ->get();
            
        return $transactions->map(function ($transaction) {
            return [
                'date' => $transaction->transaction_date,
                'description' => $transaction->description,
                'amount' => $transaction->amount,
                'type' => $transaction->transaction_type,
                'balance' => $transaction->running_balance,
            ];
        })->toArray();
    }

    public function calculateMonthlyAverage(int $months = 12): float
    {
        $startDate = now()->subMonths($months);
        
        $monthlyBalances = $this->bankTransactions()
            ->where('transaction_date', '>=', $startDate)
            ->selectRaw('DATE_FORMAT(transaction_date, "%Y-%m") as month, AVG(running_balance) as avg_balance')
            ->groupBy('month')
            ->pluck('avg_balance');
            
        return $monthlyBalances->avg() ?? $this->current_balance;
    }

    public function isReconciliationDue(): bool
    {
        if (!$this->last_reconciled_date) {
            return true;
        }
        
        return $this->last_reconciled_date->addDays(30) < now();
    }

    public function getUnreconciledTransactionsCount(): int
    {
        return $this->bankTransactions()
            ->where('is_reconciled', false)
            ->count();
    }

    public static function getDefaultAccount(int $clientId): ?self
    {
        return self::where('client_id', $clientId)
                  ->where('is_default', true)
                  ->where('is_active', true)
                  ->first();
    }

    public function setAsDefault(): void
    {
        // Remove default from other accounts
        self::where('client_id', $this->client_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);
            
        // Set this as default
        $this->update(['is_default' => true]);
    }
}
