<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ApprovalAction extends Model
{
    use HasFactory;

    protected $fillable = [
        'approval_request_id',
        'step_number',
        'approver_id',
        'action',
        'comments',
        'delegated_to',
        'action_date',
        'action_data',
    ];

    protected $casts = [
        'action_date' => 'datetime',
        'action_data' => 'array',
    ];

    /**
     * Get the approval request.
     */
    public function approvalRequest(): BelongsTo
    {
        return $this->belongsTo(ApprovalRequest::class);
    }

    /**
     * Get the approver.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    /**
     * Get the user this action was delegated to.
     */
    public function delegatedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delegated_to');
    }

    /**
     * Check if this action was approved.
     */
    public function isApproved(): bool
    {
        return $this->action === 'approved';
    }

    /**
     * Check if this action was rejected.
     */
    public function isRejected(): bool
    {
        return $this->action === 'rejected';
    }

    /**
     * Check if this action was delegated.
     */
    public function isDelegated(): bool
    {
        return $this->action === 'delegated';
    }

    /**
     * Check if this action was returned.
     */
    public function isReturned(): bool
    {
        return $this->action === 'returned';
    }

    /**
     * Scope for approved actions.
     */
    public function scopeApproved($query)
    {
        return $query->where('action', 'approved');
    }

    /**
     * Scope for rejected actions.
     */
    public function scopeRejected($query)
    {
        return $query->where('action', 'rejected');
    }

    /**
     * Scope for actions by a specific approver.
     */
    public function scopeByApprover($query, User $approver)
    {
        return $query->where('approver_id', $approver->id);
    }
}
