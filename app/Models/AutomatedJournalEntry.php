<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class AutomatedJournalEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'entry_type',
        'name',
        'description',
        'configuration',
        'frequency',
        'start_date',
        'end_date',
        'next_run_date',
        'last_run_date',
        'is_active',
        'amount',
        'reference_model',
        'reference_id',
        'created_by',
    ];

    protected $casts = [
        'configuration' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_run_date' => 'date',
        'last_run_date' => 'date',
        'is_active' => 'boolean',
        'amount' => 'decimal:2',
    ];

    // Entry types constants
    const TYPE_DEPRECIATION = 'depreciation';
    const TYPE_ACCRUAL = 'accrual';
    const TYPE_ALLOCATION = 'allocation';
    const TYPE_RECURRING = 'recurring';

    // Frequency constants
    const FREQUENCY_DAILY = 'daily';
    const FREQUENCY_WEEKLY = 'weekly';
    const FREQUENCY_MONTHLY = 'monthly';
    const FREQUENCY_QUARTERLY = 'quarterly';
    const FREQUENCY_YEARLY = 'yearly';

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    public function generatedJournals(): HasMany
    {
        return $this->hasMany(Journal::class, 'automated_entry_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('entry_type', $type);
    }

    public function scopeDueForExecution($query)
    {
        return $query->active()
            ->where('next_run_date', '<=', now()->toDateString())
            ->where(function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', now()->toDateString());
            });
    }

    // Helper methods
    public function isDueForExecution(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->next_run_date && $this->next_run_date->isPast()) {
            if (!$this->end_date || $this->end_date->isFuture()) {
                return true;
            }
        }

        return false;
    }

    public function calculateNextRunDate(): ?Carbon
    {
        if (!$this->frequency) {
            return null;
        }

        $baseDate = $this->last_run_date ?: $this->start_date;

        return match ($this->frequency) {
            self::FREQUENCY_DAILY => $baseDate->addDay(),
            self::FREQUENCY_WEEKLY => $baseDate->addWeek(),
            self::FREQUENCY_MONTHLY => $baseDate->addMonth(),
            self::FREQUENCY_QUARTERLY => $baseDate->addMonths(3),
            self::FREQUENCY_YEARLY => $baseDate->addYear(),
            default => null,
        };
    }

    public function updateNextRunDate(): void
    {
        $nextDate = $this->calculateNextRunDate();
        if ($nextDate) {
            $this->update(['next_run_date' => $nextDate]);
        }
    }

    public function markAsExecuted(): void
    {
        $this->update([
            'last_run_date' => now()->toDateString(),
        ]);
        $this->updateNextRunDate();
    }

    public static function getEntryTypes(): array
    {
        return [
            self::TYPE_DEPRECIATION => 'Penyusutan',
            self::TYPE_ACCRUAL => 'Akrual',
            self::TYPE_ALLOCATION => 'Alokasi',
            self::TYPE_RECURRING => 'Berulang',
        ];
    }

    public static function getFrequencies(): array
    {
        return [
            self::FREQUENCY_DAILY => 'Harian',
            self::FREQUENCY_WEEKLY => 'Mingguan',
            self::FREQUENCY_MONTHLY => 'Bulanan',
            self::FREQUENCY_QUARTERLY => 'Triwulanan',
            self::FREQUENCY_YEARLY => 'Tahunan',
        ];
    }
}
