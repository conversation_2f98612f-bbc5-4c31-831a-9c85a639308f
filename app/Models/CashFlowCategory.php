<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CashFlowCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'parent_id',
        'category_code',
        'category_name',
        'description',
        'activity_type', // operating, investing, financing
        'flow_type', // inflow, outflow
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(CashFlowCategory::class, 'parent_id');
    }

    public function children(): Has<PERSON>any
    {
        return $this->hasMany(CashFlowCategory::class, 'parent_id');
    }

    public function cashFlowMappings(): HasMany
    {
        return $this->hasMany(CashFlowMapping::class);
    }

    public function cashFlowForecasts(): HasMany
    {
        return $this->hasMany(CashFlowForecast::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByActivityType($query, string $type)
    {
        return $query->where('activity_type', $type);
    }

    public function scopeByFlowType($query, string $type)
    {
        return $query->where('flow_type', $type);
    }

    public function scopeRootCategories($query)
    {
        return $query->whereNull('parent_id');
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        $names = collect([$this->category_name]);
        $parent = $this->parent;
        
        while ($parent) {
            $names->prepend($parent->category_name);
            $parent = $parent->parent;
        }
        
        return $names->implode(' > ');
    }

    public function getActivityTypeColorAttribute(): string
    {
        return match($this->activity_type) {
            'operating' => 'blue',
            'investing' => 'green',
            'financing' => 'purple',
            default => 'gray'
        };
    }

    public function getFlowTypeColorAttribute(): string
    {
        return match($this->flow_type) {
            'inflow' => 'green',
            'outflow' => 'red',
            default => 'gray'
        };
    }

    // Helper Methods
    public function getAllChildren()
    {
        $children = collect();
        
        foreach ($this->children as $child) {
            $children->push($child);
            $children = $children->merge($child->getAllChildren());
        }
        
        return $children;
    }

    public function getLevel(): int
    {
        $level = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $level++;
            $parent = $parent->parent;
        }
        
        return $level;
    }

    public function calculateCashFlow($startDate, $endDate): float
    {
        $amount = 0;
        
        // Get cash flow from mapped accounts
        foreach ($this->cashFlowMappings as $mapping) {
            $accountAmount = $mapping->account->getCashFlowAmount($startDate, $endDate);
            $amount += $accountAmount * ($mapping->multiplier ?? 1);
        }
        
        // Include children categories
        foreach ($this->children as $child) {
            $amount += $child->calculateCashFlow($startDate, $endDate);
        }
        
        return $amount;
    }

    public static function getDefaultCategories(): array
    {
        return [
            // Operating Activities
            [
                'category_code' => 'OP',
                'category_name' => 'Aktivitas Operasi',
                'activity_type' => 'operating',
                'flow_type' => null,
                'children' => [
                    [
                        'category_code' => 'OP-IN',
                        'category_name' => 'Penerimaan dari Pelanggan',
                        'activity_type' => 'operating',
                        'flow_type' => 'inflow',
                    ],
                    [
                        'category_code' => 'OP-OUT-SUP',
                        'category_name' => 'Pembayaran ke Pemasok',
                        'activity_type' => 'operating',
                        'flow_type' => 'outflow',
                    ],
                    [
                        'category_code' => 'OP-OUT-EMP',
                        'category_name' => 'Pembayaran Gaji',
                        'activity_type' => 'operating',
                        'flow_type' => 'outflow',
                    ],
                    [
                        'category_code' => 'OP-OUT-TAX',
                        'category_name' => 'Pembayaran Pajak',
                        'activity_type' => 'operating',
                        'flow_type' => 'outflow',
                    ],
                ]
            ],
            // Investing Activities
            [
                'category_code' => 'INV',
                'category_name' => 'Aktivitas Investasi',
                'activity_type' => 'investing',
                'flow_type' => null,
                'children' => [
                    [
                        'category_code' => 'INV-OUT-ASSET',
                        'category_name' => 'Pembelian Aset',
                        'activity_type' => 'investing',
                        'flow_type' => 'outflow',
                    ],
                    [
                        'category_code' => 'INV-IN-ASSET',
                        'category_name' => 'Penjualan Aset',
                        'activity_type' => 'investing',
                        'flow_type' => 'inflow',
                    ],
                ]
            ],
            // Financing Activities
            [
                'category_code' => 'FIN',
                'category_name' => 'Aktivitas Pendanaan',
                'activity_type' => 'financing',
                'flow_type' => null,
                'children' => [
                    [
                        'category_code' => 'FIN-IN-LOAN',
                        'category_name' => 'Penerimaan Pinjaman',
                        'activity_type' => 'financing',
                        'flow_type' => 'inflow',
                    ],
                    [
                        'category_code' => 'FIN-OUT-LOAN',
                        'category_name' => 'Pembayaran Pinjaman',
                        'activity_type' => 'financing',
                        'flow_type' => 'outflow',
                    ],
                    [
                        'category_code' => 'FIN-IN-EQUITY',
                        'category_name' => 'Penambahan Modal',
                        'activity_type' => 'financing',
                        'flow_type' => 'inflow',
                    ],
                ]
            ],
        ];
    }
}
