<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'supplier_code',
        'supplier_name',
        'contact_person',
        'email',
        'phone',
        'mobile',
        'fax',
        'website',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'tax_number',
        'business_license',
        'payment_terms',
        'payment_method', // transfer, check, cash, credit
        'credit_limit',
        'credit_days',
        'currency_code',
        'bank_name',
        'bank_account',
        'bank_account_name',
        'supplier_type', // material, service, both
        'supplier_category', // local, import, government
        'lead_time_days',
        'minimum_order',
        'delivery_terms', // FOB, CIF, EXW, etc
        'quality_rating', // A, B, C, D
        'performance_rating', // 1-5 stars
        'is_preferred',
        'is_active',
        'registration_date',
        'last_order_date',
        'total_orders',
        'total_amount',
        'notes',
        'internal_notes',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'credit_days' => 'integer',
        'lead_time_days' => 'integer',
        'minimum_order' => 'decimal:2',
        'performance_rating' => 'integer',
        'is_preferred' => 'boolean',
        'is_active' => 'boolean',
        'registration_date' => 'date',
        'last_order_date' => 'date',
        'total_orders' => 'integer',
        'total_amount' => 'decimal:2',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'contact_id')->where('contact_type', 'supplier');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('supplier_type', $type);
    }

    // Accessors
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->transactions()
            ->where('transaction_type', 'payable')
            ->sum('amount');
    }

    public function getFormattedAddressAttribute(): string
    {
        $address = collect([
            $this->address,
            $this->city,
            $this->postal_code,
            $this->country
        ])->filter()->implode(', ');

        return $address;
    }

    // Helper Methods
    public function getTotalPurchases($year = null): float
    {
        $query = $this->purchaseOrders();

        if ($year) {
            $query->whereYear('order_date', $year);
        }

        return $query->sum('total_amount');
    }

    public function getAveragePaymentDays(): float
    {
        // Calculate average payment days based on payment history
        return 30; // Placeholder
    }

    public function getPerformanceMetrics(): array
    {
        $orders = $this->purchaseOrders()->where('status', 'completed')->get();

        if ($orders->isEmpty()) {
            return [
                'total_orders' => 0,
                'on_time_delivery_rate' => 0,
                'quality_score' => 0,
                'overall_score' => 0,
            ];
        }

        $onTimeDeliveries = $orders->filter(function ($order) {
            return $order->delivery_date && $order->received_at &&
                $order->received_at <= $order->delivery_date;
        })->count();

        $onTimeRate = ($onTimeDeliveries / $orders->count()) * 100;

        return [
            'total_orders' => $orders->count(),
            'total_value' => $orders->sum('total_amount'),
            'on_time_delivery_rate' => round($onTimeRate, 2),
            'quality_score' => $this->getQualityScore(),
            'overall_score' => $this->calculateOverallScore($onTimeRate),
        ];
    }

    public function getCreditAnalysis(): array
    {
        $outstandingAmount = $this->purchaseOrders()
            ->where('payment_status', '!=', 'paid')
            ->sum('invoice_amount') - $this->purchaseOrders()
            ->where('payment_status', '!=', 'paid')
            ->sum('paid_amount');

        $creditUtilization = $this->credit_limit > 0 ? ($outstandingAmount / $this->credit_limit) * 100 : 0;

        return [
            'credit_limit' => $this->credit_limit,
            'outstanding_amount' => $outstandingAmount,
            'available_credit' => max(0, $this->credit_limit - $outstandingAmount),
            'credit_utilization_percentage' => round($creditUtilization, 2),
            'credit_status' => $this->getCreditStatus($creditUtilization),
        ];
    }

    private function getQualityScore(): int
    {
        return match ($this->quality_rating) {
            'A' => 95,
            'B' => 80,
            'C' => 65,
            'D' => 50,
            default => 70
        };
    }

    private function calculateOverallScore(float $onTimeRate): int
    {
        $qualityWeight = 0.4;
        $deliveryWeight = 0.6;

        $score = ($this->getQualityScore() * $qualityWeight) + ($onTimeRate * $deliveryWeight);

        return round($score);
    }

    private function getCreditStatus(float $utilization): string
    {
        if ($utilization <= 50) {
            return 'Good';
        } elseif ($utilization <= 80) {
            return 'Caution';
        } else {
            return 'Critical';
        }
    }

    public static function generateSupplierCode(int $clientId): string
    {
        $lastSupplier = self::where('client_id', $clientId)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastSupplier ? (int)substr($lastSupplier->supplier_code, -4) + 1 : 1;

        return 'SUP' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
