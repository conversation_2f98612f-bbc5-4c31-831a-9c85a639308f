<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'product_id',
        'bom_id',
        'location_id',
        'po_number',
        'reference_number',
        'quantity_to_produce',
        'quantity_produced',
        'quantity_remaining',
        'unit_id',
        'priority', // low, normal, high, urgent
        'status', // draft, planned, released, in_progress, completed, cancelled
        'planned_start_date',
        'planned_end_date',
        'actual_start_date',
        'actual_end_date',
        'estimated_cost',
        'actual_cost',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'quantity_to_produce' => 'decimal:2',
        'quantity_produced' => 'decimal:2',
        'quantity_remaining' => 'decimal:2',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'planned_start_date' => 'datetime',
        'planned_end_date' => 'datetime',
        'actual_start_date' => 'datetime',
        'actual_end_date' => 'datetime',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function billOfMaterial(): BelongsTo
    {
        return $this->belongsTo(BillOfMaterial::class, 'bom_id');
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    public function workOrders(): HasMany
    {
        return $this->hasMany(WorkOrder::class, 'production_order_id');
    }

    public function materialUsages(): HasMany
    {
        return $this->hasMany(RawMaterialUsage::class, 'production_order_id');
    }

    public function productionCosts(): HasMany
    {
        return $this->hasMany(ProductionCost::class, 'production_order_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeOverdue($query)
    {
        return $query->where('planned_end_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeInProgress($query)
    {
        return $query->whereIn('status', ['released', 'in_progress']);
    }

    // Accessors
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->quantity_to_produce <= 0) {
            return 0;
        }
        
        return ($this->quantity_produced / $this->quantity_to_produce) * 100;
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->planned_end_date < now() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'planned' => 'blue',
            'released' => 'yellow',
            'in_progress' => 'orange',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'green',
            'normal' => 'blue',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'gray'
        };
    }

    // Helper Methods
    public function canBeStarted(): bool
    {
        return in_array($this->status, ['planned', 'released']);
    }

    public function canBeCompleted(): bool
    {
        return $this->status === 'in_progress';
    }

    public function canBeCancelled(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    public function start(): bool
    {
        if (!$this->canBeStarted()) {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'actual_start_date' => now(),
        ]);

        return true;
    }

    public function complete(): bool
    {
        if (!$this->canBeCompleted()) {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'actual_end_date' => now(),
            'quantity_remaining' => 0,
        ]);

        return true;
    }

    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . "\nCancelled: " . $reason,
        ]);

        return true;
    }

    public function calculateEstimatedCost(): float
    {
        if (!$this->billOfMaterial) {
            return 0;
        }

        $bomCostPerUnit = $this->billOfMaterial->cost_per_unit;
        return $bomCostPerUnit * $this->quantity_to_produce;
    }

    public function updateQuantityProduced(float $quantity): void
    {
        $this->quantity_produced += $quantity;
        $this->quantity_remaining = $this->quantity_to_produce - $this->quantity_produced;
        
        // Auto-complete if fully produced
        if ($this->quantity_remaining <= 0 && $this->status === 'in_progress') {
            $this->status = 'completed';
            $this->actual_end_date = now();
        }
        
        $this->save();
    }
}
