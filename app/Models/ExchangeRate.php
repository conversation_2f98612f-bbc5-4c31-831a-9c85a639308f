<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ExchangeRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'rate_date',
        'source',
    ];

    protected $casts = [
        'rate' => 'decimal:6',
        'rate_date' => 'date',
    ];

    /**
     * Get the from currency.
     */
    public function fromCurrency()
    {
        return $this->belongsTo(Currency::class, 'from_currency', 'code');
    }

    /**
     * Get the to currency.
     */
    public function toCurrency()
    {
        return $this->belongsTo(Currency::class, 'to_currency', 'code');
    }

    /**
     * Scope for specific currency pair.
     */
    public function scopeForPair($query, string $fromCurrency, string $toCurrency)
    {
        return $query->where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency);
    }

    /**
     * Scope for specific date.
     */
    public function scopeForDate($query, Carbon $date)
    {
        return $query->whereDate('rate_date', $date);
    }

    /**
     * Get the latest rate for a currency pair.
     */
    public static function getLatestRate(string $fromCurrency, string $toCurrency): ?float
    {
        $rate = static::forPair($fromCurrency, $toCurrency)
            ->orderBy('rate_date', 'desc')
            ->first();

        return $rate ? $rate->rate : null;
    }
}
