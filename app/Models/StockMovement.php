<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockMovement extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'product_id',
        'inventory_id',
        'location_id',
        'from_location_id',
        'to_location_id',
        'unit_id',
        'reference_type',
        'reference_id',
        'movement_type', // in, out, transfer, adjustment
        'transaction_type', // purchase, sale, production, adjustment, transfer, return
        'reason_code', // receipt, issue, transfer, adjustment, scrap, return
        'quantity',
        'unit_cost',
        'total_cost',
        'running_balance',
        'running_value',
        'movement_date',
        'reference_number',
        'batch_number',
        'serial_number',
        'expiry_date',
        'lot_number',
        'work_order_id',
        'production_order_id',
        'purchase_order_id',
        'sales_order_id',
        'is_reversed',
        'reversed_by_id',
        'reversed_at',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'quantity' => 'decimal:6',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:2',
        'running_balance' => 'decimal:6',
        'running_value' => 'decimal:2',
        'movement_date' => 'datetime',
        'expiry_date' => 'date',
        'is_reversed' => 'boolean',
        'reversed_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function inventory(): BelongsTo
    {
        return $this->belongsTo(Inventory::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function fromLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'from_location_id');
    }

    public function toLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'to_location_id');
    }

    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class);
    }

    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function reversedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reversed_by_id');
    }

    // Scopes
    public function scopeInbound($query)
    {
        return $query->where('movement_type', 'in');
    }

    public function scopeOutbound($query)
    {
        return $query->where('movement_type', 'out');
    }

    public function scopeByTransactionType($query, string $type)
    {
        return $query->where('transaction_type', $type);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    // Accessors
    public function getSignedQuantityAttribute(): float
    {
        return $this->movement_type === 'in' ? $this->quantity : -$this->quantity;
    }

    public function getMovementDescriptionAttribute(): string
    {
        $descriptions = [
            'purchase' => 'Pembelian',
            'sale' => 'Penjualan',
            'production' => 'Produksi',
            'adjustment' => 'Penyesuaian',
            'transfer' => 'Transfer',
            'return' => 'Retur',
            'waste' => 'Waste/Scrap',
        ];

        return $descriptions[$this->transaction_type] ?? $this->transaction_type;
    }

    public function getFormattedQuantityAttribute(): string
    {
        $sign = $this->movement_type === 'in' ? '+' : '-';
        return $sign . number_format($this->quantity, 2) . ' ' . $this->unit->unit_symbol;
    }

    // Helper Methods
    public static function createMovement(array $data): self
    {
        // Calculate total cost
        $data['total_cost'] = $data['quantity'] * ($data['unit_cost'] ?? 0);

        // Set movement date if not provided
        if (!isset($data['movement_date'])) {
            $data['movement_date'] = now();
        }

        // Set created_by if not provided
        if (!isset($data['created_by'])) {
            $data['created_by'] = auth()->id() ?? 1;
        }

        $movement = self::create($data);

        // Update inventory
        $movement->updateInventory();

        return $movement;
    }

    public function updateInventory(): void
    {
        if (!$this->inventory) {
            return;
        }

        $this->inventory->updateStock(
            $this->quantity,
            $this->movement_type,
            $this->unit_cost
        );

        // Update running balance
        $this->running_balance = $this->inventory->current_stock;
        $this->save();
    }

    public function reverse(): self
    {
        $reverseMovement = self::create([
            'client_id' => $this->client_id,
            'product_id' => $this->product_id,
            'inventory_id' => $this->inventory_id,
            'unit_id' => $this->unit_id,
            'reference_type' => $this->reference_type,
            'reference_id' => $this->reference_id,
            'movement_type' => $this->movement_type === 'in' ? 'out' : 'in',
            'transaction_type' => 'adjustment',
            'quantity' => $this->quantity,
            'unit_cost' => $this->unit_cost,
            'movement_date' => now(),
            'reference_number' => 'REV-' . $this->reference_number,
            'notes' => 'Reversal of movement #' . $this->id,
            'created_by' => auth()->id() ?? 1,
        ]);

        return $reverseMovement;
    }
}
