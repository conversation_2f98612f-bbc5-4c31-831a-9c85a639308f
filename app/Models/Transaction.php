<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rule;
use App\Traits\HasCreator;
use Filament\Models\Contracts\HasCurrentTenantLabel;

class Transaction extends Model implements HasCurrentTenantLabel
{
    use HasFactory, SoftDeletes, HasCreator;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'transaction_date',
        'type',
        'description',
        'amount',
        'reference_number',
        'status',
        'created_by',
        // Enhanced fields for sales transactions
        'customer_id',
        'payment_method',
        'payment_status',
        'tax_amount',
        'discount_amount',
        'subtotal',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
    ];

    /**
     * Transaction types available in the system
     */
    public static function getTransactionTypes(): array
    {
        return [
            'sales' => 'Penjualan',
            'purchase' => 'Pembelian',
            'expense' => 'Pengeluaran',
            'asset_acquisition' => 'Akuisisi Aset',
        ];
    }

    /**
     * Payment methods available for transactions
     */
    public static function getPaymentMethods(): array
    {
        return [
            'cash' => 'Tunai',
            'transfer' => 'Transfer Bank',
            'check' => 'Cek',
            'credit' => 'Kredit',
            'debit_card' => 'Kartu Debit',
            'credit_card' => 'Kartu Kredit',
        ];
    }

    /**
     * Payment statuses available for transactions
     */
    public static function getPaymentStatuses(): array
    {
        return [
            'unpaid' => 'Belum Dibayar',
            'partial' => 'Dibayar Sebagian',
            'paid' => 'Lunas',
            'overpaid' => 'Lebih Bayar',
        ];
    }

    /**
     * Transaction statuses available in the system
     */
    public static function getTransactionStatuses(): array
    {
        return [
            'pending' => 'Menunggu',
            'processed' => 'Diproses',
            'rejected' => 'Ditolak',
        ];
    }

    /**
     * Validation rules for transaction type
     */
    public static function getTransactionTypeValidationRule()
    {
        return Rule::in(array_keys(self::getTransactionTypes()));
    }

    /**
     * Validation rules for transaction status
     */
    public static function getTransactionStatusValidationRule()
    {
        return Rule::in(array_keys(self::getTransactionStatuses()));
    }

    /**
     * Relationship: Transaction belongs to a client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relationship: Transaction has many journals (referenced by)
     */
    public function journals(): HasMany
    {
        return $this->hasMany(Journal::class, 'reference_transaction_id');
    }

    /**
     * Relationship: User who created this transaction
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship: Customer for sales transactions
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Relationship: Transaction has many items
     */
    public function items(): HasMany
    {
        return $this->hasMany(TransactionItem::class);
    }

    /**
     * Scope: Filter by client
     */
    public function scopeForClient(Builder $query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope: Filter by status
     */
    public function scopeWithStatus(Builder $query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Filter by type
     */
    public function scopeOfType(Builder $query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Calculate totals for sales transactions
     */
    public function calculateTotals(): void
    {
        if ($this->type === 'sales' && $this->items()->exists()) {
            $this->subtotal = $this->items->sum('line_total');
            $this->tax_amount = $this->subtotal * 0.11; // 11% PPN
            $this->amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
            $this->save();
        }
    }

    /**
     * Check if transaction is a sales transaction
     */
    public function isSales(): bool
    {
        return $this->type === 'sales';
    }

    /**
     * Check if transaction has items
     */
    public function hasItems(): bool
    {
        return $this->items()->exists();
    }

    /**
     * Scope: Pending transactions
     */
    public function scopePending(Builder $query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope: Processed transactions
     */
    public function scopeProcessed(Builder $query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * Check if transaction is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if transaction is processed
     */
    public function isProcessed(): bool
    {
        return $this->status === 'processed';
    }

    /**
     * Check if transaction is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Get the current tenant label for Filament
     */
    public function getCurrentTenantLabel(): string
    {
        return $this->client?->name ?? 'No Client';
    }
}
