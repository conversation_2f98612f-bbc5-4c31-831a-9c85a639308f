<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FixedAsset extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'asset_code',
        'asset_name',
        'asset_category', // building, machinery, vehicle, furniture, equipment, computer
        'asset_type', // tangible, intangible
        'description',
        'manufacturer',
        'model',
        'serial_number',
        'barcode',
        'purchase_date',
        'acquisition_cost',
        'installation_cost',
        'other_costs',
        'total_cost',
        'supplier_id',
        'purchase_order_id',
        'invoice_number',
        'warranty_start_date',
        'warranty_end_date',
        'location_id',
        'department_id',
        'cost_center_id',
        'responsible_person',
        'condition', // excellent, good, fair, poor, damaged
        'status', // active, inactive, disposed, sold, stolen, damaged
        'depreciation_method', // straight_line, declining_balance, units_of_production, sum_of_years
        'useful_life_years',
        'useful_life_units',
        'salvage_value',
        'depreciation_rate',
        'accumulated_depreciation',
        'book_value',
        'fair_value',
        'last_revaluation_date',
        'insurance_policy',
        'insurance_value',
        'insurance_expiry',
        'maintenance_schedule',
        'last_maintenance_date',
        'next_maintenance_date',
        'disposal_date',
        'disposal_method', // sale, scrap, donation, trade_in
        'disposal_value',
        'disposal_gain_loss',
        'notes',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'acquisition_cost' => 'decimal:2',
        'installation_cost' => 'decimal:2',
        'other_costs' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'warranty_start_date' => 'date',
        'warranty_end_date' => 'date',
        'useful_life_years' => 'integer',
        'useful_life_units' => 'integer',
        'salvage_value' => 'decimal:2',
        'depreciation_rate' => 'decimal:6',
        'accumulated_depreciation' => 'decimal:2',
        'book_value' => 'decimal:2',
        'fair_value' => 'decimal:2',
        'last_revaluation_date' => 'date',
        'insurance_value' => 'decimal:2',
        'insurance_expiry' => 'date',
        'last_maintenance_date' => 'date',
        'next_maintenance_date' => 'date',
        'disposal_date' => 'date',
        'disposal_value' => 'decimal:2',
        'disposal_gain_loss' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function costCenter(): BelongsTo
    {
        return $this->belongsTo(CostCenter::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function depreciations(): HasMany
    {
        return $this->hasMany(Depreciation::class);
    }

    public function maintenances(): HasMany
    {
        return $this->hasMany(AssetMaintenance::class);
    }

    public function transfers(): HasMany
    {
        return $this->hasMany(AssetTransfer::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('asset_category', $category);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByCondition($query, string $condition)
    {
        return $query->where('condition', $condition);
    }

    public function scopeByLocation($query, int $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeByDepartment($query, int $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeRequiringMaintenance($query)
    {
        return $query->where('next_maintenance_date', '<=', now()->addDays(30));
    }

    public function scopeWarrantyExpiring($query)
    {
        return $query->where('warranty_end_date', '<=', now()->addDays(30))
                    ->where('warranty_end_date', '>=', now());
    }

    // Accessors
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'green',
            'inactive' => 'yellow',
            'disposed' => 'gray',
            'sold' => 'blue',
            'stolen' => 'red',
            'damaged' => 'red',
            default => 'gray'
        };
    }

    public function getConditionColorAttribute(): string
    {
        return match($this->condition) {
            'excellent' => 'green',
            'good' => 'blue',
            'fair' => 'yellow',
            'poor' => 'orange',
            'damaged' => 'red',
            default => 'gray'
        };
    }

    public function getAgeInYearsAttribute(): float
    {
        return $this->purchase_date ? $this->purchase_date->diffInYears(now()) : 0;
    }

    public function getDepreciationPercentageAttribute(): float
    {
        if ($this->total_cost <= 0) {
            return 0;
        }
        
        return ($this->accumulated_depreciation / $this->total_cost) * 100;
    }

    public function getIsWarrantyValidAttribute(): bool
    {
        return $this->warranty_end_date && $this->warranty_end_date >= now();
    }

    public function getIsMaintenanceDueAttribute(): bool
    {
        return $this->next_maintenance_date && $this->next_maintenance_date <= now();
    }

    public function getDaysUntilMaintenanceAttribute(): ?int
    {
        return $this->next_maintenance_date ? now()->diffInDays($this->next_maintenance_date, false) : null;
    }

    // Helper Methods
    public function calculateTotalCost(): void
    {
        $this->total_cost = $this->acquisition_cost + $this->installation_cost + $this->other_costs;
        $this->save();
    }

    public function calculateBookValue(): void
    {
        $this->book_value = $this->total_cost - $this->accumulated_depreciation;
        $this->save();
    }

    public function updateDepreciation(float $depreciationAmount): void
    {
        $this->accumulated_depreciation += $depreciationAmount;
        $this->calculateBookValue();
    }

    public function transfer(int $newLocationId, int $newDepartmentId, ?string $reason = null): bool
    {
        $oldLocationId = $this->location_id;
        $oldDepartmentId = $this->department_id;

        $this->update([
            'location_id' => $newLocationId,
            'department_id' => $newDepartmentId,
        ]);

        // Record transfer
        AssetTransfer::create([
            'client_id' => $this->client_id,
            'fixed_asset_id' => $this->id,
            'from_location_id' => $oldLocationId,
            'to_location_id' => $newLocationId,
            'from_department_id' => $oldDepartmentId,
            'to_department_id' => $newDepartmentId,
            'transfer_date' => now(),
            'reason' => $reason,
            'transferred_by' => auth()->id() ?? 1,
        ]);

        return true;
    }

    public function dispose(string $method, float $disposalValue, ?string $reason = null): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $gainLoss = $disposalValue - $this->book_value;

        $this->update([
            'status' => 'disposed',
            'disposal_date' => now(),
            'disposal_method' => $method,
            'disposal_value' => $disposalValue,
            'disposal_gain_loss' => $gainLoss,
            'is_active' => false,
            'notes' => ($this->notes ?? '') . "\nDisposed: " . ($reason ?? 'Manual disposal'),
        ]);

        return true;
    }

    public function scheduleMaintenance(\DateTime $maintenanceDate, string $type, ?string $description = null): void
    {
        AssetMaintenance::create([
            'client_id' => $this->client_id,
            'fixed_asset_id' => $this->id,
            'maintenance_type' => $type,
            'scheduled_date' => $maintenanceDate,
            'description' => $description,
            'status' => 'scheduled',
            'created_by' => auth()->id() ?? 1,
        ]);

        $this->update([
            'next_maintenance_date' => $maintenanceDate,
        ]);
    }

    public function getMaintenanceHistory(): array
    {
        $maintenances = $this->maintenances()->orderBy('completed_date', 'desc')->get();
        
        return [
            'total_maintenances' => $maintenances->count(),
            'total_cost' => $maintenances->sum('cost'),
            'last_maintenance' => $maintenances->first(),
            'average_cost' => $maintenances->avg('cost') ?? 0,
            'maintenance_frequency' => $this->calculateMaintenanceFrequency($maintenances),
        ];
    }

    public function getAssetValuation(): array
    {
        return [
            'original_cost' => $this->total_cost,
            'accumulated_depreciation' => $this->accumulated_depreciation,
            'book_value' => $this->book_value,
            'fair_value' => $this->fair_value,
            'depreciation_percentage' => $this->depreciation_percentage,
            'remaining_useful_life' => $this->useful_life_years - $this->age_in_years,
            'annual_depreciation' => $this->calculateAnnualDepreciation(),
        ];
    }

    private function calculateMaintenanceFrequency($maintenances): float
    {
        if ($maintenances->count() < 2) {
            return 0;
        }

        $firstMaintenance = $maintenances->last()->completed_date;
        $lastMaintenance = $maintenances->first()->completed_date;
        
        if (!$firstMaintenance || !$lastMaintenance) {
            return 0;
        }

        $monthsDiff = $firstMaintenance->diffInMonths($lastMaintenance);
        return $monthsDiff > 0 ? $maintenances->count() / $monthsDiff : 0;
    }

    private function calculateAnnualDepreciation(): float
    {
        if ($this->depreciation_method === 'straight_line') {
            return ($this->total_cost - $this->salvage_value) / $this->useful_life_years;
        }
        
        // For other methods, return current year depreciation
        return $this->depreciations()->whereYear('depreciation_date', now()->year)->sum('depreciation_amount');
    }

    public static function generateAssetCode(int $clientId, string $category): string
    {
        $prefix = match($category) {
            'building' => 'BLD',
            'machinery' => 'MCH',
            'vehicle' => 'VHC',
            'furniture' => 'FUR',
            'equipment' => 'EQP',
            'computer' => 'CMP',
            default => 'AST'
        };
        
        $lastAsset = self::where('client_id', $clientId)
            ->where('asset_category', $category)
            ->orderBy('id', 'desc')
            ->first();
            
        $sequence = $lastAsset ? (int)substr($lastAsset->asset_code, -4) + 1 : 1;
        
        return $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
