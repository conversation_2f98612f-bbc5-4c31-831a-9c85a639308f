<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockOpname extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'location_id',
        'opname_number',
        'opname_date',
        'status', // draft, in_progress, completed, cancelled
        'count_type', // full, cycle, spot
        'scheduled_date',
        'started_at',
        'completed_at',
        'total_items',
        'counted_items',
        'variance_items',
        'total_variance_value',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'opname_date' => 'date',
        'scheduled_date' => 'date',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'approved_at' => 'datetime',
        'total_items' => 'integer',
        'counted_items' => 'integer',
        'variance_items' => 'integer',
        'total_variance_value' => 'decimal:2',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(StockOpnameItem::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByLocation($query, int $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('count_type', $type);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'in_progress']);
    }

    // Accessors
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'in_progress' => 'blue',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getCompletionPercentageAttribute(): float
    {
        if ($this->total_items <= 0) {
            return 0;
        }
        
        return ($this->counted_items / $this->total_items) * 100;
    }

    public function getVariancePercentageAttribute(): float
    {
        if ($this->counted_items <= 0) {
            return 0;
        }
        
        return ($this->variance_items / $this->counted_items) * 100;
    }

    // Helper Methods
    public function generateItems(): void
    {
        if ($this->status !== 'draft') {
            throw new \Exception('Can only generate items for draft opname');
        }

        // Get all inventory items for the location
        $inventories = Inventory::where('client_id', $this->client_id)
            ->where('location_id', $this->location_id)
            ->where('current_stock', '>', 0)
            ->with('product')
            ->get();

        foreach ($inventories as $inventory) {
            StockOpnameItem::create([
                'client_id' => $this->client_id,
                'stock_opname_id' => $this->id,
                'product_id' => $inventory->product_id,
                'inventory_id' => $inventory->id,
                'system_quantity' => $inventory->current_stock,
                'unit_cost' => $inventory->getValuationCost(),
                'status' => 'pending',
            ]);
        }

        $this->update([
            'total_items' => $inventories->count(),
        ]);
    }

    public function start(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);

        return true;
    }

    public function complete(): bool
    {
        if ($this->status !== 'in_progress') {
            return false;
        }

        // Calculate statistics
        $this->calculateStatistics();

        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        return true;
    }

    public function approve(int $approvedBy): bool
    {
        if ($this->status !== 'completed') {
            return false;
        }

        // Create stock adjustments for variances
        $this->createAdjustments();

        $this->update([
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        return true;
    }

    public function cancel(?string $reason = null): bool
    {
        if (!in_array($this->status, ['draft', 'in_progress'])) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => ($this->notes ?? '') . ' | Cancelled: ' . ($reason ?? 'Manual cancellation'),
        ]);

        return true;
    }

    private function calculateStatistics(): void
    {
        $items = $this->items;
        
        $countedItems = $items->where('status', 'counted')->count();
        $varianceItems = $items->where('variance_quantity', '!=', 0)->count();
        $totalVarianceValue = $items->sum('variance_value');

        $this->update([
            'counted_items' => $countedItems,
            'variance_items' => $varianceItems,
            'total_variance_value' => $totalVarianceValue,
        ]);
    }

    private function createAdjustments(): void
    {
        $varianceItems = $this->items()->where('variance_quantity', '!=', 0)->get();

        foreach ($varianceItems as $item) {
            if ($item->variance_quantity != 0) {
                StockMovement::createMovement([
                    'client_id' => $this->client_id,
                    'product_id' => $item->product_id,
                    'location_id' => $this->location_id,
                    'inventory_id' => $item->inventory_id,
                    'movement_type' => $item->variance_quantity > 0 ? 'in' : 'out',
                    'transaction_type' => 'adjustment',
                    'reason_code' => 'stock_opname',
                    'quantity' => abs($item->variance_quantity),
                    'unit_cost' => $item->unit_cost,
                    'reference_type' => StockOpname::class,
                    'reference_id' => $this->id,
                    'reference_number' => $this->opname_number,
                    'movement_date' => $this->completed_at,
                    'notes' => "Stock opname adjustment - {$this->opname_number}",
                ]);
            }
        }
    }

    public static function generateOpnameNumber(int $clientId): string
    {
        $year = date('Y');
        $month = date('m');
        
        $lastOpname = self::where('client_id', $clientId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();
            
        $sequence = $lastOpname ? (int)substr($lastOpname->opname_number, -4) + 1 : 1;
        
        return 'SO' . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
