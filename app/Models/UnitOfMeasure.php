<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class UnitOfMeasure extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'base_unit_id',
        'unit_code',
        'unit_name',
        'unit_symbol',
        'conversion_factor',
        'is_base_unit',
        'is_active',
        'description',
    ];

    protected $casts = [
        'conversion_factor' => 'decimal:6',
        'is_base_unit' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function baseUnit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'base_unit_id');
    }

    public function derivedUnits(): HasMany
    {
        return $this->hasMany(UnitOfMeasure::class, 'base_unit_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'unit_id');
    }

    public function bomItems(): HasMany
    {
        return $this->hasMany(BillOfMaterialItem::class, 'unit_id');
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'unit_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBaseUnits($query)
    {
        return $query->where('is_base_unit', true);
    }

    // Helper Methods
    public function convertToBaseUnit(float $quantity): float
    {
        if ($this->is_base_unit) {
            return $quantity;
        }
        
        return $quantity * $this->conversion_factor;
    }

    public function convertFromBaseUnit(float $baseQuantity): float
    {
        if ($this->is_base_unit) {
            return $baseQuantity;
        }
        
        return $baseQuantity / $this->conversion_factor;
    }

    public function convertTo(UnitOfMeasure $targetUnit, float $quantity): float
    {
        // Convert to base unit first
        $baseQuantity = $this->convertToBaseUnit($quantity);
        
        // Then convert to target unit
        return $targetUnit->convertFromBaseUnit($baseQuantity);
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->unit_symbol ? 
            "{$this->unit_name} ({$this->unit_symbol})" : 
            $this->unit_name;
    }
}
