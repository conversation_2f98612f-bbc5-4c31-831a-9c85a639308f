<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetTransfer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'fixed_asset_id',
        'transfer_number',
        'transfer_date',
        'from_location_id',
        'to_location_id',
        'from_department_id',
        'to_department_id',
        'from_cost_center_id',
        'to_cost_center_id',
        'from_responsible_person',
        'to_responsible_person',
        'transfer_type', // internal, external, disposal, sale
        'reason',
        'condition_before',
        'condition_after',
        'book_value_at_transfer',
        'transfer_cost',
        'approval_required',
        'status', // pending, approved, completed, cancelled
        'transferred_by',
        'received_by',
        'approved_by',
        'approved_at',
        'completed_at',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'transfer_date' => 'date',
        'book_value_at_transfer' => 'decimal:2',
        'transfer_cost' => 'decimal:2',
        'approval_required' => 'boolean',
        'approved_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function fixedAsset(): BelongsTo
    {
        return $this->belongsTo(FixedAsset::class);
    }

    public function fromLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'from_location_id');
    }

    public function toLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'to_location_id');
    }

    public function fromDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'from_department_id');
    }

    public function toDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'to_department_id');
    }

    public function fromCostCenter(): BelongsTo
    {
        return $this->belongsTo(CostCenter::class, 'from_cost_center_id');
    }

    public function toCostCenter(): BelongsTo
    {
        return $this->belongsTo(CostCenter::class, 'to_cost_center_id');
    }

    public function transferredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'transferred_by');
    }

    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('transfer_type', $type);
    }

    public function scopePendingApproval($query)
    {
        return $query->where('approval_required', true)
                    ->where('status', 'pending');
    }

    public function scopeByLocation($query, int $locationId)
    {
        return $query->where('from_location_id', $locationId)
                    ->orWhere('to_location_id', $locationId);
    }

    public function scopeByDepartment($query, int $departmentId)
    {
        return $query->where('from_department_id', $departmentId)
                    ->orWhere('to_department_id', $departmentId);
    }

    // Accessors
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'approved' => 'blue',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getTransferTypeColorAttribute(): string
    {
        return match($this->transfer_type) {
            'internal' => 'blue',
            'external' => 'orange',
            'disposal' => 'red',
            'sale' => 'green',
            default => 'gray'
        };
    }

    public function getRequiresApprovalAttribute(): bool
    {
        return $this->approval_required && $this->status === 'pending';
    }

    // Helper Methods
    public function approve(int $approvedBy): bool
    {
        if (!$this->approval_required || $this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        return true;
    }

    public function complete(int $receivedBy, ?string $conditionAfter = null): bool
    {
        if (!in_array($this->status, ['pending', 'approved'])) {
            return false;
        }

        // Update asset location and department
        $this->fixedAsset->update([
            'location_id' => $this->to_location_id,
            'department_id' => $this->to_department_id,
            'cost_center_id' => $this->to_cost_center_id,
            'responsible_person' => $this->to_responsible_person,
            'condition' => $conditionAfter ?? $this->condition_after ?? $this->fixedAsset->condition,
        ]);

        $this->update([
            'status' => 'completed',
            'received_by' => $receivedBy,
            'completed_at' => now(),
            'condition_after' => $conditionAfter ?? $this->condition_after,
        ]);

        return true;
    }

    public function cancel(?string $reason = null): bool
    {
        if ($this->status === 'completed') {
            return false; // Cannot cancel completed transfers
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => ($this->notes ?? '') . ' | Cancelled: ' . ($reason ?? 'Manual cancellation'),
        ]);

        return true;
    }

    public function getTransferSummary(): array
    {
        return [
            'asset_info' => [
                'asset_code' => $this->fixedAsset->asset_code,
                'asset_name' => $this->fixedAsset->asset_name,
                'book_value' => $this->book_value_at_transfer,
            ],
            'transfer_details' => [
                'transfer_number' => $this->transfer_number,
                'transfer_date' => $this->transfer_date,
                'transfer_type' => $this->transfer_type,
                'reason' => $this->reason,
            ],
            'from_details' => [
                'location' => $this->fromLocation->location_name ?? 'N/A',
                'department' => $this->fromDepartment->department_name ?? 'N/A',
                'responsible_person' => $this->from_responsible_person,
            ],
            'to_details' => [
                'location' => $this->toLocation->location_name ?? 'N/A',
                'department' => $this->toDepartment->department_name ?? 'N/A',
                'responsible_person' => $this->to_responsible_person,
            ],
            'status_info' => [
                'status' => $this->status,
                'requires_approval' => $this->requires_approval,
                'approved_by' => $this->approvedBy->name ?? null,
                'completed_by' => $this->receivedBy->name ?? null,
            ],
        ];
    }

    public function generateTransferDocument(): array
    {
        return [
            'document_title' => 'Asset Transfer Document',
            'transfer_number' => $this->transfer_number,
            'transfer_date' => $this->transfer_date->format('d/m/Y'),
            'asset_details' => [
                'code' => $this->fixedAsset->asset_code,
                'name' => $this->fixedAsset->asset_name,
                'category' => $this->fixedAsset->asset_category,
                'serial_number' => $this->fixedAsset->serial_number,
                'book_value' => number_format($this->book_value_at_transfer, 2),
                'condition_before' => $this->condition_before,
                'condition_after' => $this->condition_after,
            ],
            'transfer_from' => [
                'location' => $this->fromLocation->location_name ?? 'N/A',
                'department' => $this->fromDepartment->department_name ?? 'N/A',
                'responsible_person' => $this->from_responsible_person,
            ],
            'transfer_to' => [
                'location' => $this->toLocation->location_name ?? 'N/A',
                'department' => $this->toDepartment->department_name ?? 'N/A',
                'responsible_person' => $this->to_responsible_person,
            ],
            'signatures' => [
                'transferred_by' => $this->transferredBy->name ?? 'N/A',
                'received_by' => $this->receivedBy->name ?? 'N/A',
                'approved_by' => $this->approvedBy->name ?? 'N/A',
            ],
            'notes' => $this->notes,
            'generated_at' => now()->format('d/m/Y H:i:s'),
        ];
    }

    public static function generateTransferNumber(int $clientId): string
    {
        $year = date('Y');
        $month = date('m');
        
        $lastTransfer = self::where('client_id', $clientId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();
            
        $sequence = $lastTransfer ? (int)substr($lastTransfer->transfer_number, -4) + 1 : 1;
        
        return 'TRF' . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
