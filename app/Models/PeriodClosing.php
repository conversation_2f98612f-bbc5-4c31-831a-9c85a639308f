<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PeriodClosing extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'year',
        'month',
        'period_start',
        'period_end',
        'status',
        'checklist_items',
        'validation_results',
        'total_debits',
        'total_credits',
        'is_balanced',
        'closing_notes',
        'closed_by',
        'closed_at',
        'reopened_by',
        'reopened_at',
        'reopen_reason',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'checklist_items' => 'array',
        'validation_results' => 'array',
        'total_debits' => 'decimal:2',
        'total_credits' => 'decimal:2',
        'is_balanced' => 'boolean',
        'closed_at' => 'datetime',
        'reopened_at' => 'datetime',
    ];

    // Status constants
    const STATUS_OPEN = 'open';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_CLOSED = 'closed';
    const STATUS_REOPENED = 'reopened';

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function closedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'closed_by');
    }

    public function reopenedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reopened_by');
    }

    // Scopes
    public function scopeForPeriod($query, int $year, int $month)
    {
        return $query->where('year', $year)->where('month', $month);
    }

    public function scopeOpen($query)
    {
        return $query->where('status', self::STATUS_OPEN);
    }

    public function scopeClosed($query)
    {
        return $query->where('status', self::STATUS_CLOSED);
    }

    // Helper methods
    public function isOpen(): bool
    {
        return $this->status === self::STATUS_OPEN;
    }

    public function isClosed(): bool
    {
        return $this->status === self::STATUS_CLOSED;
    }

    public function isInProgress(): bool
    {
        return $this->status === self::STATUS_IN_PROGRESS;
    }

    public function canBeClosed(): bool
    {
        return $this->status === self::STATUS_OPEN || $this->status === self::STATUS_IN_PROGRESS;
    }

    public function canBeReopened(): bool
    {
        return $this->status === self::STATUS_CLOSED;
    }

    public function getPeriodName(): string
    {
        $monthNames = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];

        return $monthNames[$this->month] . ' ' . $this->year;
    }

    public function getChecklistProgress(): array
    {
        $items = $this->checklist_items ?? [];
        $total = count($items);
        $completed = collect($items)->where('completed', true)->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'percentage' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }

    public function updateChecklistItem(string $key, bool $completed, ?string $notes = null): void
    {
        $items = $this->checklist_items ?? [];

        if (isset($items[$key])) {
            $items[$key]['completed'] = $completed;
            $items[$key]['completed_at'] = $completed ? now()->toISOString() : null;
            $items[$key]['notes'] = $notes;

            $this->update(['checklist_items' => $items]);
        }
    }

    public static function getDefaultChecklist(): array
    {
        return [
            'bank_reconciliation' => [
                'title' => 'Rekonsiliasi Bank',
                'description' => 'Pastikan semua rekening bank telah direkonsiliasi',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => true,
            ],
            'inventory_count' => [
                'title' => 'Stock Opname',
                'description' => 'Lakukan stock opname dan adjustment jika diperlukan',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => true,
            ],
            'depreciation_entries' => [
                'title' => 'Jurnal Penyusutan',
                'description' => 'Pastikan jurnal penyusutan telah dibuat',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => true,
            ],
            'accrual_entries' => [
                'title' => 'Jurnal Akrual',
                'description' => 'Buat jurnal akrual untuk beban dan pendapatan',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => true,
            ],
            'trial_balance' => [
                'title' => 'Neraca Saldo',
                'description' => 'Pastikan neraca saldo balance',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => true,
            ],
            'review_transactions' => [
                'title' => 'Review Transaksi',
                'description' => 'Review semua transaksi periode berjalan',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => false,
            ],
            'backup_data' => [
                'title' => 'Backup Data',
                'description' => 'Backup data sebelum tutup buku',
                'completed' => false,
                'completed_at' => null,
                'notes' => null,
                'required' => false,
            ],
        ];
    }

    public static function getStatuses(): array
    {
        return [
            self::STATUS_OPEN => 'Terbuka',
            self::STATUS_IN_PROGRESS => 'Sedang Proses',
            self::STATUS_CLOSED => 'Ditutup',
            self::STATUS_REOPENED => 'Dibuka Kembali',
        ];
    }
}
