<?php

namespace App\Models;

use App\Traits\HasApprovalWorkflow;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrder extends Model
{
    use HasFactory, SoftDeletes, HasApprovalWorkflow;

    protected $fillable = [
        'client_id',
        'supplier_id',
        'po_number',
        'order_date',
        'expected_date',
        'delivery_date',
        'status', // draft, sent, confirmed, partial, completed, cancelled
        'priority', // low, normal, high, urgent
        'subtotal',
        'tax_amount',
        'discount_amount',
        'shipping_cost',
        'other_charges',
        'total_amount',
        'currency_code',
        'exchange_rate',
        'payment_terms',
        'payment_method', // cash, transfer, check, credit
        'delivery_address',
        'delivery_method', // pickup, delivery, courier
        'terms_conditions',
        'internal_notes',
        'supplier_notes',
        'created_by',
        'approval_status', // none, pending, approved, rejected
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'rejection_reason',
        'received_by',
        'received_at',
        'invoice_number',
        'invoice_date',
        'invoice_amount',
        'paid_amount',
        'payment_status', // unpaid, partial, paid, overpaid
    ];

    protected $casts = [
        'order_date' => 'date',
        'expected_date' => 'date',
        'delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'other_charges' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'received_at' => 'datetime',
        'invoice_date' => 'date',
        'invoice_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'sent', 'confirmed']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_date', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled']);
    }

    // Accessors
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'gray',
            'sent' => 'blue',
            'confirmed' => 'yellow',
            'partial' => 'orange',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->expected_date < now() &&
            !in_array($this->status, ['completed', 'cancelled']);
    }

    // Helper Methods
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });

        $this->tax_amount = $this->subtotal * 0.11; // 11% PPN
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;

        $this->save();
    }

    public function approve(int $approvedBy): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->update([
            'status' => 'sent',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        return true;
    }

    public function markAsReceived(): bool
    {
        if (!in_array($this->status, ['confirmed', 'partial'])) {
            return false;
        }

        // Check if all items are fully received
        $allReceived = $this->items->every(function ($item) {
            return $item->quantity_received >= $item->quantity;
        });

        $this->update([
            'status' => $allReceived ? 'completed' : 'partial',
            'delivery_date' => now(),
        ]);

        return true;
    }

    public static function generatePONumber(int $clientId): string
    {
        $year = date('Y');
        $month = date('m');

        $lastPO = self::where('client_id', $clientId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastPO ? (int)substr($lastPO->po_number, -4) + 1 : 1;

        return 'PO' . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Override: Check if PO should auto-submit for approval.
     */
    protected function shouldAutoSubmitForApproval(): bool
    {
        // Auto-submit when status changes to 'sent' or when total_amount is significant
        return $this->status === 'sent' || ($this->total_amount && $this->total_amount > 5000000);
    }

    /**
     * Override: Get fields that trigger approval when changed.
     */
    protected function getApprovalTriggerFields(): array
    {
        return ['total_amount', 'status', 'supplier_id'];
    }

    /**
     * Check if PO can be edited.
     */
    public function canBeEdited(): bool
    {
        return !$this->isPendingApproval() && !$this->isApproved() && $this->status !== 'completed';
    }

    /**
     * Check if PO can be sent.
     */
    public function canBeSent(): bool
    {
        return $this->status === 'draft' && !$this->isPendingApproval();
    }

    /**
     * Send PO (will trigger approval if needed).
     */
    public function send(): bool
    {
        if (!$this->canBeSent()) {
            return false;
        }

        $this->update(['status' => 'sent']);

        // This will trigger approval workflow if needed
        if ($this->requiresApproval() && !$this->isPendingApproval()) {
            $this->submitForApproval();
        }

        return true;
    }

    /**
     * Scope for POs that need approval.
     */
    public function scopeNeedsApproval($query)
    {
        return $query->where('approval_status', 'pending');
    }

    /**
     * Scope for approved POs.
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }
}
