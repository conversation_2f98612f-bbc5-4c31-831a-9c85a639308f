<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'category_id',
        'unit_id',
        'product_code',
        'product_name',
        'description',
        'product_type', // raw_material, finished_good, semi_finished, service
        'barcode',
        'sku',
        'standard_cost',
        'selling_price',
        'purchase_price',
        'minimum_stock',
        'maximum_stock',
        'reorder_point',
        'lead_time_days',
        'shelf_life_days',
        'weight',
        'dimensions',
        'tax_rate',
        'discount_rate',
        'commission_rate',
        'warranty_period',
        'brand',
        'model',
        'color',
        'size',
        'material',
        'origin_country',
        'hs_code',
        'image_url',
        'is_active',
        'is_stockable',
        'is_purchasable',
        'is_saleable',
        'is_manufactured',
        'is_serialized',
        'is_batch_tracked',
        'is_expirable',
        'notes',
    ];

    protected $casts = [
        'standard_cost' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'purchase_price' => 'decimal:2',
        'minimum_stock' => 'decimal:2',
        'maximum_stock' => 'decimal:2',
        'reorder_point' => 'decimal:2',
        'lead_time_days' => 'integer',
        'shelf_life_days' => 'integer',
        'weight' => 'decimal:3',
        'dimensions' => 'json',
        'tax_rate' => 'decimal:4',
        'discount_rate' => 'decimal:4',
        'commission_rate' => 'decimal:4',
        'warranty_period' => 'integer',
        'is_active' => 'boolean',
        'is_stockable' => 'boolean',
        'is_purchasable' => 'boolean',
        'is_saleable' => 'boolean',
        'is_manufactured' => 'boolean',
        'is_serialized' => 'boolean',
        'is_batch_tracked' => 'boolean',
        'is_expirable' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    public function billOfMaterial(): HasOne
    {
        return $this->hasOne(BillOfMaterial::class);
    }

    public function bomItems(): HasMany
    {
        return $this->hasMany(BillOfMaterialItem::class, 'material_id');
    }

    public function inventory(): HasOne
    {
        return $this->hasOne(Inventory::class);
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    public function purchaseOrderItems(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    public function transactionItems(): HasMany
    {
        return $this->hasMany(TransactionItem::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeStockable($query)
    {
        return $query->where('is_stockable', true);
    }

    public function scopePurchasable($query)
    {
        return $query->where('is_purchasable', true);
    }

    public function scopeSaleable($query)
    {
        return $query->where('is_saleable', true);
    }

    public function scopeManufactured($query)
    {
        return $query->where('is_manufactured', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('product_type', $type);
    }

    // Accessors
    public function getCurrentStockAttribute(): float
    {
        return $this->inventory?->current_stock ?? 0;
    }

    public function getStockValueAttribute(): float
    {
        return $this->current_stock * $this->standard_cost;
    }

    public function getGrossProfitMarginAttribute(): float
    {
        if ($this->selling_price <= 0) {
            return 0;
        }

        return (($this->selling_price - $this->standard_cost) / $this->selling_price) * 100;
    }

    public function getIsLowStockAttribute(): bool
    {
        return $this->current_stock <= $this->reorder_point;
    }

    public function getIsOutOfStockAttribute(): bool
    {
        return $this->current_stock <= 0;
    }

    // Helper Methods
    public function hasBOM(): bool
    {
        return $this->billOfMaterial()->exists();
    }

    public function calculateStandardCost(): float
    {
        if (!$this->hasBOM()) {
            return $this->standard_cost;
        }

        $totalCost = 0;

        foreach ($this->billOfMaterial->items as $item) {
            $materialCost = $item->material->standard_cost * $item->quantity;
            $totalCost += $materialCost;
        }

        return $totalCost;
    }

    public function getCurrentStock(): float
    {
        return $this->inventories()->sum('current_stock');
    }

    public function getAvailableStock(): float
    {
        return $this->inventories()->sum('available_stock');
    }

    public function getGrossMargin(): float
    {
        if ($this->selling_price <= 0) {
            return 0;
        }

        return (($this->selling_price - $this->standard_cost) / $this->selling_price) * 100;
    }

    public function getMarkupPercentage(): float
    {
        if ($this->standard_cost <= 0) {
            return 0;
        }

        return (($this->selling_price - $this->standard_cost) / $this->standard_cost) * 100;
    }

    public function calculateSellingPriceWithTax(): float
    {
        return $this->selling_price * (1 + ($this->tax_rate / 100));
    }

    public function calculateDiscountedPrice(?float $discountPercent = null): float
    {
        $discount = $discountPercent ?? $this->discount_rate;
        return $this->selling_price * (1 - ($discount / 100));
    }

    public function getInventoryValue(): float
    {
        return $this->getCurrentStock() * $this->standard_cost;
    }

    public function getAverageMonthlyUsage(int $months = 12): float
    {
        $startDate = now()->subMonths($months);

        $totalUsage = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('created_at', '>=', $startDate)
            ->sum('quantity');

        return $totalUsage / $months;
    }

    public function getReorderSuggestion(): array
    {
        $currentStock = $this->getCurrentStock();
        $monthlyUsage = $this->getAverageMonthlyUsage();
        $leadTimeUsage = ($monthlyUsage / 30) * $this->lead_time_days;

        $suggestedReorderPoint = $leadTimeUsage * 1.2; // 20% safety stock
        $suggestedOrderQuantity = $this->maximum_stock - $currentStock;

        return [
            'current_stock' => $currentStock,
            'monthly_usage' => $monthlyUsage,
            'suggested_reorder_point' => $suggestedReorderPoint,
            'suggested_order_quantity' => max(0, $suggestedOrderQuantity),
            'days_of_stock' => $monthlyUsage > 0 ? ($currentStock / $monthlyUsage) * 30 : 999,
            'needs_reorder' => $currentStock <= $suggestedReorderPoint,
        ];
    }

    public function getQualityGrade(): string
    {
        // Calculate quality grade based on various factors
        $score = 0;

        // Stock availability (30%)
        if (!$this->is_out_of_stock) $score += 30;
        elseif (!$this->is_low_stock) $score += 20;

        // Profitability (40%)
        $margin = $this->getGrossMargin();
        if ($margin >= 30) $score += 40;
        elseif ($margin >= 20) $score += 30;
        elseif ($margin >= 10) $score += 20;

        // Activity (30%)
        $monthlyUsage = $this->getAverageMonthlyUsage();
        if ($monthlyUsage >= 100) $score += 30;
        elseif ($monthlyUsage >= 50) $score += 20;
        elseif ($monthlyUsage >= 10) $score += 10;

        return match (true) {
            $score >= 80 => 'A',
            $score >= 60 => 'B',
            $score >= 40 => 'C',
            default => 'D'
        };
    }

    public static function generateProductCode(int $clientId, string $categoryPrefix = 'PRD'): string
    {
        $year = date('Y');
        $month = date('m');

        $lastProduct = self::where('client_id', $clientId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastProduct ? (int)substr($lastProduct->product_code, -4) + 1 : 1;

        return $categoryPrefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
