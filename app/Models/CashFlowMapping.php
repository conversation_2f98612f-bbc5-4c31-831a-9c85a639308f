<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CashFlowMapping extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'cash_flow_category_id',
        'account_id',
        'multiplier',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'multiplier' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function cashFlowCategory(): BelongsTo
    {
        return $this->belongsTo(CashFlowCategory::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Helper Methods
    public function calculateAmount($startDate, $endDate): float
    {
        $accountAmount = $this->account->getCashFlowAmount($startDate, $endDate);
        return $accountAmount * $this->multiplier;
    }
}
