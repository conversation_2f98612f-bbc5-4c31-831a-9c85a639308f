<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ApprovalWorkflow extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'client_id',
        'workflow_name',
        'workflow_code',
        'description',
        'approvable_type',
        'conditions',
        'is_active',
        'max_amount',
        'is_sequential',
        'allow_self_approval',
        'auto_approve_below_limit',
        'auto_approve_limit',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'conditions' => 'array',
        'is_active' => 'boolean',
        'is_sequential' => 'boolean',
        'allow_self_approval' => 'boolean',
        'auto_approve_below_limit' => 'boolean',
        'auto_approve_limit' => 'decimal:2',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function steps(): HasMany
    {
        return $this->hasMany(ApprovalWorkflowStep::class)->orderBy('step_order');
    }

    public function requests(): HasMany
    {
        return $this->hasMany(ApprovalRequest::class);
    }

    /**
     * Check if workflow applies to given model and conditions
     */
    public function appliesTo(Model $model, array $data = []): bool
    {
        if ($this->approvable_type !== get_class($model)) {
            return false;
        }

        if (!$this->is_active) {
            return false;
        }

        // Check amount conditions
        if ($this->max_amount && isset($data['amount'])) {
            if ($data['amount'] > $this->max_amount) {
                return false;
            }
        }

        // Check auto-approve limit
        if ($this->auto_approve_below_limit && $this->auto_approve_limit) {
            if (isset($data['amount']) && $data['amount'] <= $this->auto_approve_limit) {
                return false; // Auto-approve, no workflow needed
            }
        }

        return $this->evaluateConditions($model, $data);
    }

    /**
     * Evaluate workflow conditions
     */
    private function evaluateConditions(Model $model, array $data = []): bool
    {
        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $condition) {
            $field = $condition['field'];
            $operator = $condition['operator'];
            $value = $condition['value'];

            $modelValue = data_get($model, $field) ?? data_get($data, $field);

            if (!$this->evaluateCondition($modelValue, $operator, $value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate single condition
     */
    private function evaluateCondition($modelValue, string $operator, $expectedValue): bool
    {
        return match ($operator) {
            '=' => $modelValue == $expectedValue,
            '!=' => $modelValue != $expectedValue,
            '>' => $modelValue > $expectedValue,
            '>=' => $modelValue >= $expectedValue,
            '<' => $modelValue < $expectedValue,
            '<=' => $modelValue <= $expectedValue,
            'contains' => str_contains(strtolower($modelValue), strtolower($expectedValue)),
            'starts_with' => str_starts_with(strtolower($modelValue), strtolower($expectedValue)),
            'ends_with' => str_ends_with(strtolower($modelValue), strtolower($expectedValue)),
            'in' => in_array($modelValue, (array) $expectedValue),
            'not_in' => !in_array($modelValue, (array) $expectedValue),
            default => false,
        };
    }

    /**
     * Create approval request for this workflow
     */
    public function createApprovalRequest(Model $model, User $requester, array $data = []): ApprovalRequest
    {
        return ApprovalRequest::create([
            'client_id' => $this->client_id,
            'approval_workflow_id' => $this->id,
            'approvable_type' => get_class($model),
            'approvable_id' => $model->id,
            'requested_by' => $requester->id,
            'request_data' => $data,
            'status' => 'pending',
            'current_step' => 1,
        ]);
    }

    /**
     * Get next approver for given step
     */
    public function getNextApprover(int $stepNumber): ?User
    {
        $step = $this->steps()->where('step_order', $stepNumber)->first();

        if (!$step) {
            return null;
        }

        return $step->getApprover();
    }

    /**
     * Check if workflow is complete
     */
    public function isComplete(int $currentStep): bool
    {
        return $currentStep > $this->steps()->count();
    }

    /**
     * Scope for active workflows
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific model type
     */
    public function scopeForModel($query, string $modelType)
    {
        return $query->where('approvable_type', $modelType);
    }
}
