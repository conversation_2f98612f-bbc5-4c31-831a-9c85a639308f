<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasCreator;

class JournalEntry extends Model
{
    use HasFactory, SoftDeletes, HasCreator;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'journal_id',
        'account_id',
        'debit',
        'credit',
        'description',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'debit' => 'decimal:2',
        'credit' => 'decimal:2',
    ];

    /**
     * Relationship: Journal entry belongs to a journal
     */
    public function journal(): BelongsTo
    {
        return $this->belongsTo(Journal::class);
    }

    /**
     * Relationship: Journal entry belongs to an account
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Relationship: User who created this journal entry
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope: Filter by journal
     */
    public function scopeForJournal(Builder $query, $journalId)
    {
        return $query->where('journal_id', $journalId);
    }

    /**
     * Scope: Filter by account
     */
    public function scopeForAccount(Builder $query, $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    /**
     * Scope: Only debit entries
     */
    public function scopeDebits(Builder $query)
    {
        return $query->where('debit', '>', 0);
    }

    /**
     * Scope: Only credit entries
     */
    public function scopeCredits(Builder $query)
    {
        return $query->where('credit', '>', 0);
    }

    /**
     * Check if this is a debit entry
     */
    public function isDebit(): bool
    {
        return $this->debit > 0;
    }

    /**
     * Check if this is a credit entry
     */
    public function isCredit(): bool
    {
        return $this->credit > 0;
    }

    /**
     * Get the amount (debit or credit)
     */
    public function getAmountAttribute()
    {
        return $this->debit > 0 ? $this->debit : $this->credit;
    }

    /**
     * Get the entry type (debit or credit)
     */
    public function getTypeAttribute()
    {
        return $this->debit > 0 ? 'debit' : 'credit';
    }
}
