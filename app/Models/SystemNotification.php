<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class SystemNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'type',
        'title',
        'message',
        'data',
        'priority',
        'status',
        'user_id',
        'reference_type',
        'reference_id',
        'read_at',
        'dismissed_at',
        'expires_at',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'dismissed_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    // Notification types
    const TYPE_APPROVAL_PENDING = 'approval_pending';
    const TYPE_OVERDUE_PAYMENT = 'overdue_payment';
    const TYPE_LOW_STOCK = 'low_stock';
    const TYPE_BUDGET_VARIANCE = 'budget_variance';
    const TYPE_PERIOD_CLOSING = 'period_closing';
    const TYPE_SYSTEM_ALERT = 'system_alert';

    // Priority levels
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Status
    const STATUS_UNREAD = 'unread';
    const STATUS_READ = 'read';
    const STATUS_DISMISSED = 'dismissed';

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->where('status', self::STATUS_UNREAD);
    }

    public function scopeForUser($query, int $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('user_id', $userId)
                ->orWhereNull('user_id');
        });
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
                ->orWhere('expires_at', '>', now());
        });
    }

    // Helper methods
    public function isUnread(): bool
    {
        return $this->status === self::STATUS_UNREAD;
    }

    public function isRead(): bool
    {
        return $this->status === self::STATUS_READ;
    }

    public function isDismissed(): bool
    {
        return $this->status === self::STATUS_DISMISSED;
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function markAsRead(): void
    {
        $this->update([
            'status' => self::STATUS_READ,
            'read_at' => now(),
        ]);
    }

    public function markAsDismissed(): void
    {
        $this->update([
            'status' => self::STATUS_DISMISSED,
            'dismissed_at' => now(),
        ]);
    }

    public function getPriorityColor(): string
    {
        return match ($this->priority) {
            self::PRIORITY_LOW => 'gray',
            self::PRIORITY_NORMAL => 'blue',
            self::PRIORITY_HIGH => 'orange',
            self::PRIORITY_URGENT => 'red',
            default => 'gray',
        };
    }

    public function getTypeIcon(): string
    {
        return match ($this->type) {
            self::TYPE_APPROVAL_PENDING => 'heroicon-o-clock',
            self::TYPE_OVERDUE_PAYMENT => 'heroicon-o-exclamation-triangle',
            self::TYPE_LOW_STOCK => 'heroicon-o-archive-box-x-mark',
            self::TYPE_BUDGET_VARIANCE => 'heroicon-o-chart-bar',
            self::TYPE_PERIOD_CLOSING => 'heroicon-o-calendar-days',
            self::TYPE_SYSTEM_ALERT => 'heroicon-o-bell',
            default => 'heroicon-o-information-circle',
        };
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_APPROVAL_PENDING => 'Persetujuan Pending',
            self::TYPE_OVERDUE_PAYMENT => 'Pembayaran Terlambat',
            self::TYPE_LOW_STOCK => 'Stok Rendah',
            self::TYPE_BUDGET_VARIANCE => 'Varians Budget',
            self::TYPE_PERIOD_CLOSING => 'Tutup Buku',
            self::TYPE_SYSTEM_ALERT => 'Alert Sistem',
        ];
    }

    public static function getPriorities(): array
    {
        return [
            self::PRIORITY_LOW => 'Rendah',
            self::PRIORITY_NORMAL => 'Normal',
            self::PRIORITY_HIGH => 'Tinggi',
            self::PRIORITY_URGENT => 'Mendesak',
        ];
    }

    public static function getStatuses(): array
    {
        return [
            self::STATUS_UNREAD => 'Belum Dibaca',
            self::STATUS_READ => 'Sudah Dibaca',
            self::STATUS_DISMISSED => 'Diabaikan',
        ];
    }
}
