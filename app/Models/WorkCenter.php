<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkCenter extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'location_id',
        'work_center_code',
        'work_center_name',
        'description',
        'work_center_type', // manual, semi_automatic, automatic
        'capacity_per_hour',
        'capacity_unit_id',
        'labor_rate',
        'machine_rate',
        'overhead_rate',
        'setup_time_default',
        'efficiency_percentage',
        'utilization_percentage',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'capacity_per_hour' => 'decimal:2',
        'labor_rate' => 'decimal:2',
        'machine_rate' => 'decimal:2',
        'overhead_rate' => 'decimal:2',
        'setup_time_default' => 'decimal:2',
        'efficiency_percentage' => 'decimal:2',
        'utilization_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function capacityUnit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'capacity_unit_id');
    }

    public function workOrders(): HasMany
    {
        return $this->hasMany(WorkOrder::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('work_center_type', $type);
    }

    // Accessors
    public function getCurrentUtilizationAttribute(): float
    {
        $activeWorkOrders = $this->workOrders()
            ->where('status', 'in_progress')
            ->count();
            
        return min(100, $activeWorkOrders * 100);
    }

    public function getAvailableCapacityAttribute(): float
    {
        return $this->capacity_per_hour * ($this->utilization_percentage / 100);
    }

    // Helper Methods
    public function isAvailable(): bool
    {
        return $this->is_active && $this->current_utilization < 100;
    }

    public function calculateRunTime(float $quantity): float
    {
        if ($this->capacity_per_hour <= 0) {
            return 0;
        }
        
        $baseTime = $quantity / $this->capacity_per_hour;
        $adjustedTime = $baseTime / ($this->efficiency_percentage / 100);
        
        return $adjustedTime;
    }
}
