<?php

namespace App\Models;

use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rule;
use App\Traits\HasCreator;
use Filament\Models\Contracts\HasCurrentTenantLabel;

class Account extends Model implements HasCurrentTenantLabel
{
    use HasFactory, SoftDeletes, HasCreator, Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'account_code',
        'account_name',
        'account_type',
        'account_category',
        'account_subcategory',
        'normal_balance',
        'currency',
        'parent_account_id',
        'level',
        'sort_order',
        'description',
        'is_active',
        'is_system',
        'is_header',
        'is_cash_account',
        'is_bank_account',
        'is_control_account',
        'tax_code',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'is_header' => 'boolean',
        'is_cash_account' => 'boolean',
        'is_bank_account' => 'boolean',
        'is_control_account' => 'boolean',
        'level' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Account types available in the system
     */
    public static function getAccountTypes(): array
    {
        return [
            'asset' => 'Aset',
            'liability' => 'Kewajiban',
            'equity' => 'Ekuitas',
            'revenue' => 'Pendapatan',
            'expense' => 'Beban',
        ];
    }

    /**
     * Account categories for detailed classification
     */
    public static function getAccountCategories(): array
    {
        return [
            // Assets
            'current_asset' => 'Current Asset',
            'fixed_asset' => 'Fixed Asset',
            'intangible_asset' => 'Intangible Asset',
            'investment' => 'Investment',
            'other_asset' => 'Other Asset',

            // Liabilities
            'current_liability' => 'Current Liability',
            'long_term_liability' => 'Long Term Liability',
            'other_liability' => 'Other Liability',

            // Equity
            'capital' => 'Capital',
            'retained_earnings' => 'Retained Earnings',
            'other_equity' => 'Other Equity',

            // Revenue
            'operating_revenue' => 'Operating Revenue',
            'other_revenue' => 'Other Revenue',

            // Expense
            'cost_of_goods_sold' => 'Cost of Goods Sold',
            'operating_expense' => 'Operating Expense',
            'administrative_expense' => 'Administrative Expense',
            'financial_expense' => 'Financial Expense',
            'other_expense' => 'Other Expense',
        ];
    }

    /**
     * Account subcategories for manufacturing
     */
    public static function getAccountSubcategories(): array
    {
        return [
            // Current Assets
            'cash_and_equivalents' => 'Cash and Cash Equivalents',
            'accounts_receivable' => 'Accounts Receivable',
            'inventory_raw_materials' => 'Inventory - Raw Materials',
            'inventory_wip' => 'Inventory - Work in Process',
            'inventory_finished_goods' => 'Inventory - Finished Goods',
            'prepaid_expenses' => 'Prepaid Expenses',

            // Fixed Assets
            'land' => 'Land',
            'buildings' => 'Buildings',
            'machinery_equipment' => 'Machinery & Equipment',
            'vehicles' => 'Vehicles',
            'furniture_fixtures' => 'Furniture & Fixtures',
            'accumulated_depreciation' => 'Accumulated Depreciation',

            // Current Liabilities
            'accounts_payable' => 'Accounts Payable',
            'accrued_expenses' => 'Accrued Expenses',
            'short_term_debt' => 'Short Term Debt',
            'taxes_payable' => 'Taxes Payable',

            // COGS
            'material_costs' => 'Material Costs',
            'labor_costs' => 'Labor Costs',
            'manufacturing_overhead' => 'Manufacturing Overhead',

            // Operating Expenses
            'selling_expenses' => 'Selling Expenses',
            'general_admin_expenses' => 'General & Administrative Expenses',
        ];
    }

    /**
     * Normal balance types
     */
    public static function getNormalBalanceTypes(): array
    {
        return [
            'debit' => 'Debit',
            'credit' => 'Kredit',
        ];
    }

    /**
     * Validation rules for account_type
     */
    public static function getAccountTypeValidationRule()
    {
        return Rule::in(array_keys(self::getAccountTypes()));
    }

    /**
     * Validation rules for normal_balance
     */
    public static function getNormalBalanceValidationRule()
    {
        return Rule::in(array_keys(self::getNormalBalanceTypes()));
    }

    /**
     * Relationship: Account belongs to a client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relationship: Account belongs to a parent account
     */
    public function parentAccount(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'parent_account_id');
    }

    /**
     * Relationship: Account has many child accounts
     */
    public function childAccounts(): HasMany
    {
        return $this->hasMany(Account::class, 'parent_account_id');
    }

    /**
     * Relationship: Account has many journal entries
     */
    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class);
    }

    /**
     * Relationship: User who created this account
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship: Account currency
     */
    public function currencyModel(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency', 'code');
    }

    /**
     * Scope: Filter by client
     */
    public function scopeForClient(Builder $query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope: Only active accounts
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Filter by account type
     */
    public function scopeOfType(Builder $query, string $type)
    {
        return $query->where('account_type', $type);
    }

    /**
     * Get the current tenant label for Filament
     */
    public function getCurrentTenantLabel(): string
    {
        return $this->client?->name ?? 'No Client';
    }

    // Additional Relationships
    public function bankAccounts(): HasMany
    {
        return $this->hasMany(BankAccount::class);
    }

    public function cashFlowMappings(): HasMany
    {
        return $this->hasMany(CashFlowMapping::class);
    }

    // Enhanced Scopes
    public function scopeHeader(Builder $query)
    {
        return $query->where('is_header', true);
    }

    public function scopeDetail(Builder $query)
    {
        return $query->where('is_header', false);
    }

    public function scopeByCategory(Builder $query, string $category)
    {
        return $query->where('account_category', $category);
    }

    public function scopeBySubcategory(Builder $query, string $subcategory)
    {
        return $query->where('account_subcategory', $subcategory);
    }

    public function scopeCashAccounts(Builder $query)
    {
        return $query->where('is_cash_account', true);
    }

    public function scopeBankAccounts(Builder $query)
    {
        return $query->where('is_bank_account', true);
    }

    public function scopeByLevel(Builder $query, int $level)
    {
        return $query->where('level', $level);
    }

    // Accessors
    public function getFullCodeAttribute(): string
    {
        $codes = collect([$this->account_code]);
        $parent = $this->parentAccount;

        while ($parent) {
            $codes->prepend($parent->account_code);
            $parent = $parent->parentAccount;
        }

        return $codes->implode('.');
    }

    public function getFullNameAttribute(): string
    {
        $names = collect([$this->account_name]);
        $parent = $this->parentAccount;

        while ($parent) {
            $names->prepend($parent->account_name);
            $parent = $parent->parentAccount;
        }

        return $names->implode(' > ');
    }

    public function getIndentedNameAttribute(): string
    {
        return str_repeat('  ', $this->level) . $this->account_name;
    }

    // Helper Methods
    public function getAllChildren()
    {
        $children = collect();

        foreach ($this->childAccounts as $child) {
            $children->push($child);
            $children = $children->merge($child->getAllChildren());
        }

        return $children;
    }

    public function getBalance($asOfDate = null): float
    {
        $query = $this->journalEntries();

        if ($asOfDate) {
            $query->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('journal_date', '<=', $asOfDate);
            });
        }

        $debits = $query->sum('debit');
        $credits = $query->sum('credit');

        // Calculate balance based on normal balance
        if ($this->normal_balance === 'debit') {
            return $debits - $credits;
        } else {
            return $credits - $debits;
        }
    }

    public function getBalanceWithChildren($asOfDate = null): float
    {
        $balance = $this->getBalance($asOfDate);

        foreach ($this->childAccounts as $child) {
            $balance += $child->getBalanceWithChildren($asOfDate);
        }

        return $balance;
    }

    public function getCashFlowAmount($startDate, $endDate): float
    {
        $query = $this->journalEntries()
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->whereBetween('journal_date', [$startDate, $endDate]);
            });

        $debits = $query->sum('debit');
        $credits = $query->sum('credit');

        // For cash flow, we want the net change
        return $debits - $credits;
    }

    public function updateLevel(): void
    {
        $level = 0;
        $parent = $this->parentAccount;

        while ($parent) {
            $level++;
            $parent = $parent->parentAccount;
        }

        $this->update(['level' => $level]);

        // Update children levels
        foreach ($this->childAccounts as $child) {
            $child->updateLevel();
        }
    }

    public static function generateAccountCode(int $clientId, string $accountType, ?int $parentId = null): string
    {
        $typePrefix = match ($accountType) {
            'asset' => '1',
            'liability' => '2',
            'equity' => '3',
            'revenue' => '4',
            'expense' => '5',
            default => '9'
        };

        if ($parentId) {
            $parent = self::find($parentId);
            $siblingCount = $parent->childAccounts()->count();
            return $parent->account_code . str_pad($siblingCount + 1, 2, '0', STR_PAD_LEFT);
        }

        $rootCount = self::where('client_id', $clientId)
            ->whereNull('parent_account_id')
            ->where('account_type', $accountType)
            ->count();

        return $typePrefix . str_pad($rootCount + 1, 3, '0', STR_PAD_LEFT);
    }
}
