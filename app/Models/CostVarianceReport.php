<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CostVarianceReport extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'production_order_id',
        'product_id',
        'report_period',
        'report_date',
        'total_standard_cost',
        'total_actual_cost',
        'total_variance',
        'material_variance',
        'labor_variance',
        'overhead_variance',
        'variance_percentage',
        'performance_rating',
        'significant_variances_count',
        'recommendations',
        'action_items',
        'status', // draft, reviewed, approved
        'reviewed_by',
        'reviewed_at',
        'approved_by',
        'approved_at',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'report_date' => 'date',
        'total_standard_cost' => 'decimal:2',
        'total_actual_cost' => 'decimal:2',
        'total_variance' => 'decimal:2',
        'material_variance' => 'decimal:2',
        'labor_variance' => 'decimal:2',
        'overhead_variance' => 'decimal:2',
        'variance_percentage' => 'decimal:4',
        'significant_variances_count' => 'integer',
        'recommendations' => 'json',
        'action_items' => 'json',
        'reviewed_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPeriod($query, string $period)
    {
        return $query->where('report_period', $period);
    }

    public function scopeSignificantVariances($query, float $threshold = 5.0)
    {
        return $query->where('variance_percentage', '>=', $threshold);
    }

    public function scopeByPerformance($query, string $rating)
    {
        return $query->where('performance_rating', $rating);
    }

    // Accessors
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'reviewed' => 'blue',
            'approved' => 'green',
            default => 'gray'
        };
    }

    public function getVarianceTypeAttribute(): string
    {
        return $this->total_variance >= 0 ? 'unfavorable' : 'favorable';
    }

    public function getVarianceColorAttribute(): string
    {
        return $this->total_variance >= 0 ? 'red' : 'green';
    }

    public function getPerformanceColorAttribute(): string
    {
        return match($this->performance_rating) {
            'Excellent' => 'green',
            'Good' => 'blue',
            'Fair' => 'yellow',
            'Poor' => 'red',
            default => 'gray'
        };
    }

    // Helper Methods
    public function review(int $reviewedBy, ?string $notes = null): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->update([
            'status' => 'reviewed',
            'reviewed_by' => $reviewedBy,
            'reviewed_at' => now(),
            'notes' => $notes ? ($this->notes . "\n" . $notes) : $this->notes,
        ]);

        return true;
    }

    public function approve(int $approvedBy, ?string $notes = null): bool
    {
        if ($this->status !== 'reviewed') {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
            'notes' => $notes ? ($this->notes . "\n" . $notes) : $this->notes,
        ]);

        return true;
    }

    public function getVarianceBreakdown(): array
    {
        return [
            'material' => [
                'amount' => $this->material_variance,
                'percentage' => $this->total_standard_cost > 0 
                    ? ($this->material_variance / $this->total_standard_cost) * 100 
                    : 0,
                'type' => $this->material_variance >= 0 ? 'unfavorable' : 'favorable',
            ],
            'labor' => [
                'amount' => $this->labor_variance,
                'percentage' => $this->total_standard_cost > 0 
                    ? ($this->labor_variance / $this->total_standard_cost) * 100 
                    : 0,
                'type' => $this->labor_variance >= 0 ? 'unfavorable' : 'favorable',
            ],
            'overhead' => [
                'amount' => $this->overhead_variance,
                'percentage' => $this->total_standard_cost > 0 
                    ? ($this->overhead_variance / $this->total_standard_cost) * 100 
                    : 0,
                'type' => $this->overhead_variance >= 0 ? 'unfavorable' : 'favorable',
            ],
        ];
    }

    public function getHighPriorityActions(): array
    {
        $actionItems = $this->action_items ?? [];
        
        return array_filter($actionItems, function ($item) {
            return isset($item['priority']) && $item['priority'] === 'high';
        });
    }

    public function isSignificantVariance(float $threshold = 5.0): bool
    {
        return abs($this->variance_percentage) >= $threshold;
    }

    public function requiresManagementAttention(): bool
    {
        return $this->isSignificantVariance(10.0) || 
               $this->performance_rating === 'Poor' ||
               count($this->getHighPriorityActions()) > 0;
    }

    public function generateExecutiveSummary(): array
    {
        return [
            'production_order' => $this->productionOrder->order_number ?? 'N/A',
            'product' => $this->product->product_name ?? 'N/A',
            'period' => $this->report_period,
            'total_variance' => $this->total_variance,
            'variance_percentage' => $this->variance_percentage,
            'performance_rating' => $this->performance_rating,
            'key_issues' => $this->getKeyIssues(),
            'recommendations' => array_slice($this->recommendations ?? [], 0, 3), // Top 3
            'requires_attention' => $this->requiresManagementAttention(),
        ];
    }

    private function getKeyIssues(): array
    {
        $issues = [];
        $breakdown = $this->getVarianceBreakdown();
        
        foreach ($breakdown as $type => $data) {
            if (abs($data['percentage']) >= 5) {
                $issues[] = [
                    'type' => ucfirst($type),
                    'variance' => $data['amount'],
                    'percentage' => $data['percentage'],
                    'severity' => abs($data['percentage']) >= 10 ? 'high' : 'medium',
                ];
            }
        }
        
        return $issues;
    }

    public static function generateReport(ProductionOrder $productionOrder, array $varianceData): self
    {
        return self::create([
            'client_id' => $productionOrder->client_id,
            'production_order_id' => $productionOrder->id,
            'product_id' => $productionOrder->product_id,
            'report_period' => now()->format('Y-m'),
            'report_date' => now(),
            'total_standard_cost' => $varianceData['total_variance_summary']['total_standard_cost'],
            'total_actual_cost' => $varianceData['total_variance_summary']['total_actual_cost'],
            'total_variance' => $varianceData['total_variance_summary']['total_variance'],
            'material_variance' => collect($varianceData['material_variances'])->sum('total_variance'),
            'labor_variance' => collect($varianceData['labor_variances'])->sum('total_variance'),
            'overhead_variance' => ($varianceData['overhead_variances']['variable_overhead']['spending_variance'] ?? 0) +
                                 ($varianceData['overhead_variances']['fixed_overhead']['total_variance'] ?? 0),
            'variance_percentage' => $varianceData['total_variance_summary']['variance_percentage'],
            'performance_rating' => $varianceData['total_variance_summary']['cost_performance'],
            'significant_variances_count' => $varianceData['variance_analysis']['significant_variances_count'],
            'recommendations' => $varianceData['variance_analysis']['recommendations'],
            'action_items' => $varianceData['variance_analysis']['action_items'],
            'status' => 'draft',
            'created_by' => auth()->id() ?? 1,
        ]);
    }
}
