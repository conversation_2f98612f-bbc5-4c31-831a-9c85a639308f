<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Inventory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'product_id',
        'location_id',
        'current_stock',
        'reserved_stock',
        'available_stock',
        'committed_stock',
        'on_order_stock',
        'average_cost',
        'last_cost',
        'standard_cost',
        'fifo_cost',
        'lifo_cost',
        'total_value',
        'valuation_method', // fifo, lifo, average, standard
        'last_movement_date',
        'last_count_date',
        'cycle_count_due_date',
        'abc_classification', // A, B, C, D
        'safety_stock',
        'max_stock',
        'reorder_point',
    ];

    protected $casts = [
        'current_stock' => 'decimal:6',
        'reserved_stock' => 'decimal:6',
        'available_stock' => 'decimal:6',
        'committed_stock' => 'decimal:6',
        'on_order_stock' => 'decimal:6',
        'average_cost' => 'decimal:4',
        'last_cost' => 'decimal:4',
        'standard_cost' => 'decimal:4',
        'fifo_cost' => 'decimal:4',
        'lifo_cost' => 'decimal:4',
        'total_value' => 'decimal:2',
        'safety_stock' => 'decimal:6',
        'max_stock' => 'decimal:6',
        'reorder_point' => 'decimal:6',
        'last_movement_date' => 'datetime',
        'last_count_date' => 'datetime',
        'cycle_count_due_date' => 'date',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    public function stockCounts(): HasMany
    {
        return $this->hasMany(StockCount::class);
    }

    // Scopes
    public function scopeLowStock($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->whereColumn('inventories.current_stock', '<=', 'products.reorder_point');
        });
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('current_stock', '<=', 0);
    }

    public function scopeOverStock($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->whereColumn('inventories.current_stock', '>', 'products.maximum_stock');
        });
    }

    public function scopeCycleCountDue($query)
    {
        return $query->where('cycle_count_due_date', '<=', now()->toDateString());
    }

    // Accessors
    public function getStockStatusAttribute(): string
    {
        if ($this->current_stock <= 0) {
            return 'out_of_stock';
        }

        if ($this->current_stock <= $this->product->reorder_point) {
            return 'low_stock';
        }

        if ($this->current_stock > $this->product->maximum_stock) {
            return 'over_stock';
        }

        return 'normal';
    }

    public function getTurnoverRateAttribute(): float
    {
        // Calculate inventory turnover rate (annual)
        $yearlyUsage = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('created_at', '>=', now()->subYear())
            ->sum('quantity');

        if ($this->average_cost <= 0) {
            return 0;
        }

        return $yearlyUsage / $this->current_stock;
    }

    public function getDaysOnHandAttribute(): float
    {
        $turnoverRate = $this->turnover_rate;

        if ($turnoverRate <= 0) {
            return 999; // Infinite days if no movement
        }

        return 365 / $turnoverRate;
    }

    // Helper Methods
    public function updateStock(float $quantity, string $movementType, ?float $unitCost = null): void
    {
        $oldStock = $this->current_stock;

        if ($movementType === 'in') {
            $this->current_stock += $quantity;

            // Update average cost using weighted average
            if ($unitCost && $quantity > 0) {
                $totalValue = ($oldStock * $this->average_cost) + ($quantity * $unitCost);
                $this->average_cost = $totalValue / $this->current_stock;
                $this->last_cost = $unitCost;
            }
        } else {
            $this->current_stock -= $quantity;
        }

        // Ensure stock doesn't go negative
        $this->current_stock = max(0, $this->current_stock);

        // Update available stock
        $this->available_stock = $this->current_stock - $this->reserved_stock;

        // Update total value
        $this->total_value = $this->current_stock * $this->average_cost;

        // Update last movement date
        $this->last_movement_date = now();

        $this->save();
    }

    public function reserveStock(float $quantity): bool
    {
        if ($this->available_stock < $quantity) {
            return false;
        }

        $this->reserved_stock += $quantity;
        $this->available_stock -= $quantity;
        $this->save();

        return true;
    }

    public function releaseReservedStock(float $quantity): void
    {
        $releaseQuantity = min($quantity, $this->reserved_stock);

        $this->reserved_stock -= $releaseQuantity;
        $this->available_stock += $releaseQuantity;
        $this->save();
    }

    public function isStockAvailable(float $requiredQuantity): bool
    {
        return $this->available_stock >= $requiredQuantity;
    }

    // Advanced Valuation Methods
    public function calculateFIFOCost(): float
    {
        // Get stock movements ordered by date (oldest first)
        $movements = $this->stockMovements()
            ->where('movement_type', 'in')
            ->where('quantity', '>', 0)
            ->orderBy('movement_date', 'asc')
            ->get();

        $remainingStock = $this->current_stock;
        $totalValue = 0;

        foreach ($movements as $movement) {
            if ($remainingStock <= 0) break;

            $quantityToUse = min($remainingStock, $movement->quantity);
            $totalValue += $quantityToUse * $movement->unit_cost;
            $remainingStock -= $quantityToUse;
        }

        return $this->current_stock > 0 ? $totalValue / $this->current_stock : 0;
    }

    public function calculateLIFOCost(): float
    {
        // Get stock movements ordered by date (newest first)
        $movements = $this->stockMovements()
            ->where('movement_type', 'in')
            ->where('quantity', '>', 0)
            ->orderBy('movement_date', 'desc')
            ->get();

        $remainingStock = $this->current_stock;
        $totalValue = 0;

        foreach ($movements as $movement) {
            if ($remainingStock <= 0) break;

            $quantityToUse = min($remainingStock, $movement->quantity);
            $totalValue += $quantityToUse * $movement->unit_cost;
            $remainingStock -= $quantityToUse;
        }

        return $this->current_stock > 0 ? $totalValue / $this->current_stock : 0;
    }

    public function calculateWeightedAverageCost(): float
    {
        $movements = $this->stockMovements()
            ->where('movement_type', 'in')
            ->where('quantity', '>', 0)
            ->get();

        $totalQuantity = 0;
        $totalValue = 0;

        foreach ($movements as $movement) {
            $totalQuantity += $movement->quantity;
            $totalValue += $movement->quantity * $movement->unit_cost;
        }

        return $totalQuantity > 0 ? $totalValue / $totalQuantity : 0;
    }

    public function updateValuationCosts(): void
    {
        $this->fifo_cost = $this->calculateFIFOCost();
        $this->lifo_cost = $this->calculateLIFOCost();
        $this->average_cost = $this->calculateWeightedAverageCost();
        $this->standard_cost = $this->product->standard_cost ?? 0;

        // Update total value based on selected valuation method
        $this->total_value = $this->current_stock * $this->getValuationCost();

        $this->save();
    }

    public function getValuationCost(): float
    {
        return match ($this->valuation_method) {
            'fifo' => $this->fifo_cost,
            'lifo' => $this->lifo_cost,
            'average' => $this->average_cost,
            'standard' => $this->standard_cost,
            default => $this->average_cost
        };
    }

    public function getABCClassification(): string
    {
        if ($this->abc_classification) {
            return $this->abc_classification;
        }

        // Calculate ABC based on annual value
        $annualUsage = $this->getAnnualUsage();
        $annualValue = $annualUsage * $this->getValuationCost();

        // This would typically be calculated across all products
        // For now, use simple thresholds
        if ($annualValue >= 1000000) return 'A';
        if ($annualValue >= 100000) return 'B';
        if ($annualValue >= 10000) return 'C';
        return 'D';
    }

    public function getAnnualUsage(): float
    {
        $startDate = now()->subYear();

        return $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('movement_date', '>=', $startDate)
            ->sum('quantity');
    }

    public function getReorderSuggestion(): array
    {
        $averageUsage = $this->getAverageMonthlyUsage();
        $leadTime = $this->product->lead_time_days ?? 7;
        $safetyStock = $this->safety_stock ?? 0;

        $suggestedReorderPoint = ($averageUsage / 30 * $leadTime) + $safetyStock;
        $suggestedOrderQuantity = $this->max_stock - $this->current_stock;

        return [
            'current_stock' => $this->current_stock,
            'safety_stock' => $safetyStock,
            'average_monthly_usage' => $averageUsage,
            'suggested_reorder_point' => $suggestedReorderPoint,
            'suggested_order_quantity' => max(0, $suggestedOrderQuantity),
            'needs_reorder' => $this->current_stock <= $suggestedReorderPoint,
            'abc_classification' => $this->getABCClassification(),
            'valuation_cost' => $this->getValuationCost(),
            'total_value' => $this->total_value,
        ];
    }

    private function getAverageMonthlyUsage(): float
    {
        $months = 12;
        $startDate = now()->subMonths($months);

        $totalUsage = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('movement_date', '>=', $startDate)
            ->sum('quantity');

        return $totalUsage / $months;
    }
}
