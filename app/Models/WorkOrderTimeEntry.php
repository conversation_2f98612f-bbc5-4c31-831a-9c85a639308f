<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkOrderTimeEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'work_order_id',
        'employee_id',
        'entry_type', // setup, run, downtime, maintenance
        'start_time',
        'end_time',
        'duration_minutes',
        'quantity_produced',
        'quantity_scrapped',
        'hourly_rate',
        'total_cost',
        'notes',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'duration_minutes' => 'decimal:2',
        'quantity_produced' => 'decimal:2',
        'quantity_scrapped' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'employee_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByEntryType($query, string $type)
    {
        return $query->where('entry_type', $type);
    }

    public function scopeByEmployee($query, int $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeApproved($query)
    {
        return $query->whereNotNull('approved_at');
    }

    public function scopePending($query)
    {
        return $query->whereNull('approved_at');
    }

    // Accessors
    public function getDurationHoursAttribute(): float
    {
        return $this->duration_minutes / 60;
    }

    public function getProductionRateAttribute(): float
    {
        if ($this->duration_hours <= 0) {
            return 0;
        }
        
        return $this->quantity_produced / $this->duration_hours;
    }

    public function getEfficiencyPercentageAttribute(): float
    {
        $workOrder = $this->workOrder;
        $standardRate = $workOrder->workCenter->capacity_per_hour ?? 0;
        
        if ($standardRate <= 0) {
            return 0;
        }
        
        return ($this->production_rate / $standardRate) * 100;
    }

    // Helper Methods
    public function calculateDuration(): void
    {
        if ($this->start_time && $this->end_time) {
            $this->duration_minutes = $this->start_time->diffInMinutes($this->end_time);
            $this->save();
        }
    }

    public function calculateCost(): void
    {
        $this->total_cost = $this->duration_hours * $this->hourly_rate;
        $this->save();
    }

    public function approve(int $approvedBy): void
    {
        $this->update([
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        // Update work order actual times
        $this->updateWorkOrderTimes();
    }

    private function updateWorkOrderTimes(): void
    {
        $workOrder = $this->workOrder;
        
        // Recalculate actual times from all approved time entries
        $timeEntries = $workOrder->timeEntries()->approved();
        
        $setupTime = $timeEntries->where('entry_type', 'setup')->sum('duration_minutes');
        $runTime = $timeEntries->where('entry_type', 'run')->sum('duration_minutes');
        
        $workOrder->update([
            'setup_time_actual' => $setupTime,
            'run_time_actual' => $runTime,
            'labor_hours_actual' => ($setupTime + $runTime) / 60,
        ]);

        // Recalculate costs
        $workOrder->calculateCosts();
    }
}
