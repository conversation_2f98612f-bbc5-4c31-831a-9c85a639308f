<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransactionItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'transaction_id',
        'product_id',
        'description',
        'quantity',
        'unit_price',
        'line_total',
        'unit_id',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:6',
        'unit_price' => 'decimal:4',
        'line_total' => 'decimal:2',
    ];

    /**
     * Relationship: Transaction item belongs to transaction
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Relationship: Transaction item belongs to client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relationship: Transaction item belongs to product
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Relationship: Transaction item belongs to unit of measure
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    /**
     * Calculate line total based on quantity and unit price
     */
    public function calculateLineTotal(): void
    {
        $this->line_total = $this->quantity * $this->unit_price;
        $this->save();
    }

    /**
     * Boot method to auto-calculate line total
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->line_total = $item->quantity * $item->unit_price;
        });
    }
}
