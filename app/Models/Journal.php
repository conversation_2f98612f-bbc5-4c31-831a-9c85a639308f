<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasCreator;
use Filament\Models\Contracts\HasCurrentTenantLabel;

class Journal extends Model implements HasCurrentTenantLabel
{
    use HasFactory, SoftDeletes, HasCreator;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'journal_number',
        'journal_date',
        'reference_type',
        'reference_id',
        'reference_number',
        'description',
        'total_amount',
        'is_posted',
        'posted_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'journal_date' => 'date',
        'total_amount' => 'decimal:2',
        'is_posted' => 'boolean',
        'posted_at' => 'datetime',
    ];

    /**
     * Relationship: Journal belongs to a client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relationship: Journal belongs to a reference transaction
     */
    public function referenceTransaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class, 'reference_transaction_id');
    }

    /**
     * Relationship: Journal has many journal entries
     */
    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class);
    }

    /**
     * Alias for journalEntries relationship
     */
    public function entries(): HasMany
    {
        return $this->journalEntries();
    }

    /**
     * Relationship: User who created this journal
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope: Filter by client
     */
    public function scopeForClient(Builder $query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope: Posted journals
     */
    public function scopePosted(Builder $query)
    {
        return $query->where('is_posted', true);
    }

    /**
     * Scope: Unposted journals
     */
    public function scopeUnposted(Builder $query)
    {
        return $query->where('is_posted', false);
    }

    /**
     * Scope: Filter by date range
     */
    public function scopeDateRange(Builder $query, $startDate, $endDate)
    {
        return $query->whereBetween('journal_date', [$startDate, $endDate]);
    }

    /**
     * Check if journal is posted
     */
    public function isPosted(): bool
    {
        return $this->is_posted;
    }

    /**
     * Get total debit amount for this journal
     */
    public function getTotalDebitAttribute()
    {
        return $this->journalEntries()->sum('debit');
    }

    /**
     * Get total credit amount for this journal
     */
    public function getTotalCreditAttribute()
    {
        return $this->journalEntries()->sum('credit');
    }

    /**
     * Check if journal is balanced (debit = credit)
     */
    public function isBalanced(): bool
    {
        return $this->getTotalDebitAttribute() == $this->getTotalCreditAttribute();
    }

    /**
     * Get the current tenant label for Filament
     */
    public function getCurrentTenantLabel(): string
    {
        return $this->client?->name ?? 'No Client';
    }
}
