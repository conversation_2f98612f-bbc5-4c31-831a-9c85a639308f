<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillOfMaterial extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'product_id',
        'bom_code',
        'version',
        'description',
        'quantity_produced',
        'labor_cost_per_unit',
        'overhead_cost_per_unit',
        'setup_time_minutes',
        'production_time_minutes',
        'scrap_percentage',
        'yield_percentage',
        'batch_size',
        'routing_sequence',
        'work_center_id',
        'bom_type', // standard, phantom, template
        'costing_method', // standard, average, fifo, lifo
        'is_active',
        'effective_date',
        'expiry_date',
        'approved_by',
        'approved_at',
        'notes',
    ];

    protected $casts = [
        'quantity_produced' => 'decimal:2',
        'labor_cost_per_unit' => 'decimal:2',
        'overhead_cost_per_unit' => 'decimal:2',
        'setup_time_minutes' => 'integer',
        'production_time_minutes' => 'integer',
        'scrap_percentage' => 'decimal:4',
        'yield_percentage' => 'decimal:4',
        'batch_size' => 'decimal:2',
        'routing_sequence' => 'integer',
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'expiry_date' => 'date',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(BillOfMaterialItem::class, 'bom_id');
    }

    public function workCenter(): BelongsTo
    {
        return $this->belongsTo(WorkCenter::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function workOrders(): HasMany
    {
        return $this->hasMany(WorkOrder::class, 'bom_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        $today = now()->toDateString();
        return $query->where('effective_date', '<=', $today)
            ->where(function ($q) use ($today) {
                $q->whereNull('expiry_date')
                    ->orWhere('expiry_date', '>=', $today);
            });
    }

    // Accessors
    public function getTotalMaterialCostAttribute(): float
    {
        return $this->items->sum(function ($item) {
            return $item->quantity * $item->material->standard_cost;
        });
    }

    public function getTotalLaborCostAttribute(): float
    {
        return $this->labor_cost_per_unit * $this->quantity_produced;
    }

    public function getTotalOverheadCostAttribute(): float
    {
        return $this->overhead_cost_per_unit * $this->quantity_produced;
    }

    public function getTotalCostAttribute(): float
    {
        return $this->total_material_cost + $this->total_labor_cost + $this->total_overhead_cost;
    }

    public function getCostPerUnitAttribute(): float
    {
        if ($this->quantity_produced <= 0) {
            return 0;
        }

        return $this->total_cost / $this->quantity_produced;
    }

    public function getTotalTimeMinutesAttribute(): int
    {
        return $this->setup_time_minutes + $this->production_time_minutes;
    }

    // Helper Methods
    public function calculateMaterialRequirements(float $productionQuantity): array
    {
        $requirements = [];

        foreach ($this->items as $item) {
            $requiredQuantity = ($item->quantity / $this->quantity_produced) * $productionQuantity;

            $requirements[] = [
                'material_id' => $item->material_id,
                'material_name' => $item->material->product_name,
                'required_quantity' => $requiredQuantity,
                'unit' => $item->unit->unit_name,
                'cost' => $requiredQuantity * $item->material->standard_cost,
            ];
        }

        return $requirements;
    }

    public function isEffective(): bool
    {
        $today = now()->toDate();

        if ($this->effective_date > $today) {
            return false;
        }

        if ($this->expiry_date && $this->expiry_date < $today) {
            return false;
        }

        return $this->is_active;
    }

    public function clone(?string $newVersion = null): self
    {
        $newBom = $this->replicate();
        $newBom->version = $newVersion ?? ($this->version + 1);
        $newBom->is_active = false;
        $newBom->effective_date = null;
        $newBom->save();

        // Clone BOM items
        foreach ($this->items as $item) {
            $newItem = $item->replicate();
            $newItem->bom_id = $newBom->id;
            $newItem->save();
        }

        return $newBom;
    }

    public function calculateTotalMaterialCost(): float
    {
        $totalCost = 0;

        foreach ($this->items as $item) {
            $materialCost = $item->material->standard_cost * $item->quantity;
            $totalCost += $materialCost;
        }

        // Apply yield percentage
        if ($this->yield_percentage > 0) {
            $totalCost = $totalCost / ($this->yield_percentage / 100);
        }

        return $totalCost;
    }

    public function calculateTotalCost(): float
    {
        $materialCost = $this->calculateTotalMaterialCost();
        $laborCost = $this->labor_cost_per_unit * $this->quantity_produced;
        $overheadCost = $this->overhead_cost_per_unit * $this->quantity_produced;

        return $materialCost + $laborCost + $overheadCost;
    }

    public function calculateUnitCost(): float
    {
        if ($this->quantity_produced <= 0) {
            return 0;
        }

        return $this->calculateTotalCost() / $this->quantity_produced;
    }

    public function calculateScrapCost(): float
    {
        $materialCost = $this->calculateTotalMaterialCost();
        return $materialCost * ($this->scrap_percentage / 100);
    }

    public function getRequiredQuantity(float $productionQuantity): array
    {
        $requirements = [];
        $multiplier = $productionQuantity / $this->quantity_produced;

        foreach ($this->items as $item) {
            $requiredQty = $item->quantity * $multiplier;

            // Apply scrap percentage
            if ($this->scrap_percentage > 0) {
                $requiredQty *= (1 + ($this->scrap_percentage / 100));
            }

            $requirements[] = [
                'material_id' => $item->material_id,
                'material_name' => $item->material->product_name,
                'required_quantity' => $requiredQty,
                'unit' => $item->material->unit->unit_name,
                'unit_cost' => $item->material->standard_cost,
                'total_cost' => $requiredQty * $item->material->standard_cost,
            ];
        }

        return $requirements;
    }

    public function getProductionTime(float $quantity): array
    {
        $batches = ceil($quantity / $this->batch_size);
        $setupTime = $this->setup_time_minutes * $batches;
        $productionTime = ($this->production_time_minutes * $quantity) / $this->quantity_produced;

        return [
            'batches' => $batches,
            'setup_time_minutes' => $setupTime,
            'production_time_minutes' => $productionTime,
            'total_time_minutes' => $setupTime + $productionTime,
            'total_time_hours' => ($setupTime + $productionTime) / 60,
        ];
    }

    public function approve(int $approvedBy): bool
    {
        if ($this->approved_at) {
            return false; // Already approved
        }

        $this->update([
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        return true;
    }

    public function activate(): bool
    {
        if (!$this->approved_at) {
            return false; // Must be approved first
        }

        // Deactivate other versions
        self::where('client_id', $this->client_id)
            ->where('product_id', $this->product_id)
            ->where('id', '!=', $this->id)
            ->update(['is_active' => false]);

        $this->update([
            'is_active' => true,
            'effective_date' => now(),
        ]);

        return true;
    }

    public static function generateBOMCode(int $clientId, int $productId): string
    {
        $product = Product::find($productId);
        $prefix = 'BOM-' . ($product ? $product->product_code : 'PRD');

        $lastBom = self::where('client_id', $clientId)
            ->where('product_id', $productId)
            ->orderBy('version', 'desc')
            ->first();

        $version = $lastBom ? $lastBom->version + 1 : 1;

        return $prefix . '-V' . str_pad($version, 2, '0', STR_PAD_LEFT);
    }
}
