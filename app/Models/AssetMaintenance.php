<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetMaintenance extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'fixed_asset_id',
        'maintenance_type', // preventive, corrective, emergency, upgrade
        'maintenance_category', // routine, major, overhaul
        'scheduled_date',
        'started_date',
        'completed_date',
        'status', // scheduled, in_progress, completed, cancelled, postponed
        'priority', // low, normal, high, critical
        'description',
        'work_performed',
        'parts_used',
        'labor_hours',
        'labor_cost',
        'parts_cost',
        'external_cost',
        'total_cost',
        'supplier_id',
        'technician_id',
        'supervisor_id',
        'downtime_hours',
        'next_maintenance_date',
        'maintenance_interval_days',
        'warranty_impact',
        'safety_impact',
        'performance_impact',
        'notes',
        'created_by',
        'completed_by',
    ];

    protected $casts = [
        'scheduled_date' => 'datetime',
        'started_date' => 'datetime',
        'completed_date' => 'datetime',
        'labor_hours' => 'decimal:2',
        'labor_cost' => 'decimal:2',
        'parts_cost' => 'decimal:2',
        'external_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'downtime_hours' => 'decimal:2',
        'next_maintenance_date' => 'date',
        'maintenance_interval_days' => 'integer',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function fixedAsset(): BelongsTo
    {
        return $this->belongsTo(FixedAsset::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function technician(): BelongsTo
    {
        return $this->belongsTo(User::class, 'technician_id');
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function completedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'completed_by');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('maintenance_type', $type);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'scheduled')
                    ->where('scheduled_date', '<', now());
    }

    public function scopeDueThisWeek($query)
    {
        return $query->where('status', 'scheduled')
                    ->whereBetween('scheduled_date', [now(), now()->addWeek()]);
    }

    // Accessors
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'scheduled' => 'blue',
            'in_progress' => 'yellow',
            'completed' => 'green',
            'cancelled' => 'red',
            'postponed' => 'orange',
            default => 'gray'
        };
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'gray',
            'normal' => 'blue',
            'high' => 'yellow',
            'critical' => 'red',
            default => 'blue'
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_date < now();
    }

    public function getDaysOverdueAttribute(): ?int
    {
        if (!$this->is_overdue) {
            return null;
        }
        
        return now()->diffInDays($this->scheduled_date);
    }

    public function getDurationHoursAttribute(): ?float
    {
        if (!$this->started_date || !$this->completed_date) {
            return null;
        }
        
        return $this->started_date->diffInHours($this->completed_date);
    }

    // Helper Methods
    public function start(?int $technicianId = null): bool
    {
        if ($this->status !== 'scheduled') {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'started_date' => now(),
            'technician_id' => $technicianId ?? $this->technician_id,
        ]);

        return true;
    }

    public function complete(array $completionData): bool
    {
        if ($this->status !== 'in_progress') {
            return false;
        }

        $totalCost = ($completionData['labor_cost'] ?? 0) + 
                    ($completionData['parts_cost'] ?? 0) + 
                    ($completionData['external_cost'] ?? 0);

        $this->update([
            'status' => 'completed',
            'completed_date' => now(),
            'work_performed' => $completionData['work_performed'] ?? null,
            'parts_used' => $completionData['parts_used'] ?? null,
            'labor_hours' => $completionData['labor_hours'] ?? 0,
            'labor_cost' => $completionData['labor_cost'] ?? 0,
            'parts_cost' => $completionData['parts_cost'] ?? 0,
            'external_cost' => $completionData['external_cost'] ?? 0,
            'total_cost' => $totalCost,
            'downtime_hours' => $completionData['downtime_hours'] ?? 0,
            'performance_impact' => $completionData['performance_impact'] ?? null,
            'completed_by' => auth()->id() ?? 1,
        ]);

        // Schedule next maintenance if interval is set
        if ($this->maintenance_interval_days > 0) {
            $this->scheduleNextMaintenance();
        }

        // Update asset's last maintenance date
        $this->fixedAsset->update([
            'last_maintenance_date' => $this->completed_date,
            'next_maintenance_date' => $this->next_maintenance_date,
        ]);

        return true;
    }

    public function cancel(?string $reason = null): bool
    {
        if (!in_array($this->status, ['scheduled', 'in_progress'])) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => ($this->notes ?? '') . ' | Cancelled: ' . ($reason ?? 'Manual cancellation'),
        ]);

        return true;
    }

    public function postpone(\DateTime $newDate, ?string $reason = null): bool
    {
        if ($this->status !== 'scheduled') {
            return false;
        }

        $this->update([
            'status' => 'postponed',
            'scheduled_date' => $newDate,
            'notes' => ($this->notes ?? '') . ' | Postponed: ' . ($reason ?? 'Manual postponement'),
        ]);

        return true;
    }

    private function scheduleNextMaintenance(): void
    {
        $nextDate = $this->completed_date->addDays($this->maintenance_interval_days);
        
        $this->update(['next_maintenance_date' => $nextDate]);

        // Create next maintenance record
        self::create([
            'client_id' => $this->client_id,
            'fixed_asset_id' => $this->fixed_asset_id,
            'maintenance_type' => $this->maintenance_type,
            'maintenance_category' => $this->maintenance_category,
            'scheduled_date' => $nextDate,
            'status' => 'scheduled',
            'priority' => $this->priority,
            'description' => $this->description . ' (Auto-scheduled)',
            'maintenance_interval_days' => $this->maintenance_interval_days,
            'created_by' => $this->created_by,
        ]);
    }

    public function getMaintenanceEfficiency(): array
    {
        return [
            'planned_vs_actual_hours' => [
                'planned' => $this->labor_hours,
                'actual' => $this->duration_hours,
                'variance' => $this->duration_hours - $this->labor_hours,
            ],
            'cost_breakdown' => [
                'labor_percentage' => $this->total_cost > 0 ? ($this->labor_cost / $this->total_cost) * 100 : 0,
                'parts_percentage' => $this->total_cost > 0 ? ($this->parts_cost / $this->total_cost) * 100 : 0,
                'external_percentage' => $this->total_cost > 0 ? ($this->external_cost / $this->total_cost) * 100 : 0,
            ],
            'downtime_impact' => [
                'downtime_hours' => $this->downtime_hours,
                'downtime_cost' => $this->calculateDowntimeCost(),
            ],
            'performance_rating' => $this->getPerformanceRating(),
        ];
    }

    private function calculateDowntimeCost(): float
    {
        // Estimate downtime cost based on asset value and production impact
        $hourlyAssetValue = $this->fixedAsset->total_cost / (365 * 24); // Rough hourly value
        return $this->downtime_hours * $hourlyAssetValue * 0.1; // 10% impact factor
    }

    private function getPerformanceRating(): string
    {
        $efficiency = $this->duration_hours > 0 ? ($this->labor_hours / $this->duration_hours) * 100 : 100;
        
        if ($efficiency >= 90) return 'Excellent';
        if ($efficiency >= 75) return 'Good';
        if ($efficiency >= 60) return 'Fair';
        return 'Poor';
    }
}
