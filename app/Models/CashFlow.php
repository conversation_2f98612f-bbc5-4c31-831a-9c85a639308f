<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CashFlow extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'client_id',
        'bank_account_id',
        'cash_flow_category_id',
        'reference_number',
        'transaction_date',
        'description',
        'flow_type',
        'amount',
        'payment_method',
        'reference_document',
        'journal_id',
        'status',
        'notes',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class);
    }

    public function cashFlowCategory(): BelongsTo
    {
        return $this->belongsTo(CashFlowCategory::class);
    }

    public function journal(): BelongsTo
    {
        return $this->belongsTo(Journal::class);
    }

    /**
     * Generate unique reference number
     */
    public static function generateReferenceNumber(int $clientId): string
    {
        $prefix = 'CF';
        $date = now()->format('Ymd');
        $lastNumber = static::where('client_id', $clientId)
            ->where('reference_number', 'like', $prefix . $date . '%')
            ->count();

        return $prefix . $date . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Scope for client
     */
    public function scopeForClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope for flow type
     */
    public function scopeFlowType($query, $flowType)
    {
        return $query->where('flow_type', $flowType);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }
}
