<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ApprovalRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'approval_workflow_id',
        'approvable_type',
        'approvable_id',
        'requested_by',
        'request_data',
        'status',
        'current_step',
        'requested_at',
        'completed_at',
        'rejection_reason',
        'notes',
    ];

    protected $casts = [
        'request_data' => 'array',
        'requested_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the client that owns the approval request.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the approval workflow.
     */
    public function approvalWorkflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class);
    }

    /**
     * Get the user who requested approval.
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the approvable model.
     */
    public function approvable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the approval actions.
     */
    public function actions(): HasMany
    {
        return $this->hasMany(ApprovalAction::class)->orderBy('step_number');
    }

    /**
     * Get the current step.
     */
    public function getCurrentStep(): ?ApprovalWorkflowStep
    {
        return $this->approvalWorkflow
            ->steps()
            ->where('step_order', $this->current_step)
            ->first();
    }

    /**
     * Get the next approver.
     */
    public function getNextApprover(): ?User
    {
        $step = $this->getCurrentStep();
        return $step?->getApprover($this->approvable);
    }

    /**
     * Check if the request is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the request is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the request is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if the request is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the request is completed.
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, ['approved', 'rejected', 'cancelled']);
    }

    /**
     * Approve the current step.
     */
    public function approve(User $approver, ?string $comments = null): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        // Record the approval action
        $this->actions()->create([
            'step_number' => $this->current_step,
            'approver_id' => $approver->id,
            'action' => 'approved',
            'comments' => $comments,
        ]);

        // Move to next step or complete
        if ($this->approvalWorkflow->isComplete($this->current_step)) {
            $this->update([
                'status' => 'approved',
                'completed_at' => now(),
            ]);
        } else {
            $this->increment('current_step');
        }

        return true;
    }

    /**
     * Reject the request.
     */
    public function reject(User $approver, string $reason, ?string $comments = null): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        // Record the rejection action
        $this->actions()->create([
            'step_number' => $this->current_step,
            'approver_id' => $approver->id,
            'action' => 'rejected',
            'comments' => $comments,
        ]);

        $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason,
            'completed_at' => now(),
        ]);

        return true;
    }

    /**
     * Cancel the request.
     */
    public function cancel(?string $reason = null): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'rejection_reason' => $reason,
            'completed_at' => now(),
        ]);

        return true;
    }

    /**
     * Scope for pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for requests assigned to a specific approver.
     */
    public function scopeForApprover($query, User $approver)
    {
        return $query->whereHas('approvalWorkflow.steps', function ($stepQuery) use ($approver) {
            $stepQuery->whereColumn('step_order', 'approval_requests.current_step')
                ->where(function ($approverQuery) use ($approver) {
                    $approverQuery->where('approver_user_id', $approver->id)
                        ->orWhere(function ($roleQuery) {
                            $roleQuery->where('approver_type', 'role')
                                ->where('approver_role', 'admin'); // Simplified for now
                        })
                        ->orWhere(function ($deptQuery) use ($approver) {
                            $deptQuery->where('approver_type', 'department')
                                ->where('approver_department_id', $approver->department_id ?? 0);
                        });
                });
        });
    }
}
