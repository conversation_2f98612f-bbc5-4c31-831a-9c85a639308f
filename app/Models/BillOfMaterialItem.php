<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillOfMaterialItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'bom_id',
        'material_id',
        'unit_id',
        'quantity',
        'waste_percentage',
        'sequence',
        'is_optional',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:6',
        'waste_percentage' => 'decimal:2',
        'sequence' => 'integer',
        'is_optional' => 'boolean',
    ];

    // Relationships
    public function billOfMaterial(): BelongsTo
    {
        return $this->belongsTo(BillOfMaterial::class, 'bom_id');
    }

    public function material(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'material_id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasure::class, 'unit_id');
    }

    // Accessors
    public function getQuantityWithWasteAttribute(): float
    {
        $wasteMultiplier = 1 + ($this->waste_percentage / 100);
        return $this->quantity * $wasteMultiplier;
    }

    public function getTotalCostAttribute(): float
    {
        return $this->quantity_with_waste * $this->material->standard_cost;
    }

    public function getCostPercentageAttribute(): float
    {
        $bomTotalCost = $this->billOfMaterial->total_material_cost;
        
        if ($bomTotalCost <= 0) {
            return 0;
        }
        
        return ($this->total_cost / $bomTotalCost) * 100;
    }

    // Helper Methods
    public function calculateRequiredQuantity(float $productionQuantity): float
    {
        $bomQuantity = $this->billOfMaterial->quantity_produced;
        
        if ($bomQuantity <= 0) {
            return 0;
        }
        
        $baseRequirement = ($this->quantity / $bomQuantity) * $productionQuantity;
        
        // Add waste
        $wasteMultiplier = 1 + ($this->waste_percentage / 100);
        
        return $baseRequirement * $wasteMultiplier;
    }

    public function isAvailable(float $requiredQuantity = null): bool
    {
        $required = $requiredQuantity ?? $this->quantity_with_waste;
        $available = $this->material->current_stock;
        
        return $available >= $required;
    }

    public function getShortageQuantity(float $requiredQuantity = null): float
    {
        $required = $requiredQuantity ?? $this->quantity_with_waste;
        $available = $this->material->current_stock;
        
        return max(0, $required - $available);
    }
}
