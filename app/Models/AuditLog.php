<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'user_id',
        'event',
        'auditable_type',
        'auditable_id',
        'old_values',
        'new_values',
        'changed_fields',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'description',
        'metadata',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'metadata' => 'array',
    ];

    // Event constants
    const EVENT_CREATED = 'created';
    const EVENT_UPDATED = 'updated';
    const EVENT_DELETED = 'deleted';
    const EVENT_RESTORED = 'restored';
    const EVENT_LOGIN = 'login';
    const EVENT_LOGOUT = 'logout';
    const EVENT_APPROVED = 'approved';
    const EVENT_REJECTED = 'rejected';

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function auditable(): MorphTo
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeForModel($query, string $modelType, ?int $modelId = null)
    {
        $query->where('auditable_type', $modelType);

        if ($modelId) {
            $query->where('auditable_id', $modelId);
        }

        return $query;
    }

    public function scopeByEvent($query, string $event)
    {
        return $query->where('event', $event);
    }

    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // Helper methods
    public function getModelName(): string
    {
        $modelClass = $this->auditable_type;

        return match ($modelClass) {
            'App\\Models\\Account' => 'Akun',
            'App\\Models\\Transaction' => 'Transaksi',
            'App\\Models\\Journal' => 'Jurnal',
            'App\\Models\\JournalEntry' => 'Jurnal Entry',
            'App\\Models\\Product' => 'Produk',
            'App\\Models\\Customer' => 'Pelanggan',
            'App\\Models\\Supplier' => 'Supplier',
            'App\\Models\\PurchaseOrder' => 'Purchase Order',
            // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
            'App\\Models\\Budget' => 'Budget',
            'App\\Models\\FixedAsset' => 'Aset Tetap',
            'App\\Models\\User' => 'User',
            'App\\Models\\Client' => 'Client',
            default => class_basename($modelClass),
        };
    }

    public function getEventLabel(): string
    {
        return match ($this->event) {
            self::EVENT_CREATED => 'Dibuat',
            self::EVENT_UPDATED => 'Diperbarui',
            self::EVENT_DELETED => 'Dihapus',
            self::EVENT_RESTORED => 'Dipulihkan',
            self::EVENT_LOGIN => 'Login',
            self::EVENT_LOGOUT => 'Logout',
            self::EVENT_APPROVED => 'Disetujui',
            self::EVENT_REJECTED => 'Ditolak',
            default => ucfirst($this->event),
        };
    }

    public function getEventColor(): string
    {
        return match ($this->event) {
            self::EVENT_CREATED => 'success',
            self::EVENT_UPDATED => 'warning',
            self::EVENT_DELETED => 'danger',
            self::EVENT_RESTORED => 'info',
            self::EVENT_LOGIN => 'primary',
            self::EVENT_LOGOUT => 'gray',
            self::EVENT_APPROVED => 'success',
            self::EVENT_REJECTED => 'danger',
            default => 'gray',
        };
    }

    public function getChangedFieldsFormatted(): array
    {
        $formatted = [];

        if ($this->changed_fields) {
            foreach ($this->changed_fields as $field) {
                $oldValue = $this->old_values[$field] ?? null;
                $newValue = $this->new_values[$field] ?? null;

                $formatted[] = [
                    'field' => $this->formatFieldName($field),
                    'old_value' => $this->formatValue($oldValue),
                    'new_value' => $this->formatValue($newValue),
                ];
            }
        }

        return $formatted;
    }

    protected function formatFieldName(string $field): string
    {
        return match ($field) {
            'name' => 'Nama',
            'description' => 'Deskripsi',
            'amount' => 'Jumlah',
            'status' => 'Status',
            'created_at' => 'Dibuat Pada',
            'updated_at' => 'Diperbarui Pada',
            'account_code' => 'Kode Akun',
            'account_type' => 'Tipe Akun',
            'is_active' => 'Status Aktif',
            'transaction_date' => 'Tanggal Transaksi',
            'reference_number' => 'Nomor Referensi',
            default => ucwords(str_replace('_', ' ', $field)),
        };
    }

    protected function formatValue($value): string
    {
        if (is_null($value)) {
            return '-';
        }

        if (is_bool($value)) {
            return $value ? 'Ya' : 'Tidak';
        }

        if (is_array($value)) {
            return json_encode($value);
        }

        return (string) $value;
    }

    public static function getEvents(): array
    {
        return [
            self::EVENT_CREATED => 'Dibuat',
            self::EVENT_UPDATED => 'Diperbarui',
            self::EVENT_DELETED => 'Dihapus',
            self::EVENT_RESTORED => 'Dipulihkan',
            self::EVENT_LOGIN => 'Login',
            self::EVENT_LOGOUT => 'Logout',
            self::EVENT_APPROVED => 'Disetujui',
            self::EVENT_REJECTED => 'Ditolak',
        ];
    }
}
