<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CostCenter extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'department_id',
        'parent_id',
        'cost_center_code',
        'cost_center_name',
        'description',
        'cost_center_type', // revenue, cost, profit, investment
        'manager_id',
        'budget_amount',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'budget_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(CostCenter::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(CostCenter::class, 'parent_id');
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function journalEntries(): HasMany
    {
        return $this->hasMany(JournalEntry::class);
    }

    public function budgets(): HasMany
    {
        return $this->hasMany(Budget::class);
    }

    public function productionOrders(): HasMany
    {
        return $this->hasMany(ProductionOrder::class, 'cost_center_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('cost_center_type', $type);
    }

    public function scopeByDepartment($query, int $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeRootCenters($query)
    {
        return $query->whereNull('parent_id');
    }

    // Accessors
    public function getFullCodeAttribute(): string
    {
        $codes = collect([$this->cost_center_code]);
        $parent = $this->parent;
        
        while ($parent) {
            $codes->prepend($parent->cost_center_code);
            $parent = $parent->parent;
        }
        
        return $codes->implode('.');
    }

    public function getFullNameAttribute(): string
    {
        $names = collect([$this->cost_center_name]);
        $parent = $this->parent;
        
        while ($parent) {
            $names->prepend($parent->cost_center_name);
            $parent = $parent->parent;
        }
        
        return $names->implode(' > ');
    }

    public function getTypeColorAttribute(): string
    {
        return match($this->cost_center_type) {
            'revenue' => 'green',
            'cost' => 'red',
            'profit' => 'blue',
            'investment' => 'purple',
            default => 'gray'
        };
    }

    // Helper Methods
    public function getAllChildren()
    {
        $children = collect();
        
        foreach ($this->children as $child) {
            $children->push($child);
            $children = $children->merge($child->getAllChildren());
        }
        
        return $children;
    }

    public function getActualCost($startDate, $endDate): float
    {
        return $this->journalEntries()
            ->whereHas('journal', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->whereHas('account', function ($query) {
                $query->where('account_type', 'expense');
            })
            ->sum('debit') - $this->journalEntries()
            ->whereHas('journal', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->whereHas('account', function ($query) {
                $query->where('account_type', 'expense');
            })
            ->sum('credit');
    }

    public function getActualRevenue($startDate, $endDate): float
    {
        return $this->journalEntries()
            ->whereHas('journal', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->whereHas('account', function ($query) {
                $query->where('account_type', 'revenue');
            })
            ->sum('credit') - $this->journalEntries()
            ->whereHas('journal', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->whereHas('account', function ($query) {
                $query->where('account_type', 'revenue');
            })
            ->sum('debit');
    }

    public function getBudgetVariance($startDate, $endDate): array
    {
        $actualCost = $this->getActualCost($startDate, $endDate);
        $budgetedCost = $this->getBudgetedAmount($startDate, $endDate);
        
        $variance = $budgetedCost - $actualCost;
        $variancePercentage = $budgetedCost > 0 ? ($variance / $budgetedCost) * 100 : 0;
        
        return [
            'budgeted' => $budgetedCost,
            'actual' => $actualCost,
            'variance' => $variance,
            'variance_percentage' => $variancePercentage,
            'variance_type' => $variance >= 0 ? 'favorable' : 'unfavorable',
        ];
    }

    public function getBudgetedAmount($startDate, $endDate): float
    {
        return $this->budgets()
            ->where('budget_year', date('Y', strtotime($startDate)))
            ->sum('budgeted_amount');
    }

    public function calculateProfitability($startDate, $endDate): array
    {
        $revenue = $this->getActualRevenue($startDate, $endDate);
        $cost = $this->getActualCost($startDate, $endDate);
        $profit = $revenue - $cost;
        $margin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;
        
        return [
            'revenue' => $revenue,
            'cost' => $cost,
            'profit' => $profit,
            'margin_percentage' => $margin,
        ];
    }

    public static function generateCostCenterCode(int $clientId, int $departmentId = null, int $parentId = null): string
    {
        if ($parentId) {
            $parent = self::find($parentId);
            $siblingCount = $parent->children()->count();
            return $parent->cost_center_code . '.' . str_pad($siblingCount + 1, 2, '0', STR_PAD_LEFT);
        }
        
        if ($departmentId) {
            $department = Department::find($departmentId);
            $centerCount = $department->costCenters()->count();
            return $department->department_code . '.' . str_pad($centerCount + 1, 2, '0', STR_PAD_LEFT);
        }
        
        $rootCount = self::where('client_id', $clientId)
            ->whereNull('parent_id')
            ->count();
            
        return 'CC' . str_pad($rootCount + 1, 3, '0', STR_PAD_LEFT);
    }
}
