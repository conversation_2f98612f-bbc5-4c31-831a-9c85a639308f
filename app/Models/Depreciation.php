<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Depreciation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',
        'fixed_asset_id',
        'depreciation_date',
        'depreciation_method',
        'depreciation_amount',
        'accumulated_depreciation',
        'book_value_before',
        'book_value_after',
        'useful_life_remaining',
        'period_year',
        'period_month',
        'is_manual',
        'notes',
        'journal_id',
        'created_by',
    ];

    protected $casts = [
        'depreciation_date' => 'date',
        'depreciation_amount' => 'decimal:2',
        'accumulated_depreciation' => 'decimal:2',
        'book_value_before' => 'decimal:2',
        'book_value_after' => 'decimal:2',
        'useful_life_remaining' => 'decimal:2',
        'period_year' => 'integer',
        'period_month' => 'integer',
        'is_manual' => 'boolean',
    ];

    // Relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function fixedAsset(): BelongsTo
    {
        return $this->belongsTo(FixedAsset::class);
    }

    public function journal(): BelongsTo
    {
        return $this->belongsTo(Journal::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeByPeriod($query, int $year, int $month)
    {
        return $query->where('period_year', $year)->where('period_month', $month);
    }

    public function scopeByYear($query, int $year)
    {
        return $query->where('period_year', $year);
    }

    public function scopeByMethod($query, string $method)
    {
        return $query->where('depreciation_method', $method);
    }

    public function scopeManual($query)
    {
        return $query->where('is_manual', true);
    }

    public function scopeAutomatic($query)
    {
        return $query->where('is_manual', false);
    }

    // Helper Methods
    public static function calculateStraightLine(FixedAsset $asset, \DateTime $depreciationDate): float
    {
        if ($asset->useful_life_years <= 0) {
            return 0;
        }

        $depreciableAmount = $asset->total_cost - $asset->salvage_value;
        return $depreciableAmount / $asset->useful_life_years / 12; // Monthly depreciation
    }

    public static function calculateDecliningBalance(FixedAsset $asset, \DateTime $depreciationDate): float
    {
        if ($asset->depreciation_rate <= 0) {
            return 0;
        }

        $currentBookValue = $asset->book_value;
        return $currentBookValue * ($asset->depreciation_rate / 100) / 12; // Monthly depreciation
    }

    public static function calculateUnitsOfProduction(FixedAsset $asset, int $unitsProduced): float
    {
        if ($asset->useful_life_units <= 0) {
            return 0;
        }

        $depreciableAmount = $asset->total_cost - $asset->salvage_value;
        $depreciationPerUnit = $depreciableAmount / $asset->useful_life_units;
        
        return $unitsProduced * $depreciationPerUnit;
    }

    public static function calculateSumOfYears(FixedAsset $asset, \DateTime $depreciationDate): float
    {
        $yearsElapsed = $asset->purchase_date->diffInYears($depreciationDate);
        $remainingYears = max(0, $asset->useful_life_years - $yearsElapsed);
        
        if ($remainingYears <= 0) {
            return 0;
        }

        $sumOfYears = ($asset->useful_life_years * ($asset->useful_life_years + 1)) / 2;
        $depreciableAmount = $asset->total_cost - $asset->salvage_value;
        
        return ($remainingYears / $sumOfYears) * $depreciableAmount / 12; // Monthly depreciation
    }

    public function createJournalEntry(): Journal
    {
        $journal = Journal::create([
            'client_id' => $this->client_id,
            'journal_number' => Journal::generateJournalNumber($this->client_id),
            'transaction_date' => $this->depreciation_date,
            'description' => "Depreciation - {$this->fixedAsset->asset_name}",
            'reference_type' => Depreciation::class,
            'reference_id' => $this->id,
            'total_amount' => $this->depreciation_amount,
            'created_by' => $this->created_by,
        ]);

        // Get depreciation accounts
        $depreciationExpenseAccount = Account::where('client_id', $this->client_id)
            ->where('account_code', '6200') // Depreciation Expense
            ->first();
            
        $accumulatedDepreciationAccount = Account::where('client_id', $this->client_id)
            ->where('account_code', '1290') // Accumulated Depreciation
            ->first();

        if ($depreciationExpenseAccount && $accumulatedDepreciationAccount) {
            // Debit: Depreciation Expense
            JournalEntry::create([
                'client_id' => $this->client_id,
                'journal_id' => $journal->id,
                'account_id' => $depreciationExpenseAccount->id,
                'description' => "Depreciation Expense - {$this->fixedAsset->asset_name}",
                'debit_amount' => $this->depreciation_amount,
                'credit_amount' => 0,
            ]);

            // Credit: Accumulated Depreciation
            JournalEntry::create([
                'client_id' => $this->client_id,
                'journal_id' => $journal->id,
                'account_id' => $accumulatedDepreciationAccount->id,
                'description' => "Accumulated Depreciation - {$this->fixedAsset->asset_name}",
                'debit_amount' => 0,
                'credit_amount' => $this->depreciation_amount,
            ]);
        }

        $this->update(['journal_id' => $journal->id]);

        return $journal;
    }
}
