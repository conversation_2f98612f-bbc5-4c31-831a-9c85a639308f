<?php

namespace App\Exports;

use App\Models\Customer;
use App\Models\Product;
use App\Models\UnitOfMeasure;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;

class SalesTransactionTemplateExport implements WithMultipleSheets
{
    public function sheets(): array
    {
        return [
            'Template' => new TemplateSheet(),
            'Contoh Data' => new ExampleDataSheet(),
            'Daftar Produk' => new ProductListSheet(),
            'Daftar Pelanggan' => new CustomerListSheet(),
            'Panduan' => new GuideSheet(),
        ];
    }
}

class TemplateSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        // Return empty rows for template
        return array_fill(0, 100, array_fill(0, 15, ''));
    }

    public function headings(): array
    {
        return [
            'tanggal_transaksi',
            'nomor_transaksi',
            'nama_pelanggan',
            'email_pelanggan',
            'kode_produk',
            'jumlah',
            'satuan',
            'harga_satuan',
            'diskon',
            'deskripsi',
            'metode_pembayaran',
            'status_pembayaran',
            'status_transaksi',
            'catatan',
            'deskripsi_transaksi'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 18, // tanggal_transaksi
            'B' => 20, // nomor_transaksi
            'C' => 25, // nama_pelanggan
            'D' => 25, // email_pelanggan
            'E' => 15, // kode_produk
            'F' => 10, // jumlah
            'G' => 10, // satuan
            'H' => 15, // harga_satuan
            'I' => 12, // diskon
            'J' => 30, // deskripsi
            'K' => 18, // metode_pembayaran
            'L' => 18, // status_pembayaran
            'M' => 18, // status_transaksi
            'N' => 25, // catatan
            'O' => 30, // deskripsi_transaksi
        ];
    }

    public function title(): string
    {
        return 'Template';
    }
}

class ExampleDataSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            [
                '2024-01-15',
                'TXN-20240115-0001',
                'PT. Maju Jaya',
                '<EMAIL>',
                'LAPTOP001',
                '2',
                'PCS',
                '15000000',
                '500000',
                'Laptop Gaming ROG',
                'transfer',
                'paid',
                'completed',
                'Pembelian laptop untuk kantor',
                'Penjualan laptop gaming'
            ],
            [
                '2024-01-15',
                'TXN-20240115-0001',
                'PT. Maju Jaya',
                '<EMAIL>',
                'MOUSE001',
                '2',
                'PCS',
                '500000',
                '0',
                'Gaming Mouse Wireless',
                'transfer',
                'paid',
                'completed',
                'Mouse gaming wireless',
                'Penjualan laptop gaming'
            ],
            [
                '2024-01-16',
                'TXN-20240116-0001',
                'CV. Sukses Mandiri',
                '<EMAIL>',
                'KEYBOARD001',
                '1',
                'PCS',
                '750000',
                '50000',
                'Mechanical Keyboard RGB',
                'cash',
                'unpaid',
                'pending',
                'Keyboard mechanical RGB',
                'Penjualan keyboard gaming'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'tanggal_transaksi',
            'nomor_transaksi',
            'nama_pelanggan',
            'email_pelanggan',
            'kode_produk',
            'jumlah',
            'satuan',
            'harga_satuan',
            'diskon',
            'deskripsi',
            'metode_pembayaran',
            'status_pembayaran',
            'status_transaksi',
            'catatan',
            'deskripsi_transaksi'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 18,
            'B' => 20,
            'C' => 25,
            'D' => 25,
            'E' => 15,
            'F' => 10,
            'G' => 10,
            'H' => 15,
            'I' => 12,
            'J' => 30,
            'K' => 18,
            'L' => 18,
            'M' => 18,
            'N' => 25,
            'O' => 30,
        ];
    }

    public function title(): string
    {
        return 'Contoh Data';
    }
}

class ProductListSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        $clientId = Auth::user()->client_id;

        $products = Product::where('client_id', $clientId)
            ->where('is_active', true)
            ->with(['category', 'unit'])
            ->get()
            ->map(function ($product) {
                return [
                    $product->product_code,
                    $product->product_name,
                    $product->category->category_name ?? '',
                    $product->unit->unit_code ?? '',
                    number_format($product->selling_price, 0, ',', '.'),
                    number_format($product->standard_cost, 0, ',', '.'),
                ];
            })
            ->toArray();

        return $products;
    }

    public function headings(): array
    {
        return [
            'Kode Produk',
            'Nama Produk',
            'Kategori',
            'Satuan',
            'Harga Jual',
            'Harga Pokok'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E67E22']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // Kode Produk
            'B' => 30, // Nama Produk
            'C' => 20, // Kategori
            'D' => 10, // Satuan
            'E' => 15, // Harga Jual
            'F' => 15, // Harga Pokok
        ];
    }

    public function title(): string
    {
        return 'Daftar Produk';
    }
}

class CustomerListSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        $clientId = Auth::user()->client_id;

        $customers = Customer::where('client_id', $clientId)
            ->where('is_active', true)
            ->get()
            ->map(function ($customer) {
                return [
                    $customer->customer_name,
                    $customer->email ?? '',
                    $customer->phone ?? '',
                    $customer->customer_type,
                ];
            })
            ->toArray();

        return $customers;
    }

    public function headings(): array
    {
        return [
            'Nama Pelanggan',
            'Email',
            'Telepon',
            'Tipe Pelanggan'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '9B59B6']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30, // Nama Pelanggan
            'B' => 25, // Email
            'C' => 15, // Telepon
            'D' => 15, // Tipe Pelanggan
        ];
    }

    public function title(): string
    {
        return 'Daftar Pelanggan';
    }
}

class GuideSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            ['PANDUAN IMPORT TRANSAKSI PENJUALAN', ''],
            ['', ''],
            ['1. FORMAT KOLOM WAJIB:', ''],
            ['tanggal_transaksi', 'Format: YYYY-MM-DD atau DD/MM/YYYY (contoh: 2024-01-15)'],
            ['nama_pelanggan', 'Nama pelanggan (akan dibuat otomatis jika belum ada)'],
            ['kode_produk', 'Kode produk yang sudah ada di sistem'],
            ['jumlah', 'Jumlah produk (angka desimal diperbolehkan)'],
            ['', ''],
            ['2. FORMAT KOLOM OPSIONAL:', ''],
            ['nomor_transaksi', 'Kosongkan untuk generate otomatis'],
            ['email_pelanggan', 'Email pelanggan (opsional)'],
            ['satuan', 'Kosongkan untuk menggunakan satuan default produk'],
            ['harga_satuan', 'Kosongkan untuk menggunakan harga jual produk'],
            ['diskon', 'Diskon dalam rupiah (default: 0)'],
            ['deskripsi', 'Deskripsi item (default: nama produk)'],
            ['metode_pembayaran', 'cash, transfer, check, credit, debit_card, credit_card'],
            ['status_pembayaran', 'unpaid, partial, paid, overpaid'],
            ['status_transaksi', 'pending, processed, completed, rejected'],
            ['catatan', 'Catatan untuk item'],
            ['deskripsi_transaksi', 'Deskripsi untuk transaksi'],
            ['', ''],
            ['3. TIPS IMPORT:', ''],
            ['• Gunakan sheet "Template" untuk import data Anda', ''],
            ['• Lihat "Contoh Data" untuk referensi format yang benar', ''],
            ['• Cek "Daftar Produk" untuk kode produk yang tersedia', ''],
            ['• Cek "Daftar Pelanggan" untuk pelanggan yang sudah ada', ''],
            ['• Satu transaksi bisa memiliki multiple item dengan nomor_transaksi sama', ''],
            ['• Jika ada error, sistem akan memberikan detail error per baris', ''],
            ['• Data yang berhasil akan tetap tersimpan meski ada error di baris lain', ''],
            ['', ''],
            ['4. VALIDASI OTOMATIS:', ''],
            ['• Pelanggan baru akan dibuat otomatis jika belum ada', ''],
            ['• Produk harus sudah ada di sistem', ''],
            ['• Satuan harus sudah ada di sistem', ''],
            ['• Total transaksi dihitung otomatis (subtotal + PPN 11% - diskon)', ''],
            ['• Nomor transaksi generate otomatis jika kosong', ''],
        ];
    }

    public function headings(): array
    {
        return ['Keterangan', 'Detail'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2C3E50']
                ]
            ],
            3 => ['font' => ['bold' => true, 'color' => ['rgb' => '2980B9']]],
            9 => ['font' => ['bold' => true, 'color' => ['rgb' => '27AE60']]],
            22 => ['font' => ['bold' => true, 'color' => ['rgb' => 'E74C3C']]],
            31 => ['font' => ['bold' => true, 'color' => ['rgb' => 'F39C12']]],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 60,
        ];
    }

    public function title(): string
    {
        return 'Panduan';
    }
}
