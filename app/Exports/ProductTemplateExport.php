<?php

namespace App\Exports;

use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ProductTemplateExport implements WithMultipleSheets
{
    public function sheets(): array
    {
        return [
            'Template' => new ProductTemplateSheet(),
            'Contoh Data' => new ProductExampleDataSheet(),
            'Daftar Kategori' => new CategoryListSheet(),
            'Daftar Satuan' => new UnitListSheet(),
            'Panduan' => new ProductGuideSheet(),
        ];
    }
}

class ProductTemplateSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        // Return empty rows for template
        return array_fill(0, 100, array_fill(0, 30, ''));
    }

    public function headings(): array
    {
        return [
            'kode_produk',
            'nama_produk',
            'kategori',
            'satuan',
            'deskripsi',
            'harga_pokok',
            'harga_jual',
            'harga_beli',
            'stok_minimum',
            'stok_maksimum',
            'titik_reorder',
            'barcode',
            'merek',
            'berat',
            'dimensi',
            'dapat_dimanufaktur',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // kode_produk
            'B' => 25, // nama_produk
            'C' => 20, // kategori
            'D' => 12, // satuan
            'E' => 30, // deskripsi
            'F' => 15, // harga_pokok
            'G' => 15, // harga_jual
            'H' => 15, // harga_beli
            'I' => 12, // stok_minimum
            'J' => 12, // stok_maksimum
            'K' => 12, // titik_reorder
            'L' => 15, // barcode
            'M' => 15, // merek
            'N' => 10, // berat
            'O' => 15, // dimensi
            'P' => 18, // dapat_dimanufaktur
        ];
    }

    public function title(): string
    {
        return 'Template';
    }
}

class ProductExampleDataSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            [
                'LAPTOP001',
                'Laptop Gaming ROG Strix',
                'Elektronik',
                'PCS',
                'Laptop gaming dengan spesifikasi tinggi',
                '12000000',
                '15000000',
                '11000000',
                '5',
                '50',
                '10',
                '1234567890123',
                'ASUS',
                '2.5',
                '35x25x3 cm',
                'tidak'
            ],
            [
                'MOUSE001',
                'Gaming Mouse Wireless',
                'Elektronik',
                'PCS',
                'Mouse gaming wireless dengan RGB',
                '300000',
                '500000',
                '250000',
                '10',
                '100',
                '20',
                '1234567890124',
                'Logitech',
                '0.15',
                '12x7x4 cm',
                'tidak'
            ],
            [
                'FURNITURE001',
                'Meja Kayu Custom',
                'Furniture',
                'PCS',
                'Meja kayu yang dirakit sesuai pesanan',
                '800000',
                '1200000',
                '0',
                '2',
                '10',
                '3',
                '',
                'Custom Wood',
                '15',
                '120x60x75 cm',
                'ya'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'kode_produk',
            'nama_produk',
            'kategori',
            'satuan',
            'deskripsi',
            'harga_pokok',
            'harga_jual',
            'harga_beli',
            'stok_minimum',
            'stok_maksimum',
            'titik_reorder',
            'barcode',
            'merek',
            'berat',
            'dimensi',
            'dapat_dimanufaktur',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // kode_produk
            'B' => 25, // nama_produk
            'C' => 20, // kategori
            'D' => 12, // satuan
            'E' => 30, // deskripsi
            'F' => 15, // harga_pokok
            'G' => 15, // harga_jual
            'H' => 15, // harga_beli
            'I' => 12, // stok_minimum
            'J' => 12, // stok_maksimum
            'K' => 12, // titik_reorder
            'L' => 15, // barcode
            'M' => 15, // merek
            'N' => 10, // berat
            'O' => 15, // dimensi
            'P' => 18, // dapat_dimanufaktur
        ];
    }

    public function title(): string
    {
        return 'Contoh Data';
    }
}

class CategoryListSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        $clientId = Auth::user()->client_id;

        $categories = ProductCategory::where('client_id', $clientId)
            ->where('is_active', true)
            ->get()
            ->map(function ($category) {
                return [
                    $category->category_code,
                    $category->category_name,
                    $category->description ?? '',
                ];
            })
            ->toArray();

        return $categories;
    }

    public function headings(): array
    {
        return [
            'Kode Kategori',
            'Nama Kategori',
            'Deskripsi'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E67E22']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // Kode Kategori
            'B' => 25, // Nama Kategori
            'C' => 40, // Deskripsi
        ];
    }

    public function title(): string
    {
        return 'Daftar Kategori';
    }
}

class UnitListSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        $clientId = Auth::user()->client_id;

        $units = UnitOfMeasure::where('client_id', $clientId)
            ->where('is_active', true)
            ->get()
            ->map(function ($unit) {
                return [
                    $unit->unit_code,
                    $unit->unit_name,
                    $unit->description ?? '',
                ];
            })
            ->toArray();

        return $units;
    }

    public function headings(): array
    {
        return [
            'Kode Satuan',
            'Nama Satuan',
            'Deskripsi'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '9B59B6']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // Kode Satuan
            'B' => 25, // Nama Satuan
            'C' => 40, // Deskripsi
        ];
    }

    public function title(): string
    {
        return 'Daftar Satuan';
    }
}

class ProductGuideSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            ['PANDUAN IMPORT PRODUK', ''],
            ['', ''],
            ['1. FORMAT KOLOM WAJIB:', ''],
            ['kode_produk', 'Kode unik produk (maksimal 50 karakter)'],
            ['nama_produk', 'Nama produk (maksimal 255 karakter)'],
            ['kategori', 'Nama kategori (akan dibuat otomatis jika belum ada)'],
            ['satuan', 'Kode atau nama satuan (akan dibuat otomatis jika belum ada)'],
            ['', ''],
            ['2. FORMAT KOLOM OPSIONAL:', ''],
            ['deskripsi', 'Deskripsi produk'],
            ['harga_pokok', 'Harga pokok produksi (angka)'],
            ['harga_jual', 'Harga jual ke customer (angka)'],
            ['harga_beli', 'Harga beli dari supplier (angka)'],
            ['stok_minimum', 'Stok minimum (angka)'],
            ['stok_maksimum', 'Stok maksimum (angka)'],
            ['titik_reorder', 'Titik reorder stok (angka)'],
            ['barcode', 'Kode barcode produk'],
            ['merek', 'Merek produk'],
            ['berat', 'Berat produk dalam kg (angka)'],
            ['dimensi', 'Dimensi produk (contoh: 35x25x3 cm)'],
            ['dapat_dimanufaktur', 'Apakah produk bisa diproduksi/dirakit (ya/tidak, true/false, 1/0)'],
            ['', ''],
            ['3. TIPS IMPORT:', ''],
            ['• Gunakan sheet "Template" untuk import data Anda', ''],
            ['• Lihat "Contoh Data" untuk referensi format yang benar', ''],
            ['• Cek "Daftar Kategori" untuk kategori yang sudah ada', ''],
            ['• Cek "Daftar Satuan" untuk satuan yang sudah ada', ''],
            ['• Kode produk harus unik dalam sistem', ''],
            ['• Kategori dan satuan akan dibuat otomatis jika belum ada', ''],
            ['• Field "dapat_dimanufaktur": isi "ya" untuk produk yang bisa diproduksi', ''],
            ['• Produk manufaktur biasanya memiliki BOM (Bill of Materials)', ''],
            ['• Jika ada error, sistem akan memberikan detail error per baris', ''],
            ['• Data yang berhasil akan tetap tersimpan meski ada error di baris lain', ''],
            ['• Produk yang sudah ada akan diupdate dengan data baru', ''],
            ['', ''],
            ['4. VALIDASI OTOMATIS:', ''],
            ['• Kategori baru akan dibuat otomatis jika belum ada', ''],
            ['• Satuan baru akan dibuat otomatis jika belum ada', ''],
            ['• Kode produk harus unik per client', ''],
            ['• Tipe produk harus salah satu: raw_material, finished_good, semi_finished, service', ''],
            ['• Tarif pajak, diskon, komisi maksimal 100%', ''],
            ['• Semua field angka akan divalidasi dan diformat otomatis', ''],
            ['• Field boolean akan diparse dari berbagai format (1/0, true/false, ya/tidak)', ''],
            ['', ''],
            ['5. TIPE PRODUK:', ''],
            ['raw_material', 'Bahan baku untuk produksi'],
            ['finished_good', 'Produk jadi siap jual (default)'],
            ['semi_finished', 'Produk setengah jadi'],
            ['service', 'Layanan/jasa'],
            ['', ''],
            ['6. FORMAT ANGKA:', ''],
            ['• Gunakan titik (.) untuk desimal', ''],
            ['• Koma (,) akan otomatis dikonversi ke titik', ''],
            ['• Simbol mata uang akan dihapus otomatis', ''],
            ['• Contoh: 1.500.000 atau 1,500,000 atau Rp 1.500.000', ''],
        ];
    }

    public function headings(): array
    {
        return ['Keterangan', 'Detail'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2C3E50']
                ]
            ],
            3 => ['font' => ['bold' => true, 'color' => ['rgb' => '2980B9']]],
            9 => ['font' => ['bold' => true, 'color' => ['rgb' => '27AE60']]],
            37 => ['font' => ['bold' => true, 'color' => ['rgb' => 'E74C3C']]],
            47 => ['font' => ['bold' => true, 'color' => ['rgb' => 'F39C12']]],
            55 => ['font' => ['bold' => true, 'color' => ['rgb' => '8E44AD']]],
            61 => ['font' => ['bold' => true, 'color' => ['rgb' => '16A085']]],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 60,
        ];
    }

    public function title(): string
    {
        return 'Panduan';
    }
}
