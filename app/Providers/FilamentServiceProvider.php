<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;

class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set locale untuk Filament
        App::setLocale('id');
        
        // Set locale untuk Carbon (untuk format tanggal)
        if (class_exists(\Carbon\Carbon::class)) {
            \Carbon\Carbon::setLocale('id');
        }
    }
}
