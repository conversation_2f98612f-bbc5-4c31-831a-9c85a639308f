<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        \App\Models\Account::class => \App\Policies\AccountPolicy::class,
        \App\Models\Transaction::class => \App\Policies\TransactionPolicy::class,
        \App\Models\Client::class => \App\Policies\ClientPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define additional gates if needed
        Gate::define('manage-clients', function ($user) {
            return $user->isSuperAdmin();
        });

        Gate::define('view-all-reports', function ($user) {
            return $user->isSuperAdmin();
        });

        Gate::define('approve-transactions', function ($user) {
            return $user->isSuperAdmin() || $user->user_type === 'admin';
        });
    }
}
