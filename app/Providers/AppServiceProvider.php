<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use App\Models\Journal;
use App\Observers\JournalObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set locale untuk aplikasi
        App::setLocale('id');

        // Set locale untuk Carbon (untuk format tanggal)
        if (class_exists(\Carbon\Carbon::class)) {
            \Carbon\Carbon::setLocale('id');
        }

        // Register observers
        Journal::observe(JournalObserver::class);
    }
}
