<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Set locale untuk aplikasi
        App::setLocale('id');
        
        // Set locale untuk Carbon (untuk format tanggal)
        if (class_exists(\Carbon\Carbon::class)) {
            \Carbon\Carbon::setLocale('id');
        }
        
        return $next($request);
    }
}
