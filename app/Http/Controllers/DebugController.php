<?php

namespace App\Http\Controllers;

use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DebugController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $clientId = $user?->client_id;

        $categories = ProductCategory::where('client_id', $clientId)
            ->where('is_active', true)
            ->get();

        $units = UnitOfMeasure::where('client_id', $clientId)
            ->where('is_active', true)
            ->get();

        return response()->json([
            'user' => [
                'id' => $user?->id,
                'email' => $user?->email,
                'client_id' => $clientId,
            ],
            'categories' => $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'code' => $category->category_code,
                    'name' => $category->category_name,
                    'is_active' => $category->is_active,
                ];
            }),
            'units' => $units->map(function ($unit) {
                return [
                    'id' => $unit->id,
                    'code' => $unit->unit_code,
                    'name' => $unit->unit_name,
                    'is_active' => $unit->is_active,
                ];
            }),
        ]);
    }
}
