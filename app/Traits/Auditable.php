<?php

namespace App\Traits;

use App\Models\AuditLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

trait Auditable
{
    /**
     * Boot the auditable trait for a model.
     */
    public static function bootAuditable()
    {
        static::created(function ($model) {
            $model->auditCreated();
        });

        static::updated(function ($model) {
            $model->auditUpdated();
        });

        static::deleted(function ($model) {
            $model->auditDeleted();
        });

        if (method_exists(static::class, 'restored')) {
            static::restored(function ($model) {
                $model->auditRestored();
            });
        }
    }

    /**
     * Audit model creation.
     */
    protected function auditCreated()
    {
        $this->createAuditLog(AuditLog::EVENT_CREATED, null, $this->getAuditableAttributes());
    }

    /**
     * Audit model update.
     */
    protected function auditUpdated()
    {
        $changes = $this->getChanges();
        $original = $this->getOriginal();

        if (!empty($changes)) {
            $oldValues = [];
            $newValues = [];
            $changedFields = [];

            foreach ($changes as $field => $newValue) {
                if ($this->shouldAuditField($field)) {
                    $oldValues[$field] = $original[$field] ?? null;
                    $newValues[$field] = $newValue;
                    $changedFields[] = $field;
                }
            }

            if (!empty($changedFields)) {
                $this->createAuditLog(
                    AuditLog::EVENT_UPDATED,
                    $oldValues,
                    $newValues,
                    $changedFields
                );
            }
        }
    }

    /**
     * Audit model deletion.
     */
    protected function auditDeleted()
    {
        $this->createAuditLog(AuditLog::EVENT_DELETED, $this->getAuditableAttributes(), null);
    }

    /**
     * Audit model restoration.
     */
    protected function auditRestored()
    {
        $this->createAuditLog(AuditLog::EVENT_RESTORED, null, $this->getAuditableAttributes());
    }

    /**
     * Create audit log entry.
     */
    protected function createAuditLog(
        string $event,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?array $changedFields = null
    ) {
        $clientId = $this->getClientId();

        if (!$clientId) {
            return; // Skip audit if no client context
        }

        AuditLog::create([
            'client_id' => $clientId,
            'user_id' => Auth::id(),
            'event' => $event,
            'auditable_type' => get_class($this),
            'auditable_id' => $this->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'url' => Request::fullUrl(),
            'method' => Request::method(),
            'description' => $this->getAuditDescription($event),
            'metadata' => $this->getAuditMetadata(),
        ]);
    }

    /**
     * Get client ID for audit log.
     */
    protected function getClientId(): ?int
    {
        // If model has client_id attribute
        if (isset($this->attributes['client_id'])) {
            return $this->attributes['client_id'];
        }

        // If model belongs to a client through relationship
        if (method_exists($this, 'client') && $this->client) {
            return $this->client->id;
        }

        // Get from current tenant context (Filament)
        if (function_exists('filament') && filament()->getTenant()) {
            return filament()->getTenant()->id;
        }

        return null;
    }

    /**
     * Get auditable attributes (excluding sensitive fields).
     */
    protected function getAuditableAttributes(): array
    {
        $attributes = $this->getAttributes();
        $excluded = $this->getAuditExcluded();

        return array_diff_key($attributes, array_flip($excluded));
    }

    /**
     * Get fields to exclude from audit.
     */
    protected function getAuditExcluded(): array
    {
        return array_merge([
            'password',
            'remember_token',
            'email_verified_at',
            'created_at',
            'updated_at',
            'deleted_at',
        ], $this->auditExcluded ?? []);
    }

    /**
     * Check if field should be audited.
     */
    protected function shouldAuditField(string $field): bool
    {
        $excluded = $this->getAuditExcluded();
        return !in_array($field, $excluded);
    }

    /**
     * Get audit description for the event.
     */
    protected function getAuditDescription(string $event): string
    {
        $modelName = $this->getAuditModelName();
        $identifier = $this->getAuditIdentifier();

        return match ($event) {
            AuditLog::EVENT_CREATED => "{$modelName} '{$identifier}' dibuat",
            AuditLog::EVENT_UPDATED => "{$modelName} '{$identifier}' diperbarui",
            AuditLog::EVENT_DELETED => "{$modelName} '{$identifier}' dihapus",
            AuditLog::EVENT_RESTORED => "{$modelName} '{$identifier}' dipulihkan",
            default => "{$modelName} '{$identifier}' {$event}",
        };
    }

    /**
     * Get model name for audit description.
     */
    protected function getAuditModelName(): string
    {
        return match (get_class($this)) {
            'App\\Models\\Account' => 'Akun',
            'App\\Models\\Transaction' => 'Transaksi',
            'App\\Models\\Journal' => 'Jurnal',
            'App\\Models\\JournalEntry' => 'Jurnal Entry',
            'App\\Models\\Product' => 'Produk',
            'App\\Models\\Customer' => 'Pelanggan',
            'App\\Models\\Supplier' => 'Pemasok',
            'App\\Models\\PurchaseOrder' => 'Pesanan Pembelian',
            // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
            'App\\Models\\Budget' => 'Anggaran',
            'App\\Models\\FixedAsset' => 'Aset Tetap',
            'App\\Models\\User' => 'Pengguna',
            'App\\Models\\Client' => 'Klien',
            default => class_basename($this),
        };
    }

    /**
     * Get identifier for audit description.
     */
    protected function getAuditIdentifier(): string
    {
        // Try common identifier fields
        foreach (['name', 'title', 'account_code', 'reference_number', 'email'] as $field) {
            if (isset($this->attributes[$field])) {
                return $this->attributes[$field];
            }
        }

        return "ID: {$this->getKey()}";
    }

    /**
     * Get additional metadata for audit log.
     */
    protected function getAuditMetadata(): array
    {
        $metadata = [];

        // Add model-specific metadata
        if (method_exists($this, 'getCustomAuditMetadata')) {
            $metadata = array_merge($metadata, $this->getCustomAuditMetadata());
        }

        // Add request context
        if (Request::hasHeader('X-Requested-With')) {
            $metadata['ajax'] = true;
        }

        if (Request::hasHeader('Referer')) {
            $metadata['referer'] = Request::header('Referer');
        }

        return $metadata;
    }

    /**
     * Get audit logs for this model.
     */
    public function auditLogs()
    {
        return $this->morphMany(AuditLog::class, 'auditable');
    }

    /**
     * Create custom audit log entry.
     */
    public function audit(string $event, ?string $description = null, ?array $metadata = null)
    {
        $clientId = $this->getClientId();

        if (!$clientId) {
            return;
        }

        AuditLog::create([
            'client_id' => $clientId,
            'user_id' => Auth::id(),
            'event' => $event,
            'auditable_type' => get_class($this),
            'auditable_id' => $this->getKey(),
            'old_values' => null,
            'new_values' => null,
            'changed_fields' => null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'url' => Request::fullUrl(),
            'method' => Request::method(),
            'description' => $description ?: $this->getAuditDescription($event),
            'metadata' => $metadata ?: $this->getAuditMetadata(),
        ]);
    }
}
