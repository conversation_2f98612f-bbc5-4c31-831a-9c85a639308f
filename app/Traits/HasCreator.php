<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

trait HasCreator
{
    /**
     * Boot the trait
     */
    protected static function bootHasCreator()
    {
        // Automatically set created_by when creating new records
        static::creating(function (Model $model) {
            if (Auth::check() && !$model->created_by) {
                $model->created_by = Auth::id();
            }
        });
    }
}
