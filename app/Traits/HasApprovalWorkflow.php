<?php

namespace App\Traits;

use App\Models\ApprovalRequest;
use App\Models\ApprovalWorkflow;
use App\Services\ApprovalWorkflowService;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\Auth;

trait HasApprovalWorkflow
{
    /**
     * Boot the trait.
     */
    protected static function bootHasApprovalWorkflow(): void
    {
        // Auto-submit for approval when certain conditions are met
        static::created(function ($model) {
            if ($model->shouldAutoSubmitForApproval()) {
                $model->submitForApproval();
            }
        });

        static::updated(function ($model) {
            if ($model->shouldAutoSubmitForApproval() && $model->wasChanged($model->getApprovalTriggerFields())) {
                $model->submitForApproval();
            }
        });
    }

    /**
     * Get approval requests for this model.
     */
    public function approvalRequests(): MorphMany
    {
        return $this->morphMany(ApprovalRequest::class, 'approvable');
    }

    /**
     * Get the latest approval request.
     */
    public function latestApprovalRequest(): ?ApprovalRequest
    {
        return $this->approvalRequests()->latest()->first();
    }

    /**
     * Submit this model for approval.
     */
    public function submitForApproval(array $data = []): ?ApprovalRequest
    {
        $service = app(ApprovalWorkflowService::class);
        $requester = Auth::user();

        if (!$requester) {
            return null;
        }

        // Merge model data with additional data
        $approvalData = array_merge($this->getApprovalData(), $data);

        return $service->submitForApproval($this, $requester, $approvalData);
    }

    /**
     * Check if this model is pending approval.
     */
    public function isPendingApproval(): bool
    {
        return $this->approvalRequests()
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if this model is approved.
     */
    public function isApproved(): bool
    {
        $latestRequest = $this->latestApprovalRequest();
        return $latestRequest && $latestRequest->isApproved();
    }

    /**
     * Check if this model is rejected.
     */
    public function isRejected(): bool
    {
        $latestRequest = $this->latestApprovalRequest();
        return $latestRequest && $latestRequest->isRejected();
    }

    /**
     * Get approval status.
     */
    public function getApprovalStatus(): string
    {
        if ($this->isPendingApproval()) {
            return 'pending';
        }

        if ($this->isApproved()) {
            return 'approved';
        }

        if ($this->isRejected()) {
            return 'rejected';
        }

        return 'none';
    }

    /**
     * Get approval status badge color.
     */
    public function getApprovalStatusColor(): string
    {
        return match ($this->getApprovalStatus()) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get approval status label.
     */
    public function getApprovalStatusLabel(): string
    {
        return match ($this->getApprovalStatus()) {
            'pending' => 'Menunggu Persetujuan',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            default => 'Tidak Perlu Persetujuan',
        };
    }

    /**
     * Mark as approved (called by approval service).
     */
    public function markAsApproved($approver = null): void
    {
        if ($this->hasApprovalAttribute('approval_status')) {
            $this->update(['approval_status' => 'approved']);
        }

        if ($this->hasApprovalAttribute('approved_by') && $approver) {
            $this->update(['approved_by' => $approver->id]);
        }

        if ($this->hasApprovalAttribute('approved_at')) {
            $this->update(['approved_at' => now()]);
        }

        // Fire approved event
        $this->fireModelEvent('approved');
    }

    /**
     * Mark as rejected (called by approval service).
     */
    public function markAsRejected($rejector = null, ?string $reason = null): void
    {
        if ($this->hasApprovalAttribute('approval_status')) {
            $this->update(['approval_status' => 'rejected']);
        }

        if ($this->hasApprovalAttribute('rejected_by') && $rejector) {
            $this->update(['rejected_by' => $rejector->id]);
        }

        if ($this->hasApprovalAttribute('rejected_at')) {
            $this->update(['rejected_at' => now()]);
        }

        if ($this->hasApprovalAttribute('rejection_reason') && $reason) {
            $this->update(['rejection_reason' => $reason]);
        }

        // Fire rejected event
        $this->fireModelEvent('rejected');
    }

    /**
     * Get data for approval workflow evaluation.
     */
    protected function getApprovalData(): array
    {
        $data = [];

        // Add amount if available
        if ($this->hasApprovalAttribute('total_amount')) {
            $data['amount'] = $this->total_amount;
        } elseif ($this->hasApprovalAttribute('amount')) {
            $data['amount'] = $this->amount;
        } elseif ($this->hasApprovalAttribute('grand_total')) {
            $data['amount'] = $this->grand_total;
        }

        // Add department if available
        if ($this->hasApprovalAttribute('department_id')) {
            $data['department_id'] = $this->department_id;
        }

        // Add cost center if available
        if ($this->hasApprovalAttribute('cost_center_id')) {
            $data['cost_center_id'] = $this->cost_center_id;
        }

        return $data;
    }

    /**
     * Check if model should auto-submit for approval.
     * Override this method in your model to customize.
     */
    protected function shouldAutoSubmitForApproval(): bool
    {
        return false; // Default: manual submission
    }

    /**
     * Get fields that trigger approval when changed.
     * Override this method in your model to customize.
     */
    protected function getApprovalTriggerFields(): array
    {
        return ['total_amount', 'amount', 'grand_total'];
    }

    /**
     * Check if model has a specific fillable attribute or cast.
     */
    protected function hasApprovalAttribute(string $attribute): bool
    {
        return array_key_exists($attribute, $this->attributes) ||
            in_array($attribute, $this->fillable) ||
            isset($this->casts[$attribute]);
    }

    /**
     * Register model events for approval workflow.
     */
    protected static function registerApprovalEvents(): void
    {
        static::registerModelEvent('approved', function ($model) {
            // Handle approved event
        });

        static::registerModelEvent('rejected', function ($model) {
            // Handle rejected event
        });
    }

    /**
     * Get applicable approval workflows.
     */
    public function getApplicableWorkflows(): \Illuminate\Database\Eloquent\Collection
    {
        return ApprovalWorkflow::active()
            ->forModel(get_class($this))
            ->where('client_id', $this->client_id)
            ->get()
            ->filter(function ($workflow) {
                return $workflow->appliesTo($this, $this->getApprovalData());
            });
    }

    /**
     * Check if approval is required for this model.
     */
    public function requiresApproval(): bool
    {
        return $this->getApplicableWorkflows()->isNotEmpty();
    }
}
