<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

trait BelongsToTenant
{
    /**
     * Boot the trait
     */
    protected static function bootBelongsToTenant()
    {
        // Automatically scope queries to the current user's client
        static::addGlobalScope('tenant', function (Builder $builder) {
            $user = Auth::user();
            
            if ($user && !$user->isSuperAdmin()) {
                if ($user->client_id) {
                    $builder->where('client_id', $user->client_id);
                }
            }
        });

        // Automatically set client_id when creating new records
        static::creating(function (Model $model) {
            $user = Auth::user();
            
            if ($user && !$user->isSuperAdmin() && $user->client_id) {
                $model->client_id = $user->client_id;
            }
        });
    }

    /**
     * Scope to remove tenant filtering (for super admin)
     */
    public function scopeWithoutTenantScope(Builder $query)
    {
        return $query->withoutGlobalScope('tenant');
    }

    /**
     * Scope to filter by specific client
     */
    public function scopeForTenant(Builder $query, $clientId)
    {
        return $query->withoutGlobalScope('tenant')->where('client_id', $clientId);
    }
}
