<?php

namespace App\Services;

use App\Models\WorkOrder;
use App\Models\ProductionCost;
use App\Models\RawMaterialUsage;
use App\Models\WorkOrderTimeEntry;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use Illuminate\Support\Facades\DB;

class WorkOrderCostingService
{
    /**
     * Calculate complete costs for a work order
     */
    public function calculateWorkOrderCosts(WorkOrder $workOrder): array
    {
        return DB::transaction(function () use ($workOrder) {
            $costs = [
                'work_order_id' => $workOrder->id,
                'work_order_number' => $workOrder->work_order_number,
                'product_name' => $workOrder->productionOrder->product->product_name ?? 'N/A',
                'quantity_produced' => $workOrder->quantity_completed,
                'material_costs' => $this->calculateMaterialCosts($workOrder),
                'labor_costs' => $this->calculateLaborCosts($workOrder),
                'overhead_costs' => $this->calculateOverheadCosts($workOrder),
                'setup_costs' => $this->calculateSetupCosts($workOrder),
                'quality_costs' => $this->calculateQualityCosts($workOrder),
            ];

            $costs['total_cost'] = $costs['material_costs']['total'] + 
                                 $costs['labor_costs']['total'] + 
                                 $costs['overhead_costs']['total'] + 
                                 $costs['setup_costs']['total'] + 
                                 $costs['quality_costs']['total'];

            $costs['unit_cost'] = $workOrder->quantity_completed > 0 
                ? $costs['total_cost'] / $workOrder->quantity_completed 
                : 0;

            // Update work order with calculated costs
            $workOrder->update([
                'material_cost' => $costs['material_costs']['total'],
                'labor_cost' => $costs['labor_costs']['total'],
                'overhead_cost' => $costs['overhead_costs']['total'],
                'total_cost' => $costs['total_cost'],
                'unit_cost' => $costs['unit_cost'],
            ]);

            // Record detailed costs
            $this->recordDetailedCosts($workOrder, $costs);

            return $costs;
        });
    }

    /**
     * Calculate material costs for work order
     */
    private function calculateMaterialCosts(WorkOrder $workOrder): array
    {
        $materialUsages = RawMaterialUsage::where('work_order_id', $workOrder->id)->get();
        
        $materialCosts = [
            'details' => [],
            'total' => 0,
        ];

        foreach ($materialUsages as $usage) {
            $cost = $usage->quantity_used * $usage->unit_cost;
            
            $materialCosts['details'][] = [
                'material_id' => $usage->material_id,
                'material_name' => $usage->material->product_name ?? 'Unknown',
                'quantity_used' => $usage->quantity_used,
                'unit_cost' => $usage->unit_cost,
                'total_cost' => $cost,
                'waste_percentage' => $usage->waste_percentage ?? 0,
                'waste_cost' => $cost * ($usage->waste_percentage / 100),
            ];
            
            $materialCosts['total'] += $cost;
        }

        return $materialCosts;
    }

    /**
     * Calculate labor costs for work order
     */
    private function calculateLaborCosts(WorkOrder $workOrder): array
    {
        $timeEntries = WorkOrderTimeEntry::where('work_order_id', $workOrder->id)->get();
        
        $laborCosts = [
            'details' => [],
            'total' => 0,
            'regular_hours' => 0,
            'overtime_hours' => 0,
            'setup_hours' => 0,
        ];

        foreach ($timeEntries as $entry) {
            $regularHours = min($entry->hours_worked, 8); // Assuming 8 hours regular
            $overtimeHours = max(0, $entry->hours_worked - 8);
            
            $regularCost = $regularHours * $entry->hourly_rate;
            $overtimeCost = $overtimeHours * $entry->hourly_rate * 1.5; // 1.5x overtime rate
            $totalCost = $regularCost + $overtimeCost;
            
            $laborCosts['details'][] = [
                'employee_id' => $entry->employee_id,
                'employee_name' => $entry->employee_name ?? 'Unknown',
                'work_date' => $entry->work_date,
                'hours_worked' => $entry->hours_worked,
                'regular_hours' => $regularHours,
                'overtime_hours' => $overtimeHours,
                'hourly_rate' => $entry->hourly_rate,
                'regular_cost' => $regularCost,
                'overtime_cost' => $overtimeCost,
                'total_cost' => $totalCost,
                'activity_type' => $entry->activity_type ?? 'production',
            ];
            
            $laborCosts['total'] += $totalCost;
            $laborCosts['regular_hours'] += $regularHours;
            $laborCosts['overtime_hours'] += $overtimeHours;
            
            if ($entry->activity_type === 'setup') {
                $laborCosts['setup_hours'] += $entry->hours_worked;
            }
        }

        return $laborCosts;
    }

    /**
     * Calculate overhead costs for work order
     */
    private function calculateOverheadCosts(WorkOrder $workOrder): array
    {
        $totalLaborHours = $this->getTotalLaborHours($workOrder);
        $machineHours = $this->getMachineHours($workOrder);
        
        // Get overhead rates (these would come from overhead allocation)
        $variableOverheadRate = 25000; // Per labor hour
        $fixedOverheadRate = 15000; // Per machine hour
        
        $variableOverhead = $totalLaborHours * $variableOverheadRate;
        $fixedOverhead = $machineHours * $fixedOverheadRate;
        
        return [
            'variable_overhead' => [
                'rate_per_labor_hour' => $variableOverheadRate,
                'labor_hours' => $totalLaborHours,
                'total' => $variableOverhead,
            ],
            'fixed_overhead' => [
                'rate_per_machine_hour' => $fixedOverheadRate,
                'machine_hours' => $machineHours,
                'total' => $fixedOverhead,
            ],
            'total' => $variableOverhead + $fixedOverhead,
        ];
    }

    /**
     * Calculate setup costs for work order
     */
    private function calculateSetupCosts(WorkOrder $workOrder): array
    {
        $setupTimeEntries = WorkOrderTimeEntry::where('work_order_id', $workOrder->id)
            ->where('activity_type', 'setup')
            ->get();
            
        $setupCosts = [
            'setup_hours' => $setupTimeEntries->sum('hours_worked'),
            'setup_labor_cost' => 0,
            'setup_material_cost' => 0, // Tools, fixtures, etc.
            'total' => 0,
        ];
        
        foreach ($setupTimeEntries as $entry) {
            $setupCosts['setup_labor_cost'] += $entry->hours_worked * $entry->hourly_rate;
        }
        
        // Add setup material costs (tools, fixtures, etc.)
        $setupCosts['setup_material_cost'] = $setupCosts['setup_hours'] * 10000; // Estimated setup material cost
        
        $setupCosts['total'] = $setupCosts['setup_labor_cost'] + $setupCosts['setup_material_cost'];
        
        return $setupCosts;
    }

    /**
     * Calculate quality costs for work order
     */
    private function calculateQualityCosts(WorkOrder $workOrder): array
    {
        // Quality costs include inspection, rework, scrap
        $qualityCosts = [
            'inspection_cost' => 0,
            'rework_cost' => 0,
            'scrap_cost' => 0,
            'total' => 0,
        ];
        
        // Inspection cost (estimated based on production time)
        $productionHours = $this->getProductionHours($workOrder);
        $qualityCosts['inspection_cost'] = $productionHours * 5000; // Estimated inspection cost per hour
        
        // Rework cost (if any rework was done)
        $reworkHours = WorkOrderTimeEntry::where('work_order_id', $workOrder->id)
            ->where('activity_type', 'rework')
            ->sum('hours_worked');
        $qualityCosts['rework_cost'] = $reworkHours * 60000; // Higher rate for rework
        
        // Scrap cost (based on material waste)
        $materialCosts = $this->calculateMaterialCosts($workOrder);
        $totalWasteCost = collect($materialCosts['details'])->sum('waste_cost');
        $qualityCosts['scrap_cost'] = $totalWasteCost;
        
        $qualityCosts['total'] = $qualityCosts['inspection_cost'] + 
                               $qualityCosts['rework_cost'] + 
                               $qualityCosts['scrap_cost'];
        
        return $qualityCosts;
    }

    /**
     * Record detailed costs in production_costs table
     */
    private function recordDetailedCosts(WorkOrder $workOrder, array $costs): void
    {
        // Record material costs
        foreach ($costs['material_costs']['details'] as $material) {
            ProductionCost::create([
                'client_id' => $workOrder->client_id,
                'production_order_id' => $workOrder->production_order_id,
                'work_order_id' => $workOrder->id,
                'product_id' => $workOrder->productionOrder->product_id,
                'cost_type' => 'material',
                'cost_category' => 'direct',
                'cost_element' => 'raw_material',
                'description' => "Material: {$material['material_name']}",
                'actual_cost' => $material['total_cost'],
                'quantity' => $material['quantity_used'],
                'unit_cost' => $material['unit_cost'],
                'cost_date' => now(),
                'period_month' => now()->month,
                'period_year' => now()->year,
                'created_by' => auth()->id() ?? 1,
            ]);
        }

        // Record labor costs
        foreach ($costs['labor_costs']['details'] as $labor) {
            ProductionCost::create([
                'client_id' => $workOrder->client_id,
                'production_order_id' => $workOrder->production_order_id,
                'work_order_id' => $workOrder->id,
                'product_id' => $workOrder->productionOrder->product_id,
                'cost_type' => 'labor',
                'cost_category' => 'direct',
                'cost_element' => 'direct_labor',
                'description' => "Labor: {$labor['employee_name']} - {$labor['work_date']}",
                'actual_cost' => $labor['total_cost'],
                'quantity' => $labor['hours_worked'],
                'unit_cost' => $labor['hourly_rate'],
                'cost_date' => $labor['work_date'],
                'period_month' => now()->month,
                'period_year' => now()->year,
                'created_by' => auth()->id() ?? 1,
            ]);
        }

        // Record overhead costs
        ProductionCost::create([
            'client_id' => $workOrder->client_id,
            'production_order_id' => $workOrder->production_order_id,
            'work_order_id' => $workOrder->id,
            'product_id' => $workOrder->productionOrder->product_id,
            'cost_type' => 'overhead',
            'cost_category' => 'indirect',
            'cost_element' => 'manufacturing_overhead',
            'description' => 'Manufacturing Overhead Allocation',
            'actual_cost' => $costs['overhead_costs']['total'],
            'quantity' => $costs['labor_costs']['regular_hours'] + $costs['labor_costs']['overtime_hours'],
            'unit_cost' => $costs['overhead_costs']['total'] > 0 && ($costs['labor_costs']['regular_hours'] + $costs['labor_costs']['overtime_hours']) > 0 
                ? $costs['overhead_costs']['total'] / ($costs['labor_costs']['regular_hours'] + $costs['labor_costs']['overtime_hours']) 
                : 0,
            'allocation_base' => 'direct_labor_hours',
            'cost_date' => now(),
            'period_month' => now()->month,
            'period_year' => now()->year,
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    /**
     * Create journal entry for work order completion
     */
    public function createWorkOrderJournalEntry(WorkOrder $workOrder): Journal
    {
        $costs = $this->calculateWorkOrderCosts($workOrder);
        
        $journal = Journal::create([
            'client_id' => $workOrder->client_id,
            'journal_number' => Journal::generateJournalNumber($workOrder->client_id),
            'transaction_date' => now(),
            'description' => "Work Order Completion - {$workOrder->work_order_number}",
            'reference_type' => WorkOrder::class,
            'reference_id' => $workOrder->id,
            'total_amount' => $costs['total_cost'],
            'created_by' => auth()->id() ?? 1,
        ]);

        // Get account codes
        $wipAccount = Account::where('client_id', $workOrder->client_id)
            ->where('account_code', '1132')
            ->first();
            
        $finishedGoodsAccount = Account::where('client_id', $workOrder->client_id)
            ->where('account_code', '1133')
            ->first();

        if ($wipAccount && $finishedGoodsAccount) {
            // Debit: Finished Goods
            JournalEntry::create([
                'client_id' => $workOrder->client_id,
                'journal_id' => $journal->id,
                'account_id' => $finishedGoodsAccount->id,
                'description' => "Work Order Completion - {$workOrder->productionOrder->product->product_name}",
                'debit_amount' => $costs['total_cost'],
                'credit_amount' => 0,
            ]);

            // Credit: Work in Process
            JournalEntry::create([
                'client_id' => $workOrder->client_id,
                'journal_id' => $journal->id,
                'account_id' => $wipAccount->id,
                'description' => "Work Order Completion - {$workOrder->work_order_number}",
                'debit_amount' => 0,
                'credit_amount' => $costs['total_cost'],
            ]);
        }

        return $journal;
    }

    // Helper methods
    private function getTotalLaborHours(WorkOrder $workOrder): float
    {
        return WorkOrderTimeEntry::where('work_order_id', $workOrder->id)
            ->sum('hours_worked');
    }

    private function getMachineHours(WorkOrder $workOrder): float
    {
        if ($workOrder->actual_start_time && $workOrder->actual_end_time) {
            return $workOrder->actual_start_time->diffInHours($workOrder->actual_end_time);
        }
        return 0;
    }

    private function getProductionHours(WorkOrder $workOrder): float
    {
        return WorkOrderTimeEntry::where('work_order_id', $workOrder->id)
            ->where('activity_type', 'production')
            ->sum('hours_worked');
    }
}
