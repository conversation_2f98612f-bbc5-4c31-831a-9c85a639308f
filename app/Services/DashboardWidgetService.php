<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
// use App\Models\SalesOrder; // Disabled - using Transaction model
use App\Models\PurchaseOrder;
use App\Models\ProductionOrder;
use App\Models\FixedAsset;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Inventory;
use App\Models\CashFlow;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

// TEMPORARILY DISABLED - Needs refactoring to use Transaction model instead of SalesOrder
class DashboardWidgetService_DISABLED
{
    /**
     * Get financial summary widgets
     */
    public function getFinancialSummaryWidgets(int $clientId): array
    {
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();
        $previousMonth = now()->subMonth()->startOfMonth();
        $endOfPreviousMonth = now()->subMonth()->endOfMonth();

        // Current month revenue
        $currentRevenue = $this->getRevenueForPeriod($clientId, $currentMonth, $endOfMonth);
        $previousRevenue = $this->getRevenueForPeriod($clientId, $previousMonth, $endOfPreviousMonth);
        $revenueGrowth = $this->calculateGrowthPercentage($currentRevenue, $previousRevenue);

        // Current month expenses
        $currentExpenses = $this->getExpensesForPeriod($clientId, $currentMonth, $endOfMonth);
        $previousExpenses = $this->getExpensesForPeriod($clientId, $previousMonth, $endOfPreviousMonth);
        $expenseGrowth = $this->calculateGrowthPercentage($currentExpenses, $previousExpenses);

        // Net profit
        $currentProfit = $currentRevenue - $currentExpenses;
        $previousProfit = $previousRevenue - $previousExpenses;
        $profitGrowth = $this->calculateGrowthPercentage($currentProfit, $previousProfit);

        // Cash position
        $cashPosition = $this->getCashPosition($clientId);

        return [
            'revenue' => [
                'title' => 'Pendapatan Bulan Ini',
                'value' => $currentRevenue,
                'growth' => $revenueGrowth,
                'trend' => $revenueGrowth >= 0 ? 'up' : 'down',
                'color' => $revenueGrowth >= 0 ? 'success' : 'danger',
                'icon' => 'heroicon-o-banknotes',
            ],
            'expenses' => [
                'title' => 'Beban Bulan Ini',
                'value' => $currentExpenses,
                'growth' => $expenseGrowth,
                'trend' => $expenseGrowth >= 0 ? 'up' : 'down',
                'color' => $expenseGrowth >= 0 ? 'danger' : 'success',
                'icon' => 'heroicon-o-credit-card',
            ],
            'profit' => [
                'title' => 'Laba Bersih Bulan Ini',
                'value' => $currentProfit,
                'growth' => $profitGrowth,
                'trend' => $profitGrowth >= 0 ? 'up' : 'down',
                'color' => $profitGrowth >= 0 ? 'success' : 'danger',
                'icon' => 'heroicon-o-chart-bar',
            ],
            'cash' => [
                'title' => 'Posisi Kas',
                'value' => $cashPosition,
                'growth' => 0, // Could calculate vs previous period
                'trend' => 'neutral',
                'color' => $cashPosition >= 0 ? 'success' : 'danger',
                'icon' => 'heroicon-o-currency-dollar',
            ],
        ];
    }

    /**
     * Get sales performance widgets
     */
    public function getSalesPerformanceWidgets(int $clientId): array
    {
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        // Sales orders this month
        $salesOrders = SalesOrder::where('client_id', $clientId)
            ->whereBetween('order_date', [$currentMonth, $endOfMonth])
            ->get();

        $totalOrders = $salesOrders->count();
        $totalSales = $salesOrders->sum('total_amount');
        $averageOrderValue = $totalOrders > 0 ? $totalSales / $totalOrders : 0;

        // Order status breakdown
        $ordersByStatus = $salesOrders->groupBy('status')->map->count();

        // Top customers
        $topCustomers = $salesOrders->groupBy('customer_id')
            ->map(function ($orders) {
                return [
                    'customer_name' => $orders->first()->customer->customer_name ?? 'Unknown',
                    'total_amount' => $orders->sum('total_amount'),
                    'order_count' => $orders->count(),
                ];
            })
            ->sortByDesc('total_amount')
            ->take(5)
            ->values();

        return [
            'total_orders' => [
                'title' => 'Pesanan Penjualan Bulan Ini',
                'value' => $totalOrders,
                'icon' => 'heroicon-o-shopping-cart',
                'color' => 'primary',
            ],
            'total_sales' => [
                'title' => 'Total Jumlah Penjualan',
                'value' => $totalSales,
                'icon' => 'heroicon-o-banknotes',
                'color' => 'success',
            ],
            'average_order' => [
                'title' => 'Nilai Rata-rata Pesanan',
                'value' => $averageOrderValue,
                'icon' => 'heroicon-o-calculator',
                'color' => 'info',
            ],
            'orders_by_status' => $ordersByStatus,
            'top_customers' => $topCustomers,
        ];
    }

    /**
     * Get production performance widgets
     */
    public function getProductionPerformanceWidgets(int $clientId): array
    {
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $productionOrders = ProductionOrder::where('client_id', $clientId)
            ->whereBetween('created_at', [$currentMonth, $endOfMonth])
            ->get();

        $totalOrders = $productionOrders->count();
        $completedOrders = $productionOrders->where('status', 'completed')->count();
        $totalProduced = $productionOrders->sum('quantity_produced');
        $completionRate = $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0;

        // On-time delivery rate
        $onTimeDeliveries = $productionOrders->filter(function ($order) {
            return $order->completed_at && $order->required_date &&
                $order->completed_at <= $order->required_date;
        })->count();

        $onTimeRate = $completedOrders > 0 ? ($onTimeDeliveries / $completedOrders) * 100 : 0;

        return [
            'total_production_orders' => [
                'title' => 'Order Produksi',
                'value' => $totalOrders,
                'icon' => 'heroicon-o-cog-6-tooth',
                'color' => 'primary',
            ],
            'completion_rate' => [
                'title' => 'Tingkat Penyelesaian',
                'value' => $completionRate,
                'suffix' => '%',
                'icon' => 'heroicon-o-check-circle',
                'color' => $completionRate >= 80 ? 'success' : 'warning',
            ],
            'total_produced' => [
                'title' => 'Unit Diproduksi',
                'value' => $totalProduced,
                'icon' => 'heroicon-o-cube',
                'color' => 'info',
            ],
            'on_time_rate' => [
                'title' => 'Pengiriman Tepat Waktu',
                'value' => $onTimeRate,
                'suffix' => '%',
                'icon' => 'heroicon-o-clock',
                'color' => $onTimeRate >= 90 ? 'success' : 'warning',
            ],
        ];
    }

    /**
     * Get inventory status widgets
     */
    public function getInventoryStatusWidgets(int $clientId): array
    {
        $inventories = Inventory::where('client_id', $clientId)->get();

        $totalItems = $inventories->count();
        $totalValue = $inventories->sum(function ($inventory) {
            return $inventory->current_stock * $inventory->average_cost;
        });

        $lowStockItems = $inventories->filter(function ($inventory) {
            return $inventory->current_stock <= $inventory->reorder_level;
        })->count();

        $outOfStockItems = $inventories->where('current_stock', '<=', 0)->count();

        $stockTurnover = $this->calculateStockTurnover($clientId);

        return [
            'total_items' => [
                'title' => 'Total Item Inventori',
                'value' => $totalItems,
                'icon' => 'heroicon-o-cube',
                'color' => 'primary',
            ],
            'total_value' => [
                'title' => 'Total Nilai Inventori',
                'value' => $totalValue,
                'icon' => 'heroicon-o-banknotes',
                'color' => 'success',
            ],
            'low_stock' => [
                'title' => 'Item Stok Rendah',
                'value' => $lowStockItems,
                'icon' => 'heroicon-o-exclamation-triangle',
                'color' => $lowStockItems > 0 ? 'warning' : 'success',
            ],
            'out_of_stock' => [
                'title' => 'Item Stok Habis',
                'value' => $outOfStockItems,
                'icon' => 'heroicon-o-x-circle',
                'color' => $outOfStockItems > 0 ? 'danger' : 'success',
            ],
            'stock_turnover' => [
                'title' => 'Rasio Perputaran Stok',
                'value' => $stockTurnover,
                'icon' => 'heroicon-o-arrow-path',
                'color' => 'info',
            ],
        ];
    }

    /**
     * Get asset management widgets
     */
    public function getAssetManagementWidgets(int $clientId): array
    {
        $assets = FixedAsset::where('client_id', $clientId)->get();

        $totalAssets = $assets->count();
        $totalValue = $assets->sum('total_cost');
        $totalBookValue = $assets->sum('book_value');
        $depreciationPercentage = $totalValue > 0 ? (($totalValue - $totalBookValue) / $totalValue) * 100 : 0;

        $maintenanceDue = $assets->filter(function ($asset) {
            return $asset->next_maintenance_date &&
                $asset->next_maintenance_date <= now()->addDays(30);
        })->count();

        return [
            'total_assets' => [
                'title' => 'Total Aset Tetap',
                'value' => $totalAssets,
                'icon' => 'heroicon-o-building-office',
                'color' => 'primary',
            ],
            'asset_value' => [
                'title' => 'Total Nilai Aset',
                'value' => $totalValue,
                'icon' => 'heroicon-o-banknotes',
                'color' => 'success',
            ],
            'book_value' => [
                'title' => 'Nilai Buku Saat Ini',
                'value' => $totalBookValue,
                'icon' => 'heroicon-o-calculator',
                'color' => 'info',
            ],
            'depreciation' => [
                'title' => 'Tingkat Depresiasi',
                'value' => $depreciationPercentage,
                'suffix' => '%',
                'icon' => 'heroicon-o-chart-bar',
                'color' => 'warning',
            ],
            'maintenance_due' => [
                'title' => 'Pemeliharaan Jatuh Tempo (30 hari)',
                'value' => $maintenanceDue,
                'icon' => 'heroicon-o-wrench-screwdriver',
                'color' => $maintenanceDue > 0 ? 'warning' : 'success',
            ],
        ];
    }

    /**
     * Get chart data for revenue trend
     */
    public function getRevenueTrendChart(int $clientId, int $months = 12): array
    {
        $data = [];
        $labels = [];

        for ($i = $months - 1; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            $revenue = $this->getRevenueForPeriod($clientId, $startOfMonth, $endOfMonth);

            $labels[] = $date->format('M Y');
            $data[] = $revenue;
        }

        return [
            'labels' => $labels,
            'data' => $data,
        ];
    }

    /**
     * Get chart data for expense breakdown
     */
    public function getExpenseBreakdownChart(int $clientId): array
    {
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $expenseCategories = [
            '6100' => 'Cost of Goods Sold',
            '6200' => 'Operating Expenses',
            '6300' => 'Administrative Expenses',
            '6400' => 'Marketing Expenses',
            '6500' => 'Financial Expenses',
        ];

        $data = [];
        $labels = [];

        foreach ($expenseCategories as $code => $label) {
            $amount = $this->getAccountGroupTotal($clientId, $code, $currentMonth, $endOfMonth);
            if ($amount > 0) {
                $labels[] = $label;
                $data[] = $amount;
            }
        }

        return [
            'labels' => $labels,
            'data' => $data,
        ];
    }

    // Helper methods
    private function getRevenueForPeriod(int $clientId, Carbon $startDate, Carbon $endDate): float
    {
        return $this->getAccountGroupTotal($clientId, '4', $startDate, $endDate);
    }

    private function getExpensesForPeriod(int $clientId, Carbon $startDate, Carbon $endDate): float
    {
        return $this->getAccountGroupTotal($clientId, '6', $startDate, $endDate);
    }

    private function getCashPosition(int $clientId): float
    {
        return $this->getAccountGroupTotal($clientId, '1111', null, now());
    }

    private function getAccountGroupTotal(int $clientId, string $accountPrefix, ?Carbon $startDate, Carbon $endDate): float
    {
        $query = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $endDate);
                if ($startDate) {
                    $q->where('journal_date', '>=', $startDate);
                }
            });

        $totalDebits = $query->sum('debit');
        $totalCredits = $query->sum('credit');

        // Return based on account type
        if (in_array($accountPrefix, ['4', '7'])) { // Revenue
            return $totalCredits - $totalDebits;
        } else { // Assets, Expenses
            return $totalDebits - $totalCredits;
        }
    }

    private function calculateGrowthPercentage(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / abs($previous)) * 100;
    }

    private function calculateStockTurnover(int $clientId): float
    {
        // Simplified stock turnover calculation
        // COGS / Average Inventory Value
        $currentYear = now()->startOfYear();
        $endOfYear = now()->endOfYear();

        $cogs = $this->getAccountGroupTotal($clientId, '5', $currentYear, $endOfYear);

        $inventories = Inventory::where('client_id', $clientId)->get();
        $averageInventoryValue = $inventories->avg(function ($inventory) {
            return $inventory->current_stock * $inventory->average_cost;
        });

        return $averageInventoryValue > 0 ? $cogs / $averageInventoryValue : 0;
    }
}
