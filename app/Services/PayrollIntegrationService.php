<?php

namespace App\Services;

use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Transaction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PayrollIntegrationService
{
    protected array $payrollConfigs;

    public function __construct()
    {
        $this->payrollConfigs = config('payroll.providers', []);
    }

    /**
     * Sync payroll data and create journal entries.
     */
    public function syncPayrollData(int $clientId, ?Carbon $fromDate = null, ?Carbon $toDate = null): array
    {
        try {
            $fromDate = $fromDate ?? now()->subMonth()->startOfMonth();
            $toDate = $toDate ?? now()->subMonth()->endOfMonth();

            Log::info("Starting payroll sync for client {$clientId}", [
                'from_date' => $fromDate->toDateString(),
                'to_date' => $toDate->toDateString(),
            ]);

            // Get payroll data from external system
            $payrollData = $this->fetchPayrollData($clientId, $fromDate, $toDate);
            
            $syncResults = [
                'total_payrolls' => count($payrollData),
                'processed' => 0,
                'errors' => 0,
                'created_journals' => [],
                'created_transactions' => [],
                'errors_detail' => [],
            ];

            foreach ($payrollData as $payroll) {
                try {
                    // Process each payroll period
                    $result = $this->processPayrollData($clientId, $payroll);
                    
                    if ($result['success']) {
                        $syncResults['processed']++;
                        $syncResults['created_journals'][] = $result['journal_id'];
                        $syncResults['created_transactions'] = array_merge(
                            $syncResults['created_transactions'], 
                            $result['transaction_ids']
                        );
                    } else {
                        $syncResults['errors']++;
                        $syncResults['errors_detail'][] = $result['error'];
                    }
                } catch (\Exception $e) {
                    $syncResults['errors']++;
                    $syncResults['errors_detail'][] = [
                        'payroll_id' => $payroll['id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ];
                }
            }

            Log::info("Payroll sync completed", $syncResults);
            return $syncResults;

        } catch (\Exception $e) {
            Log::error("Payroll sync failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fetch payroll data from external system.
     */
    protected function fetchPayrollData(int $clientId, Carbon $fromDate, Carbon $toDate): array
    {
        $provider = $this->getPayrollProvider();
        
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $provider['api_key'],
            'Content-Type' => 'application/json',
        ])->get($provider['base_url'] . '/payroll', [
            'client_id' => $clientId,
            'from_date' => $fromDate->toDateString(),
            'to_date' => $toDate->toDateString(),
            'status' => 'approved',
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to fetch payroll data: " . $response->body());
        }

        return $response->json('data', []);
    }

    /**
     * Process individual payroll data.
     */
    protected function processPayrollData(int $clientId, array $payrollData): array
    {
        try {
            // Check if payroll already processed
            $existingJournal = Journal::where('client_id', $clientId)
                ->where('reference_type', 'payroll')
                ->where('reference_number', $payrollData['payroll_number'])
                ->first();

            if ($existingJournal) {
                return [
                    'success' => true,
                    'journal_id' => $existingJournal->id,
                    'transaction_ids' => [],
                    'message' => 'Payroll already processed',
                ];
            }

            // Create journal for payroll
            $journal = $this->createPayrollJournal($clientId, $payrollData);

            // Create journal entries
            $transactionIds = $this->createPayrollJournalEntries($journal, $payrollData);

            return [
                'success' => true,
                'journal_id' => $journal->id,
                'transaction_ids' => $transactionIds,
                'message' => 'Payroll processed successfully',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create payroll journal.
     */
    protected function createPayrollJournal(int $clientId, array $payrollData): Journal
    {
        return Journal::create([
            'client_id' => $clientId,
            'journal_number' => 'PAYROLL-' . $payrollData['payroll_number'],
            'transaction_date' => Carbon::parse($payrollData['pay_date']),
            'reference_type' => 'payroll',
            'reference_id' => $payrollData['id'],
            'reference_number' => $payrollData['payroll_number'],
            'description' => 'Payroll - ' . $payrollData['period_name'],
            'total_amount' => $payrollData['total_gross_pay'],
        ]);
    }

    /**
     * Create payroll journal entries.
     */
    protected function createPayrollJournalEntries(Journal $journal, array $payrollData): array
    {
        $transactionIds = [];
        
        // Get accounts
        $accounts = $this->getPayrollAccounts($journal->client_id);

        // 1. Salary Expense (Debit)
        $salaryEntry = JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $accounts['salary_expense']->id,
            'description' => 'Salary expense - ' . $payrollData['period_name'],
            'debit' => $payrollData['total_gross_pay'],
            'credit' => 0,
        ]);

        // Create transaction for salary expense
        $salaryTransaction = Transaction::create([
            'client_id' => $journal->client_id,
            'account_id' => $accounts['salary_expense']->id,
            'transaction_date' => $journal->transaction_date,
            'type' => 'expense',
            'amount' => $payrollData['total_gross_pay'],
            'description' => 'Salary expense - ' . $payrollData['period_name'],
            'reference_number' => $journal->journal_number,
            'status' => 'completed',
        ]);
        $transactionIds[] = $salaryTransaction->id;

        // 2. Tax Withholding (Credit)
        if ($payrollData['total_tax_withheld'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['tax_payable']->id,
                'description' => 'Tax withholding - ' . $payrollData['period_name'],
                'debit' => 0,
                'credit' => $payrollData['total_tax_withheld'],
            ]);
        }

        // 3. Social Security Contributions (Credit)
        if ($payrollData['total_social_security'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['social_security_payable']->id,
                'description' => 'Social security contributions - ' . $payrollData['period_name'],
                'debit' => 0,
                'credit' => $payrollData['total_social_security'],
            ]);
        }

        // 4. Other Deductions (Credit)
        if ($payrollData['total_other_deductions'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['other_payable']->id,
                'description' => 'Other deductions - ' . $payrollData['period_name'],
                'debit' => 0,
                'credit' => $payrollData['total_other_deductions'],
            ]);
        }

        // 5. Net Pay (Credit to Cash/Bank)
        $netPay = $payrollData['total_gross_pay'] - 
                  $payrollData['total_tax_withheld'] - 
                  $payrollData['total_social_security'] - 
                  $payrollData['total_other_deductions'];

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $accounts['cash']->id,
            'description' => 'Net pay - ' . $payrollData['period_name'],
            'debit' => 0,
            'credit' => $netPay,
        ]);

        // Create transaction for net pay
        $netPayTransaction = Transaction::create([
            'client_id' => $journal->client_id,
            'account_id' => $accounts['cash']->id,
            'transaction_date' => $journal->transaction_date,
            'type' => 'expense',
            'amount' => $netPay,
            'description' => 'Net pay - ' . $payrollData['period_name'],
            'reference_number' => $journal->journal_number,
            'status' => 'completed',
        ]);
        $transactionIds[] = $netPayTransaction->id;

        return $transactionIds;
    }

    /**
     * Get payroll-related accounts.
     */
    protected function getPayrollAccounts(int $clientId): array
    {
        $accounts = [
            'salary_expense' => Account::where('client_id', $clientId)->where('account_code', '6100')->first(),
            'tax_payable' => Account::where('client_id', $clientId)->where('account_code', '2300')->first(),
            'social_security_payable' => Account::where('client_id', $clientId)->where('account_code', '2310')->first(),
            'other_payable' => Account::where('client_id', $clientId)->where('account_code', '2320')->first(),
            'cash' => Account::where('client_id', $clientId)->where('account_code', '1100')->first(),
        ];

        // Check if all required accounts exist
        foreach ($accounts as $type => $account) {
            if (!$account) {
                throw new \Exception("Required payroll account not found: {$type}");
            }
        }

        return $accounts;
    }

    /**
     * Get payroll provider configuration.
     */
    protected function getPayrollProvider(): array
    {
        $provider = config('payroll.default_provider');
        
        if (!isset($this->payrollConfigs[$provider])) {
            throw new \Exception("Payroll provider configuration not found: {$provider}");
        }

        return $this->payrollConfigs[$provider];
    }

    /**
     * Test payroll system connection.
     */
    public function testPayrollConnection(): array
    {
        try {
            $provider = $this->getPayrollProvider();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $provider['api_key'],
            ])->get($provider['base_url'] . '/status');

            if (!$response->successful()) {
                return [
                    'connected' => false,
                    'error' => 'Failed to connect to payroll system',
                ];
            }

            return [
                'connected' => true,
                'provider' => $provider['name'],
                'last_tested' => now()->toDateTimeString(),
                'status' => $response->json('status'),
            ];

        } catch (\Exception $e) {
            return [
                'connected' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payroll summary for a period.
     */
    public function getPayrollSummary(int $clientId, Carbon $fromDate, Carbon $toDate): array
    {
        $journals = Journal::where('client_id', $clientId)
            ->where('reference_type', 'payroll')
            ->whereBetween('transaction_date', [$fromDate, $toDate])
            ->with('journalEntries.account')
            ->get();

        $summary = [
            'total_payrolls' => $journals->count(),
            'total_gross_pay' => 0,
            'total_net_pay' => 0,
            'total_tax_withheld' => 0,
            'total_social_security' => 0,
            'total_other_deductions' => 0,
            'payroll_details' => [],
        ];

        foreach ($journals as $journal) {
            $grossPay = $journal->journalEntries->where('account.account_code', '6100')->sum('debit');
            $taxWithheld = $journal->journalEntries->where('account.account_code', '2300')->sum('credit');
            $socialSecurity = $journal->journalEntries->where('account.account_code', '2310')->sum('credit');
            $otherDeductions = $journal->journalEntries->where('account.account_code', '2320')->sum('credit');
            $netPay = $journal->journalEntries->where('account.account_code', '1100')->sum('credit');

            $summary['total_gross_pay'] += $grossPay;
            $summary['total_net_pay'] += $netPay;
            $summary['total_tax_withheld'] += $taxWithheld;
            $summary['total_social_security'] += $socialSecurity;
            $summary['total_other_deductions'] += $otherDeductions;

            $summary['payroll_details'][] = [
                'journal_number' => $journal->journal_number,
                'transaction_date' => $journal->transaction_date->toDateString(),
                'description' => $journal->description,
                'gross_pay' => $grossPay,
                'net_pay' => $netPay,
                'tax_withheld' => $taxWithheld,
                'social_security' => $socialSecurity,
                'other_deductions' => $otherDeductions,
            ];
        }

        return $summary;
    }
}
