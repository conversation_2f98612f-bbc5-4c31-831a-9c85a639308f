<?php

namespace App\Services;

use App\Models\AutomatedJournalEntry;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use App\Models\FixedAsset;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutomatedJournalService
{
    /**
     * Execute all due automated journal entries.
     */
    public function executeAllDue(): array
    {
        $results = [];
        $dueEntries = AutomatedJournalEntry::dueForExecution()->get();

        foreach ($dueEntries as $entry) {
            try {
                $result = $this->executeEntry($entry);
                $results[] = [
                    'entry' => $entry,
                    'success' => true,
                    'journal' => $result,
                ];
            } catch (\Exception $e) {
                Log::error("Failed to execute automated journal entry {$entry->id}: " . $e->getMessage());
                $results[] = [
                    'entry' => $entry,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Execute a specific automated journal entry.
     */
    public function executeEntry(AutomatedJournalEntry $entry): Journal
    {
        return DB::transaction(function () use ($entry) {
            $journal = match ($entry->entry_type) {
                AutomatedJournalEntry::TYPE_DEPRECIATION => $this->createDepreciationEntry($entry),
                AutomatedJournalEntry::TYPE_ACCRUAL => $this->createAccrualEntry($entry),
                AutomatedJournalEntry::TYPE_ALLOCATION => $this->createAllocationEntry($entry),
                AutomatedJournalEntry::TYPE_RECURRING => $this->createRecurringEntry($entry),
                default => throw new \InvalidArgumentException("Unknown entry type: {$entry->entry_type}"),
            };

            $entry->markAsExecuted();

            return $journal;
        });
    }

    /**
     * Create depreciation journal entry.
     */
    protected function createDepreciationEntry(AutomatedJournalEntry $entry): Journal
    {
        $config = $entry->configuration;
        $asset = FixedAsset::find($entry->reference_id);

        if (!$asset) {
            throw new \Exception("Fixed asset not found for depreciation entry");
        }

        // Calculate monthly depreciation
        $monthlyDepreciation = $asset->calculateMonthlyDepreciation();

        $journal = Journal::create([
            'client_id' => $entry->client_id,
            'automated_entry_id' => $entry->id,
            'journal_number' => $this->generateJournalNumber($entry),
            'transaction_date' => now()->toDateString(),
            'description' => "Penyusutan {$asset->name} - " . now()->format('F Y'),
            'reference_type' => 'automated_depreciation',
            'reference_id' => $asset->id,
            'created_by' => $entry->created_by,
        ]);

        // Debit: Depreciation Expense
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $config['depreciation_expense_account_id'],
            'description' => "Beban penyusutan {$asset->name}",
            'debit' => $monthlyDepreciation,
            'credit' => 0,
        ]);

        // Credit: Accumulated Depreciation
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $config['accumulated_depreciation_account_id'],
            'description' => "Akumulasi penyusutan {$asset->name}",
            'debit' => 0,
            'credit' => $monthlyDepreciation,
        ]);

        return $journal;
    }

    /**
     * Create accrual journal entry.
     */
    protected function createAccrualEntry(AutomatedJournalEntry $entry): Journal
    {
        $config = $entry->configuration;
        $amount = $entry->amount ?: $config['amount'];

        $journal = Journal::create([
            'client_id' => $entry->client_id,
            'automated_entry_id' => $entry->id,
            'journal_number' => $this->generateJournalNumber($entry),
            'transaction_date' => now()->toDateString(),
            'description' => $entry->description ?: "Jurnal akrual - " . now()->format('F Y'),
            'reference_type' => 'automated_accrual',
            'created_by' => $entry->created_by,
        ]);

        // Create journal entries based on configuration
        foreach ($config['entries'] as $entryConfig) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $entryConfig['account_id'],
                'description' => $entryConfig['description'],
                'debit' => $entryConfig['type'] === 'debit' ? $amount : 0,
                'credit' => $entryConfig['type'] === 'credit' ? $amount : 0,
            ]);
        }

        return $journal;
    }

    /**
     * Create allocation journal entry.
     */
    protected function createAllocationEntry(AutomatedJournalEntry $entry): Journal
    {
        $config = $entry->configuration;
        $totalAmount = $entry->amount ?: $config['total_amount'];

        $journal = Journal::create([
            'client_id' => $entry->client_id,
            'automated_entry_id' => $entry->id,
            'journal_number' => $this->generateJournalNumber($entry),
            'transaction_date' => now()->toDateString(),
            'description' => $entry->description ?: "Alokasi biaya - " . now()->format('F Y'),
            'reference_type' => 'automated_allocation',
            'created_by' => $entry->created_by,
        ]);

        // Allocate based on percentages or fixed amounts
        foreach ($config['allocations'] as $allocation) {
            $amount = $allocation['type'] === 'percentage' 
                ? ($totalAmount * $allocation['value'] / 100)
                : $allocation['value'];

            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $allocation['account_id'],
                'description' => $allocation['description'],
                'debit' => $allocation['side'] === 'debit' ? $amount : 0,
                'credit' => $allocation['side'] === 'credit' ? $amount : 0,
            ]);
        }

        return $journal;
    }

    /**
     * Create recurring journal entry.
     */
    protected function createRecurringEntry(AutomatedJournalEntry $entry): Journal
    {
        $config = $entry->configuration;

        $journal = Journal::create([
            'client_id' => $entry->client_id,
            'automated_entry_id' => $entry->id,
            'journal_number' => $this->generateJournalNumber($entry),
            'transaction_date' => now()->toDateString(),
            'description' => $entry->description ?: "Jurnal berulang - " . now()->format('F Y'),
            'reference_type' => 'automated_recurring',
            'created_by' => $entry->created_by,
        ]);

        // Create predefined journal entries
        foreach ($config['entries'] as $entryConfig) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $entryConfig['account_id'],
                'description' => $entryConfig['description'],
                'debit' => $entryConfig['debit'] ?? 0,
                'credit' => $entryConfig['credit'] ?? 0,
            ]);
        }

        return $journal;
    }

    /**
     * Generate journal number for automated entries.
     */
    protected function generateJournalNumber(AutomatedJournalEntry $entry): string
    {
        $prefix = match ($entry->entry_type) {
            AutomatedJournalEntry::TYPE_DEPRECIATION => 'DEP',
            AutomatedJournalEntry::TYPE_ACCRUAL => 'ACR',
            AutomatedJournalEntry::TYPE_ALLOCATION => 'ALL',
            AutomatedJournalEntry::TYPE_RECURRING => 'REC',
            default => 'AUTO',
        };

        $year = now()->format('Y');
        $month = now()->format('m');
        
        $sequence = Journal::where('client_id', $entry->client_id)
            ->where('journal_number', 'like', "{$prefix}{$year}{$month}%")
            ->count() + 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Preview what would be created for an automated entry.
     */
    public function previewEntry(AutomatedJournalEntry $entry): array
    {
        return match ($entry->entry_type) {
            AutomatedJournalEntry::TYPE_DEPRECIATION => $this->previewDepreciationEntry($entry),
            AutomatedJournalEntry::TYPE_ACCRUAL => $this->previewAccrualEntry($entry),
            AutomatedJournalEntry::TYPE_ALLOCATION => $this->previewAllocationEntry($entry),
            AutomatedJournalEntry::TYPE_RECURRING => $this->previewRecurringEntry($entry),
            default => [],
        };
    }

    /**
     * Preview depreciation entry.
     */
    protected function previewDepreciationEntry(AutomatedJournalEntry $entry): array
    {
        $config = $entry->configuration;
        $asset = FixedAsset::find($entry->reference_id);

        if (!$asset) {
            return ['error' => 'Fixed asset not found'];
        }

        $monthlyDepreciation = $asset->calculateMonthlyDepreciation();

        return [
            'description' => "Penyusutan {$asset->name} - " . now()->format('F Y'),
            'entries' => [
                [
                    'account' => Account::find($config['depreciation_expense_account_id'])?->name,
                    'description' => "Beban penyusutan {$asset->name}",
                    'debit' => $monthlyDepreciation,
                    'credit' => 0,
                ],
                [
                    'account' => Account::find($config['accumulated_depreciation_account_id'])?->name,
                    'description' => "Akumulasi penyusutan {$asset->name}",
                    'debit' => 0,
                    'credit' => $monthlyDepreciation,
                ],
            ],
        ];
    }

    /**
     * Preview accrual entry.
     */
    protected function previewAccrualEntry(AutomatedJournalEntry $entry): array
    {
        $config = $entry->configuration;
        $amount = $entry->amount ?: $config['amount'];

        $entries = [];
        foreach ($config['entries'] as $entryConfig) {
            $entries[] = [
                'account' => Account::find($entryConfig['account_id'])?->name,
                'description' => $entryConfig['description'],
                'debit' => $entryConfig['type'] === 'debit' ? $amount : 0,
                'credit' => $entryConfig['type'] === 'credit' ? $amount : 0,
            ];
        }

        return [
            'description' => $entry->description ?: "Jurnal akrual - " . now()->format('F Y'),
            'entries' => $entries,
        ];
    }

    /**
     * Preview allocation entry.
     */
    protected function previewAllocationEntry(AutomatedJournalEntry $entry): array
    {
        $config = $entry->configuration;
        $totalAmount = $entry->amount ?: $config['total_amount'];

        $entries = [];
        foreach ($config['allocations'] as $allocation) {
            $amount = $allocation['type'] === 'percentage' 
                ? ($totalAmount * $allocation['value'] / 100)
                : $allocation['value'];

            $entries[] = [
                'account' => Account::find($allocation['account_id'])?->name,
                'description' => $allocation['description'],
                'debit' => $allocation['side'] === 'debit' ? $amount : 0,
                'credit' => $allocation['side'] === 'credit' ? $amount : 0,
            ];
        }

        return [
            'description' => $entry->description ?: "Alokasi biaya - " . now()->format('F Y'),
            'entries' => $entries,
        ];
    }

    /**
     * Preview recurring entry.
     */
    protected function previewRecurringEntry(AutomatedJournalEntry $entry): array
    {
        $config = $entry->configuration;

        $entries = [];
        foreach ($config['entries'] as $entryConfig) {
            $entries[] = [
                'account' => Account::find($entryConfig['account_id'])?->name,
                'description' => $entryConfig['description'],
                'debit' => $entryConfig['debit'] ?? 0,
                'credit' => $entryConfig['credit'] ?? 0,
            ];
        }

        return [
            'description' => $entry->description ?: "Jurnal berulang - " . now()->format('F Y'),
            'entries' => $entries,
        ];
    }
}
