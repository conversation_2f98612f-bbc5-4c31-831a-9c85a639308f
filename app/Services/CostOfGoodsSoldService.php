<?php

namespace App\Services;

use App\Models\ProductionCost;
use App\Models\ProductionOrder;
use App\Models\WorkOrder;
use App\Models\BillOfMaterial;
use App\Models\Product;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\Journal;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CostOfGoodsSoldService
{
    /**
     * Calculate Cost of Goods Sold for a production order
     */
    public function calculateCOGS(ProductionOrder $productionOrder): array
    {
        return DB::transaction(function () use ($productionOrder) {
            $costs = [
                'material_costs' => $this->calculateMaterialCosts($productionOrder),
                'labor_costs' => $this->calculateLaborCosts($productionOrder),
                'overhead_costs' => $this->calculateOverheadCosts($productionOrder),
            ];

            $totalCOGS = array_sum($costs);
            
            // Update production order with calculated costs
            $productionOrder->update([
                'material_cost' => $costs['material_costs'],
                'labor_cost' => $costs['labor_costs'],
                'overhead_cost' => $costs['overhead_costs'],
                'total_cost' => $totalCOGS,
                'unit_cost' => $productionOrder->quantity_produced > 0 
                    ? $totalCOGS / $productionOrder->quantity_produced 
                    : 0,
            ]);

            return [
                'production_order_id' => $productionOrder->id,
                'quantity_produced' => $productionOrder->quantity_produced,
                'costs' => $costs,
                'total_cogs' => $totalCOGS,
                'unit_cost' => $productionOrder->unit_cost,
                'cost_breakdown' => $this->getCostBreakdown($productionOrder),
            ];
        });
    }

    /**
     * Calculate material costs based on BOM and actual usage
     */
    private function calculateMaterialCosts(ProductionOrder $productionOrder): float
    {
        $totalMaterialCost = 0;
        $bom = $productionOrder->product->billOfMaterial;
        
        if (!$bom) {
            return 0;
        }

        foreach ($bom->items as $bomItem) {
            $requiredQuantity = ($bomItem->quantity * $productionOrder->quantity_to_produce) / $bom->quantity_produced;
            
            // Get actual usage from raw material usage records
            $actualUsage = $productionOrder->rawMaterialUsages()
                ->where('material_id', $bomItem->material_id)
                ->sum('quantity_used');
                
            $usedQuantity = $actualUsage > 0 ? $actualUsage : $requiredQuantity;
            $materialCost = $usedQuantity * $bomItem->material->standard_cost;
            
            $totalMaterialCost += $materialCost;
            
            // Record material cost
            ProductionCost::create([
                'client_id' => $productionOrder->client_id,
                'production_order_id' => $productionOrder->id,
                'product_id' => $productionOrder->product_id,
                'cost_type' => 'material',
                'cost_category' => 'direct',
                'cost_element' => 'raw_material',
                'description' => "Material: {$bomItem->material->product_name}",
                'standard_cost' => $requiredQuantity * $bomItem->material->standard_cost,
                'actual_cost' => $materialCost,
                'variance_amount' => $materialCost - ($requiredQuantity * $bomItem->material->standard_cost),
                'quantity' => $usedQuantity,
                'unit_cost' => $bomItem->material->standard_cost,
                'cost_date' => now(),
                'period_month' => now()->month,
                'period_year' => now()->year,
                'created_by' => auth()->id() ?? 1,
            ]);
        }

        return $totalMaterialCost;
    }

    /**
     * Calculate labor costs from work order time entries
     */
    private function calculateLaborCosts(ProductionOrder $productionOrder): float
    {
        $totalLaborCost = 0;
        
        foreach ($productionOrder->workOrders as $workOrder) {
            $laborHours = $workOrder->timeEntries->sum('hours_worked');
            $laborRate = $workOrder->workCenter->labor_rate_per_hour ?? 50000; // Default rate
            $laborCost = $laborHours * $laborRate;
            
            $totalLaborCost += $laborCost;
            
            // Record labor cost
            ProductionCost::create([
                'client_id' => $productionOrder->client_id,
                'production_order_id' => $productionOrder->id,
                'work_order_id' => $workOrder->id,
                'product_id' => $productionOrder->product_id,
                'cost_type' => 'labor',
                'cost_category' => 'direct',
                'cost_element' => 'direct_labor',
                'description' => "Direct Labor - {$workOrder->workCenter->center_name}",
                'actual_cost' => $laborCost,
                'quantity' => $laborHours,
                'unit_cost' => $laborRate,
                'cost_date' => now(),
                'period_month' => now()->month,
                'period_year' => now()->year,
                'created_by' => auth()->id() ?? 1,
            ]);
        }

        return $totalLaborCost;
    }

    /**
     * Calculate overhead costs using allocation methods
     */
    private function calculateOverheadCosts(ProductionOrder $productionOrder): float
    {
        $totalOverheadCost = 0;
        
        // Variable overhead allocation (based on direct labor hours)
        $totalLaborHours = $productionOrder->workOrders->sum(function ($workOrder) {
            return $workOrder->timeEntries->sum('hours_worked');
        });
        
        $variableOverheadRate = $this->getVariableOverheadRate($productionOrder->client_id);
        $variableOverhead = $totalLaborHours * $variableOverheadRate;
        $totalOverheadCost += $variableOverhead;
        
        // Fixed overhead allocation (based on production units)
        $fixedOverheadRate = $this->getFixedOverheadRate($productionOrder->client_id);
        $fixedOverhead = $productionOrder->quantity_produced * $fixedOverheadRate;
        $totalOverheadCost += $fixedOverhead;
        
        // Record variable overhead
        ProductionCost::create([
            'client_id' => $productionOrder->client_id,
            'production_order_id' => $productionOrder->id,
            'product_id' => $productionOrder->product_id,
            'cost_type' => 'overhead',
            'cost_category' => 'indirect',
            'cost_element' => 'variable_overhead',
            'description' => 'Variable Manufacturing Overhead',
            'actual_cost' => $variableOverhead,
            'quantity' => $totalLaborHours,
            'unit_cost' => $variableOverheadRate,
            'allocation_base' => 'direct_labor_hours',
            'allocation_rate' => $variableOverheadRate,
            'cost_date' => now(),
            'period_month' => now()->month,
            'period_year' => now()->year,
            'created_by' => auth()->id() ?? 1,
        ]);
        
        // Record fixed overhead
        ProductionCost::create([
            'client_id' => $productionOrder->client_id,
            'production_order_id' => $productionOrder->id,
            'product_id' => $productionOrder->product_id,
            'cost_type' => 'overhead',
            'cost_category' => 'indirect',
            'cost_element' => 'fixed_overhead',
            'description' => 'Fixed Manufacturing Overhead',
            'actual_cost' => $fixedOverhead,
            'quantity' => $productionOrder->quantity_produced,
            'unit_cost' => $fixedOverheadRate,
            'allocation_base' => 'production_units',
            'allocation_rate' => $fixedOverheadRate,
            'cost_date' => now(),
            'period_month' => now()->month,
            'period_year' => now()->year,
            'created_by' => auth()->id() ?? 1,
        ]);

        return $totalOverheadCost;
    }

    /**
     * Create journal entries for COGS
     */
    public function createCOGSJournalEntry(ProductionOrder $productionOrder): Journal
    {
        $journal = Journal::create([
            'client_id' => $productionOrder->client_id,
            'journal_number' => Journal::generateJournalNumber($productionOrder->client_id),
            'transaction_date' => now(),
            'description' => "COGS for Production Order {$productionOrder->order_number}",
            'reference_type' => ProductionOrder::class,
            'reference_id' => $productionOrder->id,
            'total_amount' => $productionOrder->total_cost,
            'created_by' => auth()->id() ?? 1,
        ]);

        // Get account codes
        $finishedGoodsAccount = Account::where('client_id', $productionOrder->client_id)
            ->where('account_code', '1133')
            ->first();
            
        $cogsAccount = Account::where('client_id', $productionOrder->client_id)
            ->where('account_code', '5100')
            ->first();

        if ($finishedGoodsAccount && $cogsAccount) {
            // Debit: Finished Goods Inventory
            JournalEntry::create([
                'client_id' => $productionOrder->client_id,
                'journal_id' => $journal->id,
                'account_id' => $finishedGoodsAccount->id,
                'description' => "Finished Goods - {$productionOrder->product->product_name}",
                'debit_amount' => $productionOrder->total_cost,
                'credit_amount' => 0,
            ]);

            // Credit: Work in Process / Raw Materials (detailed breakdown)
            $this->createDetailedCreditEntries($journal, $productionOrder);
        }

        return $journal;
    }

    /**
     * Get cost breakdown for analysis
     */
    private function getCostBreakdown(ProductionOrder $productionOrder): array
    {
        $costs = ProductionCost::where('production_order_id', $productionOrder->id)->get();
        
        return [
            'material_breakdown' => $costs->where('cost_type', 'material')->groupBy('cost_element'),
            'labor_breakdown' => $costs->where('cost_type', 'labor')->groupBy('cost_element'),
            'overhead_breakdown' => $costs->where('cost_type', 'overhead')->groupBy('cost_element'),
            'cost_per_unit' => $productionOrder->unit_cost,
            'total_variance' => $costs->sum('variance_amount'),
        ];
    }

    private function getVariableOverheadRate(int $clientId): float
    {
        // This would typically be calculated from budget or historical data
        // For now, return a default rate
        return 25000; // Rp 25,000 per direct labor hour
    }

    private function getFixedOverheadRate(int $clientId): float
    {
        // This would typically be calculated from budget or historical data
        // For now, return a default rate
        return 5000; // Rp 5,000 per unit produced
    }

    private function createDetailedCreditEntries(Journal $journal, ProductionOrder $productionOrder): void
    {
        $costs = ProductionCost::where('production_order_id', $productionOrder->id)->get();
        
        foreach ($costs->groupBy('cost_type') as $costType => $typeCosts) {
            $totalAmount = $typeCosts->sum('actual_cost');
            
            $accountCode = match($costType) {
                'material' => '1132001', // WIP - Bahan Baku
                'labor' => '1132002',    // WIP - Tenaga Kerja
                'overhead' => '1132003', // WIP - Overhead
                default => '1132001'
            };
            
            $account = Account::where('client_id', $productionOrder->client_id)
                ->where('account_code', $accountCode)
                ->first();
                
            if ($account) {
                JournalEntry::create([
                    'client_id' => $productionOrder->client_id,
                    'journal_id' => $journal->id,
                    'account_id' => $account->id,
                    'description' => "WIP Transfer - " . ucfirst($costType),
                    'debit_amount' => 0,
                    'credit_amount' => $totalAmount,
                ]);
            }
        }
    }
}
