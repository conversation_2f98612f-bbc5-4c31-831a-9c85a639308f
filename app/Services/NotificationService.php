<?php

namespace App\Services;

use App\Models\SystemNotification;
use App\Models\ApprovalRequest;
use App\Models\Transaction;
use App\Models\Inventory;
use App\Models\PurchaseOrder;
// use App\Models\SalesOrder; // Disabled - using Transaction model
use App\Models\PeriodClosing;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Create a new notification.
     */
    public function create(array $data): SystemNotification
    {
        return SystemNotification::create($data);
    }

    /**
     * Send approval pending notification.
     */
    public function sendApprovalPendingNotification(ApprovalRequest $approvalRequest): void
    {
        $approvers = $this->getApproversForRequest($approvalRequest);

        foreach ($approvers as $approver) {
            $this->create([
                'client_id' => $approvalRequest->client_id,
                'type' => SystemNotification::TYPE_APPROVAL_PENDING,
                'title' => 'Persetujuan Menunggu',
                'message' => "Terdapat {$this->getModelName($approvalRequest->approvable_type)} yang menunggu persetujuan Anda",
                'data' => [
                    'approval_request_id' => $approvalRequest->id,
                    'workflow_name' => $approvalRequest->approvalWorkflow->workflow_name ?? '',
                    'requester' => $approvalRequest->requester->name ?? '',
                ],
                'priority' => SystemNotification::PRIORITY_NORMAL,
                'user_id' => $approver->id,
                'reference_type' => get_class($approvalRequest),
                'reference_id' => $approvalRequest->id,
                'expires_at' => now()->addDays(7),
            ]);
        }
    }

    /**
     * Send overdue payment notification.
     */
    public function sendOverduePaymentNotification(int $clientId): void
    {
        // Check for overdue purchase orders
        $overduePOs = PurchaseOrder::where('client_id', $clientId)
            ->where('status', 'approved')
            ->where('due_date', '<', now())
            ->whereNull('paid_at')
            ->get();

        foreach ($overduePOs as $po) {
            $daysOverdue = now()->diffInDays($po->due_date);

            $this->create([
                'client_id' => $clientId,
                'type' => SystemNotification::TYPE_OVERDUE_PAYMENT,
                'title' => 'Pembayaran Terlambat',
                'message' => "Purchase Order {$po->po_number} terlambat {$daysOverdue} hari",
                'data' => [
                    'po_id' => $po->id,
                    'po_number' => $po->po_number,
                    'amount' => $po->total_amount,
                    'days_overdue' => $daysOverdue,
                    'supplier' => $po->supplier->name ?? '',
                ],
                'priority' => $daysOverdue > 30 ? SystemNotification::PRIORITY_URGENT : SystemNotification::PRIORITY_HIGH,
                'reference_type' => get_class($po),
                'reference_id' => $po->id,
                'expires_at' => now()->addDays(30),
            ]);
        }

        // Check for overdue sales transactions (receivables)
        $overdueSOs = Transaction::where('client_id', $clientId)
            ->where('type', 'sales')
            ->where('payment_status', '!=', 'paid')
            ->where('transaction_date', '<', now()->subDays(30))
            ->get();

        foreach ($overdueSOs as $so) {
            $daysOverdue = now()->diffInDays($so->due_date);

            $this->create([
                'client_id' => $clientId,
                'type' => SystemNotification::TYPE_OVERDUE_PAYMENT,
                'title' => 'Piutang Terlambat',
                'message' => "Sales Order {$so->so_number} terlambat {$daysOverdue} hari",
                'data' => [
                    'so_id' => $so->id,
                    'so_number' => $so->so_number,
                    'amount' => $so->total_amount,
                    'days_overdue' => $daysOverdue,
                    'customer' => $so->customer->name ?? '',
                ],
                'priority' => $daysOverdue > 30 ? SystemNotification::PRIORITY_URGENT : SystemNotification::PRIORITY_HIGH,
                'reference_type' => get_class($so),
                'reference_id' => $so->id,
                'expires_at' => now()->addDays(30),
            ]);
        }
    }

    /**
     * Send low stock notification.
     */
    public function sendLowStockNotification(int $clientId): void
    {
        $lowStockItems = Inventory::where('client_id', $clientId)
            ->whereColumn('current_stock', '<=', 'minimum_stock')
            ->where('minimum_stock', '>', 0)
            ->with('product')
            ->get();

        foreach ($lowStockItems as $inventory) {
            $this->create([
                'client_id' => $clientId,
                'type' => SystemNotification::TYPE_LOW_STOCK,
                'title' => 'Stok Rendah',
                'message' => "Stok {$inventory->product->name} sudah mencapai batas minimum",
                'data' => [
                    'product_id' => $inventory->product_id,
                    'product_name' => $inventory->product->name,
                    'current_stock' => $inventory->current_stock,
                    'minimum_stock' => $inventory->minimum_stock,
                    'unit' => $inventory->product->unit ?? 'pcs',
                ],
                'priority' => $inventory->current_stock == 0 ? SystemNotification::PRIORITY_URGENT : SystemNotification::PRIORITY_HIGH,
                'reference_type' => get_class($inventory),
                'reference_id' => $inventory->id,
                'expires_at' => now()->addDays(14),
            ]);
        }
    }

    /**
     * Send budget variance notification.
     */
    public function sendBudgetVarianceNotification(int $clientId, array $variances): void
    {
        foreach ($variances as $variance) {
            if (abs($variance['variance_percentage']) > 10) { // Only notify if variance > 10%
                $this->create([
                    'client_id' => $clientId,
                    'type' => SystemNotification::TYPE_BUDGET_VARIANCE,
                    'title' => 'Varians Budget Signifikan',
                    'message' => "Varians budget {$variance['account_name']}: {$variance['variance_percentage']}%",
                    'data' => $variance,
                    'priority' => abs($variance['variance_percentage']) > 25 ? SystemNotification::PRIORITY_HIGH : SystemNotification::PRIORITY_NORMAL,
                    'expires_at' => now()->addDays(30),
                ]);
            }
        }
    }

    /**
     * Send period closing reminder.
     */
    public function sendPeriodClosingReminder(PeriodClosing $periodClosing): void
    {
        $this->create([
            'client_id' => $periodClosing->client_id,
            'type' => SystemNotification::TYPE_PERIOD_CLOSING,
            'title' => 'Reminder Tutup Buku',
            'message' => "Periode {$periodClosing->getPeriodName()} perlu ditutup",
            'data' => [
                'period_id' => $periodClosing->id,
                'period_name' => $periodClosing->getPeriodName(),
                'checklist_progress' => $periodClosing->getChecklistProgress(),
            ],
            'priority' => SystemNotification::PRIORITY_HIGH,
            'reference_type' => get_class($periodClosing),
            'reference_id' => $periodClosing->id,
            'expires_at' => now()->addDays(7),
        ]);
    }

    /**
     * Get notifications for a user.
     */
    public function getNotificationsForUser(int $userId, int $clientId, array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = SystemNotification::where('client_id', $clientId)
            ->forUser($userId)
            ->notExpired()
            ->orderBy('created_at', 'desc');

        if (isset($filters['type'])) {
            $query->byType($filters['type']);
        }

        if (isset($filters['priority'])) {
            $query->byPriority($filters['priority']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->get();
    }

    /**
     * Get unread notification count for a user.
     */
    public function getUnreadCount(int $userId, int $clientId): int
    {
        return SystemNotification::where('client_id', $clientId)
            ->forUser($userId)
            ->unread()
            ->notExpired()
            ->count();
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(int $notificationId): bool
    {
        $notification = SystemNotification::find($notificationId);
        if ($notification) {
            $notification->markAsRead();
            return true;
        }
        return false;
    }

    /**
     * Mark notification as dismissed.
     */
    public function markAsDismissed(int $notificationId): bool
    {
        $notification = SystemNotification::find($notificationId);
        if ($notification) {
            $notification->markAsDismissed();
            return true;
        }
        return false;
    }

    /**
     * Mark all notifications as read for a user.
     */
    public function markAllAsRead(int $userId, int $clientId): int
    {
        return SystemNotification::where('client_id', $clientId)
            ->forUser($userId)
            ->unread()
            ->update([
                'status' => SystemNotification::STATUS_READ,
                'read_at' => now(),
            ]);
    }

    /**
     * Clean up expired notifications.
     */
    public function cleanupExpiredNotifications(): int
    {
        return SystemNotification::where('expires_at', '<', now())
            ->delete();
    }

    /**
     * Run all notification checks.
     */
    public function runAllChecks(int $clientId): array
    {
        $results = [];

        try {
            // Check for approval pending notifications
            $this->checkApprovalPendingNotifications($clientId);
            $results['approval_pending'] = 'success';
        } catch (\Exception $e) {
            $results['approval_pending'] = 'error: ' . $e->getMessage();
            Log::error('Approval pending notification check failed', ['error' => $e->getMessage()]);
        }

        try {
            // Check for overdue payments
            $this->sendOverduePaymentNotification($clientId);
            $results['overdue_payment'] = 'success';
        } catch (\Exception $e) {
            $results['overdue_payment'] = 'error: ' . $e->getMessage();
            Log::error('Overdue payment notification check failed', ['error' => $e->getMessage()]);
        }

        try {
            // Check for low stock
            $this->sendLowStockNotification($clientId);
            $results['low_stock'] = 'success';
        } catch (\Exception $e) {
            $results['low_stock'] = 'error: ' . $e->getMessage();
            Log::error('Low stock notification check failed', ['error' => $e->getMessage()]);
        }

        return $results;
    }

    /**
     * Check for approval pending notifications.
     */
    protected function checkApprovalPendingNotifications(int $clientId): void
    {
        $pendingApprovals = ApprovalRequest::where('client_id', $clientId)
            ->where('status', 'pending')
            ->whereDoesntHave('systemNotifications', function ($query) {
                $query->where('type', SystemNotification::TYPE_APPROVAL_PENDING)
                    ->where('status', '!=', SystemNotification::STATUS_DISMISSED)
                    ->where('created_at', '>', now()->subHours(24)); // Don't spam notifications
            })
            ->get();

        foreach ($pendingApprovals as $approval) {
            $this->sendApprovalPendingNotification($approval);
        }
    }

    /**
     * Get approvers for an approval request.
     */
    protected function getApproversForRequest(ApprovalRequest $approvalRequest): \Illuminate\Database\Eloquent\Collection
    {
        // Simplified - in real implementation, you'd get actual approvers based on workflow steps
        return collect([$approvalRequest->requester]); // Placeholder
    }

    /**
     * Get human-readable model name.
     */
    protected function getModelName(string $modelClass): string
    {
        return match ($modelClass) {
            'App\\Models\\PurchaseOrder' => 'Pesanan Pembelian',
            // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
            'App\\Models\\Journal' => 'Jurnal Entry',
            'App\\Models\\Expense' => 'Beban',
            'App\\Models\\Invoice' => 'Invoice',
            default => 'Dokumen',
        };
    }
}
