<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use ZipArchive;

class BackupService
{
    protected string $backupDisk = 'local';
    protected string $backupPath = 'backups';

    /**
     * Create full system backup.
     */
    public function createFullBackup(?int $clientId = null): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = $clientId ? "client_{$clientId}_{$timestamp}" : "full_system_{$timestamp}";

        try {
            // Create backup directory
            $backupDir = "{$this->backupPath}/{$backupName}";
            Storage::disk($this->backupDisk)->makeDirectory($backupDir);

            $result = [
                'backup_name' => $backupName,
                'timestamp' => $timestamp,
                'client_id' => $clientId,
                'status' => 'success',
                'files' => [],
                'size' => 0,
                'duration' => 0,
            ];

            $startTime = microtime(true);

            // 1. Database backup
            $dbBackupFile = $this->createDatabaseBackup($backupDir, $clientId);
            $result['files']['database'] = $dbBackupFile;

            // 2. Files backup (uploads, storage)
            $filesBackupFile = $this->createFilesBackup($backupDir);
            $result['files']['files'] = $filesBackupFile;

            // 3. Configuration backup
            $configBackupFile = $this->createConfigBackup($backupDir);
            $result['files']['config'] = $configBackupFile;

            // 4. Create compressed archive
            $archiveFile = $this->createCompressedArchive($backupDir, $backupName);
            $result['files']['archive'] = $archiveFile;

            // Calculate total size and duration
            $result['size'] = $this->getBackupSize($backupDir);
            $result['duration'] = round(microtime(true) - $startTime, 2);

            // Clean up temporary files
            $this->cleanupTemporaryFiles($backupDir);

            // Log backup creation
            Log::info("Backup created successfully", $result);

            return $result;
        } catch (\Exception $e) {
            Log::error("Backup creation failed: " . $e->getMessage());

            return [
                'backup_name' => $backupName,
                'timestamp' => $timestamp,
                'client_id' => $clientId,
                'status' => 'failed',
                'error' => $e->getMessage(),
                'duration' => round(microtime(true) - $startTime, 2),
            ];
        }
    }

    /**
     * Create database backup.
     */
    protected function createDatabaseBackup(string $backupDir, ?int $clientId = null): string
    {
        $filename = 'database_' . now()->format('Y-m-d_H-i-s') . '.sql';

        try {
            // Try to use Laravel's database export functionality first
            return $this->createDatabaseBackupWithLaravel($backupDir, $filename, $clientId);
        } catch (\Exception $e) {
            Log::warning("Laravel database backup failed: " . $e->getMessage());

            // Try mysqldump as fallback
            try {
                return $this->createDatabaseBackupWithMysqldump($backupDir, $filename, $clientId);
            } catch (\Exception) {
                Log::error("Both Laravel and mysqldump backup methods failed");

                // Create a simple backup with basic SQL export
                return $this->createSimpleDatabaseBackup($backupDir, $filename, $clientId);
            }
        }
    }

    /**
     * Create database backup using Laravel's database functionality.
     */
    protected function createDatabaseBackupWithLaravel(string $backupDir, string $filename, ?int $clientId = null): string
    {
        $filepath = "{$backupDir}/{$filename}";
        $fullPath = Storage::disk($this->backupDisk)->path($filepath);

        // Get database connection
        $connection = DB::connection();
        $database = $connection->getDatabaseName();

        // Get all tables
        $tables = $connection->getDoctrineSchemaManager()->listTableNames();

        $sql = "-- Database backup created at " . now()->toDateTimeString() . "\n";
        $sql .= "-- Database: {$database}\n\n";

        foreach ($tables as $table) {
            // Skip tables if client-specific backup is requested
            if ($clientId && !$this->isClientSpecificTable($table)) {
                continue;
            }

            // Get table structure
            $createTable = $connection->select("SHOW CREATE TABLE `{$table}`")[0];
            $sql .= "-- Table structure for `{$table}`\n";
            $sql .= "DROP TABLE IF EXISTS `{$table}`;\n";
            $sql .= $createTable->{'Create Table'} . ";\n\n";

            // Get table data
            $query = $connection->table($table);

            // Add client filter if needed
            if ($clientId && $this->hasClientIdColumn($table)) {
                $query->where('client_id', $clientId);
            }

            $rows = $query->get();

            if ($rows->count() > 0) {
                $sql .= "-- Data for table `{$table}`\n";
                $sql .= "INSERT INTO `{$table}` VALUES\n";

                $values = [];
                foreach ($rows as $row) {
                    $rowData = array_map(function ($value) {
                        return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);
                    $values[] = '(' . implode(',', $rowData) . ')';
                }

                $sql .= implode(",\n", $values) . ";\n\n";
            }
        }

        // Write to file
        file_put_contents($fullPath, $sql);

        return $filename;
    }

    /**
     * Create database backup using mysqldump command.
     */
    protected function createDatabaseBackupWithMysqldump(string $backupDir, string $filename, ?int $clientId = null): string
    {
        $filepath = "{$backupDir}/{$filename}";

        $host = config('database.connections.mysql.host');
        $port = config('database.connections.mysql.port');
        $database = config('database.connections.mysql.database');
        $username = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');

        // Check if mysqldump is available
        exec('which mysqldump', $output, $returnCode);
        if ($returnCode !== 0) {
            // Try common paths
            $mysqldumpPaths = [
                '/usr/bin/mysqldump',
                '/usr/local/bin/mysqldump',
                '/opt/homebrew/bin/mysqldump',
                'mysqldump'
            ];

            $mysqldumpPath = null;
            foreach ($mysqldumpPaths as $path) {
                if (is_executable($path) || exec("which {$path}", $testOutput, $testReturn) === 0) {
                    $mysqldumpPath = $path;
                    break;
                }
            }

            if (!$mysqldumpPath) {
                throw new \Exception("mysqldump command not found. Please install MySQL client tools or use Laravel backup method.");
            }
        } else {
            $mysqldumpPath = 'mysqldump';
        }

        // Build mysqldump command
        $command = sprintf(
            '%s --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s',
            $mysqldumpPath,
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($database)
        );

        // Add client-specific filtering if needed
        if ($clientId) {
            $tables = $this->getClientSpecificTables();
            $whereClause = "--where=\"client_id={$clientId}\"";
            $command .= " {$whereClause} " . implode(' ', $tables);
        }

        $command .= ' > ' . Storage::disk($this->backupDisk)->path($filepath);

        // Execute backup command
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception("Database backup failed with return code: {$returnCode}. Output: " . implode("\n", $output));
        }

        return $filename;
    }

    /**
     * Create simple database backup as last resort.
     */
    protected function createSimpleDatabaseBackup(string $backupDir, string $filename, ?int $clientId = null): string
    {
        $filepath = "{$backupDir}/{$filename}";
        $fullPath = Storage::disk($this->backupDisk)->path($filepath);

        $sql = "-- Simple Database backup created at " . now()->toDateTimeString() . "\n";
        $sql .= "-- This is a basic backup without table structure\n\n";

        try {
            // Get basic table list
            $tables = DB::select('SHOW TABLES');
            $databaseName = DB::getDatabaseName();

            foreach ($tables as $table) {
                $tableName = $table->{"Tables_in_{$databaseName}"};

                // Skip if client-specific and not relevant
                if ($clientId && !$this->isClientSpecificTable($tableName)) {
                    continue;
                }

                $sql .= "-- Data for table: {$tableName}\n";

                // Get data with basic query
                $query = DB::table($tableName);

                if ($clientId && $this->hasClientIdColumn($tableName)) {
                    $query->where('client_id', $clientId);
                }

                $rows = $query->get();

                if ($rows->count() > 0) {
                    $sql .= "-- {$rows->count()} rows\n";
                    foreach ($rows as $row) {
                        $sql .= "-- Row data: " . json_encode($row) . "\n";
                    }
                }

                $sql .= "\n";
            }

            file_put_contents($fullPath, $sql);
        } catch (\Exception $e) {
            // Even simpler fallback
            $sql .= "-- Backup failed: " . $e->getMessage() . "\n";
            $sql .= "-- Database: " . config('database.connections.mysql.database') . "\n";
            $sql .= "-- Timestamp: " . now()->toDateTimeString() . "\n";

            file_put_contents($fullPath, $sql);
        }

        return $filename;
    }

    /**
     * Create files backup.
     */
    protected function createFilesBackup(string $backupDir): string
    {
        $filename = 'files_' . now()->format('Y-m-d_H-i-s') . '.zip';
        $filepath = Storage::disk($this->backupDisk)->path("{$backupDir}/{$filename}");

        $zip = new ZipArchive();
        if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create files backup archive");
        }

        // Add storage files
        $this->addDirectoryToZip($zip, storage_path('app/public'), 'storage/');

        // Add upload files if they exist
        if (Storage::disk('public')->exists('uploads')) {
            $this->addDirectoryToZip($zip, Storage::disk('public')->path('uploads'), 'uploads/');
        }

        $zip->close();

        return $filename;
    }

    /**
     * Create configuration backup.
     */
    protected function createConfigBackup(string $backupDir): string
    {
        $filename = 'config_' . now()->format('Y-m-d_H-i-s') . '.zip';
        $filepath = Storage::disk($this->backupDisk)->path("{$backupDir}/{$filename}");

        $zip = new ZipArchive();
        if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create config backup archive");
        }

        // Add configuration files
        $configFiles = [
            '.env.example' => base_path('.env.example'),
            'composer.json' => base_path('composer.json'),
            'composer.lock' => base_path('composer.lock'),
            'package.json' => base_path('package.json'),
        ];

        foreach ($configFiles as $archiveName => $filePath) {
            if (file_exists($filePath)) {
                $zip->addFile($filePath, $archiveName);
            }
        }

        // Add config directory
        $this->addDirectoryToZip($zip, config_path(), 'config/');

        $zip->close();

        return $filename;
    }

    /**
     * Create compressed archive of all backup files.
     */
    protected function createCompressedArchive(string $backupDir, string $backupName): string
    {
        $archiveFilename = "{$backupName}.zip";
        $archivePath = Storage::disk($this->backupDisk)->path("{$this->backupPath}/{$archiveFilename}");

        $zip = new ZipArchive();
        if ($zip->open($archivePath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception("Cannot create backup archive");
        }

        $backupDirPath = Storage::disk($this->backupDisk)->path($backupDir);
        $this->addDirectoryToZip($zip, $backupDirPath, '');

        $zip->close();

        return $archiveFilename;
    }

    /**
     * Add directory to ZIP archive recursively.
     */
    protected function addDirectoryToZip(ZipArchive $zip, string $dirPath, string $zipPath): void
    {
        if (!is_dir($dirPath)) {
            return;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dirPath, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $zipPath . substr($filePath, strlen($dirPath) + 1);

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } elseif ($file->isFile()) {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Get backup size in bytes.
     */
    protected function getBackupSize(string $backupDir): int
    {
        $size = 0;
        $files = Storage::disk($this->backupDisk)->allFiles($backupDir);

        foreach ($files as $file) {
            $size += Storage::disk($this->backupDisk)->size($file);
        }

        return $size;
    }

    /**
     * Clean up temporary backup files.
     */
    protected function cleanupTemporaryFiles(string $backupDir): void
    {
        Storage::disk($this->backupDisk)->deleteDirectory($backupDir);
    }

    /**
     * Get list of client-specific tables.
     */
    protected function getClientSpecificTables(): array
    {
        return [
            'accounts',
            'transactions',
            'journals',
            'journal_entries',
            'products',
            'customers',
            'suppliers',
            'purchase_orders',
            'sales_orders',
            'inventories',
            'budgets',
            'budget_items',
            'fixed_assets',
            'bank_accounts',
            'audit_logs',
            'system_notifications'
        ];
    }

    /**
     * List all available backups.
     */
    public function listBackups(): array
    {
        $backups = [];
        $files = Storage::disk($this->backupDisk)->files($this->backupPath);

        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'zip') {
                $filename = basename($file);
                $backups[] = [
                    'filename' => $filename,
                    'path' => $file,
                    'size' => Storage::disk($this->backupDisk)->size($file),
                    'created_at' => Carbon::createFromTimestamp(Storage::disk($this->backupDisk)->lastModified($file)),
                    'human_size' => $this->formatBytes(Storage::disk($this->backupDisk)->size($file)),
                ];
            }
        }

        // Sort by creation date (newest first)
        usort($backups, function ($a, $b) {
            return $b['created_at']->timestamp - $a['created_at']->timestamp;
        });

        return $backups;
    }

    /**
     * Delete old backups based on retention policy.
     */
    public function cleanupOldBackups(int $retentionDays = 30): int
    {
        $cutoffDate = now()->subDays($retentionDays);
        $deletedCount = 0;

        $backups = $this->listBackups();

        foreach ($backups as $backup) {
            if ($backup['created_at']->lt($cutoffDate)) {
                Storage::disk($this->backupDisk)->delete($backup['path']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Download backup file.
     */
    public function downloadBackup(string $filename)
    {
        $filepath = "{$this->backupPath}/{$filename}";

        if (!Storage::disk($this->backupDisk)->exists($filepath)) {
            throw new \Exception("Backup file not found: {$filename}");
        }

        return response()->download(Storage::disk($this->backupDisk)->path($filepath));
    }

    /**
     * Check if table is client-specific.
     */
    protected function isClientSpecificTable(string $table): bool
    {
        $clientSpecificTables = [
            'accounts',
            'transactions',
            'journals',
            'customers',
            'suppliers',
            'products',
            'inventories',
            'purchase_orders',
            'production_orders',
            'bank_accounts',
            'bank_transactions',
            'budgets',
            'fixed_assets',
            'depreciations',
            'approval_requests',
            'approval_workflows',
            'cost_centers',
            'departments',
            'locations',
            'product_categories',
            'unit_of_measures',
            'work_centers',
            'work_orders',
            'bill_of_materials',
            'stock_movements',
            'stock_opnames',
            'asset_transfers',
            'automated_journal_entries',
            'cash_flow_categories',
            'period_closings',
            'cost_variance_reports',
            'system_notifications'
        ];

        return in_array($table, $clientSpecificTables);
    }

    /**
     * Check if table has client_id column.
     */
    protected function hasClientIdColumn(string $table): bool
    {
        try {
            $connection = DB::connection();
            $columns = $connection->getSchemaBuilder()->getColumnListing($table);
            return in_array('client_id', $columns);
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Format bytes to human readable format.
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Verify backup integrity.
     */
    public function verifyBackup(string $filename): array
    {
        $filepath = "{$this->backupPath}/{$filename}";

        if (!Storage::disk($this->backupDisk)->exists($filepath)) {
            throw new \Exception("Backup file not found: {$filename}");
        }

        $fullPath = Storage::disk($this->backupDisk)->path($filepath);

        $zip = new ZipArchive();
        $result = $zip->open($fullPath, ZipArchive::CHECKCONS);

        if ($result !== TRUE) {
            return [
                'valid' => false,
                'error' => "Archive verification failed with code: {$result}",
            ];
        }

        $zip->close();

        return [
            'valid' => true,
            'file_count' => $zip->numFiles,
            'size' => Storage::disk($this->backupDisk)->size($filepath),
        ];
    }
}
