<?php

namespace App\Services;

// use App\Models\SalesOrder; // Disabled - using Transaction model
use App\Models\PurchaseOrder;
use App\Models\ProductionOrder;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\FixedAsset;
use App\Models\Journal;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

// TEMPORARILY DISABLED - Needs refactoring to use Transaction model instead of SalesOrder
class WorkflowAutomationService_DISABLED
{
    /**
     * Process automatic reorder suggestions
     */
    public function processAutomaticReorders(int $clientId): array
    {
        $results = [
            'reorder_suggestions' => [],
            'auto_created_pos' => [],
            'notifications_sent' => 0,
        ];

        $lowStockItems = Inventory::where('client_id', $clientId)
            ->whereColumn('current_stock', '<=', 'reorder_level')
            ->where('auto_reorder', true)
            ->with(['product', 'product.preferredSupplier'])
            ->get();

        foreach ($lowStockItems as $inventory) {
            $suggestion = $this->createReorderSuggestion($inventory);
            $results['reorder_suggestions'][] = $suggestion;

            // Auto-create PO if enabled
            if ($inventory->auto_create_po && $inventory->product->preferredSupplier) {
                $po = $this->createAutomaticPurchaseOrder($inventory);
                if ($po) {
                    $results['auto_created_pos'][] = $po;
                }
            }

            // Send notification
            $this->sendLowStockNotification($inventory);
            $results['notifications_sent']++;
        }

        return $results;
    }

    /**
     * Process automatic production scheduling
     */
    public function processAutomaticProductionScheduling(int $clientId): array
    {
        $results = [
            'production_orders_created' => [],
            'material_shortages' => [],
            'capacity_issues' => [],
        ];

        // Check sales orders that need production
        $salesOrders = SalesOrder::where('client_id', $clientId)
            ->where('status', 'confirmed')
            ->whereDoesntHave('productionOrders')
            ->with(['items.product'])
            ->get();

        foreach ($salesOrders as $salesOrder) {
            foreach ($salesOrder->items as $item) {
                if ($item->product->is_manufactured) {
                    // Check if we need to create production order
                    $availableStock = $this->getAvailableStock($item->product_id, $clientId);
                    $requiredQuantity = $item->quantity_ordered;

                    if ($availableStock < $requiredQuantity) {
                        $productionQuantity = $requiredQuantity - $availableStock;
                        
                        // Check material availability
                        $materialCheck = $this->checkMaterialAvailability($item->product, $productionQuantity, $clientId);
                        
                        if ($materialCheck['sufficient']) {
                            $productionOrder = $this->createAutomaticProductionOrder($salesOrder, $item, $productionQuantity);
                            $results['production_orders_created'][] = $productionOrder;
                        } else {
                            $results['material_shortages'][] = [
                                'product' => $item->product->product_name,
                                'required_quantity' => $productionQuantity,
                                'missing_materials' => $materialCheck['missing_materials'],
                            ];
                        }
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Process automatic journal entries
     */
    public function processAutomaticJournalEntries(int $clientId): array
    {
        $results = [
            'depreciation_entries' => 0,
            'accrual_entries' => 0,
            'recurring_entries' => 0,
        ];

        // Monthly depreciation
        if (now()->day == 1) { // First day of month
            $depreciationResults = app(DepreciationService::class)
                ->calculateMonthlyDepreciation($clientId, now()->year, now()->month);
            $results['depreciation_entries'] = $depreciationResults['assets_processed'];
        }

        // Process recurring journal entries
        $results['recurring_entries'] = $this->processRecurringJournalEntries($clientId);

        // Process automatic accruals
        $results['accrual_entries'] = $this->processAutomaticAccruals($clientId);

        return $results;
    }

    /**
     * Process automatic notifications and alerts
     */
    public function processAutomaticNotifications(int $clientId): array
    {
        $results = [
            'low_stock_alerts' => 0,
            'overdue_payments' => 0,
            'maintenance_reminders' => 0,
            'approval_requests' => 0,
        ];

        // Low stock alerts
        $results['low_stock_alerts'] = $this->sendLowStockAlerts($clientId);

        // Overdue payment alerts
        $results['overdue_payments'] = $this->sendOverduePaymentAlerts($clientId);

        // Maintenance reminders
        $results['maintenance_reminders'] = $this->sendMaintenanceReminders($clientId);

        // Approval request reminders
        $results['approval_requests'] = $this->sendApprovalRequestReminders($clientId);

        return $results;
    }

    /**
     * Process automatic data cleanup
     */
    public function processAutomaticDataCleanup(int $clientId): array
    {
        $results = [
            'old_logs_deleted' => 0,
            'temp_files_cleaned' => 0,
            'cache_cleared' => false,
        ];

        // Delete old system logs (older than 6 months)
        $oldLogs = DB::table('activity_log')
            ->where('created_at', '<', now()->subMonths(6))
            ->delete();
        $results['old_logs_deleted'] = $oldLogs;

        // Clean temporary files
        $results['temp_files_cleaned'] = $this->cleanTemporaryFiles();

        // Clear old cache entries
        $this->clearOldCacheEntries($clientId);
        $results['cache_cleared'] = true;

        return $results;
    }

    // Helper methods
    private function createReorderSuggestion(Inventory $inventory): array
    {
        $reorderQuantity = max(
            $inventory->reorder_quantity,
            $inventory->reorder_level - $inventory->current_stock
        );

        return [
            'product_id' => $inventory->product_id,
            'product_name' => $inventory->product->product_name,
            'current_stock' => $inventory->current_stock,
            'reorder_level' => $inventory->reorder_level,
            'suggested_quantity' => $reorderQuantity,
            'preferred_supplier' => $inventory->product->preferredSupplier->supplier_name ?? null,
            'estimated_cost' => $reorderQuantity * $inventory->product->standard_cost,
        ];
    }

    private function createAutomaticPurchaseOrder(Inventory $inventory): ?PurchaseOrder
    {
        $supplier = $inventory->product->preferredSupplier;
        if (!$supplier) {
            return null;
        }

        $reorderQuantity = max(
            $inventory->reorder_quantity,
            $inventory->reorder_level - $inventory->current_stock
        );

        return DB::transaction(function () use ($inventory, $supplier, $reorderQuantity) {
            $po = PurchaseOrder::create([
                'client_id' => $inventory->client_id,
                'supplier_id' => $supplier->id,
                'po_number' => PurchaseOrder::generatePONumber($inventory->client_id),
                'order_date' => now(),
                'expected_date' => now()->addDays($supplier->lead_time_days ?? 7),
                'status' => 'draft',
                'priority' => 'normal',
                'subtotal' => $reorderQuantity * $inventory->product->standard_cost,
                'total_amount' => $reorderQuantity * $inventory->product->standard_cost,
                'currency_code' => 'IDR',
                'payment_terms' => $supplier->payment_terms,
                'notes' => 'Auto-generated from reorder automation',
                'created_by' => 1, // System user
            ]);

            // Create PO item
            $po->items()->create([
                'client_id' => $inventory->client_id,
                'product_id' => $inventory->product_id,
                'description' => $inventory->product->product_name,
                'quantity_ordered' => $reorderQuantity,
                'unit_price' => $inventory->product->standard_cost,
                'line_total' => $reorderQuantity * $inventory->product->standard_cost,
                'unit_id' => $inventory->product->unit_id,
                'status' => 'pending',
            ]);

            return $po;
        });
    }

    private function createAutomaticProductionOrder(SalesOrder $salesOrder, $item, float $quantity): ProductionOrder
    {
        return ProductionOrder::create([
            'client_id' => $salesOrder->client_id,
            'product_id' => $item->product_id,
            'sales_order_id' => $salesOrder->id,
            'order_number' => ProductionOrder::generateOrderNumber($salesOrder->client_id),
            'quantity_to_produce' => $quantity,
            'required_date' => $salesOrder->delivery_date,
            'status' => 'planned',
            'priority' => $salesOrder->priority,
            'notes' => 'Auto-generated from sales order automation',
            'created_by' => 1, // System user
        ]);
    }

    private function getAvailableStock(int $productId, int $clientId): float
    {
        $inventory = Inventory::where('client_id', $clientId)
            ->where('product_id', $productId)
            ->first();

        return $inventory ? $inventory->current_stock : 0;
    }

    private function checkMaterialAvailability(Product $product, float $quantity, int $clientId): array
    {
        $bom = $product->billOfMaterial;
        if (!$bom) {
            return ['sufficient' => true, 'missing_materials' => []];
        }

        $missingMaterials = [];
        
        foreach ($bom->items as $bomItem) {
            $requiredQuantity = ($bomItem->quantity * $quantity) / $bom->quantity_produced;
            $availableStock = $this->getAvailableStock($bomItem->material_id, $clientId);
            
            if ($availableStock < $requiredQuantity) {
                $missingMaterials[] = [
                    'material_name' => $bomItem->material->product_name,
                    'required' => $requiredQuantity,
                    'available' => $availableStock,
                    'shortage' => $requiredQuantity - $availableStock,
                ];
            }
        }

        return [
            'sufficient' => empty($missingMaterials),
            'missing_materials' => $missingMaterials,
        ];
    }

    private function sendLowStockNotification(Inventory $inventory): void
    {
        // Create system notification
        Notification::create([
            'client_id' => $inventory->client_id,
            'type' => 'low_stock_alert',
            'title' => 'Low Stock Alert',
            'message' => "Product {$inventory->product->product_name} is below reorder level. Current stock: {$inventory->current_stock}, Reorder level: {$inventory->reorder_level}",
            'data' => json_encode([
                'product_id' => $inventory->product_id,
                'current_stock' => $inventory->current_stock,
                'reorder_level' => $inventory->reorder_level,
            ]),
            'is_read' => false,
        ]);
    }

    private function processRecurringJournalEntries(int $clientId): int
    {
        // This would process recurring journal entries like rent, insurance, etc.
        // For now, return 0 as placeholder
        return 0;
    }

    private function processAutomaticAccruals(int $clientId): int
    {
        // This would process automatic accruals like depreciation, amortization
        // For now, return 0 as placeholder
        return 0;
    }

    private function sendLowStockAlerts(int $clientId): int
    {
        $lowStockItems = Inventory::where('client_id', $clientId)
            ->whereColumn('current_stock', '<=', 'reorder_level')
            ->count();

        // Send notifications for each low stock item
        return $lowStockItems;
    }

    private function sendOverduePaymentAlerts(int $clientId): int
    {
        $overdueOrders = PurchaseOrder::where('client_id', $clientId)
            ->where('payment_status', '!=', 'paid')
            ->whereNotNull('invoice_date')
            ->where('invoice_date', '<', now()->subDays(30))
            ->count();

        return $overdueOrders;
    }

    private function sendMaintenanceReminders(int $clientId): int
    {
        $maintenanceDue = FixedAsset::where('client_id', $clientId)
            ->where('next_maintenance_date', '<=', now()->addDays(7))
            ->count();

        return $maintenanceDue;
    }

    private function sendApprovalRequestReminders(int $clientId): int
    {
        $pendingApprovals = PurchaseOrder::where('client_id', $clientId)
            ->where('status', 'pending')
            ->where('approval_required', true)
            ->where('created_at', '<', now()->subDays(2))
            ->count();

        return $pendingApprovals;
    }

    private function cleanTemporaryFiles(): int
    {
        // Clean temporary files older than 24 hours
        $tempPath = storage_path('app/temp');
        $count = 0;
        
        if (is_dir($tempPath)) {
            $files = glob($tempPath . '/*');
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < time() - 86400) {
                    unlink($file);
                    $count++;
                }
            }
        }
        
        return $count;
    }

    private function clearOldCacheEntries(int $clientId): void
    {
        // Clear cache entries older than 24 hours
        $patterns = [
            "dashboard_data_{$clientId}",
            "financial_reports_{$clientId}_*",
            "inventory_summary_{$clientId}",
        ];
        
        foreach ($patterns as $pattern) {
            \Cache::forget($pattern);
        }
    }
}
