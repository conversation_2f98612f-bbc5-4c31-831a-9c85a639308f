<?php

namespace App\Services;

use App\Models\Budget;
use App\Models\BudgetItem;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\SystemNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BudgetService
{
    /**
     * Create a new budget.
     */
    public function createBudget(array $data): Budget
    {
        return DB::transaction(function () use ($data) {
            $budget = Budget::create($data);
            
            // Initialize budget items for all revenue and expense accounts
            $this->initializeBudgetItems($budget);
            
            return $budget;
        });
    }

    /**
     * Initialize budget items for all relevant accounts.
     */
    protected function initializeBudgetItems(Budget $budget): void
    {
        $accounts = Account::where('client_id', $budget->client_id)
            ->whereIn('account_type', ['revenue', 'expense'])
            ->where('is_active', true)
            ->get();

        foreach ($accounts as $account) {
            BudgetItem::create([
                'budget_id' => $budget->id,
                'account_id' => $account->id,
                'cost_center_id' => null,
                'total_annual' => 0,
            ]);
        }
    }

    /**
     * Update budget item amounts.
     */
    public function updateBudgetItem(BudgetItem $budgetItem, array $monthlyAmounts): BudgetItem
    {
        $budgetItem->update($monthlyAmounts);
        $budgetItem->calculateTotal();
        
        // Recalculate budget totals
        $budgetItem->budget->calculateTotals();
        
        return $budgetItem;
    }

    /**
     * Submit budget for approval.
     */
    public function submitBudget(Budget $budget): bool
    {
        if (!$budget->canBeSubmitted()) {
            throw new \Exception('Budget tidak dapat diajukan dengan status saat ini');
        }

        $budget->update(['status' => Budget::STATUS_SUBMITTED]);
        
        // Send notification to approvers
        $this->sendApprovalNotification($budget);
        
        Log::info("Budget {$budget->budget_name} submitted for approval");
        
        return true;
    }

    /**
     * Approve budget.
     */
    public function approveBudget(Budget $budget, int $approverId, ?string $notes = null): bool
    {
        if (!$budget->canBeApproved()) {
            throw new \Exception('Budget tidak dapat disetujui dengan status saat ini');
        }

        $budget->update([
            'status' => Budget::STATUS_APPROVED,
            'approved_by' => $approverId,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
        
        Log::info("Budget {$budget->budget_name} approved by user {$approverId}");
        
        return true;
    }

    /**
     * Reject budget.
     */
    public function rejectBudget(Budget $budget, int $approverId, string $reason): bool
    {
        if (!$budget->canBeApproved()) {
            throw new \Exception('Budget tidak dapat ditolak dengan status saat ini');
        }

        $budget->update([
            'status' => Budget::STATUS_REJECTED,
            'approved_by' => $approverId,
            'approved_at' => now(),
            'approval_notes' => $reason,
        ]);
        
        Log::info("Budget {$budget->budget_name} rejected by user {$approverId}");
        
        return true;
    }

    /**
     * Activate budget.
     */
    public function activateBudget(Budget $budget): bool
    {
        if (!$budget->canBeActivated()) {
            throw new \Exception('Budget tidak dapat diaktifkan dengan status saat ini');
        }

        // Deactivate other active budgets for the same year
        Budget::where('client_id', $budget->client_id)
            ->where('budget_year', $budget->budget_year)
            ->where('status', Budget::STATUS_ACTIVE)
            ->update(['status' => Budget::STATUS_CLOSED]);

        $budget->update(['status' => Budget::STATUS_ACTIVE]);
        
        Log::info("Budget {$budget->budget_name} activated");
        
        return true;
    }

    /**
     * Get budget variance analysis.
     */
    public function getBudgetVarianceAnalysis(Budget $budget, ?int $month = null): array
    {
        $month = $month ?: now()->month;
        $year = $budget->budget_year;
        
        $variances = [];
        
        foreach ($budget->budgetItems as $budgetItem) {
            $account = $budgetItem->account;
            
            // Get actual amount for the period
            $actualAmount = $this->getActualAmount($account, $year, $month);
            $budgetAmount = $budgetItem->getAmountForMonth($month);
            
            $variance = $actualAmount - $budgetAmount;
            $variancePercentage = $budgetAmount != 0 ? ($variance / $budgetAmount) * 100 : 0;
            
            $variances[] = [
                'account_id' => $account->id,
                'account_code' => $account->account_code,
                'account_name' => $account->name,
                'account_type' => $account->account_type,
                'budget_amount' => $budgetAmount,
                'actual_amount' => $actualAmount,
                'variance' => $variance,
                'variance_percentage' => $variancePercentage,
                'status' => $this->getVarianceStatus($variancePercentage, $account->account_type),
            ];
        }
        
        return $variances;
    }

    /**
     * Get actual amount for an account in a specific period.
     */
    protected function getActualAmount(Account $account, int $year, int $month): float
    {
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        
        $debitSum = JournalEntry::whereHas('journal', function ($query) use ($startDate, $endDate, $account) {
            $query->where('client_id', $account->client_id)
                ->whereBetween('transaction_date', [$startDate, $endDate]);
        })
        ->where('account_id', $account->id)
        ->sum('debit');
        
        $creditSum = JournalEntry::whereHas('journal', function ($query) use ($startDate, $endDate, $account) {
            $query->where('client_id', $account->client_id)
                ->whereBetween('transaction_date', [$startDate, $endDate]);
        })
        ->where('account_id', $account->id)
        ->sum('credit');
        
        // For revenue accounts, credit increases the balance
        // For expense accounts, debit increases the balance
        if ($account->account_type === 'revenue') {
            return $creditSum - $debitSum;
        } else {
            return $debitSum - $creditSum;
        }
    }

    /**
     * Get variance status based on percentage and account type.
     */
    protected function getVarianceStatus(float $variancePercentage, string $accountType): string
    {
        $absVariance = abs($variancePercentage);
        
        if ($absVariance <= 5) {
            return 'on_track';
        } elseif ($absVariance <= 15) {
            return 'warning';
        } else {
            return 'critical';
        }
    }

    /**
     * Get budget summary for dashboard.
     */
    public function getBudgetSummary(Budget $budget): array
    {
        $currentMonth = now()->month;
        $variances = $this->getBudgetVarianceAnalysis($budget, $currentMonth);
        
        $totalBudgetRevenue = collect($variances)
            ->where('account_type', 'revenue')
            ->sum('budget_amount');
            
        $totalActualRevenue = collect($variances)
            ->where('account_type', 'revenue')
            ->sum('actual_amount');
            
        $totalBudgetExpense = collect($variances)
            ->where('account_type', 'expense')
            ->sum('budget_amount');
            
        $totalActualExpense = collect($variances)
            ->where('account_type', 'expense')
            ->sum('actual_amount');
        
        $criticalVariances = collect($variances)
            ->where('status', 'critical')
            ->count();
            
        $warningVariances = collect($variances)
            ->where('status', 'warning')
            ->count();
        
        return [
            'budget_name' => $budget->budget_name,
            'budget_year' => $budget->budget_year,
            'status' => $budget->status,
            'current_month' => $currentMonth,
            'revenue' => [
                'budget' => $totalBudgetRevenue,
                'actual' => $totalActualRevenue,
                'variance' => $totalActualRevenue - $totalBudgetRevenue,
                'variance_percentage' => $totalBudgetRevenue != 0 ? (($totalActualRevenue - $totalBudgetRevenue) / $totalBudgetRevenue) * 100 : 0,
            ],
            'expense' => [
                'budget' => $totalBudgetExpense,
                'actual' => $totalActualExpense,
                'variance' => $totalActualExpense - $totalBudgetExpense,
                'variance_percentage' => $totalBudgetExpense != 0 ? (($totalActualExpense - $totalBudgetExpense) / $totalBudgetExpense) * 100 : 0,
            ],
            'net_income' => [
                'budget' => $totalBudgetRevenue - $totalBudgetExpense,
                'actual' => $totalActualRevenue - $totalActualExpense,
            ],
            'variance_alerts' => [
                'critical' => $criticalVariances,
                'warning' => $warningVariances,
            ],
        ];
    }

    /**
     * Send approval notification.
     */
    protected function sendApprovalNotification(Budget $budget): void
    {
        // Create notification for budget approval
        SystemNotification::create([
            'client_id' => $budget->client_id,
            'type' => SystemNotification::TYPE_APPROVAL_PENDING,
            'title' => 'Budget Menunggu Persetujuan',
            'message' => "Budget {$budget->budget_name} untuk tahun {$budget->budget_year} menunggu persetujuan",
            'data' => [
                'budget_id' => $budget->id,
                'budget_name' => $budget->budget_name,
                'budget_year' => $budget->budget_year,
                'total_revenue' => $budget->total_revenue_budget,
                'total_expense' => $budget->total_expense_budget,
            ],
            'priority' => SystemNotification::PRIORITY_HIGH,
            'reference_type' => get_class($budget),
            'reference_id' => $budget->id,
            'expires_at' => now()->addDays(14),
        ]);
    }

    /**
     * Check for budget variances and send notifications.
     */
    public function checkBudgetVariances(int $clientId): void
    {
        $activeBudgets = Budget::where('client_id', $clientId)
            ->active()
            ->get();

        foreach ($activeBudgets as $budget) {
            $variances = $this->getBudgetVarianceAnalysis($budget);
            
            $significantVariances = collect($variances)
                ->where('status', 'critical')
                ->toArray();
            
            if (!empty($significantVariances)) {
                $notificationService = app(NotificationService::class);
                $notificationService->sendBudgetVarianceNotification($clientId, $significantVariances);
            }
        }
    }

    /**
     * Copy budget from previous year.
     */
    public function copyFromPreviousYear(int $clientId, int $fromYear, int $toYear, string $budgetName): Budget
    {
        $sourceBudget = Budget::where('client_id', $clientId)
            ->where('budget_year', $fromYear)
            ->first();

        if (!$sourceBudget) {
            throw new \Exception("Budget untuk tahun {$fromYear} tidak ditemukan");
        }

        return DB::transaction(function () use ($sourceBudget, $toYear, $budgetName) {
            // Create new budget
            $newBudget = Budget::create([
                'client_id' => $sourceBudget->client_id,
                'budget_name' => $budgetName,
                'description' => "Budget {$toYear} (disalin dari {$sourceBudget->budget_year})",
                'budget_year' => $toYear,
                'budget_type' => $sourceBudget->budget_type,
                'status' => Budget::STATUS_DRAFT,
                'period_start' => Carbon::create($toYear, 1, 1),
                'period_end' => Carbon::create($toYear, 12, 31),
                'created_by' => auth()->id(),
            ]);

            // Copy budget items
            foreach ($sourceBudget->budgetItems as $sourceItem) {
                BudgetItem::create([
                    'budget_id' => $newBudget->id,
                    'account_id' => $sourceItem->account_id,
                    'cost_center_id' => $sourceItem->cost_center_id,
                    'january' => $sourceItem->january,
                    'february' => $sourceItem->february,
                    'march' => $sourceItem->march,
                    'april' => $sourceItem->april,
                    'may' => $sourceItem->may,
                    'june' => $sourceItem->june,
                    'july' => $sourceItem->july,
                    'august' => $sourceItem->august,
                    'september' => $sourceItem->september,
                    'october' => $sourceItem->october,
                    'november' => $sourceItem->november,
                    'december' => $sourceItem->december,
                    'total_annual' => $sourceItem->total_annual,
                    'notes' => $sourceItem->notes,
                ]);
            }

            $newBudget->calculateTotals();

            return $newBudget;
        });
    }
}
