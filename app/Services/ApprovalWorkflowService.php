<?php

namespace App\Services;

use App\Models\ApprovalRequest;
use App\Models\ApprovalWorkflow;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ApprovalWorkflowService
{
    /**
     * Submit a model for approval.
     */
    public function submitForApproval(Model $model, User $requester, array $data = []): ?ApprovalRequest
    {
        $workflow = $this->findApplicableWorkflow($model, $data);
        
        if (!$workflow) {
            return null; // No workflow needed
        }

        // Check if auto-approve applies
        if ($this->shouldAutoApprove($workflow, $data)) {
            $this->autoApprove($model, $requester);
            return null;
        }

        return DB::transaction(function () use ($workflow, $model, $requester, $data) {
            return $workflow->createApprovalRequest($model, $requester, $data);
        });
    }

    /**
     * Approve a request.
     */
    public function approve(ApprovalRequest $request, User $approver, ?string $comments = null): bool
    {
        if (!$request->isPending()) {
            return false;
        }

        // Check if approver is authorized for current step
        if (!$this->isAuthorizedApprover($request, $approver)) {
            return false;
        }

        return DB::transaction(function () use ($request, $approver, $comments) {
            return $request->approve($approver, $comments);
        });
    }

    /**
     * Reject a request.
     */
    public function reject(ApprovalRequest $request, User $approver, string $reason, ?string $comments = null): bool
    {
        if (!$request->isPending()) {
            return false;
        }

        // Check if approver is authorized for current step
        if (!$this->isAuthorizedApprover($request, $approver)) {
            return false;
        }

        return DB::transaction(function () use ($request, $approver, $reason, $comments) {
            return $request->reject($approver, $reason, $comments);
        });
    }

    /**
     * Delegate approval to another user.
     */
    public function delegate(ApprovalRequest $request, User $approver, User $delegateTo, ?string $comments = null): bool
    {
        if (!$request->isPending()) {
            return false;
        }

        $currentStep = $request->getCurrentStep();
        if (!$currentStep || !$currentStep->can_delegate) {
            return false;
        }

        return DB::transaction(function () use ($request, $approver, $delegateTo, $comments) {
            $request->actions()->create([
                'step_number' => $request->current_step,
                'approver_id' => $approver->id,
                'action' => 'delegated',
                'comments' => $comments,
                'delegated_to' => $delegateTo->id,
            ]);

            return true;
        });
    }

    /**
     * Get pending approvals for a user.
     */
    public function getPendingApprovals(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return ApprovalRequest::pending()
            ->forApprover($user)
            ->with(['approvable', 'approvalWorkflow', 'requester'])
            ->orderBy('requested_at')
            ->get();
    }

    /**
     * Get approval history for a model.
     */
    public function getApprovalHistory(Model $model): \Illuminate\Database\Eloquent\Collection
    {
        return ApprovalRequest::where('approvable_type', get_class($model))
            ->where('approvable_id', $model->id)
            ->with(['actions.approver', 'approvalWorkflow', 'requester'])
            ->orderBy('requested_at', 'desc')
            ->get();
    }

    /**
     * Find applicable workflow for a model.
     */
    private function findApplicableWorkflow(Model $model, array $data = []): ?ApprovalWorkflow
    {
        $workflows = ApprovalWorkflow::active()
            ->forModel(get_class($model))
            ->where('client_id', $model->client_id)
            ->orderBy('max_amount', 'desc') // Higher amount limits first
            ->get();

        foreach ($workflows as $workflow) {
            if ($workflow->appliesTo($model, $data)) {
                return $workflow;
            }
        }

        return null;
    }

    /**
     * Check if auto-approve should be applied.
     */
    private function shouldAutoApprove(ApprovalWorkflow $workflow, array $data): bool
    {
        if (!$workflow->auto_approve_below_limit || !$workflow->auto_approve_limit) {
            return false;
        }

        $amount = $data['amount'] ?? 0;
        return $amount <= $workflow->auto_approve_limit;
    }

    /**
     * Auto-approve a model.
     */
    private function autoApprove(Model $model, User $requester): void
    {
        // Mark model as approved if it has approval status
        if (method_exists($model, 'markAsApproved')) {
            $model->markAsApproved($requester);
        } elseif ($model->hasAttribute('approval_status')) {
            $model->update(['approval_status' => 'approved']);
        }
    }

    /**
     * Check if user is authorized to approve current step.
     */
    private function isAuthorizedApprover(ApprovalRequest $request, User $approver): bool
    {
        $currentStep = $request->getCurrentStep();
        if (!$currentStep) {
            return false;
        }

        $authorizedApprover = $currentStep->getApprover($request->approvable);
        return $authorizedApprover && $authorizedApprover->id === $approver->id;
    }

    /**
     * Check if workflow allows self-approval.
     */
    private function allowsSelfApproval(ApprovalWorkflow $workflow, User $requester, User $approver): bool
    {
        if ($workflow->allow_self_approval) {
            return true;
        }

        return $requester->id !== $approver->id;
    }

    /**
     * Process timeout approvals.
     */
    public function processTimeouts(): int
    {
        $processedCount = 0;
        
        $timedOutRequests = ApprovalRequest::pending()
            ->whereHas('approvalWorkflow.steps', function ($query) {
                $query->whereNotNull('timeout_hours')
                    ->whereRaw('DATE_ADD(approval_requests.requested_at, INTERVAL approval_workflow_steps.timeout_hours HOUR) <= NOW()');
            })
            ->get();

        foreach ($timedOutRequests as $request) {
            $currentStep = $request->getCurrentStep();
            if ($currentStep && $currentStep->hasTimedOut($request->requested_at)) {
                // Auto-approve on timeout
                $systemUser = User::where('email', '<EMAIL>')->first();
                if ($systemUser && $this->approve($request, $systemUser, 'Auto-approved due to timeout')) {
                    $processedCount++;
                }
            }
        }

        return $processedCount;
    }

    /**
     * Cancel an approval request.
     */
    public function cancel(ApprovalRequest $request, User $user, ?string $reason = null): bool
    {
        if (!$request->isPending()) {
            return false;
        }

        // Only requester or admin can cancel
        if ($request->requested_by !== $user->id && !$user->hasRole('admin')) {
            return false;
        }

        return $request->cancel($reason);
    }
}
