<?php

namespace App\Services;

use App\Models\Client;
use App\Models\Product;
use App\Models\Customer;
// SalesOrder and SalesOrderItem removed - using Transaction model instead
// use App\Models\SalesOrder;
// use App\Models\SalesOrderItem;
use App\Models\Transaction;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

// TEMPORARILY DISABLED - Needs refactoring to use Transaction model instead of SalesOrder
class PosIntegrationService_DISABLED
{
    protected string $baseUrl;
    protected string $apiKey;
    protected string $storeId;

    public function __construct()
    {
        $this->baseUrl = config('pos.olsera.base_url', 'https://api.olsera.com/v1');
        $this->apiKey = config('pos.olsera.api_key');
        $this->storeId = config('pos.olsera.store_id');
    }

    /**
     * Sync sales data from POS system.
     */
    public function syncSalesData(int $clientId, ?Carbon $fromDate = null, ?Carbon $toDate = null): array
    {
        try {
            $fromDate = $fromDate ?? now()->subDays(1);
            $toDate = $toDate ?? now();

            Log::info("Starting POS sync for client {$clientId}", [
                'from_date' => $fromDate->toDateString(),
                'to_date' => $toDate->toDateString(),
            ]);

            // Get sales data from POS
            $salesData = $this->fetchSalesFromPos($fromDate, $toDate);

            $syncResults = [
                'total_sales' => count($salesData),
                'processed' => 0,
                'errors' => 0,
                'created_orders' => [],
                'created_transactions' => [],
                'errors_detail' => [],
            ];

            foreach ($salesData as $saleData) {
                try {
                    // Process each sale
                    $result = $this->processSaleData($clientId, $saleData);

                    if ($result['success']) {
                        $syncResults['processed']++;
                        $syncResults['created_orders'][] = $result['sales_order_id'];
                        $syncResults['created_transactions'][] = $result['transaction_id'];
                    } else {
                        $syncResults['errors']++;
                        $syncResults['errors_detail'][] = $result['error'];
                    }
                } catch (\Exception $e) {
                    $syncResults['errors']++;
                    $syncResults['errors_detail'][] = [
                        'sale_id' => $saleData['id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ];
                }
            }

            Log::info("POS sync completed", $syncResults);
            return $syncResults;
        } catch (\Exception $e) {
            Log::error("POS sync failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fetch sales data from POS system.
     */
    protected function fetchSalesFromPos(Carbon $fromDate, Carbon $toDate): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])->get($this->baseUrl . '/sales', [
            'store_id' => $this->storeId,
            'from_date' => $fromDate->toDateString(),
            'to_date' => $toDate->toDateString(),
            'status' => 'completed',
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to fetch sales data from POS: " . $response->body());
        }

        return $response->json('data', []);
    }

    /**
     * Process individual sale data.
     */
    protected function processSaleData(int $clientId, array $saleData): array
    {
        try {
            // Check if sale already exists
            $existingSale = SalesOrder::where('client_id', $clientId)
                ->where('pos_reference_id', $saleData['id'])
                ->first();

            if ($existingSale) {
                return [
                    'success' => true,
                    'sales_order_id' => $existingSale->id,
                    'transaction_id' => null,
                    'message' => 'Sale already exists',
                ];
            }

            // Get or create customer
            $customer = $this->getOrCreateCustomer($clientId, $saleData['customer'] ?? []);

            // Create sales order
            $salesOrder = $this->createSalesOrder($clientId, $customer, $saleData);

            // Create sales order items
            $this->createSalesOrderItems($salesOrder, $saleData['items'] ?? []);

            // Create accounting transaction
            $transaction = $this->createAccountingTransaction($clientId, $salesOrder, $saleData);

            return [
                'success' => true,
                'sales_order_id' => $salesOrder->id,
                'transaction_id' => $transaction->id,
                'message' => 'Sale processed successfully',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get or create customer from POS data.
     */
    protected function getOrCreateCustomer(int $clientId, array $customerData): ?Customer
    {
        if (empty($customerData)) {
            return null;
        }

        $customer = Customer::where('client_id', $clientId)
            ->where('pos_customer_id', $customerData['id'])
            ->first();

        if (!$customer) {
            $customer = Customer::create([
                'client_id' => $clientId,
                'customer_code' => 'POS-' . $customerData['id'],
                'pos_customer_id' => $customerData['id'],
                'name' => $customerData['name'] ?? 'POS Customer',
                'email' => $customerData['email'] ?? null,
                'phone' => $customerData['phone'] ?? null,
                'address' => $customerData['address'] ?? null,
                'city' => $customerData['city'] ?? null,
                'state' => $customerData['state'] ?? null,
                'postal_code' => $customerData['postal_code'] ?? null,
                'is_active' => true,
            ]);
        }

        return $customer;
    }

    /**
     * Create sales order from POS data.
     */
    protected function createSalesOrder(int $clientId, ?Customer $customer, array $saleData): SalesOrder
    {
        return SalesOrder::create([
            'client_id' => $clientId,
            'customer_id' => $customer?->id,
            'sales_order_number' => 'POS-' . $saleData['id'],
            'pos_reference_id' => $saleData['id'],
            'order_date' => Carbon::parse($saleData['created_at']),
            'delivery_date' => Carbon::parse($saleData['created_at']),
            'status' => 'completed',
            'subtotal' => $saleData['subtotal'] ?? 0,
            'tax_amount' => $saleData['tax_amount'] ?? 0,
            'discount_amount' => $saleData['discount_amount'] ?? 0,
            'total_amount' => $saleData['total_amount'] ?? 0,
            'payment_method' => $saleData['payment_method'] ?? 'cash',
            'payment_status' => 'paid',
            'notes' => 'Imported from POS system',
        ]);
    }

    /**
     * Create sales order items.
     */
    protected function createSalesOrderItems(SalesOrder $salesOrder, array $items): void
    {
        foreach ($items as $itemData) {
            // Get or create product
            $product = $this->getOrCreateProduct($salesOrder->client_id, $itemData);

            SalesOrderItem::create([
                'sales_order_id' => $salesOrder->id,
                'product_id' => $product->id,
                'quantity' => $itemData['quantity'] ?? 1,
                'unit_price' => $itemData['unit_price'] ?? 0,
                'discount_amount' => $itemData['discount_amount'] ?? 0,
                'total_amount' => $itemData['total_amount'] ?? 0,
            ]);
        }
    }

    /**
     * Get or create product from POS data.
     */
    protected function getOrCreateProduct(int $clientId, array $itemData): Product
    {
        $product = Product::where('client_id', $clientId)
            ->where('pos_product_id', $itemData['product_id'])
            ->first();

        if (!$product) {
            $product = Product::create([
                'client_id' => $clientId,
                'product_code' => 'POS-' . $itemData['product_id'],
                'pos_product_id' => $itemData['product_id'],
                'name' => $itemData['product_name'] ?? 'POS Product',
                'selling_price' => $itemData['unit_price'] ?? 0,
                'purchase_price' => $itemData['unit_price'] ?? 0,
                'is_active' => true,
            ]);
        }

        return $product;
    }

    /**
     * Create accounting transaction for the sale.
     */
    protected function createAccountingTransaction(int $clientId, SalesOrder $salesOrder, array $saleData): Transaction
    {
        // Get accounts
        $salesAccount = Account::where('client_id', $clientId)
            ->where('account_code', '4000')
            ->first();

        $cashAccount = Account::where('client_id', $clientId)
            ->where('account_code', '1100')
            ->first();

        if (!$salesAccount || !$cashAccount) {
            throw new \Exception('Required accounts not found for POS transaction');
        }

        // Create journal
        $journal = Journal::create([
            'client_id' => $clientId,
            'journal_number' => 'POS-' . $saleData['id'],
            'transaction_date' => Carbon::parse($saleData['created_at']),
            'reference_type' => 'pos_sale',
            'reference_id' => $salesOrder->id,
            'reference_number' => $salesOrder->sales_order_number,
            'description' => 'POS Sale - ' . $salesOrder->sales_order_number,
            'total_amount' => $salesOrder->total_amount,
        ]);

        // Create journal entries
        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $cashAccount->id,
            'description' => 'Cash from POS sale',
            'debit' => $salesOrder->total_amount,
            'credit' => 0,
        ]);

        JournalEntry::create([
            'journal_id' => $journal->id,
            'account_id' => $salesAccount->id,
            'description' => 'Sales revenue from POS',
            'debit' => 0,
            'credit' => $salesOrder->total_amount,
        ]);

        // Create transaction record
        return Transaction::create([
            'client_id' => $clientId,
            'account_id' => $cashAccount->id,
            'transaction_date' => Carbon::parse($saleData['created_at']),
            'type' => 'income',
            'amount' => $salesOrder->total_amount,
            'description' => 'POS Sale - ' . $salesOrder->sales_order_number,
            'reference_number' => $salesOrder->sales_order_number,
            'status' => 'completed',
        ]);
    }

    /**
     * Sync product data to POS system.
     */
    public function syncProductsToPos(int $clientId): array
    {
        $products = Product::where('client_id', $clientId)
            ->where('is_active', true)
            ->whereNull('pos_product_id')
            ->get();

        $syncResults = [
            'total_products' => $products->count(),
            'synced' => 0,
            'errors' => 0,
            'errors_detail' => [],
        ];

        foreach ($products as $product) {
            try {
                $posProductId = $this->createProductInPos($product);

                $product->update(['pos_product_id' => $posProductId]);
                $syncResults['synced']++;
            } catch (\Exception $e) {
                $syncResults['errors']++;
                $syncResults['errors_detail'][] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $syncResults;
    }

    /**
     * Create product in POS system.
     */
    protected function createProductInPos(Product $product): string
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl . '/products', [
            'store_id' => $this->storeId,
            'name' => $product->name,
            'sku' => $product->product_code,
            'price' => $product->selling_price,
            'cost' => $product->purchase_price,
            'category' => $product->category?->name,
            'description' => $product->description,
            'is_active' => $product->is_active,
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to create product in POS: " . $response->body());
        }

        return $response->json('data.id');
    }

    /**
     * Get POS system status.
     */
    public function getPosStatus(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->baseUrl . '/status');

            if (!$response->successful()) {
                return [
                    'connected' => false,
                    'error' => 'Failed to connect to POS system',
                ];
            }

            return [
                'connected' => true,
                'store_id' => $this->storeId,
                'last_sync' => now()->toDateTimeString(),
                'status' => $response->json('status'),
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
