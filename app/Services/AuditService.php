<?php

namespace App\Services;

use App\Models\AuditLog;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class AuditService
{
    /**
     * Get audit logs with filters.
     */
    public function getAuditLogs(int $clientId, array $filters = []): Collection
    {
        $query = AuditLog::where('client_id', $clientId)
            ->with(['user'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['event'])) {
            $query->where('event', $filters['event']);
        }

        if (isset($filters['auditable_type'])) {
            $query->where('auditable_type', $filters['auditable_type']);
        }

        if (isset($filters['auditable_id'])) {
            $query->where('auditable_id', $filters['auditable_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                    ->orWhere('url', 'like', "%{$search}%")
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        return $query->get();
    }

    /**
     * Get audit logs for specific model.
     */
    public function getModelAuditLogs(string $modelType, int $modelId, int $clientId): Collection
    {
        return AuditLog::where('client_id', $clientId)
            ->forModel($modelType, $modelId)
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get audit statistics.
     */
    public function getAuditStatistics(int $clientId, ?string $period = 'month'): array
    {
        $startDate = match ($period) {
            'day' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };

        $endDate = now();

        // Total activities
        $totalActivities = AuditLog::where('client_id', $clientId)
            ->inDateRange($startDate, $endDate)
            ->count();

        // Activities by event type
        $activitiesByEvent = AuditLog::where('client_id', $clientId)
            ->inDateRange($startDate, $endDate)
            ->select('event', DB::raw('count(*) as count'))
            ->groupBy('event')
            ->pluck('count', 'event')
            ->toArray();

        // Activities by user
        $activitiesByUser = AuditLog::where('client_id', $clientId)
            ->inDateRange($startDate, $endDate)
            ->with('user')
            ->select('user_id', DB::raw('count(*) as count'))
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'user_name' => $item->user->name ?? 'Unknown',
                    'count' => $item->count,
                ];
            })
            ->toArray();

        // Activities by model type
        $activitiesByModel = AuditLog::where('client_id', $clientId)
            ->inDateRange($startDate, $endDate)
            ->select('auditable_type', DB::raw('count(*) as count'))
            ->groupBy('auditable_type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'model_name' => $this->getModelDisplayName($item->auditable_type),
                    'count' => $item->count,
                ];
            })
            ->toArray();

        // Daily activity trend (last 30 days)
        $dailyTrend = AuditLog::where('client_id', $clientId)
            ->where('created_at', '>=', now()->subDays(30))
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        return [
            'total_activities' => $totalActivities,
            'activities_by_event' => $activitiesByEvent,
            'activities_by_user' => $activitiesByUser,
            'activities_by_model' => $activitiesByModel,
            'daily_trend' => $dailyTrend,
            'period' => $period,
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
        ];
    }

    /**
     * Get recent activities.
     */
    public function getRecentActivities(int $clientId, int $limit = 20): Collection
    {
        return AuditLog::where('client_id', $clientId)
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get user activity summary.
     */
    public function getUserActivitySummary(int $userId, int $clientId, ?string $period = 'month'): array
    {
        $startDate = match ($period) {
            'day' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };

        $activities = AuditLog::where('client_id', $clientId)
            ->where('user_id', $userId)
            ->inDateRange($startDate, now())
            ->get();

        $summary = [
            'total_activities' => $activities->count(),
            'activities_by_event' => $activities->groupBy('event')->map->count(),
            'activities_by_model' => $activities->groupBy('auditable_type')->map->count(),
            'last_activity' => $activities->first()?->created_at,
            'most_active_day' => $activities->groupBy(function ($item) {
                return $item->created_at->format('Y-m-d');
            })->sortByDesc->count()->keys()->first(),
        ];

        return $summary;
    }

    /**
     * Clean up old audit logs.
     */
    public function cleanupOldLogs(int $clientId, int $daysToKeep = 365): int
    {
        $cutoffDate = now()->subDays($daysToKeep);

        return AuditLog::where('client_id', $clientId)
            ->where('created_at', '<', $cutoffDate)
            ->delete();
    }

    /**
     * Export audit logs to array.
     */
    public function exportAuditLogs(int $clientId, array $filters = []): array
    {
        $logs = $this->getAuditLogs($clientId, $filters);

        return $logs->map(function ($log) {
            return [
                'id' => $log->id,
                'timestamp' => $log->created_at->format('Y-m-d H:i:s'),
                'user' => $log->user->name ?? 'System',
                'event' => $log->getEventLabel(),
                'model' => $log->getModelName(),
                'description' => $log->description,
                'ip_address' => $log->ip_address,
                'url' => $log->url,
                'method' => $log->method,
                'changed_fields' => $log->changed_fields ? implode(', ', $log->changed_fields) : '',
            ];
        })->toArray();
    }

    /**
     * Get model display name.
     */
    protected function getModelDisplayName(string $modelClass): string
    {
        return match ($modelClass) {
            'App\\Models\\Account' => 'Akun',
            'App\\Models\\Transaction' => 'Transaksi',
            'App\\Models\\Journal' => 'Jurnal',
            'App\\Models\\JournalEntry' => 'Jurnal Entry',
            'App\\Models\\Product' => 'Produk',
            'App\\Models\\Customer' => 'Pelanggan',
            'App\\Models\\Supplier' => 'Pemasok',
            'App\\Models\\PurchaseOrder' => 'Pesanan Pembelian',
            // 'App\\Models\\SalesOrder' => 'Sales Order', // Removed - using Transaction model
            'App\\Models\\Budget' => 'Anggaran',
            'App\\Models\\FixedAsset' => 'Aset Tetap',
            'App\\Models\\User' => 'Pengguna',
            'App\\Models\\Client' => 'Klien',
            'App\\Models\\Inventory' => 'Inventori',
            'App\\Models\\BankAccount' => 'Rekening Bank',
            default => class_basename($modelClass),
        };
    }

    /**
     * Create manual audit log entry.
     */
    public function createManualLog(
        int $clientId,
        int $userId,
        string $event,
        string $description,
        ?array $metadata = null
    ): AuditLog {
        return AuditLog::create([
            'client_id' => $clientId,
            'user_id' => $userId,
            'event' => $event,
            'auditable_type' => null,
            'auditable_id' => null,
            'old_values' => null,
            'new_values' => null,
            'changed_fields' => null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'description' => $description,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Get audit trail for a specific record.
     */
    public function getRecordAuditTrail(string $modelType, int $modelId, int $clientId): array
    {
        $logs = $this->getModelAuditLogs($modelType, $modelId, $clientId);

        $trail = [];

        foreach ($logs as $log) {
            $trail[] = [
                'timestamp' => $log->created_at,
                'user' => $log->user->name ?? 'System',
                'event' => $log->getEventLabel(),
                'description' => $log->description,
                'changes' => $log->getChangedFieldsFormatted(),
                'ip_address' => $log->ip_address,
                'metadata' => $log->metadata,
            ];
        }

        return $trail;
    }
}
