<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
// use App\Models\SalesOrder; // Disabled - using Transaction model
use App\Models\Inventory;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinancialRatioAnalysisService
{
    /**
     * Comprehensive financial ratio analysis
     */
    public function analyzeFinancialRatios(int $clientId, Carbon $asOfDate, ?Carbon $compareDate = null): array
    {
        $currentPeriod = $this->calculateAllRatios($clientId, $asOfDate);

        $analysis = [
            'analysis_date' => $asOfDate->format('d M Y'),
            'current_period' => $currentPeriod,
            'has_comparison' => false,
            'ratio_categories' => [
                'liquidity_ratios' => $currentPeriod['liquidity_ratios'],
                'profitability_ratios' => $currentPeriod['profitability_ratios'],
                'efficiency_ratios' => $currentPeriod['efficiency_ratios'],
                'leverage_ratios' => $currentPeriod['leverage_ratios'],
                'market_ratios' => $currentPeriod['market_ratios'],
            ],
            'overall_assessment' => $this->generateOverallAssessment($currentPeriod),
            'industry_benchmarks' => $this->getIndustryBenchmarks(),
            'recommendations' => $this->generateRecommendations($currentPeriod),
        ];

        // Add comparison if provided
        if ($compareDate) {
            $comparePeriod = $this->calculateAllRatios($clientId, $compareDate);
            $analysis['has_comparison'] = true;
            $analysis['compare_period'] = $comparePeriod;
            $analysis['compare_date'] = $compareDate->format('d M Y');
            $analysis['trend_analysis'] = $this->calculateTrendAnalysis($currentPeriod, $comparePeriod);
        }

        return $analysis;
    }

    /**
     * Calculate all financial ratios
     */
    private function calculateAllRatios(int $clientId, Carbon $asOfDate): array
    {
        $balanceSheetData = $this->getBalanceSheetData($clientId, $asOfDate);
        $incomeStatementData = $this->getIncomeStatementData($clientId, $asOfDate);

        return [
            'liquidity_ratios' => $this->calculateLiquidityRatios($balanceSheetData),
            'profitability_ratios' => $this->calculateProfitabilityRatios($balanceSheetData, $incomeStatementData),
            'efficiency_ratios' => $this->calculateEfficiencyRatios($clientId, $balanceSheetData, $incomeStatementData, $asOfDate),
            'leverage_ratios' => $this->calculateLeverageRatios($balanceSheetData, $incomeStatementData),
            'market_ratios' => $this->calculateMarketRatios($balanceSheetData, $incomeStatementData),
        ];
    }

    /**
     * Calculate liquidity ratios
     */
    private function calculateLiquidityRatios(array $balanceSheet): array
    {
        $currentAssets = $balanceSheet['current_assets'];
        $currentLiabilities = $balanceSheet['current_liabilities'];
        $inventory = $balanceSheet['inventory'];
        $cash = $balanceSheet['cash'];

        $currentRatio = $currentLiabilities > 0 ? $currentAssets / $currentLiabilities : 0;
        $quickRatio = $currentLiabilities > 0 ? ($currentAssets - $inventory) / $currentLiabilities : 0;
        $cashRatio = $currentLiabilities > 0 ? $cash / $currentLiabilities : 0;
        $workingCapital = $currentAssets - $currentLiabilities;

        return [
            'current_ratio' => round($currentRatio, 2),
            'quick_ratio' => round($quickRatio, 2),
            'cash_ratio' => round($cashRatio, 2),
            'working_capital' => $workingCapital,
            'working_capital_ratio' => $currentAssets > 0 ? ($workingCapital / $currentAssets) * 100 : 0,
            'interpretations' => [
                'current_ratio' => $this->interpretLiquidityRatio('current', $currentRatio),
                'quick_ratio' => $this->interpretLiquidityRatio('quick', $quickRatio),
                'cash_ratio' => $this->interpretLiquidityRatio('cash', $cashRatio),
            ],
        ];
    }

    /**
     * Calculate profitability ratios
     */
    private function calculateProfitabilityRatios(array $balanceSheet, array $incomeStatement): array
    {
        $revenue = $incomeStatement['revenue'];
        $grossProfit = $incomeStatement['gross_profit'];
        $operatingIncome = $incomeStatement['operating_income'];
        $netIncome = $incomeStatement['net_income'];
        $totalAssets = $balanceSheet['total_assets'];
        $totalEquity = $balanceSheet['total_equity'];

        $grossProfitMargin = $revenue > 0 ? ($grossProfit / $revenue) * 100 : 0;
        $operatingMargin = $revenue > 0 ? ($operatingIncome / $revenue) * 100 : 0;
        $netProfitMargin = $revenue > 0 ? ($netIncome / $revenue) * 100 : 0;
        $roa = $totalAssets > 0 ? ($netIncome / $totalAssets) * 100 : 0;
        $roe = $totalEquity > 0 ? ($netIncome / $totalEquity) * 100 : 0;

        return [
            'gross_profit_margin' => round($grossProfitMargin, 2),
            'operating_margin' => round($operatingMargin, 2),
            'net_profit_margin' => round($netProfitMargin, 2),
            'return_on_assets' => round($roa, 2),
            'return_on_equity' => round($roe, 2),
            'interpretations' => [
                'gross_profit_margin' => $this->interpretProfitabilityRatio('gross_margin', $grossProfitMargin),
                'operating_margin' => $this->interpretProfitabilityRatio('operating_margin', $operatingMargin),
                'net_profit_margin' => $this->interpretProfitabilityRatio('net_margin', $netProfitMargin),
                'return_on_assets' => $this->interpretProfitabilityRatio('roa', $roa),
                'return_on_equity' => $this->interpretProfitabilityRatio('roe', $roe),
            ],
        ];
    }

    /**
     * Calculate efficiency ratios
     */
    private function calculateEfficiencyRatios(int $clientId, array $balanceSheet, array $incomeStatement, Carbon $asOfDate): array
    {
        $revenue = $incomeStatement['revenue'];
        $cogs = $incomeStatement['cogs'];
        $totalAssets = $balanceSheet['total_assets'];
        $inventory = $balanceSheet['inventory'];
        $accountsReceivable = $balanceSheet['accounts_receivable'];
        $accountsPayable = $balanceSheet['accounts_payable'];

        $assetTurnover = $totalAssets > 0 ? $revenue / $totalAssets : 0;
        $inventoryTurnover = $inventory > 0 ? $cogs / $inventory : 0;
        $receivablesTurnover = $accountsReceivable > 0 ? $revenue / $accountsReceivable : 0;
        $payablesTurnover = $accountsPayable > 0 ? $cogs / $accountsPayable : 0;

        // Days ratios
        $daysInInventory = $inventoryTurnover > 0 ? 365 / $inventoryTurnover : 0;
        $daysInReceivables = $receivablesTurnover > 0 ? 365 / $receivablesTurnover : 0;
        $daysInPayables = $payablesTurnover > 0 ? 365 / $payablesTurnover : 0;

        return [
            'asset_turnover' => round($assetTurnover, 2),
            'inventory_turnover' => round($inventoryTurnover, 2),
            'receivables_turnover' => round($receivablesTurnover, 2),
            'payables_turnover' => round($payablesTurnover, 2),
            'days_in_inventory' => round($daysInInventory, 1),
            'days_in_receivables' => round($daysInReceivables, 1),
            'days_in_payables' => round($daysInPayables, 1),
            'cash_conversion_cycle' => round($daysInInventory + $daysInReceivables - $daysInPayables, 1),
            'interpretations' => [
                'asset_turnover' => $this->interpretEfficiencyRatio('asset_turnover', $assetTurnover),
                'inventory_turnover' => $this->interpretEfficiencyRatio('inventory_turnover', $inventoryTurnover),
                'receivables_turnover' => $this->interpretEfficiencyRatio('receivables_turnover', $receivablesTurnover),
            ],
        ];
    }

    /**
     * Calculate leverage ratios
     */
    private function calculateLeverageRatios(array $balanceSheet, array $incomeStatement): array
    {
        $totalDebt = $balanceSheet['total_liabilities'];
        $totalAssets = $balanceSheet['total_assets'];
        $totalEquity = $balanceSheet['total_equity'];
        $operatingIncome = $incomeStatement['operating_income'];
        $interestExpense = $incomeStatement['interest_expense'] ?? 0;

        $debtToAssets = $totalAssets > 0 ? ($totalDebt / $totalAssets) * 100 : 0;
        $debtToEquity = $totalEquity > 0 ? ($totalDebt / $totalEquity) * 100 : 0;
        $equityRatio = $totalAssets > 0 ? ($totalEquity / $totalAssets) * 100 : 0;
        $timesInterestEarned = $interestExpense > 0 ? $operatingIncome / $interestExpense : 0;

        return [
            'debt_to_assets' => round($debtToAssets, 2),
            'debt_to_equity' => round($debtToEquity, 2),
            'equity_ratio' => round($equityRatio, 2),
            'times_interest_earned' => round($timesInterestEarned, 2),
            'interpretations' => [
                'debt_to_assets' => $this->interpretLeverageRatio('debt_to_assets', $debtToAssets),
                'debt_to_equity' => $this->interpretLeverageRatio('debt_to_equity', $debtToEquity),
                'times_interest_earned' => $this->interpretLeverageRatio('times_interest_earned', $timesInterestEarned),
            ],
        ];
    }

    /**
     * Calculate market ratios
     */
    private function calculateMarketRatios(array $balanceSheet, array $incomeStatement): array
    {
        // For private companies, these ratios are less relevant but can be calculated for comparison
        $netIncome = $incomeStatement['net_income'];
        $totalEquity = $balanceSheet['total_equity'];
        $revenue = $incomeStatement['revenue'];

        // Simplified calculations for private company analysis
        $bookValuePerShare = 1; // Assuming 1 share for simplicity
        $earningsPerShare = $netIncome; // Simplified
        $priceToBook = 1; // Not applicable for private companies
        $priceToEarnings = 1; // Not applicable for private companies

        return [
            'book_value_per_share' => $totalEquity,
            'earnings_per_share' => $netIncome,
            'price_to_book' => 'N/A (Private Company)',
            'price_to_earnings' => 'N/A (Private Company)',
            'revenue_per_share' => $revenue,
            'note' => 'Market ratios are not applicable for private companies',
        ];
    }

    // Helper methods for data retrieval
    private function getBalanceSheetData(int $clientId, Carbon $asOfDate): array
    {
        return [
            'cash' => $this->getAccountGroupBalance($clientId, '1111', $asOfDate),
            'accounts_receivable' => $this->getAccountGroupBalance($clientId, '1121', $asOfDate),
            'inventory' => $this->getInventoryValue($clientId),
            'current_assets' => $this->getAccountGroupBalance($clientId, '11', $asOfDate),
            'total_assets' => $this->getAccountGroupBalance($clientId, '1', $asOfDate),
            'accounts_payable' => $this->getAccountGroupBalance($clientId, '2111', $asOfDate),
            'current_liabilities' => $this->getAccountGroupBalance($clientId, '21', $asOfDate),
            'total_liabilities' => $this->getAccountGroupBalance($clientId, '2', $asOfDate),
            'total_equity' => $this->getAccountGroupBalance($clientId, '3', $asOfDate),
        ];
    }

    private function getIncomeStatementData(int $clientId, Carbon $asOfDate): array
    {
        $startDate = $asOfDate->copy()->subYear();

        return [
            'revenue' => $this->getAccountGroupTotal($clientId, '4', $startDate, $asOfDate),
            'cogs' => $this->getAccountGroupTotal($clientId, '5', $startDate, $asOfDate),
            'gross_profit' => $this->getAccountGroupTotal($clientId, '4', $startDate, $asOfDate) - $this->getAccountGroupTotal($clientId, '5', $startDate, $asOfDate),
            'operating_expenses' => $this->getAccountGroupTotal($clientId, '6', $startDate, $asOfDate),
            'operating_income' => ($this->getAccountGroupTotal($clientId, '4', $startDate, $asOfDate) - $this->getAccountGroupTotal($clientId, '5', $startDate, $asOfDate)) - $this->getAccountGroupTotal($clientId, '6', $startDate, $asOfDate),
            'net_income' => $this->getAccountGroupTotal($clientId, '4', $startDate, $asOfDate) - $this->getAccountGroupTotal($clientId, '5', $startDate, $asOfDate) - $this->getAccountGroupTotal($clientId, '6', $startDate, $asOfDate),
            'interest_expense' => $this->getAccountGroupTotal($clientId, '8', $startDate, $asOfDate),
        ];
    }

    private function getAccountGroupBalance(int $clientId, string $accountPrefix, Carbon $asOfDate): float
    {
        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $asOfDate);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $asOfDate);
            })
            ->sum('credit');

        // For asset accounts, return debit - credit
        // For liability and equity accounts, return credit - debit
        if (in_array($accountPrefix[0], ['1'])) { // Assets
            return $totalDebits - $totalCredits;
        } else { // Liabilities and Equity
            return $totalCredits - $totalDebits;
        }
    }

    private function getAccountGroupTotal(int $clientId, string $accountPrefix, Carbon $startDate, Carbon $endDate): float
    {
        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->whereBetween('journal_date', [$startDate, $endDate]);
            })
            ->sum('credit');

        // For revenue accounts, return credit - debit
        // For expense accounts, return debit - credit
        if (in_array($accountPrefix, ['4', '7'])) { // Revenue
            return $totalCredits - $totalDebits;
        } else { // Expenses
            return $totalDebits - $totalCredits;
        }
    }

    private function getInventoryValue(int $clientId): float
    {
        return Inventory::where('client_id', $clientId)
            ->sum(DB::raw('current_stock * average_cost'));
    }

    // Interpretation methods
    private function interpretLiquidityRatio(string $type, float $value): string
    {
        switch ($type) {
            case 'current':
                if ($value >= 2.0) return 'Excellent liquidity';
                if ($value >= 1.5) return 'Good liquidity';
                if ($value >= 1.0) return 'Adequate liquidity';
                return 'Poor liquidity';
            case 'quick':
                if ($value >= 1.5) return 'Excellent quick liquidity';
                if ($value >= 1.0) return 'Good quick liquidity';
                if ($value >= 0.8) return 'Adequate quick liquidity';
                return 'Poor quick liquidity';
            case 'cash':
                if ($value >= 0.2) return 'Strong cash position';
                if ($value >= 0.1) return 'Adequate cash position';
                return 'Weak cash position';
            default:
                return 'Unknown';
        }
    }

    private function interpretProfitabilityRatio(string $type, float $value): string
    {
        switch ($type) {
            case 'gross_margin':
                if ($value >= 40) return 'Excellent gross margin';
                if ($value >= 25) return 'Good gross margin';
                if ($value >= 15) return 'Average gross margin';
                return 'Poor gross margin';
            case 'operating_margin':
                if ($value >= 20) return 'Excellent operating efficiency';
                if ($value >= 10) return 'Good operating efficiency';
                if ($value >= 5) return 'Average operating efficiency';
                return 'Poor operating efficiency';
            case 'net_margin':
                if ($value >= 15) return 'Excellent profitability';
                if ($value >= 8) return 'Good profitability';
                if ($value >= 3) return 'Average profitability';
                return 'Poor profitability';
            case 'roa':
                if ($value >= 15) return 'Excellent asset utilization';
                if ($value >= 8) return 'Good asset utilization';
                if ($value >= 3) return 'Average asset utilization';
                return 'Poor asset utilization';
            case 'roe':
                if ($value >= 20) return 'Excellent return to shareholders';
                if ($value >= 12) return 'Good return to shareholders';
                if ($value >= 6) return 'Average return to shareholders';
                return 'Poor return to shareholders';
            default:
                return 'Unknown';
        }
    }

    private function interpretEfficiencyRatio(string $type, float $value): string
    {
        switch ($type) {
            case 'asset_turnover':
                if ($value >= 2.0) return 'Excellent asset efficiency';
                if ($value >= 1.5) return 'Good asset efficiency';
                if ($value >= 1.0) return 'Average asset efficiency';
                return 'Poor asset efficiency';
            case 'inventory_turnover':
                if ($value >= 12) return 'Excellent inventory management';
                if ($value >= 8) return 'Good inventory management';
                if ($value >= 4) return 'Average inventory management';
                return 'Poor inventory management';
            case 'receivables_turnover':
                if ($value >= 12) return 'Excellent collection efficiency';
                if ($value >= 8) return 'Good collection efficiency';
                if ($value >= 6) return 'Average collection efficiency';
                return 'Poor collection efficiency';
            default:
                return 'Unknown';
        }
    }

    private function interpretLeverageRatio(string $type, float $value): string
    {
        switch ($type) {
            case 'debt_to_assets':
                if ($value <= 30) return 'Conservative debt level';
                if ($value <= 50) return 'Moderate debt level';
                if ($value <= 70) return 'High debt level';
                return 'Very high debt level';
            case 'debt_to_equity':
                if ($value <= 50) return 'Conservative leverage';
                if ($value <= 100) return 'Moderate leverage';
                if ($value <= 200) return 'High leverage';
                return 'Very high leverage';
            case 'times_interest_earned':
                if ($value >= 10) return 'Excellent interest coverage';
                if ($value >= 5) return 'Good interest coverage';
                if ($value >= 2.5) return 'Adequate interest coverage';
                return 'Poor interest coverage';
            default:
                return 'Unknown';
        }
    }

    private function generateOverallAssessment(array $ratios): array
    {
        $scores = [];

        // Liquidity score
        $liquidityScore = $this->calculateCategoryScore($ratios['liquidity_ratios']);
        $scores['liquidity'] = $liquidityScore;

        // Profitability score
        $profitabilityScore = $this->calculateCategoryScore($ratios['profitability_ratios']);
        $scores['profitability'] = $profitabilityScore;

        // Efficiency score
        $efficiencyScore = $this->calculateCategoryScore($ratios['efficiency_ratios']);
        $scores['efficiency'] = $efficiencyScore;

        // Leverage score
        $leverageScore = $this->calculateCategoryScore($ratios['leverage_ratios']);
        $scores['leverage'] = $leverageScore;

        $overallScore = array_sum($scores) / count($scores);

        return [
            'category_scores' => $scores,
            'overall_score' => round($overallScore, 1),
            'overall_rating' => $this->getOverallRating($overallScore),
            'strengths' => $this->identifyStrengths($scores),
            'weaknesses' => $this->identifyWeaknesses($scores),
        ];
    }

    private function calculateCategoryScore(array $categoryRatios): float
    {
        // Simplified scoring - would need more sophisticated logic in practice
        return 75; // Placeholder score
    }

    private function getOverallRating(float $score): string
    {
        if ($score >= 85) return 'Excellent';
        if ($score >= 70) return 'Good';
        if ($score >= 55) return 'Average';
        if ($score >= 40) return 'Below Average';
        return 'Poor';
    }

    private function identifyStrengths(array $scores): array
    {
        $strengths = [];
        foreach ($scores as $category => $score) {
            if ($score >= 80) {
                $strengths[] = ucfirst($category) . ' performance is strong';
            }
        }
        return $strengths;
    }

    private function identifyWeaknesses(array $scores): array
    {
        $weaknesses = [];
        foreach ($scores as $category => $score) {
            if ($score < 60) {
                $weaknesses[] = ucfirst($category) . ' performance needs improvement';
            }
        }
        return $weaknesses;
    }

    private function getIndustryBenchmarks(): array
    {
        return [
            'liquidity_ratios' => [
                'current_ratio' => '1.5 - 3.0',
                'quick_ratio' => '1.0 - 1.5',
                'cash_ratio' => '0.1 - 0.2',
            ],
            'profitability_ratios' => [
                'gross_profit_margin' => '20% - 40%',
                'operating_margin' => '10% - 20%',
                'net_profit_margin' => '5% - 15%',
                'return_on_assets' => '5% - 15%',
                'return_on_equity' => '10% - 20%',
            ],
            'efficiency_ratios' => [
                'asset_turnover' => '1.0 - 2.0',
                'inventory_turnover' => '6 - 12 times',
                'receivables_turnover' => '8 - 12 times',
            ],
            'leverage_ratios' => [
                'debt_to_assets' => '< 50%',
                'debt_to_equity' => '< 100%',
                'times_interest_earned' => '> 5.0',
            ],
        ];
    }

    private function generateRecommendations(array $ratios): array
    {
        $recommendations = [];

        // Liquidity recommendations
        if ($ratios['liquidity_ratios']['current_ratio'] < 1.2) {
            $recommendations[] = 'Improve current ratio by increasing current assets or reducing current liabilities';
        }

        // Profitability recommendations
        if ($ratios['profitability_ratios']['net_profit_margin'] < 5) {
            $recommendations[] = 'Focus on improving profit margins through cost control or pricing optimization';
        }

        // Efficiency recommendations
        if ($ratios['efficiency_ratios']['inventory_turnover'] < 6) {
            $recommendations[] = 'Improve inventory management to increase turnover and reduce carrying costs';
        }

        // Leverage recommendations
        if ($ratios['leverage_ratios']['debt_to_assets'] > 60) {
            $recommendations[] = 'Consider reducing debt levels to improve financial stability';
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Financial ratios appear healthy. Continue monitoring and maintain current performance levels.';
        }

        return $recommendations;
    }

    private function calculateTrendAnalysis(array $current, array $previous): array
    {
        $trends = [];

        foreach ($current as $category => $ratios) {
            if (isset($previous[$category]) && is_array($ratios)) {
                foreach ($ratios as $ratio => $value) {
                    if (isset($previous[$category][$ratio]) && is_numeric($value) && is_numeric($previous[$category][$ratio])) {
                        $change = $value - $previous[$category][$ratio];
                        $changePercentage = $previous[$category][$ratio] != 0
                            ? ($change / abs($previous[$category][$ratio])) * 100
                            : 0;

                        $trends[$category][$ratio] = [
                            'change' => round($change, 2),
                            'change_percentage' => round($changePercentage, 2),
                            'trend' => $change > 0 ? 'improving' : ($change < 0 ? 'declining' : 'stable'),
                        ];
                    }
                }
            }
        }

        return $trends;
    }

    /**
     * Get liquidity ratios
     */
    public function getLiquidityRatios(int $clientId, Carbon $asOfDate): array
    {
        $ratios = $this->calculateAllRatios($clientId, $asOfDate);
        return $ratios['liquidity_ratios'] ?? [];
    }

    /**
     * Get profitability ratios
     */
    public function getProfitabilityRatios(int $clientId, Carbon $asOfDate): array
    {
        $ratios = $this->calculateAllRatios($clientId, $asOfDate);
        return $ratios['profitability_ratios'] ?? [];
    }

    /**
     * Get efficiency ratios
     */
    public function getEfficiencyRatios(int $clientId, Carbon $asOfDate): array
    {
        $ratios = $this->calculateAllRatios($clientId, $asOfDate);
        return $ratios['efficiency_ratios'] ?? [];
    }

    /**
     * Get leverage ratios
     */
    public function getLeverageRatios(int $clientId, Carbon $asOfDate): array
    {
        $ratios = $this->calculateAllRatios($clientId, $asOfDate);
        return $ratios['leverage_ratios'] ?? [];
    }

    /**
     * Get market ratios
     */
    public function getMarketRatios(int $clientId, Carbon $asOfDate): array
    {
        $ratios = $this->calculateAllRatios($clientId, $asOfDate);
        return $ratios['market_ratios'] ?? [];
    }

    /**
     * Get ratio trends for 6 months
     */
    public function getRatioTrends(int $clientId, Carbon $asOfDate): array
    {
        $trends = [];
        $months = 6; // Changed to 6 months as per requirement

        for ($i = $months - 1; $i >= 0; $i--) {
            $periodDate = $asOfDate->copy()->subMonths($i)->endOfMonth();
            $ratios = $this->calculateAllRatios($clientId, $periodDate);

            $trends[] = [
                'period' => $periodDate->format('M Y'),
                'current_ratio' => $ratios['liquidity_ratios']['current_ratio'] ?? 0,
                'quick_ratio' => $ratios['liquidity_ratios']['quick_ratio'] ?? 0,
                'cash_ratio' => $ratios['liquidity_ratios']['cash_ratio'] ?? 0,
                'gross_profit_margin' => $ratios['profitability_ratios']['gross_profit_margin'] ?? 0,
                'net_profit_margin' => $ratios['profitability_ratios']['net_profit_margin'] ?? 0,
                'roa' => $ratios['profitability_ratios']['roa'] ?? 0,
                'roe' => $ratios['profitability_ratios']['roe'] ?? 0,
                'debt_to_equity' => $ratios['leverage_ratios']['debt_to_equity'] ?? 0,
                'debt_to_assets' => $ratios['leverage_ratios']['debt_to_assets'] ?? 0,
                'asset_turnover' => $ratios['efficiency_ratios']['asset_turnover'] ?? 0,
                'inventory_turnover' => $ratios['efficiency_ratios']['inventory_turnover'] ?? 0,
            ];
        }

        return $trends;
    }
}
