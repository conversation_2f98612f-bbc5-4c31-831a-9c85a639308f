<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\Account;
use App\Models\Transaction;
use App\Models\Journal;
use App\Models\JournalEntry;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class CurrencyService
{
    protected string $baseCurrency;
    protected array $exchangeRateProviders;

    public function __construct()
    {
        $this->baseCurrency = config('currency.base_currency', 'IDR');
        $this->exchangeRateProviders = config('currency.providers', []);
    }

    /**
     * Get current exchange rate between two currencies.
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency, ?Carbon $date = null): float
    {
        $date = $date ?? now();
        
        // If same currency, return 1
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        // Check cache first
        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}_{$date->toDateString()}";
        $cachedRate = Cache::get($cacheKey);
        
        if ($cachedRate !== null) {
            return $cachedRate;
        }

        // Check database
        $exchangeRate = ExchangeRate::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->whereDate('rate_date', $date->toDateString())
            ->first();

        if ($exchangeRate) {
            Cache::put($cacheKey, $exchangeRate->rate, now()->addHours(1));
            return $exchangeRate->rate;
        }

        // Fetch from external API
        try {
            $rate = $this->fetchExchangeRateFromApi($fromCurrency, $toCurrency, $date);
            
            // Store in database
            ExchangeRate::create([
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'rate' => $rate,
                'rate_date' => $date,
                'source' => 'api',
            ]);

            Cache::put($cacheKey, $rate, now()->addHours(1));
            return $rate;

        } catch (\Exception $e) {
            Log::error("Failed to fetch exchange rate: " . $e->getMessage());
            
            // Return last known rate as fallback
            $lastRate = ExchangeRate::where('from_currency', $fromCurrency)
                ->where('to_currency', $toCurrency)
                ->orderBy('rate_date', 'desc')
                ->first();

            return $lastRate ? $lastRate->rate : 1.0;
        }
    }

    /**
     * Convert amount from one currency to another.
     */
    public function convertAmount(float $amount, string $fromCurrency, string $toCurrency, ?Carbon $date = null): float
    {
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency, $date);
        return $amount * $rate;
    }

    /**
     * Convert amount to base currency.
     */
    public function convertToBaseCurrency(float $amount, string $fromCurrency, ?Carbon $date = null): float
    {
        return $this->convertAmount($amount, $fromCurrency, $this->baseCurrency, $date);
    }

    /**
     * Convert amount from base currency.
     */
    public function convertFromBaseCurrency(float $amount, string $toCurrency, ?Carbon $date = null): float
    {
        return $this->convertAmount($amount, $this->baseCurrency, $toCurrency, $date);
    }

    /**
     * Update exchange rates from external API.
     */
    public function updateExchangeRates(?Carbon $date = null): array
    {
        $date = $date ?? now();
        $currencies = Currency::where('is_active', true)->pluck('code')->toArray();
        
        $results = [
            'date' => $date->toDateString(),
            'updated' => 0,
            'errors' => 0,
            'rates' => [],
            'errors_detail' => [],
        ];

        foreach ($currencies as $currency) {
            if ($currency === $this->baseCurrency) {
                continue;
            }

            try {
                // Update both directions
                $rateToBase = $this->fetchExchangeRateFromApi($currency, $this->baseCurrency, $date);
                $rateFromBase = $this->fetchExchangeRateFromApi($this->baseCurrency, $currency, $date);

                // Store rates
                ExchangeRate::updateOrCreate([
                    'from_currency' => $currency,
                    'to_currency' => $this->baseCurrency,
                    'rate_date' => $date,
                ], [
                    'rate' => $rateToBase,
                    'source' => 'api',
                ]);

                ExchangeRate::updateOrCreate([
                    'from_currency' => $this->baseCurrency,
                    'to_currency' => $currency,
                    'rate_date' => $date,
                ], [
                    'rate' => $rateFromBase,
                    'source' => 'api',
                ]);

                $results['updated'] += 2;
                $results['rates'][$currency] = [
                    'to_base' => $rateToBase,
                    'from_base' => $rateFromBase,
                ];

                // Clear cache
                Cache::forget("exchange_rate_{$currency}_{$this->baseCurrency}_{$date->toDateString()}");
                Cache::forget("exchange_rate_{$this->baseCurrency}_{$currency}_{$date->toDateString()}");

            } catch (\Exception $e) {
                $results['errors']++;
                $results['errors_detail'][] = [
                    'currency' => $currency,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Perform currency revaluation for accounts.
     */
    public function performCurrencyRevaluation(int $clientId, ?Carbon $date = null): array
    {
        $date = $date ?? now();
        
        // Get foreign currency accounts
        $foreignAccounts = Account::where('client_id', $clientId)
            ->where('currency', '!=', $this->baseCurrency)
            ->whereNotNull('currency')
            ->get();

        $results = [
            'date' => $date->toDateString(),
            'accounts_processed' => 0,
            'total_revaluation' => 0,
            'journal_id' => null,
            'revaluations' => [],
        ];

        if ($foreignAccounts->isEmpty()) {
            return $results;
        }

        $totalRevaluation = 0;
        $revaluationEntries = [];

        foreach ($foreignAccounts as $account) {
            // Get account balance in foreign currency
            $foreignBalance = $this->getAccountBalance($account);
            
            if ($foreignBalance == 0) {
                continue;
            }

            // Get current exchange rate
            $currentRate = $this->getExchangeRate($account->currency, $this->baseCurrency, $date);
            
            // Get historical rate (last revaluation or account creation)
            $historicalRate = $this->getHistoricalRate($account, $date);
            
            // Calculate revaluation amount
            $currentValue = $foreignBalance * $currentRate;
            $historicalValue = $foreignBalance * $historicalRate;
            $revaluationAmount = $currentValue - $historicalValue;

            if (abs($revaluationAmount) < 0.01) {
                continue; // Skip if difference is negligible
            }

            $revaluationEntries[] = [
                'account' => $account,
                'foreign_balance' => $foreignBalance,
                'historical_rate' => $historicalRate,
                'current_rate' => $currentRate,
                'revaluation_amount' => $revaluationAmount,
            ];

            $totalRevaluation += $revaluationAmount;
            $results['accounts_processed']++;
        }

        // Create revaluation journal if there are entries
        if (!empty($revaluationEntries)) {
            $journal = $this->createRevaluationJournal($clientId, $date, $revaluationEntries);
            $results['journal_id'] = $journal->id;
            $results['total_revaluation'] = $totalRevaluation;
            $results['revaluations'] = $revaluationEntries;
        }

        return $results;
    }

    /**
     * Fetch exchange rate from external API.
     */
    protected function fetchExchangeRateFromApi(string $fromCurrency, string $toCurrency, Carbon $date): float
    {
        $provider = $this->getExchangeRateProvider();
        
        $response = Http::timeout(30)->get($provider['url'], [
            'access_key' => $provider['api_key'],
            'from' => $fromCurrency,
            'to' => $toCurrency,
            'date' => $date->toDateString(),
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to fetch exchange rate from API: " . $response->body());
        }

        $data = $response->json();
        
        if (!isset($data['quotes'])) {
            throw new \Exception("Invalid response format from exchange rate API");
        }

        $quoteKey = $fromCurrency . $toCurrency;
        if (!isset($data['quotes'][$quoteKey])) {
            throw new \Exception("Exchange rate not found for {$fromCurrency} to {$toCurrency}");
        }

        return (float) $data['quotes'][$quoteKey];
    }

    /**
     * Get account balance in foreign currency.
     */
    protected function getAccountBalance(Account $account): float
    {
        // This is a simplified calculation
        // In a real implementation, you'd calculate the actual balance
        $transactions = Transaction::where('account_id', $account->id)->sum('amount');
        return $transactions;
    }

    /**
     * Get historical exchange rate for account.
     */
    protected function getHistoricalRate(Account $account, Carbon $date): float
    {
        // Get the last revaluation rate or use rate from account creation
        $lastRevaluation = ExchangeRate::where('from_currency', $account->currency)
            ->where('to_currency', $this->baseCurrency)
            ->where('rate_date', '<', $date)
            ->orderBy('rate_date', 'desc')
            ->first();

        if ($lastRevaluation) {
            return $lastRevaluation->rate;
        }

        // Fallback to rate from account creation date
        return $this->getExchangeRate($account->currency, $this->baseCurrency, $account->created_at);
    }

    /**
     * Create revaluation journal.
     */
    protected function createRevaluationJournal(int $clientId, Carbon $date, array $revaluationEntries): Journal
    {
        $journal = Journal::create([
            'client_id' => $clientId,
            'journal_number' => 'REVAL-' . $date->format('Ymd'),
            'transaction_date' => $date,
            'reference_type' => 'currency_revaluation',
            'reference_id' => null,
            'reference_number' => 'CURRENCY-REVALUATION-' . $date->format('Ymd'),
            'description' => 'Currency revaluation - ' . $date->toDateString(),
            'total_amount' => abs(array_sum(array_column($revaluationEntries, 'revaluation_amount'))),
        ]);

        // Get revaluation gain/loss account
        $revaluationAccount = Account::where('client_id', $clientId)
            ->where('account_code', '7100') // Foreign Exchange Gain/Loss
            ->first();

        if (!$revaluationAccount) {
            throw new \Exception("Revaluation account not found (7100)");
        }

        foreach ($revaluationEntries as $entry) {
            $amount = abs($entry['revaluation_amount']);
            $isGain = $entry['revaluation_amount'] > 0;

            // Account entry
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $entry['account']->id,
                'description' => "Revaluation - {$entry['account']->name}",
                'debit' => $isGain ? $amount : 0,
                'credit' => $isGain ? 0 : $amount,
            ]);

            // Contra entry to revaluation account
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $revaluationAccount->id,
                'description' => "Revaluation " . ($isGain ? 'gain' : 'loss') . " - {$entry['account']->name}",
                'debit' => $isGain ? 0 : $amount,
                'credit' => $isGain ? $amount : 0,
            ]);
        }

        return $journal;
    }

    /**
     * Get exchange rate provider configuration.
     */
    protected function getExchangeRateProvider(): array
    {
        $provider = config('currency.default_provider', 'currencylayer');
        
        if (!isset($this->exchangeRateProviders[$provider])) {
            throw new \Exception("Exchange rate provider not configured: {$provider}");
        }

        return $this->exchangeRateProviders[$provider];
    }

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array
    {
        return Currency::where('is_active', true)
            ->orderBy('code')
            ->pluck('name', 'code')
            ->toArray();
    }

    /**
     * Format amount with currency.
     */
    public function formatAmount(float $amount, string $currency): string
    {
        $currencyData = Currency::where('code', $currency)->first();
        
        if (!$currencyData) {
            return number_format($amount, 2) . ' ' . $currency;
        }

        return $currencyData->symbol . ' ' . number_format($amount, $currencyData->decimal_places);
    }
}
