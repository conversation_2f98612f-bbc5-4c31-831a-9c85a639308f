<?php

namespace App\Services;

use App\Models\ProductionCost;
use App\Models\ProductionOrder;
use App\Models\BillOfMaterial;
use App\Models\Product;
use Illuminate\Support\Facades\DB;

class CostVarianceAnalysisService
{
    /**
     * Perform comprehensive variance analysis for a production order
     */
    public function analyzeVariances(ProductionOrder $productionOrder): array
    {
        return [
            'material_variances' => $this->analyzeMaterialVariances($productionOrder),
            'labor_variances' => $this->analyzeLaborVariances($productionOrder),
            'overhead_variances' => $this->analyzeOverheadVariances($productionOrder),
            'total_variance_summary' => $this->getTotalVarianceSummary($productionOrder),
            'variance_analysis' => $this->getVarianceAnalysis($productionOrder),
        ];
    }

    /**
     * Analyze material variances (Price and Quantity variances)
     */
    private function analyzeMaterialVariances(ProductionOrder $productionOrder): array
    {
        $variances = [];
        $bom = $productionOrder->product->billOfMaterial;
        
        if (!$bom) {
            return $variances;
        }

        foreach ($bom->items as $bomItem) {
            $standardQuantity = ($bomItem->quantity * $productionOrder->quantity_produced) / $bom->quantity_produced;
            $standardPrice = $bomItem->material->standard_cost;
            $standardCost = $standardQuantity * $standardPrice;

            // Get actual usage and cost
            $actualUsage = $productionOrder->rawMaterialUsages()
                ->where('material_id', $bomItem->material_id)
                ->first();

            if ($actualUsage) {
                $actualQuantity = $actualUsage->quantity_used;
                $actualPrice = $actualUsage->unit_cost;
                $actualCost = $actualQuantity * $actualPrice;

                // Calculate variances
                $priceVariance = ($actualPrice - $standardPrice) * $actualQuantity;
                $quantityVariance = ($actualQuantity - $standardQuantity) * $standardPrice;
                $totalVariance = $actualCost - $standardCost;

                $variances[] = [
                    'material_id' => $bomItem->material_id,
                    'material_name' => $bomItem->material->product_name,
                    'standard_quantity' => $standardQuantity,
                    'actual_quantity' => $actualQuantity,
                    'standard_price' => $standardPrice,
                    'actual_price' => $actualPrice,
                    'standard_cost' => $standardCost,
                    'actual_cost' => $actualCost,
                    'price_variance' => $priceVariance,
                    'quantity_variance' => $quantityVariance,
                    'total_variance' => $totalVariance,
                    'price_variance_type' => $priceVariance >= 0 ? 'unfavorable' : 'favorable',
                    'quantity_variance_type' => $quantityVariance >= 0 ? 'unfavorable' : 'favorable',
                    'efficiency_percentage' => $standardQuantity > 0 ? ($actualQuantity / $standardQuantity) * 100 : 0,
                ];

                // Record variance in production costs
                $this->recordMaterialVariance($productionOrder, $bomItem, $priceVariance, $quantityVariance);
            }
        }

        return $variances;
    }

    /**
     * Analyze labor variances (Rate and Efficiency variances)
     */
    private function analyzeLaborVariances(ProductionOrder $productionOrder): array
    {
        $variances = [];
        $bom = $productionOrder->product->billOfMaterial;
        
        if (!$bom) {
            return $variances;
        }

        foreach ($productionOrder->workOrders as $workOrder) {
            $standardHours = ($bom->production_time_minutes / 60) * 
                           ($productionOrder->quantity_produced / $bom->quantity_produced);
            $standardRate = $workOrder->workCenter->labor_rate_per_hour ?? 50000;
            $standardCost = $standardHours * $standardRate;

            // Get actual labor data
            $actualHours = $workOrder->timeEntries->sum('hours_worked');
            $actualRate = $workOrder->timeEntries->avg('hourly_rate') ?? $standardRate;
            $actualCost = $actualHours * $actualRate;

            // Calculate variances
            $rateVariance = ($actualRate - $standardRate) * $actualHours;
            $efficiencyVariance = ($actualHours - $standardHours) * $standardRate;
            $totalVariance = $actualCost - $standardCost;

            $variances[] = [
                'work_order_id' => $workOrder->id,
                'work_center_name' => $workOrder->workCenter->center_name,
                'standard_hours' => $standardHours,
                'actual_hours' => $actualHours,
                'standard_rate' => $standardRate,
                'actual_rate' => $actualRate,
                'standard_cost' => $standardCost,
                'actual_cost' => $actualCost,
                'rate_variance' => $rateVariance,
                'efficiency_variance' => $efficiencyVariance,
                'total_variance' => $totalVariance,
                'rate_variance_type' => $rateVariance >= 0 ? 'unfavorable' : 'favorable',
                'efficiency_variance_type' => $efficiencyVariance >= 0 ? 'unfavorable' : 'favorable',
                'efficiency_percentage' => $standardHours > 0 ? ($actualHours / $standardHours) * 100 : 0,
            ];

            // Record variance in production costs
            $this->recordLaborVariance($productionOrder, $workOrder, $rateVariance, $efficiencyVariance);
        }

        return $variances;
    }

    /**
     * Analyze overhead variances (Spending and Volume variances)
     */
    private function analyzeOverheadVariances(ProductionOrder $productionOrder): array
    {
        $variances = [];
        
        // Variable overhead variance analysis
        $standardVariableRate = 25000; // Per direct labor hour
        $actualLaborHours = $productionOrder->workOrders->sum(function ($workOrder) {
            return $workOrder->timeEntries->sum('hours_worked');
        });
        
        $standardVariableOverhead = $actualLaborHours * $standardVariableRate;
        $actualVariableOverhead = $this->getActualVariableOverhead($productionOrder);
        
        $variableSpendingVariance = $actualVariableOverhead - $standardVariableOverhead;
        
        // Fixed overhead variance analysis
        $budgetedFixedOverhead = $this->getBudgetedFixedOverhead($productionOrder->client_id);
        $standardFixedRate = 5000; // Per unit
        $standardFixedOverhead = $productionOrder->quantity_produced * $standardFixedRate;
        $actualFixedOverhead = $this->getActualFixedOverhead($productionOrder);
        
        $fixedSpendingVariance = $actualFixedOverhead - $budgetedFixedOverhead;
        $fixedVolumeVariance = $budgetedFixedOverhead - $standardFixedOverhead;
        
        $variances = [
            'variable_overhead' => [
                'standard_rate' => $standardVariableRate,
                'actual_hours' => $actualLaborHours,
                'standard_cost' => $standardVariableOverhead,
                'actual_cost' => $actualVariableOverhead,
                'spending_variance' => $variableSpendingVariance,
                'variance_type' => $variableSpendingVariance >= 0 ? 'unfavorable' : 'favorable',
            ],
            'fixed_overhead' => [
                'budgeted_cost' => $budgetedFixedOverhead,
                'standard_cost' => $standardFixedOverhead,
                'actual_cost' => $actualFixedOverhead,
                'spending_variance' => $fixedSpendingVariance,
                'volume_variance' => $fixedVolumeVariance,
                'total_variance' => $fixedSpendingVariance + $fixedVolumeVariance,
                'spending_variance_type' => $fixedSpendingVariance >= 0 ? 'unfavorable' : 'favorable',
                'volume_variance_type' => $fixedVolumeVariance >= 0 ? 'unfavorable' : 'favorable',
            ],
        ];

        return $variances;
    }

    /**
     * Get total variance summary
     */
    private function getTotalVarianceSummary(ProductionOrder $productionOrder): array
    {
        $costs = ProductionCost::where('production_order_id', $productionOrder->id)->get();
        
        $totalStandardCost = $costs->sum('standard_cost');
        $totalActualCost = $costs->sum('actual_cost');
        $totalVariance = $costs->sum('variance_amount');
        
        return [
            'total_standard_cost' => $totalStandardCost,
            'total_actual_cost' => $totalActualCost,
            'total_variance' => $totalVariance,
            'variance_percentage' => $totalStandardCost > 0 ? ($totalVariance / $totalStandardCost) * 100 : 0,
            'overall_variance_type' => $totalVariance >= 0 ? 'unfavorable' : 'favorable',
            'cost_performance' => $this->getCostPerformanceRating($totalVariance, $totalStandardCost),
        ];
    }

    /**
     * Get detailed variance analysis with recommendations
     */
    private function getVarianceAnalysis(ProductionOrder $productionOrder): array
    {
        $costs = ProductionCost::where('production_order_id', $productionOrder->id)->get();
        
        $significantVariances = $costs->filter(function ($cost) {
            return abs($cost->variance_percentage) >= 5; // 5% threshold
        });
        
        $analysis = [
            'significant_variances_count' => $significantVariances->count(),
            'largest_variance' => $costs->sortByDesc(function ($cost) {
                return abs($cost->variance_amount);
            })->first(),
            'recommendations' => $this->generateRecommendations($costs),
            'action_items' => $this->generateActionItems($significantVariances),
        ];
        
        return $analysis;
    }

    /**
     * Generate recommendations based on variance analysis
     */
    private function generateRecommendations($costs): array
    {
        $recommendations = [];
        
        $materialVariances = $costs->where('cost_type', 'material');
        $laborVariances = $costs->where('cost_type', 'labor');
        $overheadVariances = $costs->where('cost_type', 'overhead');
        
        if ($materialVariances->sum('variance_amount') > 0) {
            $recommendations[] = [
                'category' => 'Material Cost Control',
                'recommendation' => 'Review supplier contracts and material usage efficiency',
                'priority' => 'high',
            ];
        }
        
        if ($laborVariances->sum('variance_amount') > 0) {
            $recommendations[] = [
                'category' => 'Labor Efficiency',
                'recommendation' => 'Analyze production processes and worker training needs',
                'priority' => 'medium',
            ];
        }
        
        if ($overheadVariances->sum('variance_amount') > 0) {
            $recommendations[] = [
                'category' => 'Overhead Management',
                'recommendation' => 'Review overhead allocation methods and cost drivers',
                'priority' => 'medium',
            ];
        }
        
        return $recommendations;
    }

    /**
     * Generate action items for significant variances
     */
    private function generateActionItems($significantVariances): array
    {
        return $significantVariances->map(function ($variance) {
            return [
                'description' => "Investigate {$variance->cost_type} variance of " . 
                               number_format($variance->variance_amount) . 
                               " ({$variance->variance_percentage}%)",
                'responsible' => $this->getResponsiblePerson($variance->cost_type),
                'due_date' => now()->addDays(7),
                'priority' => abs($variance->variance_percentage) >= 10 ? 'high' : 'medium',
            ];
        })->toArray();
    }

    // Helper methods
    private function recordMaterialVariance($productionOrder, $bomItem, $priceVariance, $quantityVariance): void
    {
        if ($priceVariance != 0) {
            ProductionCost::create([
                'client_id' => $productionOrder->client_id,
                'production_order_id' => $productionOrder->id,
                'product_id' => $productionOrder->product_id,
                'cost_type' => 'variance',
                'cost_element' => 'material_price_variance',
                'description' => "Material Price Variance - {$bomItem->material->product_name}",
                'actual_cost' => $priceVariance,
                'variance_amount' => $priceVariance,
                'variance_type' => $priceVariance >= 0 ? 'unfavorable' : 'favorable',
                'cost_date' => now(),
                'created_by' => auth()->id() ?? 1,
            ]);
        }
    }

    private function recordLaborVariance($productionOrder, $workOrder, $rateVariance, $efficiencyVariance): void
    {
        // Similar implementation for labor variances
    }

    private function getActualVariableOverhead($productionOrder): float
    {
        return ProductionCost::where('production_order_id', $productionOrder->id)
            ->where('cost_element', 'variable_overhead')
            ->sum('actual_cost');
    }

    private function getActualFixedOverhead($productionOrder): float
    {
        return ProductionCost::where('production_order_id', $productionOrder->id)
            ->where('cost_element', 'fixed_overhead')
            ->sum('actual_cost');
    }

    private function getBudgetedFixedOverhead($clientId): float
    {
        // This would come from budget data
        return 10000000; // Default budget
    }

    private function getCostPerformanceRating($variance, $standardCost): string
    {
        if ($standardCost <= 0) return 'N/A';
        
        $percentage = abs($variance / $standardCost) * 100;
        
        if ($percentage <= 2) return 'Excellent';
        if ($percentage <= 5) return 'Good';
        if ($percentage <= 10) return 'Fair';
        return 'Poor';
    }

    private function getResponsiblePerson($costType): string
    {
        return match($costType) {
            'material' => 'Purchasing Manager',
            'labor' => 'Production Manager',
            'overhead' => 'Plant Manager',
            default => 'Cost Accountant'
        };
    }
}
