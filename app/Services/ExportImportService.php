<?php

namespace App\Services;

use Maatwebsite\Excel\Facades\Excel;
use App\Models\Account;
use App\Models\Transaction;
use App\Models\Journal;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Budget;
use App\Models\AuditLog;
use Illuminate\Support\Collection;
use Illuminate\Http\Response;

class ExportImportService
{
    /**
     * Export accounts to Excel.
     */
    public function exportAccounts(int $clientId, string $format = 'xlsx')
    {
        $accounts = Account::where('client_id', $clientId)
            ->with(['parent', 'costCenter'])
            ->orderBy('account_code')
            ->get();

        $data = $accounts->map(function ($account) {
            return [
                'Kode Akun' => $account->account_code,
                'Nama Akun' => $account->account_name,
                'Tipe Akun' => $account->account_type,
                'Sub Tipe' => $account->account_subtype,
                'Parent' => $account->parent?->account_name,
                'Cost Center' => $account->costCenter?->cost_center_name,
                'Saldo Normal' => $account->normal_balance,
                'Status' => $account->is_active ? 'Aktif' : 'Tidak Aktif',
                'Deskripsi' => $account->description,
                'Dibuat' => $account->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return $this->createExport($data, 'Chart of Accounts', $format);
    }

    /**
     * Export transactions to Excel.
     */
    public function exportTransactions(int $clientId, array $filters = [], string $format = 'xlsx')
    {
        $query = Transaction::where('client_id', $clientId)
            ->with(['account', 'category']);

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->whereDate('transaction_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('transaction_date', '<=', $filters['date_to']);
        }

        if (isset($filters['account_id'])) {
            $query->where('account_id', $filters['account_id']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        $transactions = $query->orderBy('transaction_date', 'desc')->get();

        $data = $transactions->map(function ($transaction) {
            return [
                'Tanggal' => $transaction->transaction_date->format('Y-m-d'),
                'Nomor Referensi' => $transaction->reference_number,
                'Akun' => $transaction->account->account_name,
                'Kategori' => $transaction->category?->category_name ?? '',
                'Tipe' => ucfirst($transaction->transaction_type),
                'Jumlah' => $transaction->amount,
                'Deskripsi' => $transaction->description,
                'Status' => ucfirst($transaction->status),
                'Dibuat' => $transaction->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return $this->createExport($data, 'Transactions', $format);
    }

    /**
     * Export journals to Excel.
     */
    public function exportJournals(int $clientId, array $filters = [], string $format = 'xlsx')
    {
        $query = Journal::where('client_id', $clientId)
            ->with(['journalEntries.account']);

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->whereDate('transaction_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('transaction_date', '<=', $filters['date_to']);
        }

        $journals = $query->orderBy('transaction_date', 'desc')->get();

        $data = [];
        foreach ($journals as $journal) {
            foreach ($journal->journalEntries as $entry) {
                $data[] = [
                    'Tanggal' => $journal->journal_date->format('Y-m-d'),
                    'Nomor Jurnal' => $journal->journal_number,
                    'Kode Akun' => $entry->account->account_code,
                    'Nama Akun' => $entry->account->account_name,
                    'Deskripsi' => $entry->description,
                    'Debit' => $entry->debit,
                    'Kredit' => $entry->credit,
                    'Referensi' => $journal->reference_number,
                    'Tipe Referensi' => $journal->reference_type,
                ];
            }
        }

        return $this->createExport(collect($data), 'Journal Entries', $format);
    }

    /**
     * Export products to Excel.
     */
    public function exportProducts(int $clientId, string $format = 'xlsx')
    {
        $products = Product::where('client_id', $clientId)
            ->with(['category', 'unit'])
            ->orderBy('name')
            ->get();

        $data = $products->map(function ($product) {
            return [
                'Kode Produk' => $product->product_code,
                'Nama Produk' => $product->product_name,
                'Kategori' => $product->category?->category_name,
                'Unit' => $product->unit?->unit_name,
                'Harga Standar' => $product->standard_cost,
                'Harga Jual' => $product->selling_price,
                'Stok Minimum' => $product->minimum_stock,
                'Status' => $product->is_active ? 'Aktif' : 'Tidak Aktif',
                'Deskripsi' => $product->description,
                'Dibuat' => $product->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return $this->createExport($data, 'Products', $format);
    }

    /**
     * Export customers to Excel.
     */
    public function exportCustomers(int $clientId, string $format = 'xlsx')
    {
        $customers = Customer::where('client_id', $clientId)
            ->orderBy('name')
            ->get();

        $data = $customers->map(function ($customer) {
            return [
                'Kode Customer' => $customer->customer_code,
                'Nama' => $customer->customer_name,
                'Email' => $customer->email,
                'Telepon' => $customer->phone,
                'Alamat' => $customer->address,
                'Kota' => $customer->city,
                'Negara' => $customer->country,
                'Kode Pos' => $customer->postal_code,
                'Credit Limit' => $customer->credit_limit,
                'Payment Terms' => $customer->payment_terms,
                'Status' => $customer->is_active ? 'Aktif' : 'Tidak Aktif',
                'Dibuat' => $customer->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return $this->createExport($data, 'Customers', $format);
    }

    /**
     * Export suppliers to Excel.
     */
    public function exportSuppliers(int $clientId, string $format = 'xlsx')
    {
        $suppliers = Supplier::where('client_id', $clientId)
            ->orderBy('name')
            ->get();

        $data = $suppliers->map(function ($supplier) {
            return [
                'Kode Supplier' => $supplier->supplier_code,
                'Nama' => $supplier->supplier_name,
                'Email' => $supplier->email,
                'Telepon' => $supplier->phone,
                'Alamat' => $supplier->address,
                'Kota' => $supplier->city,
                'Negara' => $supplier->country,
                'Kode Pos' => $supplier->postal_code,
                'Payment Terms' => $supplier->payment_terms,
                'Status' => $supplier->is_active ? 'Aktif' : 'Tidak Aktif',
                'Dibuat' => $supplier->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return $this->createExport($data, 'Suppliers', $format);
    }

    /**
     * Export audit logs to Excel.
     */
    public function exportAuditLogs(int $clientId, array $filters = [], string $format = 'xlsx')
    {
        $query = AuditLog::where('client_id', $clientId)
            ->with(['user']);

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['event'])) {
            $query->where('event', $filters['event']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        $auditLogs = $query->orderBy('created_at', 'desc')->get();

        $data = $auditLogs->map(function ($log) {
            return [
                'Waktu' => $log->created_at->format('Y-m-d H:i:s'),
                'User' => $log->user->name ?? 'System',
                'Event' => $log->getEventLabel(),
                'Model' => $log->getModelName(),
                'Model ID' => $log->auditable_id,
                'Deskripsi' => $log->description,
                'IP Address' => $log->ip_address,
                'Method' => $log->method,
                'URL' => $log->url,
                'Field yang Berubah' => $log->changed_fields ? implode(', ', $log->changed_fields) : '',
            ];
        });

        return $this->createExport($data, 'Audit Logs', $format);
    }

    /**
     * Create export file.
     */
    protected function createExport(Collection $data, string $title, string $format)
    {
        $export = new class($data, $title) implements
            \Maatwebsite\Excel\Concerns\FromCollection,
            \Maatwebsite\Excel\Concerns\WithHeadings,
            \Maatwebsite\Excel\Concerns\WithTitle,
            \Maatwebsite\Excel\Concerns\WithStyles,
            \Maatwebsite\Excel\Concerns\ShouldAutoSize
        {
            protected $data;
            protected $title;

            public function __construct(Collection $data, string $title)
            {
                $this->data = $data;
                $this->title = $title;
            }

            public function collection()
            {
                return $this->data;
            }

            public function headings(): array
            {
                return $this->data->isNotEmpty() ? array_keys($this->data->first()) : [];
            }

            public function title(): string
            {
                return $this->title;
            }

            public function styles(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet)
            {
                return [
                    1 => ['font' => ['bold' => true]],
                ];
            }
        };

        $filename = $title . '_' . now()->format('Y-m-d_H-i-s') . '.' . $format;

        return Excel::download($export, $filename);
    }

    /**
     * Get available export formats.
     */
    public function getAvailableFormats(): array
    {
        return [
            'xlsx' => 'Excel (XLSX)',
            'csv' => 'CSV',
            'ods' => 'OpenDocument Spreadsheet',
            'html' => 'HTML',
        ];
    }

    /**
     * Get available export types.
     */
    public function getAvailableExportTypes(): array
    {
        return [
            'accounts' => 'Chart of Accounts',
            'transactions' => 'Transaksi',
            'journals' => 'Jurnal Entries',
            'products' => 'Produk',
            'customers' => 'Pelanggan',
            'suppliers' => 'Supplier',
            'audit_logs' => 'Audit Logs',
        ];
    }
}
