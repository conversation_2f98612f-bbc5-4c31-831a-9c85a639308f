<?php

namespace App\Services;

use App\Models\FixedAsset;
use App\Models\Depreciation;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DepreciationService
{
    /**
     * Calculate and record depreciation for all assets
     */
    public function calculateMonthlyDepreciation(int $clientId, int $year, int $month): array
    {
        return DB::transaction(function () use ($clientId, $year, $month) {
            $results = [
                'period' => "{$year}-{$month}",
                'total_depreciation' => 0,
                'assets_processed' => 0,
                'depreciation_details' => [],
            ];

            $assets = FixedAsset::where('client_id', $clientId)
                ->where('is_active', true)
                ->where('status', 'active')
                ->get();

            $depreciationDate = Carbon::create($year, $month, 1)->endOfMonth();

            foreach ($assets as $asset) {
                // Skip if already depreciated for this period
                if ($this->isAlreadyDepreciated($asset, $year, $month)) {
                    continue;
                }

                $depreciationAmount = $this->calculateDepreciationAmount($asset, $depreciationDate);
                
                if ($depreciationAmount > 0) {
                    $depreciation = $this->recordDepreciation($asset, $depreciationAmount, $depreciationDate, $year, $month);
                    
                    $results['depreciation_details'][] = [
                        'asset_id' => $asset->id,
                        'asset_name' => $asset->asset_name,
                        'depreciation_amount' => $depreciationAmount,
                        'accumulated_depreciation' => $asset->accumulated_depreciation + $depreciationAmount,
                        'book_value' => $asset->total_cost - ($asset->accumulated_depreciation + $depreciationAmount),
                    ];
                    
                    $results['total_depreciation'] += $depreciationAmount;
                    $results['assets_processed']++;
                }
            }

            return $results;
        });
    }

    /**
     * Calculate depreciation amount for a specific asset
     */
    private function calculateDepreciationAmount(FixedAsset $asset, Carbon $depreciationDate): float
    {
        // Don't depreciate if asset is fully depreciated
        if ($asset->book_value <= $asset->salvage_value) {
            return 0;
        }

        $depreciationAmount = match($asset->depreciation_method) {
            'straight_line' => $this->calculateStraightLineDepreciation($asset),
            'declining_balance' => $this->calculateDecliningBalanceDepreciation($asset),
            'sum_of_years' => $this->calculateSumOfYearsDepreciation($asset, $depreciationDate),
            'units_of_production' => $this->calculateUnitsOfProductionDepreciation($asset),
            default => 0
        };

        // Ensure we don't depreciate below salvage value
        $maxDepreciation = $asset->book_value - $asset->salvage_value;
        return min($depreciationAmount, $maxDepreciation);
    }

    /**
     * Straight-line depreciation calculation
     */
    private function calculateStraightLineDepreciation(FixedAsset $asset): float
    {
        if ($asset->useful_life_years <= 0) {
            return 0;
        }

        $depreciableAmount = $asset->total_cost - $asset->salvage_value;
        return $depreciableAmount / $asset->useful_life_years / 12; // Monthly depreciation
    }

    /**
     * Declining balance depreciation calculation
     */
    private function calculateDecliningBalanceDepreciation(FixedAsset $asset): float
    {
        if ($asset->depreciation_rate <= 0) {
            return 0;
        }

        return $asset->book_value * ($asset->depreciation_rate / 100) / 12; // Monthly depreciation
    }

    /**
     * Sum of years digits depreciation calculation
     */
    private function calculateSumOfYearsDepreciation(FixedAsset $asset, Carbon $depreciationDate): float
    {
        $yearsElapsed = $asset->purchase_date->diffInYears($depreciationDate);
        $remainingYears = max(0, $asset->useful_life_years - $yearsElapsed);
        
        if ($remainingYears <= 0) {
            return 0;
        }

        $sumOfYears = ($asset->useful_life_years * ($asset->useful_life_years + 1)) / 2;
        $depreciableAmount = $asset->total_cost - $asset->salvage_value;
        
        return ($remainingYears / $sumOfYears) * $depreciableAmount / 12; // Monthly depreciation
    }

    /**
     * Units of production depreciation calculation
     */
    private function calculateUnitsOfProductionDepreciation(FixedAsset $asset): float
    {
        // This would require production data - for now return 0
        // In a real implementation, you'd get actual production units for the month
        return 0;
    }

    /**
     * Record depreciation entry
     */
    private function recordDepreciation(FixedAsset $asset, float $amount, Carbon $date, int $year, int $month): Depreciation
    {
        $bookValueBefore = $asset->book_value;
        $newAccumulatedDepreciation = $asset->accumulated_depreciation + $amount;
        $bookValueAfter = $asset->total_cost - $newAccumulatedDepreciation;

        $depreciation = Depreciation::create([
            'client_id' => $asset->client_id,
            'fixed_asset_id' => $asset->id,
            'depreciation_date' => $date,
            'depreciation_method' => $asset->depreciation_method,
            'depreciation_amount' => $amount,
            'accumulated_depreciation' => $newAccumulatedDepreciation,
            'book_value_before' => $bookValueBefore,
            'book_value_after' => $bookValueAfter,
            'useful_life_remaining' => max(0, $asset->useful_life_years - $asset->age_in_years),
            'period_year' => $year,
            'period_month' => $month,
            'is_manual' => false,
            'created_by' => auth()->id() ?? 1,
        ]);

        // Update asset
        $asset->update([
            'accumulated_depreciation' => $newAccumulatedDepreciation,
            'book_value' => $bookValueAfter,
        ]);

        // Create journal entry
        $depreciation->createJournalEntry();

        return $depreciation;
    }

    /**
     * Check if asset is already depreciated for the period
     */
    private function isAlreadyDepreciated(FixedAsset $asset, int $year, int $month): bool
    {
        return Depreciation::where('fixed_asset_id', $asset->id)
            ->where('period_year', $year)
            ->where('period_month', $month)
            ->exists();
    }

    /**
     * Calculate depreciation schedule for an asset
     */
    public function calculateDepreciationSchedule(FixedAsset $asset): array
    {
        $schedule = [];
        $currentDate = $asset->purchase_date->copy();
        $endDate = $asset->purchase_date->copy()->addYears($asset->useful_life_years);
        $remainingValue = $asset->total_cost - $asset->salvage_value;
        $accumulatedDepreciation = 0;

        while ($currentDate <= $endDate && $remainingValue > 0) {
            $monthlyDepreciation = $this->calculateDepreciationAmount($asset, $currentDate);
            
            if ($monthlyDepreciation > 0) {
                $accumulatedDepreciation += $monthlyDepreciation;
                $bookValue = $asset->total_cost - $accumulatedDepreciation;
                
                $schedule[] = [
                    'period' => $currentDate->format('Y-m'),
                    'depreciation_amount' => $monthlyDepreciation,
                    'accumulated_depreciation' => $accumulatedDepreciation,
                    'book_value' => max($bookValue, $asset->salvage_value),
                ];
                
                $remainingValue -= $monthlyDepreciation;
            }
            
            $currentDate->addMonth();
        }

        return $schedule;
    }

    /**
     * Generate depreciation report
     */
    public function generateDepreciationReport(int $clientId, int $year, ?int $month = null): array
    {
        $query = Depreciation::where('client_id', $clientId)
            ->where('period_year', $year)
            ->with(['fixedAsset']);

        if ($month) {
            $query->where('period_month', $month);
        }

        $depreciations = $query->get();

        $report = [
            'period' => $month ? "{$year}-{$month}" : $year,
            'total_depreciation' => $depreciations->sum('depreciation_amount'),
            'asset_count' => $depreciations->count(),
            'by_category' => [],
            'by_method' => [],
            'by_department' => [],
            'details' => [],
        ];

        // Group by category
        $byCategory = $depreciations->groupBy(function ($item) {
            return $item->fixedAsset->asset_category;
        });

        foreach ($byCategory as $category => $items) {
            $report['by_category'][$category] = [
                'count' => $items->count(),
                'total_depreciation' => $items->sum('depreciation_amount'),
                'total_book_value' => $items->sum('book_value_after'),
            ];
        }

        // Group by method
        $byMethod = $depreciations->groupBy('depreciation_method');
        foreach ($byMethod as $method => $items) {
            $report['by_method'][$method] = [
                'count' => $items->count(),
                'total_depreciation' => $items->sum('depreciation_amount'),
            ];
        }

        // Group by department
        $byDepartment = $depreciations->groupBy(function ($item) {
            return $item->fixedAsset->department->department_name ?? 'Unassigned';
        });

        foreach ($byDepartment as $department => $items) {
            $report['by_department'][$department] = [
                'count' => $items->count(),
                'total_depreciation' => $items->sum('depreciation_amount'),
            ];
        }

        // Add details
        foreach ($depreciations as $depreciation) {
            $report['details'][] = [
                'asset_code' => $depreciation->fixedAsset->asset_code,
                'asset_name' => $depreciation->fixedAsset->asset_name,
                'category' => $depreciation->fixedAsset->asset_category,
                'method' => $depreciation->depreciation_method,
                'depreciation_amount' => $depreciation->depreciation_amount,
                'accumulated_depreciation' => $depreciation->accumulated_depreciation,
                'book_value' => $depreciation->book_value_after,
            ];
        }

        return $report;
    }

    /**
     * Reverse depreciation (for corrections)
     */
    public function reverseDepreciation(Depreciation $depreciation): bool
    {
        return DB::transaction(function () use ($depreciation) {
            $asset = $depreciation->fixedAsset;
            
            // Restore asset values
            $asset->update([
                'accumulated_depreciation' => $asset->accumulated_depreciation - $depreciation->depreciation_amount,
                'book_value' => $asset->book_value + $depreciation->depreciation_amount,
            ]);

            // Reverse journal entry if exists
            if ($depreciation->journal_id) {
                $journal = $depreciation->journal;
                if ($journal) {
                    // Create reversing journal entry
                    $reversingJournal = Journal::create([
                        'client_id' => $journal->client_id,
                        'journal_number' => Journal::generateJournalNumber($journal->client_id),
                        'transaction_date' => now(),
                        'description' => "Reversal: {$journal->description}",
                        'reference_type' => Depreciation::class,
                        'reference_id' => $depreciation->id,
                        'total_amount' => $journal->total_amount,
                        'created_by' => auth()->id() ?? 1,
                    ]);

                    // Reverse journal entries
                    foreach ($journal->entries as $entry) {
                        JournalEntry::create([
                            'client_id' => $entry->client_id,
                            'journal_id' => $reversingJournal->id,
                            'account_id' => $entry->account_id,
                            'description' => "Reversal: {$entry->description}",
                            'debit_amount' => $entry->credit_amount, // Swap debit/credit
                            'credit_amount' => $entry->debit_amount,
                        ]);
                    }
                }
            }

            // Delete depreciation record
            $depreciation->delete();

            return true;
        });
    }
}
