<?php

namespace App\Services;

use App\Models\StockMovement;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\Location;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockMovementService
{
    /**
     * Create a stock movement with automatic inventory update
     */
    public function createMovement(array $data): StockMovement
    {
        return DB::transaction(function () use ($data) {
            // Validate required fields
            $this->validateMovementData($data);
            
            // Calculate costs and running balances
            $data = $this->calculateMovementData($data);
            
            // Create the movement
            $movement = StockMovement::create($data);
            
            // Update inventory
            $this->updateInventoryFromMovement($movement);
            
            // Log the movement
            Log::info('Stock movement created', [
                'movement_id' => $movement->id,
                'product_id' => $movement->product_id,
                'quantity' => $movement->quantity,
                'type' => $movement->movement_type
            ]);
            
            return $movement;
        });
    }

    /**
     * Create receipt movement (goods received)
     */
    public function createReceipt(array $data): StockMovement
    {
        $data['movement_type'] = 'in';
        $data['transaction_type'] = $data['transaction_type'] ?? 'purchase';
        $data['reason_code'] = $data['reason_code'] ?? 'receipt';
        
        return $this->createMovement($data);
    }

    /**
     * Create issue movement (goods issued)
     */
    public function createIssue(array $data): StockMovement
    {
        $data['movement_type'] = 'out';
        $data['transaction_type'] = $data['transaction_type'] ?? 'production';
        $data['reason_code'] = $data['reason_code'] ?? 'issue';
        
        return $this->createMovement($data);
    }

    /**
     * Create transfer movement between locations
     */
    public function createTransfer(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $movements = [];
            
            // Create outbound movement from source location
            $outboundData = array_merge($data, [
                'movement_type' => 'out',
                'transaction_type' => 'transfer',
                'reason_code' => 'transfer',
                'location_id' => $data['from_location_id'],
                'to_location_id' => $data['to_location_id'],
            ]);
            
            $outboundMovement = $this->createMovement($outboundData);
            $movements[] = $outboundMovement;
            
            // Create inbound movement to destination location
            $inboundData = array_merge($data, [
                'movement_type' => 'in',
                'transaction_type' => 'transfer',
                'reason_code' => 'transfer',
                'location_id' => $data['to_location_id'],
                'from_location_id' => $data['from_location_id'],
                'reference_number' => $data['reference_number'] . '-IN',
            ]);
            
            $inboundMovement = $this->createMovement($inboundData);
            $movements[] = $inboundMovement;
            
            return $movements;
        });
    }

    /**
     * Create adjustment movement
     */
    public function createAdjustment(array $data): StockMovement
    {
        $currentStock = $this->getCurrentStock($data['product_id'], $data['location_id']);
        $adjustmentQuantity = $data['new_quantity'] - $currentStock;
        
        $data['movement_type'] = $adjustmentQuantity >= 0 ? 'in' : 'out';
        $data['transaction_type'] = 'adjustment';
        $data['reason_code'] = $data['reason_code'] ?? 'adjustment';
        $data['quantity'] = abs($adjustmentQuantity);
        
        return $this->createMovement($data);
    }

    /**
     * Reverse a stock movement
     */
    public function reverseMovement(StockMovement $movement, ?string $reason = null): StockMovement
    {
        if ($movement->is_reversed) {
            throw new \Exception('Movement has already been reversed');
        }
        
        return DB::transaction(function () use ($movement, $reason) {
            // Create reverse movement
            $reverseMovement = $movement->reverse();
            
            // Mark original as reversed
            $movement->update([
                'is_reversed' => true,
                'reversed_by_id' => auth()->id() ?? 1,
                'reversed_at' => now(),
                'notes' => ($movement->notes ?? '') . ' | Reversed: ' . ($reason ?? 'Manual reversal')
            ]);
            
            return $reverseMovement;
        });
    }

    /**
     * Get stock movement history for a product
     */
    public function getMovementHistory(int $productId, ?int $locationId = null, ?int $days = 30): array
    {
        $query = StockMovement::where('product_id', $productId)
            ->where('movement_date', '>=', now()->subDays($days))
            ->orderBy('movement_date', 'desc');
            
        if ($locationId) {
            $query->where('location_id', $locationId);
        }
        
        $movements = $query->with(['location', 'createdBy', 'reference'])->get();
        
        return [
            'movements' => $movements,
            'summary' => [
                'total_in' => $movements->where('movement_type', 'in')->sum('quantity'),
                'total_out' => $movements->where('movement_type', 'out')->sum('quantity'),
                'net_movement' => $movements->where('movement_type', 'in')->sum('quantity') - 
                                $movements->where('movement_type', 'out')->sum('quantity'),
                'total_value' => $movements->sum('total_cost'),
            ]
        ];
    }

    /**
     * Get ABC analysis for products
     */
    public function getABCAnalysis(int $clientId, ?int $months = 12): array
    {
        $startDate = now()->subMonths($months);
        
        $analysis = DB::table('stock_movements')
            ->join('products', 'stock_movements.product_id', '=', 'products.id')
            ->where('stock_movements.client_id', $clientId)
            ->where('stock_movements.movement_date', '>=', $startDate)
            ->where('stock_movements.movement_type', 'out')
            ->select([
                'products.id',
                'products.product_code',
                'products.product_name',
                DB::raw('SUM(stock_movements.quantity) as total_usage'),
                DB::raw('SUM(stock_movements.total_cost) as total_value'),
                DB::raw('AVG(stock_movements.unit_cost) as avg_cost')
            ])
            ->groupBy('products.id', 'products.product_code', 'products.product_name')
            ->orderBy('total_value', 'desc')
            ->get();
            
        $totalValue = $analysis->sum('total_value');
        $runningValue = 0;
        
        return $analysis->map(function ($item) use ($totalValue, &$runningValue) {
            $runningValue += $item->total_value;
            $percentage = $totalValue > 0 ? ($runningValue / $totalValue) * 100 : 0;
            
            $classification = 'D';
            if ($percentage <= 80) $classification = 'A';
            elseif ($percentage <= 95) $classification = 'B';
            elseif ($percentage <= 99) $classification = 'C';
            
            return [
                'product_id' => $item->id,
                'product_code' => $item->product_code,
                'product_name' => $item->product_name,
                'total_usage' => $item->total_usage,
                'total_value' => $item->total_value,
                'avg_cost' => $item->avg_cost,
                'percentage' => round($percentage, 2),
                'classification' => $classification,
            ];
        })->toArray();
    }

    /**
     * Private helper methods
     */
    private function validateMovementData(array $data): void
    {
        $required = ['client_id', 'product_id', 'location_id', 'quantity', 'movement_type'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === null) {
                throw new \InvalidArgumentException("Field {$field} is required");
            }
        }
        
        if ($data['quantity'] <= 0) {
            throw new \InvalidArgumentException("Quantity must be greater than 0");
        }
    }

    private function calculateMovementData(array $data): array
    {
        // Set default values
        $data['movement_date'] = $data['movement_date'] ?? now();
        $data['created_by'] = $data['created_by'] ?? auth()->id() ?? 1;
        
        // Calculate unit cost if not provided
        if (!isset($data['unit_cost'])) {
            $product = Product::find($data['product_id']);
            $data['unit_cost'] = $product->standard_cost ?? 0;
        }
        
        // Calculate total cost
        $data['total_cost'] = $data['quantity'] * $data['unit_cost'];
        
        // Calculate running balance
        $currentStock = $this->getCurrentStock($data['product_id'], $data['location_id']);
        $data['running_balance'] = $data['movement_type'] === 'in' 
            ? $currentStock + $data['quantity']
            : $currentStock - $data['quantity'];
            
        // Calculate running value
        $data['running_value'] = $data['running_balance'] * $data['unit_cost'];
        
        return $data;
    }

    private function updateInventoryFromMovement(StockMovement $movement): void
    {
        $inventory = Inventory::firstOrCreate([
            'client_id' => $movement->client_id,
            'product_id' => $movement->product_id,
            'location_id' => $movement->location_id,
        ]);
        
        $inventory->updateStock(
            $movement->quantity,
            $movement->movement_type,
            $movement->unit_cost
        );
    }

    private function getCurrentStock(int $productId, int $locationId): float
    {
        $inventory = Inventory::where('product_id', $productId)
            ->where('location_id', $locationId)
            ->first();
            
        return $inventory ? $inventory->current_stock : 0;
    }
}
