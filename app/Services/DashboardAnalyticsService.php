<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
// use App\Models\SalesOrder; // Disabled - using Transaction model
use App\Models\PurchaseOrder;
use App\Models\ProductionOrder;
use App\Models\FixedAsset;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Inventory;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

// TEMPORARILY DISABLED - Needs refactoring to use Transaction model instead of SalesOrder
class DashboardAnalyticsService_DISABLED
{
    /**
     * Get comprehensive dashboard data
     */
    public function getDashboardData(int $clientId, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->startOfMonth();
        $endDate = $endDate ?? now()->endOfMonth();

        return [
            'financial_overview' => $this->getFinancialOverview($clientId, $startDate, $endDate),
            'sales_analytics' => $this->getSalesAnalytics($clientId, $startDate, $endDate),
            'purchase_analytics' => $this->getPurchaseAnalytics($clientId, $startDate, $endDate),
            'production_analytics' => $this->getProductionAnalytics($clientId, $startDate, $endDate),
            'inventory_analytics' => $this->getInventoryAnalytics($clientId),
            'asset_analytics' => $this->getAssetAnalytics($clientId),
            'kpi_metrics' => $this->getKPIMetrics($clientId, $startDate, $endDate),
            'trends' => $this->getTrendAnalysis($clientId, $startDate, $endDate),
        ];
    }

    /**
     * Get financial overview
     */
    private function getFinancialOverview(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        // Revenue
        $revenue = $this->getAccountGroupTotal($clientId, '4', $startDate, $endDate);
        $previousRevenue = $this->getAccountGroupTotal(
            $clientId,
            '4',
            $startDate->copy()->subMonth(),
            $endDate->copy()->subMonth()
        );

        // Expenses
        $expenses = $this->getAccountGroupTotal($clientId, '6', $startDate, $endDate);
        $previousExpenses = $this->getAccountGroupTotal(
            $clientId,
            '6',
            $startDate->copy()->subMonth(),
            $endDate->copy()->subMonth()
        );

        // Net Income
        $netIncome = $revenue - $expenses;
        $previousNetIncome = $previousRevenue - $previousExpenses;

        // Cash Position
        $cashPosition = $this->getAccountGroupTotal($clientId, '1111', null, $endDate);

        return [
            'revenue' => [
                'current' => $revenue,
                'previous' => $previousRevenue,
                'growth_percentage' => $this->calculateGrowthPercentage($revenue, $previousRevenue),
            ],
            'expenses' => [
                'current' => $expenses,
                'previous' => $previousExpenses,
                'growth_percentage' => $this->calculateGrowthPercentage($expenses, $previousExpenses),
            ],
            'net_income' => [
                'current' => $netIncome,
                'previous' => $previousNetIncome,
                'growth_percentage' => $this->calculateGrowthPercentage($netIncome, $previousNetIncome),
            ],
            'cash_position' => $cashPosition,
            'profit_margin' => $revenue > 0 ? ($netIncome / $revenue) * 100 : 0,
        ];
    }

    /**
     * Get sales analytics
     */
    private function getSalesAnalytics(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        $salesOrders = SalesOrder::where('client_id', $clientId)
            ->whereBetween('order_date', [$startDate, $endDate])
            ->get();

        $totalSales = $salesOrders->sum('total_amount');
        $totalOrders = $salesOrders->count();
        $averageOrderValue = $totalOrders > 0 ? $totalSales / $totalOrders : 0;

        // Top customers
        $topCustomers = $salesOrders->groupBy('customer_id')
            ->map(function ($orders) {
                return [
                    'customer_name' => $orders->first()->customer->customer_name ?? 'Unknown',
                    'total_amount' => $orders->sum('total_amount'),
                    'order_count' => $orders->count(),
                ];
            })
            ->sortByDesc('total_amount')
            ->take(5)
            ->values();

        // Sales by status
        $salesByStatus = $salesOrders->groupBy('status')
            ->map(function ($orders, $status) {
                return [
                    'status' => $status,
                    'count' => $orders->count(),
                    'total_amount' => $orders->sum('total_amount'),
                ];
            })
            ->values();

        return [
            'total_sales' => $totalSales,
            'total_orders' => $totalOrders,
            'average_order_value' => $averageOrderValue,
            'top_customers' => $topCustomers,
            'sales_by_status' => $salesByStatus,
            'monthly_trend' => $this->getMonthlySalesTrend($clientId, $startDate, $endDate),
        ];
    }

    /**
     * Get purchase analytics
     */
    private function getPurchaseAnalytics(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        $purchaseOrders = PurchaseOrder::where('client_id', $clientId)
            ->whereBetween('order_date', [$startDate, $endDate])
            ->get();

        $totalPurchases = $purchaseOrders->sum('total_amount');
        $totalOrders = $purchaseOrders->count();

        // Top suppliers
        $topSuppliers = $purchaseOrders->groupBy('supplier_id')
            ->map(function ($orders) {
                return [
                    'supplier_name' => $orders->first()->supplier->supplier_name ?? 'Unknown',
                    'total_amount' => $orders->sum('total_amount'),
                    'order_count' => $orders->count(),
                ];
            })
            ->sortByDesc('total_amount')
            ->take(5)
            ->values();

        return [
            'total_purchases' => $totalPurchases,
            'total_orders' => $totalOrders,
            'average_order_value' => $totalOrders > 0 ? $totalPurchases / $totalOrders : 0,
            'top_suppliers' => $topSuppliers,
            'purchase_by_status' => $purchaseOrders->groupBy('status')->map->count(),
        ];
    }

    /**
     * Get production analytics
     */
    private function getProductionAnalytics(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        $productionOrders = ProductionOrder::where('client_id', $clientId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $totalProduction = $productionOrders->sum('quantity_produced');
        $totalOrders = $productionOrders->count();
        $completedOrders = $productionOrders->where('status', 'completed')->count();
        $completionRate = $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0;

        // Production efficiency
        $onTimeDeliveries = $productionOrders->filter(function ($order) {
            return $order->completed_at && $order->required_date &&
                $order->completed_at <= $order->required_date;
        })->count();

        $onTimeRate = $completedOrders > 0 ? ($onTimeDeliveries / $completedOrders) * 100 : 0;

        return [
            'total_production' => $totalProduction,
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'completion_rate' => $completionRate,
            'on_time_delivery_rate' => $onTimeRate,
            'production_by_status' => $productionOrders->groupBy('status')->map->count(),
            'top_products' => $this->getTopProducedProducts($clientId, $startDate, $endDate),
        ];
    }

    /**
     * Get inventory analytics
     */
    private function getInventoryAnalytics(int $clientId): array
    {
        $inventories = Inventory::where('client_id', $clientId)
            ->with('product')
            ->get();

        $totalValue = $inventories->sum(function ($inventory) {
            return $inventory->current_stock * $inventory->average_cost;
        });

        $lowStockItems = $inventories->filter(function ($inventory) {
            return $inventory->current_stock <= $inventory->reorder_level;
        })->count();

        $outOfStockItems = $inventories->where('current_stock', '<=', 0)->count();

        // ABC Analysis
        $abcAnalysis = $inventories->groupBy(function ($inventory) {
            return $inventory->product->abc_classification ?? 'C';
        })->map(function ($items, $classification) {
            return [
                'classification' => $classification,
                'count' => $items->count(),
                'total_value' => $items->sum(function ($inventory) {
                    return $inventory->current_stock * $inventory->average_cost;
                }),
            ];
        });

        return [
            'total_inventory_value' => $totalValue,
            'total_items' => $inventories->count(),
            'low_stock_items' => $lowStockItems,
            'out_of_stock_items' => $outOfStockItems,
            'abc_analysis' => $abcAnalysis->values(),
            'top_value_items' => $this->getTopValueInventoryItems($clientId),
        ];
    }

    /**
     * Get asset analytics
     */
    private function getAssetAnalytics(int $clientId): array
    {
        $assets = FixedAsset::where('client_id', $clientId)->get();

        $totalAssetValue = $assets->sum('total_cost');
        $totalBookValue = $assets->sum('book_value');
        $totalDepreciation = $assets->sum('accumulated_depreciation');

        $assetsByCategory = $assets->groupBy('asset_category')
            ->map(function ($items, $category) {
                return [
                    'category' => $category,
                    'count' => $items->count(),
                    'total_cost' => $items->sum('total_cost'),
                    'book_value' => $items->sum('book_value'),
                ];
            });

        $maintenanceDue = $assets->filter(function ($asset) {
            return $asset->next_maintenance_date &&
                $asset->next_maintenance_date <= now()->addDays(30);
        })->count();

        return [
            'total_asset_value' => $totalAssetValue,
            'total_book_value' => $totalBookValue,
            'total_depreciation' => $totalDepreciation,
            'depreciation_percentage' => $totalAssetValue > 0 ? ($totalDepreciation / $totalAssetValue) * 100 : 0,
            'assets_by_category' => $assetsByCategory->values(),
            'maintenance_due' => $maintenanceDue,
            'asset_condition' => $assets->groupBy('condition')->map->count(),
        ];
    }

    /**
     * Get KPI metrics
     */
    private function getKPIMetrics(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        $revenue = $this->getAccountGroupTotal($clientId, '4', $startDate, $endDate);
        $expenses = $this->getAccountGroupTotal($clientId, '6', $startDate, $endDate);
        $assets = $this->getAccountGroupTotal($clientId, '1', null, $endDate);
        $equity = $this->getAccountGroupTotal($clientId, '3', null, $endDate);

        return [
            'gross_profit_margin' => $revenue > 0 ? (($revenue - $expenses) / $revenue) * 100 : 0,
            'return_on_assets' => $assets > 0 ? (($revenue - $expenses) / $assets) * 100 : 0,
            'return_on_equity' => $equity > 0 ? (($revenue - $expenses) / $equity) * 100 : 0,
            'asset_turnover' => $assets > 0 ? $revenue / $assets : 0,
            'current_ratio' => $this->getCurrentRatio($clientId, $endDate),
            'quick_ratio' => $this->getQuickRatio($clientId, $endDate),
        ];
    }

    /**
     * Get trend analysis
     */
    private function getTrendAnalysis(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        return [
            'revenue_trend' => $this->getMonthlyTrend($clientId, '4', $startDate, $endDate),
            'expense_trend' => $this->getMonthlyTrend($clientId, '6', $startDate, $endDate),
            'profit_trend' => $this->getProfitTrend($clientId, $startDate, $endDate),
        ];
    }

    // Helper methods
    private function getAccountGroupTotal(int $clientId, string $accountPrefix, ?Carbon $startDate, Carbon $endDate): float
    {
        $query = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $endDate);
                if ($startDate) {
                    $q->where('journal_date', '>=', $startDate);
                }
            });

        $totalDebits = $query->sum('debit');
        $totalCredits = $query->sum('credit');

        // Return based on account type
        if (in_array($accountPrefix, ['4', '7'])) { // Revenue
            return $totalCredits - $totalDebits;
        } else { // Assets, Expenses
            return $totalDebits - $totalCredits;
        }
    }

    private function calculateGrowthPercentage(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / abs($previous)) * 100;
    }

    private function getMonthlySalesTrend(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        return SalesOrder::where('client_id', $clientId)
            ->whereBetween('order_date', [$startDate, $endDate])
            ->selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, SUM(total_amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'period' => Carbon::create($item->year, $item->month)->format('M Y'),
                    'total' => $item->total,
                ];
            })
            ->toArray();
    }

    private function getTopProducedProducts(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        return ProductionOrder::where('client_id', $clientId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with('product')
            ->selectRaw('product_id, SUM(quantity_produced) as total_produced')
            ->groupBy('product_id')
            ->orderByDesc('total_produced')
            ->take(5)
            ->get()
            ->map(function ($item) {
                return [
                    'product_name' => $item->product->product_name ?? 'Unknown',
                    'total_produced' => $item->total_produced,
                ];
            })
            ->toArray();
    }

    private function getTopValueInventoryItems(int $clientId): array
    {
        return Inventory::where('client_id', $clientId)
            ->with('product')
            ->get()
            ->map(function ($inventory) {
                return [
                    'product_name' => $inventory->product->product_name ?? 'Unknown',
                    'current_stock' => $inventory->current_stock,
                    'average_cost' => $inventory->average_cost,
                    'total_value' => $inventory->current_stock * $inventory->average_cost,
                ];
            })
            ->sortByDesc('total_value')
            ->take(10)
            ->values()
            ->toArray();
    }

    private function getCurrentRatio(int $clientId, Carbon $endDate): float
    {
        $currentAssets = $this->getAccountGroupTotal($clientId, '11', null, $endDate);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', null, $endDate);

        return $currentLiabilities > 0 ? $currentAssets / $currentLiabilities : 0;
    }

    private function getQuickRatio(int $clientId, Carbon $endDate): float
    {
        $currentAssets = $this->getAccountGroupTotal($clientId, '11', null, $endDate);
        $inventory = $this->getAccountGroupTotal($clientId, '1131', null, $endDate);
        $currentLiabilities = $this->getAccountGroupTotal($clientId, '21', null, $endDate);

        $quickAssets = $currentAssets - $inventory;
        return $currentLiabilities > 0 ? $quickAssets / $currentLiabilities : 0;
    }

    private function getMonthlyTrend(int $clientId, string $accountPrefix, Carbon $startDate, Carbon $endDate): array
    {
        $months = [];
        $current = $startDate->copy()->startOfMonth();

        while ($current <= $endDate) {
            $monthEnd = $current->copy()->endOfMonth();
            $total = $this->getAccountGroupTotal($clientId, $accountPrefix, $current, $monthEnd);

            $months[] = [
                'period' => $current->format('M Y'),
                'total' => $total,
            ];

            $current->addMonth();
        }

        return $months;
    }

    private function getProfitTrend(int $clientId, Carbon $startDate, Carbon $endDate): array
    {
        $months = [];
        $current = $startDate->copy()->startOfMonth();

        while ($current <= $endDate) {
            $monthEnd = $current->copy()->endOfMonth();
            $revenue = $this->getAccountGroupTotal($clientId, '4', $current, $monthEnd);
            $expenses = $this->getAccountGroupTotal($clientId, '6', $current, $monthEnd);
            $profit = $revenue - $expenses;

            $months[] = [
                'period' => $current->format('M Y'),
                'profit' => $profit,
                'margin' => $revenue > 0 ? ($profit / $revenue) * 100 : 0,
            ];

            $current->addMonth();
        }

        return $months;
    }
}
