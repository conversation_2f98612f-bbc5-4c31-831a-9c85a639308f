<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\Journal;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class FinancialReportService
{
    /**
     * Get general ledger data with optimized queries
     */
    public function getGeneralLedger($clientId = null, $startDate = null, $endDate = null, $accountId = null)
    {
        $cacheKey = "general_ledger_{$clientId}_{$startDate}_{$endDate}_{$accountId}";

        return Cache::remember($cacheKey, 300, function () use ($clientId, $startDate, $endDate, $accountId) {
            $query = JournalEntry::with(['journal:id,journal_date,description,client_id', 'account:id,account_code,account_name'])
                ->whereHas('journal', function ($q) use ($clientId, $startDate, $endDate) {
                    $q->where('is_posted', true);

                    if ($clientId) {
                        $q->where('client_id', $clientId);
                    }

                    if ($startDate) {
                        $q->where('journal_date', '>=', $startDate);
                    }

                    if ($endDate) {
                        $q->where('journal_date', '<=', $endDate);
                    }
                });

            if ($accountId) {
                $query->where('account_id', $accountId);
            } elseif ($clientId) {
                $query->whereHas('account', function ($q) use ($clientId) {
                    $q->where('client_id', $clientId);
                });
            }

            return $query->orderBy('created_at')->get();
        });
    }

    /**
     * Get income statement data
     */
    public function getIncomeStatement($clientId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "income_statement_{$clientId}_{$startDate}_{$endDate}";

        return Cache::remember($cacheKey, 600, function () use ($clientId, $startDate, $endDate) {
            // Revenue accounts
            $revenues = $this->getAccountBalances($clientId, 'revenue', $startDate, $endDate);

            // Expense accounts
            $expenses = $this->getAccountBalances($clientId, 'expense', $startDate, $endDate);

            $totalRevenue = $revenues->sum('balance');
            $totalExpenses = $expenses->sum('balance');
            $netIncome = $totalRevenue - $totalExpenses;

            return [
                'revenues' => $revenues,
                'expenses' => $expenses,
                'total_revenue' => $totalRevenue,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
            ];
        });
    }

    /**
     * Get balance sheet data
     */
    public function getBalanceSheet($clientId = null, $asOfDate = null)
    {
        $asOfDate = $asOfDate ?: Carbon::now()->format('Y-m-d');
        $cacheKey = "balance_sheet_{$clientId}_{$asOfDate}";

        return Cache::remember($cacheKey, 600, function () use ($clientId, $asOfDate) {
            // Assets
            $assets = $this->getAccountBalances($clientId, 'asset', null, $asOfDate);

            // Liabilities
            $liabilities = $this->getAccountBalances($clientId, 'liability', null, $asOfDate);

            // Equity
            $equity = $this->getAccountBalances($clientId, 'equity', null, $asOfDate);

            $totalAssets = $assets->sum('balance');
            $totalLiabilities = $liabilities->sum('balance');
            $totalEquity = $equity->sum('balance');

            return [
                'assets' => $assets,
                'liabilities' => $liabilities,
                'equity' => $equity,
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
            ];
        });
    }

    /**
     * Get account balances by type with optimized query
     */
    private function getAccountBalances($clientId, $accountType, $startDate = null, $endDate = null)
    {
        $query = DB::table('accounts')
            ->leftJoin('journal_entries', 'accounts.id', '=', 'journal_entries.account_id')
            ->leftJoin('journals', 'journal_entries.journal_id', '=', 'journals.id')
            ->select(
                'accounts.id',
                'accounts.account_code',
                'accounts.account_name',
                'accounts.normal_balance',
                DB::raw('COALESCE(SUM(
                    CASE
                        WHEN accounts.normal_balance = "debit" THEN journal_entries.debit - journal_entries.credit
                        ELSE journal_entries.credit - journal_entries.debit
                    END
                ), 0) as balance')
            )
            ->where('accounts.account_type', $accountType)
            ->where('accounts.is_active', true)
            ->where(function ($q) use ($startDate, $endDate) {
                $q->whereNull('journals.id')
                    ->orWhere(function ($subQ) use ($startDate, $endDate) {
                        $subQ->where('journals.is_posted', true);

                        if ($startDate) {
                            $subQ->where('journals.journal_date', '>=', $startDate);
                        }

                        if ($endDate) {
                            $subQ->where('journals.journal_date', '<=', $endDate);
                        }
                    });
            });

        if ($clientId) {
            $query->where('accounts.client_id', $clientId);
        }

        return $query->groupBy('accounts.id', 'accounts.account_code', 'accounts.account_name', 'accounts.normal_balance')
            ->orderBy('accounts.account_code')
            ->get();
    }

    /**
     * Get monthly revenue trend data
     */
    public function getMonthlyRevenueTrend($clientId = null, $months = 12)
    {
        $cacheKey = "monthly_revenue_trend_{$clientId}_{$months}";

        return Cache::remember($cacheKey, 300, function () use ($clientId, $months) {
            $data = [];

            for ($i = $months - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subMonths($i);
                $monthKey = $date->format('Y-m');

                $revenue = JournalEntry::whereHas('account', function ($q) use ($clientId) {
                    $q->where('account_type', 'revenue');
                    if ($clientId) {
                        $q->where('client_id', $clientId);
                    }
                })
                    ->whereHas('journal', function ($q) use ($date) {
                        $q->where('is_posted', true)
                            ->whereYear('journal_date', $date->year)
                            ->whereMonth('journal_date', $date->month);
                    })
                    ->sum('credit');

                $data[] = [
                    'month' => $date->format('M Y'),
                    'revenue' => $revenue,
                ];
            }

            return $data;
        });
    }

    /**
     * Clear cache for specific client
     */
    public function clearClientCache($clientId)
    {
        $patterns = [
            "general_ledger_{$clientId}_*",
            "income_statement_{$clientId}_*",
            "balance_sheet_{$clientId}_*",
            "monthly_revenue_trend_{$clientId}_*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Generate comprehensive Balance Sheet with comparison
     */
    public function generateEnhancedBalanceSheet(int $clientId, Carbon $asOfDate, ?Carbon $compareDate = null): array
    {
        $currentPeriod = $this->getBalanceSheetData($clientId, $asOfDate);
        $report = [
            'report_title' => 'Balance Sheet',
            'company_name' => \App\Models\Client::find($clientId)->client_name,
            'as_of_date' => $asOfDate->format('d M Y'),
            'current_period' => $currentPeriod,
            'has_comparison' => false,
        ];

        // Add comparison if provided
        if ($compareDate) {
            $comparePeriod = $this->getBalanceSheetData($clientId, $compareDate);
            $report['has_comparison'] = true;
            $report['compare_period'] = $comparePeriod;
            $report['compare_date'] = $compareDate->format('d M Y');
            $report['variance_analysis'] = $this->calculateBalanceSheetVariance($currentPeriod, $comparePeriod);
        }

        return $report;
    }

    /**
     * Get Balance Sheet data
     */
    private function getBalanceSheetData(int $clientId, Carbon $asOfDate): array
    {
        // Assets
        $currentAssets = $this->getAccountGroupBalanceAsOf($clientId, '11', $asOfDate);
        $fixedAssets = $this->getAccountGroupBalanceAsOf($clientId, '12', $asOfDate);
        $otherAssets = $this->getAccountGroupBalanceAsOf($clientId, '13', $asOfDate);
        $totalAssets = $currentAssets + $fixedAssets + $otherAssets;

        // Liabilities
        $currentLiabilities = $this->getAccountGroupBalanceAsOf($clientId, '21', $asOfDate);
        $longTermLiabilities = $this->getAccountGroupBalanceAsOf($clientId, '22', $asOfDate);
        $totalLiabilities = $currentLiabilities + $longTermLiabilities;

        // Equity
        $equity = $this->getAccountGroupBalanceAsOf($clientId, '3', $asOfDate);

        $totalLiabilitiesAndEquity = $totalLiabilities + $equity;

        return [
            'current_assets' => $currentAssets,
            'fixed_assets' => $fixedAssets,
            'other_assets' => $otherAssets,
            'total_assets' => $totalAssets,
            'current_liabilities' => $currentLiabilities,
            'long_term_liabilities' => $longTermLiabilities,
            'total_liabilities' => $totalLiabilities,
            'equity' => $equity,
            'total_liabilities_and_equity' => $totalLiabilitiesAndEquity,
            'is_balanced' => abs($totalAssets - $totalLiabilitiesAndEquity) < 0.01,
            'difference' => $totalAssets - $totalLiabilitiesAndEquity,
        ];
    }

    /**
     * Get account group balance as of date
     */
    private function getAccountGroupBalanceAsOf(int $clientId, string $accountPrefix, Carbon $asOfDate): float
    {
        $totalDebits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $asOfDate);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::whereHas('account', function ($q) use ($clientId, $accountPrefix) {
            $q->where('client_id', $clientId)
                ->where('account_code', 'like', $accountPrefix . '%');
        })
            ->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $asOfDate);
            })
            ->sum('credit');

        // For asset accounts, return debit - credit
        // For liability and equity accounts, return credit - debit
        if (in_array($accountPrefix, ['11', '12', '13'])) { // Assets
            return $totalDebits - $totalCredits;
        } else { // Liabilities and Equity
            return $totalCredits - $totalDebits;
        }
    }

    /**
     * Calculate balance sheet variance
     */
    private function calculateBalanceSheetVariance(array $current, array $compare): array
    {
        $variance = [];

        foreach ($current as $key => $value) {
            if (isset($compare[$key]) && is_numeric($value) && is_numeric($compare[$key])) {
                $variance[$key] = [
                    'amount_variance' => $value - $compare[$key],
                    'percentage_variance' => $compare[$key] != 0 ? (($value - $compare[$key]) / abs($compare[$key])) * 100 : 0,
                ];
            }
        }

        return $variance;
    }

    /**
     * Generate Trial Balance
     */
    public function generateTrialBalance(int $clientId, Carbon $asOfDate): array
    {
        $accounts = Account::where('client_id', $clientId)
            ->where('is_active', true)
            ->orderBy('account_code')
            ->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $balance = $this->getAccountBalanceAsOf($account, $asOfDate);

            if (abs($balance) >= 0.01) { // Only include accounts with balance
                $debitBalance = 0;
                $creditBalance = 0;

                // Determine debit/credit based on account type and balance
                if (in_array($account->account_type, ['asset', 'expense'])) {
                    if ($balance >= 0) {
                        $debitBalance = $balance;
                    } else {
                        $creditBalance = abs($balance);
                    }
                } else { // liability, equity, revenue
                    if ($balance >= 0) {
                        $creditBalance = $balance;
                    } else {
                        $debitBalance = abs($balance);
                    }
                }

                $trialBalance[] = [
                    'account_code' => $account->account_code,
                    'account_name' => $account->account_name,
                    'account_type' => $account->account_type,
                    'debit_balance' => $debitBalance,
                    'credit_balance' => $creditBalance,
                ];

                $totalDebits += $debitBalance;
                $totalCredits += $creditBalance;
            }
        }

        return [
            'report_title' => 'Trial Balance',
            'company_name' => \App\Models\Client::find($clientId)->client_name,
            'as_of_date' => $asOfDate->format('d M Y'),
            'accounts' => $trialBalance,
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
            'difference' => $totalDebits - $totalCredits,
        ];
    }

    /**
     * Get account balance as of date
     */
    private function getAccountBalanceAsOf(Account $account, Carbon $asOfDate): float
    {
        $totalDebits = JournalEntry::where('account_id', $account->id)
            ->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $asOfDate);
            })
            ->sum('debit');

        $totalCredits = JournalEntry::where('account_id', $account->id)
            ->whereHas('journal', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                    ->where('journal_date', '<=', $asOfDate);
            })
            ->sum('credit');

        return $totalDebits - $totalCredits;
    }
}
