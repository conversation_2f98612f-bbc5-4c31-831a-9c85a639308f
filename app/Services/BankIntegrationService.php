<?php

namespace App\Services;

use App\Models\BankAccount;
use App\Models\BankReconciliation;
use App\Models\Transaction;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BankIntegrationService
{
    protected array $bankConfigs;

    public function __construct()
    {
        $this->bankConfigs = config('banking.providers', []);
    }

    /**
     * Sync bank account balances.
     */
    public function syncAccountBalances(int $clientId): array
    {
        $bankAccounts = BankAccount::where('client_id', $clientId)
            ->where('is_active', true)
            ->whereNotNull('api_credentials')
            ->get();

        $results = [
            'total_accounts' => $bankAccounts->count(),
            'synced' => 0,
            'errors' => 0,
            'balances' => [],
            'errors_detail' => [],
        ];

        foreach ($bankAccounts as $bankAccount) {
            try {
                $balance = $this->fetchAccountBalance($bankAccount);
                
                // Update bank account balance
                $bankAccount->update([
                    'current_balance' => $balance['available_balance'],
                    'available_balance' => $balance['available_balance'],
                    'pending_balance' => $balance['pending_balance'] ?? 0,
                    'last_sync_at' => now(),
                ]);

                $results['synced']++;
                $results['balances'][] = [
                    'account_name' => $bankAccount->account_name,
                    'account_number' => $bankAccount->account_number,
                    'balance' => $balance['available_balance'],
                    'currency' => $balance['currency'],
                ];

            } catch (\Exception $e) {
                $results['errors']++;
                $results['errors_detail'][] = [
                    'account_name' => $bankAccount->account_name,
                    'error' => $e->getMessage(),
                ];
                
                Log::error("Failed to sync balance for bank account {$bankAccount->id}: " . $e->getMessage());
            }
        }

        return $results;
    }

    /**
     * Import bank transactions.
     */
    public function importBankTransactions(int $clientId, ?Carbon $fromDate = null, ?Carbon $toDate = null): array
    {
        $fromDate = $fromDate ?? now()->subDays(7);
        $toDate = $toDate ?? now();

        $bankAccounts = BankAccount::where('client_id', $clientId)
            ->where('is_active', true)
            ->whereNotNull('api_credentials')
            ->get();

        $results = [
            'total_accounts' => $bankAccounts->count(),
            'total_transactions' => 0,
            'imported' => 0,
            'duplicates' => 0,
            'errors' => 0,
            'transactions' => [],
            'errors_detail' => [],
        ];

        foreach ($bankAccounts as $bankAccount) {
            try {
                $transactions = $this->fetchBankTransactions($bankAccount, $fromDate, $toDate);
                $results['total_transactions'] += count($transactions);

                foreach ($transactions as $transactionData) {
                    $result = $this->processBankTransaction($bankAccount, $transactionData);
                    
                    if ($result['imported']) {
                        $results['imported']++;
                        $results['transactions'][] = $result['transaction_id'];
                    } else {
                        $results['duplicates']++;
                    }
                }

            } catch (\Exception $e) {
                $results['errors']++;
                $results['errors_detail'][] = [
                    'account_name' => $bankAccount->account_name,
                    'error' => $e->getMessage(),
                ];
                
                Log::error("Failed to import transactions for bank account {$bankAccount->id}: " . $e->getMessage());
            }
        }

        return $results;
    }

    /**
     * Fetch account balance from bank API.
     */
    protected function fetchAccountBalance(BankAccount $bankAccount): array
    {
        $provider = $this->getBankProvider($bankAccount->bank_code);
        $credentials = json_decode($bankAccount->api_credentials, true);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $credentials['access_token'],
            'Content-Type' => 'application/json',
        ])->get($provider['balance_endpoint'], [
            'account_number' => $bankAccount->account_number,
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to fetch balance: " . $response->body());
        }

        $data = $response->json();

        return [
            'available_balance' => $data['available_balance'] ?? 0,
            'pending_balance' => $data['pending_balance'] ?? 0,
            'currency' => $data['currency'] ?? 'IDR',
        ];
    }

    /**
     * Fetch bank transactions from bank API.
     */
    protected function fetchBankTransactions(BankAccount $bankAccount, Carbon $fromDate, Carbon $toDate): array
    {
        $provider = $this->getBankProvider($bankAccount->bank_code);
        $credentials = json_decode($bankAccount->api_credentials, true);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $credentials['access_token'],
            'Content-Type' => 'application/json',
        ])->get($provider['transactions_endpoint'], [
            'account_number' => $bankAccount->account_number,
            'from_date' => $fromDate->toDateString(),
            'to_date' => $toDate->toDateString(),
        ]);

        if (!$response->successful()) {
            throw new \Exception("Failed to fetch transactions: " . $response->body());
        }

        return $response->json('data', []);
    }

    /**
     * Process individual bank transaction.
     */
    protected function processBankTransaction(BankAccount $bankAccount, array $transactionData): array
    {
        // Check if transaction already exists
        $existingTransaction = Transaction::where('client_id', $bankAccount->client_id)
            ->where('reference_number', $transactionData['reference_number'])
            ->first();

        if ($existingTransaction) {
            return ['imported' => false, 'transaction_id' => $existingTransaction->id];
        }

        // Create transaction
        $transaction = Transaction::create([
            'client_id' => $bankAccount->client_id,
            'account_id' => $bankAccount->account_id,
            'transaction_date' => Carbon::parse($transactionData['transaction_date']),
            'type' => $transactionData['amount'] > 0 ? 'income' : 'expense',
            'amount' => abs($transactionData['amount']),
            'description' => $transactionData['description'] ?? 'Bank transaction',
            'reference_number' => $transactionData['reference_number'],
            'status' => 'completed',
        ]);

        // Create journal entry
        $this->createJournalEntry($bankAccount, $transaction, $transactionData);

        return ['imported' => true, 'transaction_id' => $transaction->id];
    }

    /**
     * Create journal entry for bank transaction.
     */
    protected function createJournalEntry(BankAccount $bankAccount, Transaction $transaction, array $transactionData): void
    {
        $journal = Journal::create([
            'client_id' => $bankAccount->client_id,
            'journal_number' => 'BANK-' . $transaction->id,
            'transaction_date' => $transaction->transaction_date,
            'reference_type' => 'bank_transaction',
            'reference_id' => $transaction->id,
            'reference_number' => $transaction->reference_number,
            'description' => 'Bank transaction - ' . $transaction->description,
            'total_amount' => $transaction->amount,
        ]);

        // Get contra account (simplified logic)
        $contraAccount = $this->getContraAccount($bankAccount->client_id, $transactionData);

        if ($transaction->type === 'income') {
            // Debit bank account, credit contra account
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $bankAccount->account_id,
                'description' => 'Bank receipt',
                'debit' => $transaction->amount,
                'credit' => 0,
            ]);

            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $contraAccount->id,
                'description' => $transaction->description,
                'debit' => 0,
                'credit' => $transaction->amount,
            ]);
        } else {
            // Credit bank account, debit contra account
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $contraAccount->id,
                'description' => $transaction->description,
                'debit' => $transaction->amount,
                'credit' => 0,
            ]);

            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $bankAccount->account_id,
                'description' => 'Bank payment',
                'debit' => 0,
                'credit' => $transaction->amount,
            ]);
        }
    }

    /**
     * Get contra account for bank transaction.
     */
    protected function getContraAccount(int $clientId, array $transactionData): Account
    {
        // Simplified logic - in real implementation, this would be more sophisticated
        $accountCode = $transactionData['amount'] > 0 ? '4000' : '6000'; // Sales or General Expense
        
        return Account::where('client_id', $clientId)
            ->where('account_code', $accountCode)
            ->firstOrFail();
    }

    /**
     * Get bank provider configuration.
     */
    protected function getBankProvider(string $bankCode): array
    {
        if (!isset($this->bankConfigs[$bankCode])) {
            throw new \Exception("Bank provider configuration not found for: {$bankCode}");
        }

        return $this->bankConfigs[$bankCode];
    }

    /**
     * Test bank connection.
     */
    public function testBankConnection(BankAccount $bankAccount): array
    {
        try {
            $balance = $this->fetchAccountBalance($bankAccount);
            
            return [
                'connected' => true,
                'account_name' => $bankAccount->account_name,
                'balance' => $balance['available_balance'],
                'currency' => $balance['currency'],
                'last_tested' => now()->toDateTimeString(),
            ];

        } catch (\Exception $e) {
            return [
                'connected' => false,
                'error' => $e->getMessage(),
                'last_tested' => now()->toDateTimeString(),
            ];
        }
    }

    /**
     * Auto-reconcile bank transactions.
     */
    public function autoReconcile(BankAccount $bankAccount, Carbon $fromDate, Carbon $toDate): array
    {
        // Get bank transactions from API
        $bankTransactions = $this->fetchBankTransactions($bankAccount, $fromDate, $toDate);
        
        // Get system transactions
        $systemTransactions = Transaction::where('client_id', $bankAccount->client_id)
            ->where('account_id', $bankAccount->account_id)
            ->whereBetween('transaction_date', [$fromDate, $toDate])
            ->get();

        $results = [
            'bank_transactions' => count($bankTransactions),
            'system_transactions' => $systemTransactions->count(),
            'matched' => 0,
            'unmatched_bank' => 0,
            'unmatched_system' => 0,
            'matches' => [],
        ];

        // Simple matching logic (can be enhanced)
        foreach ($bankTransactions as $bankTxn) {
            $match = $systemTransactions->first(function ($sysTxn) use ($bankTxn) {
                return abs($sysTxn->amount - abs($bankTxn['amount'])) < 0.01 &&
                       abs($sysTxn->transaction_date->diffInDays(Carbon::parse($bankTxn['transaction_date']))) <= 1;
            });

            if ($match) {
                $results['matched']++;
                $results['matches'][] = [
                    'bank_reference' => $bankTxn['reference_number'],
                    'system_transaction_id' => $match->id,
                    'amount' => $bankTxn['amount'],
                ];
                
                // Mark as reconciled
                $match->update(['is_reconciled' => true]);
            } else {
                $results['unmatched_bank']++;
            }
        }

        $results['unmatched_system'] = $systemTransactions->where('is_reconciled', false)->count();

        return $results;
    }
}
