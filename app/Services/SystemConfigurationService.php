<?php

namespace App\Services;

use App\Models\Client;
use App\Models\Account;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SystemConfigurationService
{
    /**
     * Initialize system for new client
     */
    public function initializeClientSystem(Client $client): array
    {
        return DB::transaction(function () use ($client) {
            $results = [
                'client_id' => $client->id,
                'client_name' => $client->client_name,
                'initialization_steps' => [],
                'success' => true,
                'errors' => [],
            ];

            try {
                // 1. Create Chart of Accounts
                $accountsCreated = $this->createDefaultChartOfAccounts($client->id);
                $results['initialization_steps'][] = [
                    'step' => 'Chart of Accounts Creation',
                    'status' => 'completed',
                    'details' => "Created {$accountsCreated} default accounts",
                ];

                // 2. Create Default Cost Centers
                $costCentersCreated = $this->createDefaultCostCenters($client->id);
                $results['initialization_steps'][] = [
                    'step' => 'Cost Centers Setup',
                    'status' => 'completed',
                    'details' => "Created {$costCentersCreated} cost centers",
                ];

                // 3. Create Default Product Categories
                $categoriesCreated = $this->createDefaultProductCategories($client->id);
                $results['initialization_steps'][] = [
                    'step' => 'Product Categories Setup',
                    'status' => 'completed',
                    'details' => "Created {$categoriesCreated} product categories",
                ];

                // 4. Create Default Units of Measure
                $unitsCreated = $this->createDefaultUnits($client->id);
                $results['initialization_steps'][] = [
                    'step' => 'Units of Measure Setup',
                    'status' => 'completed',
                    'details' => "Created {$unitsCreated} units of measure",
                ];

                // 5. Create Default Cash Flow Categories
                $cashFlowCategoriesCreated = $this->createDefaultCashFlowCategories($client->id);
                $results['initialization_steps'][] = [
                    'step' => 'Cash Flow Categories Setup',
                    'status' => 'completed',
                    'details' => "Created {$cashFlowCategoriesCreated} cash flow categories",
                ];

                // 6. Set Default System Settings
                $this->setDefaultSystemSettings($client->id);
                $results['initialization_steps'][] = [
                    'step' => 'System Settings Configuration',
                    'status' => 'completed',
                    'details' => 'Default system settings applied',
                ];

                // 7. Create Admin User
                $adminUser = $this->createClientAdminUser($client);
                $results['initialization_steps'][] = [
                    'step' => 'Admin User Creation',
                    'status' => 'completed',
                    'details' => "Admin user created: {$adminUser->email}",
                ];

                $results['admin_user'] = [
                    'name' => $adminUser->name,
                    'email' => $adminUser->email,
                    'temporary_password' => 'Please check email for login credentials',
                ];

            } catch (\Exception $e) {
                $results['success'] = false;
                $results['errors'][] = $e->getMessage();
            }

            return $results;
        });
    }

    /**
     * Create default chart of accounts
     */
    private function createDefaultChartOfAccounts(int $clientId): int
    {
        $defaultAccounts = [
            // ASSETS (1xxx)
            ['1111', 'Kas', 'asset', 'current_asset', null],
            ['1112', 'Bank BCA', 'asset', 'current_asset', null],
            ['1113', 'Bank Mandiri', 'asset', 'current_asset', null],
            ['1121', 'Piutang Usaha', 'asset', 'current_asset', null],
            ['1122', 'Piutang Karyawan', 'asset', 'current_asset', null],
            ['1131', 'Persediaan Bahan Baku', 'asset', 'current_asset', null],
            ['1132', 'Persediaan Barang Dalam Proses', 'asset', 'current_asset', null],
            ['1133', 'Persediaan Barang Jadi', 'asset', 'current_asset', null],
            ['1141', 'Biaya Dibayar Dimuka', 'asset', 'current_asset', null],
            ['1211', 'Tanah', 'asset', 'fixed_asset', null],
            ['1212', 'Bangunan', 'asset', 'fixed_asset', null],
            ['1213', 'Akumulasi Penyusutan Bangunan', 'asset', 'fixed_asset', '1212'],
            ['1221', 'Mesin dan Peralatan', 'asset', 'fixed_asset', null],
            ['1222', 'Akumulasi Penyusutan Mesin', 'asset', 'fixed_asset', '1221'],
            ['1231', 'Kendaraan', 'asset', 'fixed_asset', null],
            ['1232', 'Akumulasi Penyusutan Kendaraan', 'asset', 'fixed_asset', '1231'],

            // LIABILITIES (2xxx)
            ['2111', 'Hutang Usaha', 'liability', 'current_liability', null],
            ['2112', 'Hutang Pajak', 'liability', 'current_liability', null],
            ['2113', 'Hutang Gaji', 'liability', 'current_liability', null],
            ['2121', 'Hutang Bank Jangka Pendek', 'liability', 'current_liability', null],
            ['2211', 'Hutang Bank Jangka Panjang', 'liability', 'long_term_liability', null],

            // EQUITY (3xxx)
            ['3111', 'Modal Disetor', 'equity', 'equity', null],
            ['3211', 'Laba Ditahan', 'equity', 'retained_earnings', null],
            ['3311', 'Laba Tahun Berjalan', 'equity', 'current_earnings', null],

            // REVENUE (4xxx)
            ['4111', 'Penjualan Produk', 'revenue', 'operating_revenue', null],
            ['4112', 'Penjualan Jasa', 'revenue', 'operating_revenue', null],
            ['4211', 'Pendapatan Lain-lain', 'revenue', 'other_revenue', null],

            // COST OF GOODS SOLD (5xxx)
            ['5111', 'Harga Pokok Penjualan', 'expense', 'cogs', null],
            ['5112', 'Biaya Bahan Baku', 'expense', 'cogs', null],
            ['5113', 'Biaya Tenaga Kerja Langsung', 'expense', 'cogs', null],
            ['5114', 'Biaya Overhead Pabrik', 'expense', 'cogs', null],

            // OPERATING EXPENSES (6xxx)
            ['6111', 'Biaya Gaji dan Upah', 'expense', 'operating_expense', null],
            ['6112', 'Biaya Listrik', 'expense', 'operating_expense', null],
            ['6113', 'Biaya Telepon', 'expense', 'operating_expense', null],
            ['6114', 'Biaya Sewa', 'expense', 'operating_expense', null],
            ['6115', 'Biaya Penyusutan', 'expense', 'operating_expense', null],
            ['6116', 'Biaya Pemasaran', 'expense', 'operating_expense', null],
            ['6117', 'Biaya Administrasi', 'expense', 'operating_expense', null],

            // OTHER EXPENSES (8xxx)
            ['8111', 'Biaya Bunga', 'expense', 'other_expense', null],
            ['8112', 'Biaya Lain-lain', 'expense', 'other_expense', null],
        ];

        $accountsCreated = 0;
        foreach ($defaultAccounts as $accountData) {
            $parentAccount = null;
            if ($accountData[4]) {
                $parentAccount = Account::where('client_id', $clientId)
                    ->where('account_code', $accountData[4])
                    ->first();
            }

            Account::create([
                'client_id' => $clientId,
                'account_code' => $accountData[0],
                'account_name' => $accountData[1],
                'account_type' => $accountData[2],
                'account_category' => $accountData[3],
                'parent_account_id' => $parentAccount?->id,
                'is_active' => true,
                'is_system' => true,
                'description' => 'Default system account',
            ]);
            $accountsCreated++;
        }

        return $accountsCreated;
    }

    /**
     * Create default cost centers
     */
    private function createDefaultCostCenters(int $clientId): int
    {
        $defaultCostCenters = [
            ['PROD', 'Produksi', 'Production department'],
            ['SALES', 'Penjualan', 'Sales department'],
            ['ADMIN', 'Administrasi', 'Administration department'],
            ['HR', 'SDM', 'Human Resources department'],
            ['IT', 'IT', 'Information Technology department'],
        ];

        $costCentersCreated = 0;
        foreach ($defaultCostCenters as $costCenterData) {
            DB::table('cost_centers')->insert([
                'client_id' => $clientId,
                'cost_center_code' => $costCenterData[0],
                'cost_center_name' => $costCenterData[1],
                'description' => $costCenterData[2],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $costCentersCreated++;
        }

        return $costCentersCreated;
    }

    /**
     * Create default product categories
     */
    private function createDefaultProductCategories(int $clientId): int
    {
        $defaultCategories = [
            ['RAW', 'Bahan Baku', 'Raw materials'],
            ['WIP', 'Barang Dalam Proses', 'Work in process'],
            ['FG', 'Barang Jadi', 'Finished goods'],
            ['SPARE', 'Spare Part', 'Spare parts'],
            ['SUPPLY', 'Supplies', 'Office and factory supplies'],
        ];

        $categoriesCreated = 0;
        foreach ($defaultCategories as $categoryData) {
            DB::table('categories')->insert([
                'client_id' => $clientId,
                'category_code' => $categoryData[0],
                'category_name' => $categoryData[1],
                'description' => $categoryData[2],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $categoriesCreated++;
        }

        return $categoriesCreated;
    }

    /**
     * Create default units of measure
     */
    private function createDefaultUnits(int $clientId): int
    {
        $defaultUnits = [
            ['PCS', 'Pieces', 'Pieces'],
            ['KG', 'Kilogram', 'Kilogram'],
            ['LITER', 'Liter', 'Liter'],
            ['METER', 'Meter', 'Meter'],
            ['SET', 'Set', 'Set'],
            ['BOX', 'Box', 'Box'],
            ['PACK', 'Pack', 'Pack'],
        ];

        $unitsCreated = 0;
        foreach ($defaultUnits as $unitData) {
            DB::table('units')->insert([
                'client_id' => $clientId,
                'unit_code' => $unitData[0],
                'unit_name' => $unitData[1],
                'description' => $unitData[2],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $unitsCreated++;
        }

        return $unitsCreated;
    }

    /**
     * Create default cash flow categories
     */
    private function createDefaultCashFlowCategories(int $clientId): int
    {
        $defaultCategories = [
            ['OP_IN', 'Penerimaan Operasional', 'operating', 'inflow'],
            ['OP_OUT', 'Pengeluaran Operasional', 'operating', 'outflow'],
            ['INV_IN', 'Penerimaan Investasi', 'investing', 'inflow'],
            ['INV_OUT', 'Pengeluaran Investasi', 'investing', 'outflow'],
            ['FIN_IN', 'Penerimaan Pendanaan', 'financing', 'inflow'],
            ['FIN_OUT', 'Pengeluaran Pendanaan', 'financing', 'outflow'],
        ];

        $categoriesCreated = 0;
        foreach ($defaultCategories as $categoryData) {
            DB::table('cash_flow_categories')->insert([
                'client_id' => $clientId,
                'category_code' => $categoryData[0],
                'category_name' => $categoryData[1],
                'category_type' => $categoryData[2],
                'flow_type' => $categoryData[3],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $categoriesCreated++;
        }

        return $categoriesCreated;
    }

    /**
     * Set default system settings
     */
    private function setDefaultSystemSettings(int $clientId): void
    {
        $defaultSettings = [
            'currency_code' => 'IDR',
            'currency_symbol' => 'Rp',
            'decimal_places' => 2,
            'date_format' => 'd/m/Y',
            'fiscal_year_start' => '01-01',
            'inventory_valuation_method' => 'average',
            'auto_generate_journal' => true,
            'require_approval_po' => true,
            'require_approval_so' => false,
            'auto_reorder_enabled' => true,
            'backup_frequency' => 'daily',
            'notification_enabled' => true,
        ];

        foreach ($defaultSettings as $key => $value) {
            DB::table('system_settings')->updateOrInsert([
                'client_id' => $clientId,
                'setting_key' => $key,
            ], [
                'setting_value' => is_bool($value) ? ($value ? '1' : '0') : $value,
                'updated_at' => now(),
                'created_at' => now(),
            ]);
        }
    }

    /**
     * Create client admin user
     */
    private function createClientAdminUser(Client $client): User
    {
        $adminEmail = 'admin@' . strtolower(str_replace(' ', '', $client->client_name)) . '.com';
        $temporaryPassword = 'Admin123!';

        $adminUser = User::create([
            'name' => $client->client_name . ' Administrator',
            'email' => $adminEmail,
            'password' => bcrypt($temporaryPassword),
            'email_verified_at' => now(),
            'is_active' => true,
            'role' => 'admin',
        ]);

        // Attach user to client
        $adminUser->clients()->attach($client->id, [
            'role' => 'admin',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return $adminUser;
    }

    /**
     * Get system health check
     */
    public function getSystemHealthCheck(int $clientId): array
    {
        $health = [
            'overall_status' => 'healthy',
            'checks' => [],
            'warnings' => [],
            'errors' => [],
        ];

        // Check database connectivity
        try {
            DB::connection()->getPdo();
            $health['checks'][] = ['component' => 'Database', 'status' => 'healthy'];
        } catch (\Exception $e) {
            $health['checks'][] = ['component' => 'Database', 'status' => 'error'];
            $health['errors'][] = 'Database connection failed';
            $health['overall_status'] = 'error';
        }

        // Check cache
        try {
            Cache::put('health_check', 'test', 60);
            $health['checks'][] = ['component' => 'Cache', 'status' => 'healthy'];
        } catch (\Exception $e) {
            $health['checks'][] = ['component' => 'Cache', 'status' => 'warning'];
            $health['warnings'][] = 'Cache system not working properly';
        }

        // Check accounts setup
        $accountCount = Account::where('client_id', $clientId)->count();
        if ($accountCount > 0) {
            $health['checks'][] = ['component' => 'Chart of Accounts', 'status' => 'healthy', 'details' => "{$accountCount} accounts"];
        } else {
            $health['checks'][] = ['component' => 'Chart of Accounts', 'status' => 'warning'];
            $health['warnings'][] = 'No accounts configured';
        }

        // Check users
        $userCount = User::whereHas('clients', function ($q) use ($clientId) {
            $q->where('client_id', $clientId);
        })->count();
        
        if ($userCount > 0) {
            $health['checks'][] = ['component' => 'Users', 'status' => 'healthy', 'details' => "{$userCount} users"];
        } else {
            $health['checks'][] = ['component' => 'Users', 'status' => 'warning'];
            $health['warnings'][] = 'No users configured';
        }

        return $health;
    }

    /**
     * Generate system statistics
     */
    public function getSystemStatistics(int $clientId): array
    {
        return [
            'accounts_count' => Account::where('client_id', $clientId)->count(),
            'active_accounts' => Account::where('client_id', $clientId)->where('is_active', true)->count(),
            'users_count' => User::whereHas('clients', function ($q) use ($clientId) {
                $q->where('client_id', $clientId);
            })->count(),
            'journals_count' => DB::table('journals')->where('client_id', $clientId)->count(),
            'posted_journals' => DB::table('journals')->where('client_id', $clientId)->where('is_posted', true)->count(),
            'products_count' => DB::table('products')->where('client_id', $clientId)->count(),
            'customers_count' => DB::table('customers')->where('client_id', $clientId)->count(),
            'suppliers_count' => DB::table('suppliers')->where('client_id', $clientId)->count(),
            'sales_orders_count' => DB::table('sales_orders')->where('client_id', $clientId)->count(),
            'purchase_orders_count' => DB::table('purchase_orders')->where('client_id', $clientId)->count(),
            'production_orders_count' => DB::table('production_orders')->where('client_id', $clientId)->count(),
            'fixed_assets_count' => DB::table('fixed_assets')->where('client_id', $clientId)->count(),
            'last_backup' => 'Not configured',
            'system_uptime' => 'Running',
        ];
    }
}
