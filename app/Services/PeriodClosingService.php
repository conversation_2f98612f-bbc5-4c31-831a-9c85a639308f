<?php

namespace App\Services;

use App\Models\PeriodClosing;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Account;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PeriodClosingService
{
    /**
     * Initialize period closing for a specific month/year.
     */
    public function initializePeriod(int $clientId, int $year, int $month): PeriodClosing
    {
        // Check if period already exists
        $existing = PeriodClosing::where('client_id', $clientId)
            ->forPeriod($year, $month)
            ->first();

        if ($existing) {
            return $existing;
        }

        // Create new period closing
        $periodStart = Carbon::create($year, $month, 1);
        $periodEnd = $periodStart->copy()->endOfMonth();

        return PeriodClosing::create([
            'client_id' => $clientId,
            'year' => $year,
            'month' => $month,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
            'status' => PeriodClosing::STATUS_OPEN,
            'checklist_items' => PeriodClosing::getDefaultChecklist(),
        ]);
    }

    /**
     * Run validation checks for period closing.
     */
    public function runValidations(PeriodClosing $periodClosing): array
    {
        $validations = [];

        // 1. Check trial balance
        $trialBalance = $this->checkTrialBalance($periodClosing);
        $validations['trial_balance'] = $trialBalance;

        // 2. Check for unreconciled bank accounts
        $bankReconciliation = $this->checkBankReconciliation($periodClosing);
        $validations['bank_reconciliation'] = $bankReconciliation;

        // 3. Check for pending transactions
        $pendingTransactions = $this->checkPendingTransactions($periodClosing);
        $validations['pending_transactions'] = $pendingTransactions;

        // 4. Check inventory valuation
        $inventoryCheck = $this->checkInventoryValuation($periodClosing);
        $validations['inventory_valuation'] = $inventoryCheck;

        // 5. Check depreciation entries
        $depreciationCheck = $this->checkDepreciationEntries($periodClosing);
        $validations['depreciation_entries'] = $depreciationCheck;

        // Update validation results
        $periodClosing->update(['validation_results' => $validations]);

        return $validations;
    }

    /**
     * Check if trial balance is balanced.
     */
    protected function checkTrialBalance(PeriodClosing $periodClosing): array
    {
        $totalDebits = JournalEntry::whereHas('journal', function ($query) use ($periodClosing) {
            $query->where('client_id', $periodClosing->client_id)
                ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end]);
        })->sum('debit');

        $totalCredits = JournalEntry::whereHas('journal', function ($query) use ($periodClosing) {
            $query->where('client_id', $periodClosing->client_id)
                ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end]);
        })->sum('credit');

        $isBalanced = abs($totalDebits - $totalCredits) < 0.01; // Allow for rounding differences

        // Update period closing totals
        $periodClosing->update([
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => $isBalanced,
        ]);

        return [
            'status' => $isBalanced ? 'passed' : 'failed',
            'message' => $isBalanced ? 'Neraca saldo seimbang' : 'Neraca saldo tidak seimbang',
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'difference' => $totalDebits - $totalCredits,
        ];
    }

    /**
     * Check bank reconciliation status.
     */
    protected function checkBankReconciliation(PeriodClosing $periodClosing): array
    {
        // This is a simplified check - in real implementation, you'd check actual bank reconciliation records
        $bankAccounts = Account::where('client_id', $periodClosing->client_id)
            ->where('account_type', 'asset')
            ->where('account_subtype', 'bank')
            ->count();

        return [
            'status' => 'warning',
            'message' => "Pastikan {$bankAccounts} rekening bank telah direkonsiliasi",
            'bank_accounts_count' => $bankAccounts,
        ];
    }

    /**
     * Check for pending transactions.
     */
    protected function checkPendingTransactions(PeriodClosing $periodClosing): array
    {
        $pendingCount = Transaction::where('client_id', $periodClosing->client_id)
            ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end])
            ->where('status', 'pending')
            ->count();

        return [
            'status' => $pendingCount === 0 ? 'passed' : 'failed',
            'message' => $pendingCount === 0 
                ? 'Tidak ada transaksi pending' 
                : "Terdapat {$pendingCount} transaksi pending",
            'pending_count' => $pendingCount,
        ];
    }

    /**
     * Check inventory valuation.
     */
    protected function checkInventoryValuation(PeriodClosing $periodClosing): array
    {
        // Simplified check - in real implementation, you'd validate inventory calculations
        return [
            'status' => 'warning',
            'message' => 'Pastikan stock opname telah dilakukan dan inventory dinilai dengan benar',
        ];
    }

    /**
     * Check depreciation entries.
     */
    protected function checkDepreciationEntries(PeriodClosing $periodClosing): array
    {
        $depreciationEntries = Journal::where('client_id', $periodClosing->client_id)
            ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end])
            ->where('reference_type', 'automated_depreciation')
            ->count();

        return [
            'status' => $depreciationEntries > 0 ? 'passed' : 'warning',
            'message' => $depreciationEntries > 0 
                ? "Terdapat {$depreciationEntries} jurnal penyusutan" 
                : 'Belum ada jurnal penyusutan untuk periode ini',
            'depreciation_entries_count' => $depreciationEntries,
        ];
    }

    /**
     * Close the period.
     */
    public function closePeriod(PeriodClosing $periodClosing, int $userId, ?string $notes = null): bool
    {
        if (!$periodClosing->canBeClosed()) {
            throw new \Exception('Period tidak dapat ditutup dengan status saat ini');
        }

        // Run final validations
        $validations = $this->runValidations($periodClosing);

        // Check if all required checklist items are completed
        $checklist = $periodClosing->checklist_items ?? [];
        $requiredItems = collect($checklist)->where('required', true);
        $completedRequired = $requiredItems->where('completed', true);

        if ($requiredItems->count() !== $completedRequired->count()) {
            throw new \Exception('Semua item checklist wajib harus diselesaikan sebelum tutup buku');
        }

        // Check if trial balance is balanced
        if (!$periodClosing->is_balanced) {
            throw new \Exception('Neraca saldo harus seimbang sebelum tutup buku');
        }

        return DB::transaction(function () use ($periodClosing, $userId, $notes) {
            // Create closing entries if needed
            $this->createClosingEntries($periodClosing, $userId);

            // Update period status
            $periodClosing->update([
                'status' => PeriodClosing::STATUS_CLOSED,
                'closed_by' => $userId,
                'closed_at' => now(),
                'closing_notes' => $notes,
            ]);

            Log::info("Period {$periodClosing->getPeriodName()} closed by user {$userId}");

            return true;
        });
    }

    /**
     * Reopen a closed period.
     */
    public function reopenPeriod(PeriodClosing $periodClosing, int $userId, string $reason): bool
    {
        if (!$periodClosing->canBeReopened()) {
            throw new \Exception('Period tidak dapat dibuka kembali dengan status saat ini');
        }

        $periodClosing->update([
            'status' => PeriodClosing::STATUS_REOPENED,
            'reopened_by' => $userId,
            'reopened_at' => now(),
            'reopen_reason' => $reason,
        ]);

        Log::info("Period {$periodClosing->getPeriodName()} reopened by user {$userId}. Reason: {$reason}");

        return true;
    }

    /**
     * Create closing entries for income and expense accounts.
     */
    protected function createClosingEntries(PeriodClosing $periodClosing, int $userId): void
    {
        // Get retained earnings account
        $retainedEarningsAccount = Account::where('client_id', $periodClosing->client_id)
            ->where('account_code', 'like', '3%') // Equity accounts
            ->where('name', 'like', '%retained%')
            ->first();

        if (!$retainedEarningsAccount) {
            // Create retained earnings account if not exists
            $retainedEarningsAccount = Account::create([
                'client_id' => $periodClosing->client_id,
                'account_code' => '3100',
                'name' => 'Retained Earnings',
                'account_type' => 'equity',
                'account_subtype' => 'retained_earnings',
                'is_active' => true,
            ]);
        }

        // Calculate net income
        $netIncome = $this->calculateNetIncome($periodClosing);

        if (abs($netIncome) > 0.01) {
            // Create closing journal entry
            $journal = Journal::create([
                'client_id' => $periodClosing->client_id,
                'journal_number' => $this->generateClosingJournalNumber($periodClosing),
                'transaction_date' => $periodClosing->period_end,
                'description' => "Closing Entry - {$periodClosing->getPeriodName()}",
                'reference_type' => 'period_closing',
                'reference_id' => $periodClosing->id,
                'created_by' => $userId,
            ]);

            // Create journal entry for net income
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $retainedEarningsAccount->id,
                'description' => 'Net Income Transfer',
                'debit' => $netIncome > 0 ? $netIncome : 0,
                'credit' => $netIncome < 0 ? abs($netIncome) : 0,
            ]);

            // Close income and expense accounts (simplified - in real implementation, you'd close each account individually)
        }
    }

    /**
     * Calculate net income for the period.
     */
    protected function calculateNetIncome(PeriodClosing $periodClosing): float
    {
        // Get total revenue
        $revenue = JournalEntry::whereHas('journal', function ($query) use ($periodClosing) {
            $query->where('client_id', $periodClosing->client_id)
                ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end]);
        })->whereHas('account', function ($query) {
            $query->where('account_type', 'revenue');
        })->sum('credit') - JournalEntry::whereHas('journal', function ($query) use ($periodClosing) {
            $query->where('client_id', $periodClosing->client_id)
                ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end]);
        })->whereHas('account', function ($query) {
            $query->where('account_type', 'revenue');
        })->sum('debit');

        // Get total expenses
        $expenses = JournalEntry::whereHas('journal', function ($query) use ($periodClosing) {
            $query->where('client_id', $periodClosing->client_id)
                ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end]);
        })->whereHas('account', function ($query) {
            $query->where('account_type', 'expense');
        })->sum('debit') - JournalEntry::whereHas('journal', function ($query) use ($periodClosing) {
            $query->where('client_id', $periodClosing->client_id)
                ->whereBetween('transaction_date', [$periodClosing->period_start, $periodClosing->period_end]);
        })->whereHas('account', function ($query) {
            $query->where('account_type', 'expense');
        })->sum('credit');

        return $revenue - $expenses;
    }

    /**
     * Generate closing journal number.
     */
    protected function generateClosingJournalNumber(PeriodClosing $periodClosing): string
    {
        $year = $periodClosing->year;
        $month = str_pad($periodClosing->month, 2, '0', STR_PAD_LEFT);
        
        return "CLOSE{$year}{$month}001";
    }

    /**
     * Get period closing summary.
     */
    public function getPeriodSummary(PeriodClosing $periodClosing): array
    {
        $validations = $periodClosing->validation_results ?? [];
        $checklist = $periodClosing->getChecklistProgress();

        return [
            'period' => $periodClosing->getPeriodName(),
            'status' => $periodClosing->status,
            'checklist_progress' => $checklist,
            'is_balanced' => $periodClosing->is_balanced,
            'total_debits' => $periodClosing->total_debits,
            'total_credits' => $periodClosing->total_credits,
            'validations' => $validations,
            'can_be_closed' => $this->canPeriodBeClosed($periodClosing),
        ];
    }

    /**
     * Check if period can be closed.
     */
    protected function canPeriodBeClosed(PeriodClosing $periodClosing): bool
    {
        if (!$periodClosing->canBeClosed()) {
            return false;
        }

        // Check required checklist items
        $checklist = $periodClosing->checklist_items ?? [];
        $requiredItems = collect($checklist)->where('required', true);
        $completedRequired = $requiredItems->where('completed', true);

        if ($requiredItems->count() !== $completedRequired->count()) {
            return false;
        }

        // Check if balanced
        if (!$periodClosing->is_balanced) {
            return false;
        }

        return true;
    }
}
