<?php

namespace App\Services;

use App\Models\Client;
use App\Models\Account;
use App\Models\Journal;
use App\Models\JournalEntry;
use App\Models\Transaction;
// use App\Models\SalesOrder; // Disabled - using Transaction model
use App\Models\PurchaseOrder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

// TEMPORARILY DISABLED - Needs refactoring to use Transaction model instead of SalesOrder
class TaxIntegrationService_DISABLED
{
    protected array $taxConfigs;

    public function __construct()
    {
        $this->taxConfigs = config('tax.providers', []);
    }

    /**
     * Calculate tax for sales transaction.
     */
    public function calculateSalesTax(int $clientId, array $salesData): array
    {
        try {
            $provider = $this->getTaxProvider();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $provider['api_key'],
                'Content-Type' => 'application/json',
            ])->post($provider['base_url'] . '/calculate/sales', [
                'client_id' => $clientId,
                'transaction_type' => 'sales',
                'amount' => $salesData['amount'],
                'customer_type' => $salesData['customer_type'] ?? 'individual',
                'product_category' => $salesData['product_category'] ?? 'goods',
                'transaction_date' => $salesData['transaction_date'],
                'is_export' => $salesData['is_export'] ?? false,
            ]);

            if (!$response->successful()) {
                throw new \Exception("Failed to calculate sales tax: " . $response->body());
            }

            $taxData = $response->json();

            return [
                'success' => true,
                'ppn_rate' => $taxData['ppn_rate'] ?? 11,
                'ppn_amount' => $taxData['ppn_amount'] ?? 0,
                'pph_rate' => $taxData['pph_rate'] ?? 0,
                'pph_amount' => $taxData['pph_amount'] ?? 0,
                'total_tax' => $taxData['total_tax'] ?? 0,
                'net_amount' => $taxData['net_amount'] ?? $salesData['amount'],
                'tax_details' => $taxData['details'] ?? [],
            ];

        } catch (\Exception $e) {
            Log::error("Sales tax calculation failed: " . $e->getMessage());
            
            // Fallback to manual calculation
            return $this->calculateManualSalesTax($salesData);
        }
    }

    /**
     * Calculate tax for purchase transaction.
     */
    public function calculatePurchaseTax(int $clientId, array $purchaseData): array
    {
        try {
            $provider = $this->getTaxProvider();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $provider['api_key'],
                'Content-Type' => 'application/json',
            ])->post($provider['base_url'] . '/calculate/purchase', [
                'client_id' => $clientId,
                'transaction_type' => 'purchase',
                'amount' => $purchaseData['amount'],
                'supplier_type' => $purchaseData['supplier_type'] ?? 'corporate',
                'product_category' => $purchaseData['product_category'] ?? 'goods',
                'transaction_date' => $purchaseData['transaction_date'],
                'is_import' => $purchaseData['is_import'] ?? false,
            ]);

            if (!$response->successful()) {
                throw new \Exception("Failed to calculate purchase tax: " . $response->body());
            }

            $taxData = $response->json();

            return [
                'success' => true,
                'ppn_rate' => $taxData['ppn_rate'] ?? 11,
                'ppn_amount' => $taxData['ppn_amount'] ?? 0,
                'pph_rate' => $taxData['pph_rate'] ?? 0,
                'pph_amount' => $taxData['pph_amount'] ?? 0,
                'total_tax' => $taxData['total_tax'] ?? 0,
                'net_amount' => $taxData['net_amount'] ?? $purchaseData['amount'],
                'tax_details' => $taxData['details'] ?? [],
            ];

        } catch (\Exception $e) {
            Log::error("Purchase tax calculation failed: " . $e->getMessage());
            
            // Fallback to manual calculation
            return $this->calculateManualPurchaseTax($purchaseData);
        }
    }

    /**
     * Create tax journal entries for sales.
     */
    public function createSalesTaxJournal(int $clientId, SalesOrder $salesOrder, array $taxData): Journal
    {
        $journal = Journal::create([
            'client_id' => $clientId,
            'journal_number' => 'TAX-SALES-' . $salesOrder->id,
            'transaction_date' => $salesOrder->order_date,
            'reference_type' => 'sales_tax',
            'reference_id' => $salesOrder->id,
            'reference_number' => $salesOrder->so_number,
            'description' => 'Sales tax - ' . $salesOrder->so_number,
            'total_amount' => $taxData['total_tax'],
        ]);

        // Get tax accounts
        $accounts = $this->getTaxAccounts($clientId);

        // PPN Output (Credit)
        if ($taxData['ppn_amount'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['ppn_output']->id,
                'description' => 'PPN Output - ' . $salesOrder->so_number,
                'debit' => 0,
                'credit' => $taxData['ppn_amount'],
            ]);
        }

        // PPh (Debit if withheld by customer)
        if ($taxData['pph_amount'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['pph_receivable']->id,
                'description' => 'PPh Receivable - ' . $salesOrder->so_number,
                'debit' => $taxData['pph_amount'],
                'credit' => 0,
            ]);
        }

        return $journal;
    }

    /**
     * Create tax journal entries for purchases.
     */
    public function createPurchaseTaxJournal(int $clientId, PurchaseOrder $purchaseOrder, array $taxData): Journal
    {
        $journal = Journal::create([
            'client_id' => $clientId,
            'journal_number' => 'TAX-PURCHASE-' . $purchaseOrder->id,
            'transaction_date' => $purchaseOrder->order_date,
            'reference_type' => 'purchase_tax',
            'reference_id' => $purchaseOrder->id,
            'reference_number' => $purchaseOrder->po_number,
            'description' => 'Purchase tax - ' . $purchaseOrder->po_number,
            'total_amount' => $taxData['total_tax'],
        ]);

        // Get tax accounts
        $accounts = $this->getTaxAccounts($clientId);

        // PPN Input (Debit)
        if ($taxData['ppn_amount'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['ppn_input']->id,
                'description' => 'PPN Input - ' . $purchaseOrder->po_number,
                'debit' => $taxData['ppn_amount'],
                'credit' => 0,
            ]);
        }

        // PPh (Credit if withheld from supplier)
        if ($taxData['pph_amount'] > 0) {
            JournalEntry::create([
                'journal_id' => $journal->id,
                'account_id' => $accounts['pph_payable']->id,
                'description' => 'PPh Payable - ' . $purchaseOrder->po_number,
                'debit' => 0,
                'credit' => $taxData['pph_amount'],
            ]);
        }

        return $journal;
    }

    /**
     * Generate tax report for a period.
     */
    public function generateTaxReport(int $clientId, Carbon $fromDate, Carbon $toDate): array
    {
        // Get all tax-related journal entries
        $taxJournals = Journal::where('client_id', $clientId)
            ->whereIn('reference_type', ['sales_tax', 'purchase_tax'])
            ->whereBetween('transaction_date', [$fromDate, $toDate])
            ->with('journalEntries.account')
            ->get();

        $report = [
            'period' => [
                'from_date' => $fromDate->toDateString(),
                'to_date' => $toDate->toDateString(),
            ],
            'ppn_summary' => [
                'ppn_output' => 0,
                'ppn_input' => 0,
                'ppn_payable' => 0,
            ],
            'pph_summary' => [
                'pph_receivable' => 0,
                'pph_payable' => 0,
            ],
            'transactions' => [],
        ];

        foreach ($taxJournals as $journal) {
            $transactionData = [
                'journal_number' => $journal->journal_number,
                'transaction_date' => $journal->transaction_date->toDateString(),
                'reference_number' => $journal->reference_number,
                'description' => $journal->description,
                'ppn_output' => 0,
                'ppn_input' => 0,
                'pph_receivable' => 0,
                'pph_payable' => 0,
            ];

            foreach ($journal->journalEntries as $entry) {
                switch ($entry->account->account_code) {
                    case '2200': // PPN Output
                        $transactionData['ppn_output'] = $entry->credit;
                        $report['ppn_summary']['ppn_output'] += $entry->credit;
                        break;
                    case '1300': // PPN Input
                        $transactionData['ppn_input'] = $entry->debit;
                        $report['ppn_summary']['ppn_input'] += $entry->debit;
                        break;
                    case '1310': // PPh Receivable
                        $transactionData['pph_receivable'] = $entry->debit;
                        $report['pph_summary']['pph_receivable'] += $entry->debit;
                        break;
                    case '2210': // PPh Payable
                        $transactionData['pph_payable'] = $entry->credit;
                        $report['pph_summary']['pph_payable'] += $entry->credit;
                        break;
                }
            }

            $report['transactions'][] = $transactionData;
        }

        // Calculate net PPN payable
        $report['ppn_summary']['ppn_payable'] = $report['ppn_summary']['ppn_output'] - $report['ppn_summary']['ppn_input'];

        return $report;
    }

    /**
     * Submit tax report to tax authority.
     */
    public function submitTaxReport(int $clientId, array $reportData): array
    {
        try {
            $provider = $this->getTaxProvider();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $provider['api_key'],
                'Content-Type' => 'application/json',
            ])->post($provider['base_url'] . '/submit/report', [
                'client_id' => $clientId,
                'report_type' => 'monthly_ppn',
                'period' => $reportData['period'],
                'ppn_summary' => $reportData['ppn_summary'],
                'pph_summary' => $reportData['pph_summary'],
                'transactions' => $reportData['transactions'],
            ]);

            if (!$response->successful()) {
                throw new \Exception("Failed to submit tax report: " . $response->body());
            }

            $result = $response->json();

            return [
                'success' => true,
                'submission_id' => $result['submission_id'],
                'status' => $result['status'],
                'submitted_at' => now()->toDateTimeString(),
                'message' => 'Tax report submitted successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Tax report submission failed: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Manual sales tax calculation (fallback).
     */
    protected function calculateManualSalesTax(array $salesData): array
    {
        $amount = $salesData['amount'];
        $ppnRate = 11; // Default PPN rate
        $ppnAmount = $amount * ($ppnRate / 100);

        return [
            'success' => true,
            'ppn_rate' => $ppnRate,
            'ppn_amount' => $ppnAmount,
            'pph_rate' => 0,
            'pph_amount' => 0,
            'total_tax' => $ppnAmount,
            'net_amount' => $amount,
            'tax_details' => ['method' => 'manual_calculation'],
        ];
    }

    /**
     * Manual purchase tax calculation (fallback).
     */
    protected function calculateManualPurchaseTax(array $purchaseData): array
    {
        $amount = $purchaseData['amount'];
        $ppnRate = 11; // Default PPN rate
        $ppnAmount = $amount * ($ppnRate / 100);

        return [
            'success' => true,
            'ppn_rate' => $ppnRate,
            'ppn_amount' => $ppnAmount,
            'pph_rate' => 0,
            'pph_amount' => 0,
            'total_tax' => $ppnAmount,
            'net_amount' => $amount,
            'tax_details' => ['method' => 'manual_calculation'],
        ];
    }

    /**
     * Get tax-related accounts.
     */
    protected function getTaxAccounts(int $clientId): array
    {
        $accounts = [
            'ppn_output' => Account::where('client_id', $clientId)->where('account_code', '2200')->first(),
            'ppn_input' => Account::where('client_id', $clientId)->where('account_code', '1300')->first(),
            'pph_receivable' => Account::where('client_id', $clientId)->where('account_code', '1310')->first(),
            'pph_payable' => Account::where('client_id', $clientId)->where('account_code', '2210')->first(),
        ];

        // Check if all required accounts exist
        foreach ($accounts as $type => $account) {
            if (!$account) {
                throw new \Exception("Required tax account not found: {$type}");
            }
        }

        return $accounts;
    }

    /**
     * Get tax provider configuration.
     */
    protected function getTaxProvider(): array
    {
        $provider = config('tax.default_provider');
        
        if (!isset($this->taxConfigs[$provider])) {
            throw new \Exception("Tax provider configuration not found: {$provider}");
        }

        return $this->taxConfigs[$provider];
    }

    /**
     * Test tax system connection.
     */
    public function testTaxConnection(): array
    {
        try {
            $provider = $this->getTaxProvider();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $provider['api_key'],
            ])->get($provider['base_url'] . '/status');

            if (!$response->successful()) {
                return [
                    'connected' => false,
                    'error' => 'Failed to connect to tax system',
                ];
            }

            return [
                'connected' => true,
                'provider' => $provider['name'],
                'last_tested' => now()->toDateTimeString(),
                'status' => $response->json('status'),
            ];

        } catch (\Exception $e) {
            return [
                'connected' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
