<?php

namespace App\Services;

use App\Models\ProductionCost;
use App\Models\ProductionOrder;
use App\Models\WorkOrder;
use App\Models\CostCenter;
use App\Models\Department;
use App\Models\Account;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ManufacturingOverheadService
{
    /**
     * Allocate manufacturing overhead to production orders
     */
    public function allocateOverhead(int $clientId, string $period): array
    {
        return DB::transaction(function () use ($clientId, $period) {
            $results = [
                'period' => $period,
                'total_overhead_allocated' => 0,
                'allocation_details' => [],
                'cost_centers' => [],
            ];

            // Get all production orders for the period
            $productionOrders = $this->getProductionOrdersForPeriod($clientId, $period);
            
            // Get overhead costs for the period
            $overheadCosts = $this->getOverheadCosts($clientId, $period);
            
            // Calculate allocation rates
            $allocationRates = $this->calculateAllocationRates($clientId, $period);
            
            foreach ($productionOrders as $productionOrder) {
                $allocation = $this->allocateOverheadToOrder($productionOrder, $allocationRates, $overheadCosts);
                $results['allocation_details'][] = $allocation;
                $results['total_overhead_allocated'] += $allocation['total_allocated'];
            }
            
            $results['cost_centers'] = $this->getCostCenterSummary($clientId, $period);
            
            return $results;
        });
    }

    /**
     * Calculate overhead allocation rates for different cost drivers
     */
    private function calculateAllocationRates(int $clientId, string $period): array
    {
        $periodStart = Carbon::createFromFormat('Y-m', $period)->startOfMonth();
        $periodEnd = Carbon::createFromFormat('Y-m', $period)->endOfMonth();

        // Get total activity levels for the period
        $totalDirectLaborHours = $this->getTotalDirectLaborHours($clientId, $periodStart, $periodEnd);
        $totalMachineHours = $this->getTotalMachineHours($clientId, $periodStart, $periodEnd);
        $totalDirectLaborCost = $this->getTotalDirectLaborCost($clientId, $periodStart, $periodEnd);
        $totalUnitsProduced = $this->getTotalUnitsProduced($clientId, $periodStart, $periodEnd);

        // Get total overhead costs by category
        $variableOverheadCosts = $this->getVariableOverheadCosts($clientId, $period);
        $fixedOverheadCosts = $this->getFixedOverheadCosts($clientId, $period);

        return [
            'variable_overhead_per_labor_hour' => $totalDirectLaborHours > 0 
                ? $variableOverheadCosts / $totalDirectLaborHours 
                : 0,
            'variable_overhead_per_machine_hour' => $totalMachineHours > 0 
                ? $variableOverheadCosts / $totalMachineHours 
                : 0,
            'fixed_overhead_per_unit' => $totalUnitsProduced > 0 
                ? $fixedOverheadCosts / $totalUnitsProduced 
                : 0,
            'fixed_overhead_per_labor_hour' => $totalDirectLaborHours > 0 
                ? $fixedOverheadCosts / $totalDirectLaborHours 
                : 0,
            'total_overhead_per_labor_cost' => $totalDirectLaborCost > 0 
                ? ($variableOverheadCosts + $fixedOverheadCosts) / $totalDirectLaborCost 
                : 0,
        ];
    }

    /**
     * Allocate overhead to a specific production order
     */
    private function allocateOverheadToOrder(ProductionOrder $productionOrder, array $rates, array $overheadCosts): array
    {
        $allocation = [
            'production_order_id' => $productionOrder->id,
            'product_name' => $productionOrder->product->product_name,
            'quantity_produced' => $productionOrder->quantity_produced,
            'allocations' => [],
            'total_allocated' => 0,
        ];

        // Get activity data for this production order
        $directLaborHours = $this->getDirectLaborHours($productionOrder);
        $machineHours = $this->getMachineHours($productionOrder);
        $directLaborCost = $this->getDirectLaborCost($productionOrder);

        // Variable overhead allocation based on labor hours
        $variableOverheadByLaborHours = $directLaborHours * $rates['variable_overhead_per_labor_hour'];
        $this->createOverheadAllocation($productionOrder, 'variable_overhead_labor', $variableOverheadByLaborHours, $directLaborHours, 'direct_labor_hours');
        
        // Variable overhead allocation based on machine hours
        $variableOverheadByMachineHours = $machineHours * $rates['variable_overhead_per_machine_hour'];
        $this->createOverheadAllocation($productionOrder, 'variable_overhead_machine', $variableOverheadByMachineHours, $machineHours, 'machine_hours');

        // Fixed overhead allocation based on units produced
        $fixedOverheadByUnits = $productionOrder->quantity_produced * $rates['fixed_overhead_per_unit'];
        $this->createOverheadAllocation($productionOrder, 'fixed_overhead_units', $fixedOverheadByUnits, $productionOrder->quantity_produced, 'units_produced');

        // Fixed overhead allocation based on labor hours
        $fixedOverheadByLaborHours = $directLaborHours * $rates['fixed_overhead_per_labor_hour'];
        $this->createOverheadAllocation($productionOrder, 'fixed_overhead_labor', $fixedOverheadByLaborHours, $directLaborHours, 'direct_labor_hours');

        $allocation['allocations'] = [
            'variable_overhead_labor' => $variableOverheadByLaborHours,
            'variable_overhead_machine' => $variableOverheadByMachineHours,
            'fixed_overhead_units' => $fixedOverheadByUnits,
            'fixed_overhead_labor' => $fixedOverheadByLaborHours,
        ];

        $allocation['total_allocated'] = array_sum($allocation['allocations']);

        return $allocation;
    }

    /**
     * Create overhead allocation record
     */
    private function createOverheadAllocation(ProductionOrder $productionOrder, string $allocationMethod, float $amount, float $quantity, string $allocationBase): void
    {
        if ($amount > 0) {
            ProductionCost::create([
                'client_id' => $productionOrder->client_id,
                'production_order_id' => $productionOrder->id,
                'product_id' => $productionOrder->product_id,
                'cost_type' => 'overhead',
                'cost_category' => 'indirect',
                'cost_element' => $allocationMethod,
                'description' => "Manufacturing Overhead - " . str_replace('_', ' ', ucwords($allocationMethod)),
                'actual_cost' => $amount,
                'quantity' => $quantity,
                'unit_cost' => $quantity > 0 ? $amount / $quantity : 0,
                'allocation_base' => $allocationBase,
                'allocation_rate' => $quantity > 0 ? $amount / $quantity : 0,
                'cost_date' => now(),
                'period_month' => now()->month,
                'period_year' => now()->year,
                'is_allocated' => true,
                'allocated_at' => now(),
                'created_by' => auth()->id() ?? 1,
            ]);
        }
    }

    /**
     * Get production orders for a specific period
     */
    private function getProductionOrdersForPeriod(int $clientId, string $period): \Illuminate\Database\Eloquent\Collection
    {
        $periodStart = Carbon::createFromFormat('Y-m', $period)->startOfMonth();
        $periodEnd = Carbon::createFromFormat('Y-m', $period)->endOfMonth();

        return ProductionOrder::where('client_id', $clientId)
            ->where('status', 'completed')
            ->whereBetween('completed_at', [$periodStart, $periodEnd])
            ->with(['product', 'workOrders.timeEntries'])
            ->get();
    }

    /**
     * Get overhead costs for the period
     */
    private function getOverheadCosts(int $clientId, string $period): array
    {
        // This would typically come from actual expense accounts
        // For now, return sample data
        return [
            'variable_overhead' => [
                'utilities' => 5000000,
                'maintenance' => 3000000,
                'supplies' => 2000000,
            ],
            'fixed_overhead' => [
                'depreciation' => 8000000,
                'insurance' => 2000000,
                'rent' => 5000000,
                'supervision' => ********,
            ],
        ];
    }

    /**
     * Calculate total direct labor hours for the period
     */
    private function getTotalDirectLaborHours(int $clientId, Carbon $start, Carbon $end): float
    {
        return DB::table('work_order_time_entries')
            ->join('work_orders', 'work_order_time_entries.work_order_id', '=', 'work_orders.id')
            ->join('production_orders', 'work_orders.production_order_id', '=', 'production_orders.id')
            ->where('production_orders.client_id', $clientId)
            ->whereBetween('work_order_time_entries.work_date', [$start, $end])
            ->sum('work_order_time_entries.hours_worked');
    }

    /**
     * Calculate total machine hours for the period
     */
    private function getTotalMachineHours(int $clientId, Carbon $start, Carbon $end): float
    {
        return DB::table('work_orders')
            ->join('production_orders', 'work_orders.production_order_id', '=', 'production_orders.id')
            ->where('production_orders.client_id', $clientId)
            ->whereBetween('work_orders.actual_end_time', [$start, $end])
            ->whereNotNull('work_orders.actual_end_time')
            ->sum(DB::raw('TIMESTAMPDIFF(HOUR, work_orders.actual_start_time, work_orders.actual_end_time)'));
    }

    /**
     * Calculate total direct labor cost for the period
     */
    private function getTotalDirectLaborCost(int $clientId, Carbon $start, Carbon $end): float
    {
        return ProductionCost::where('client_id', $clientId)
            ->where('cost_type', 'labor')
            ->where('cost_category', 'direct')
            ->whereBetween('cost_date', [$start, $end])
            ->sum('actual_cost');
    }

    /**
     * Calculate total units produced for the period
     */
    private function getTotalUnitsProduced(int $clientId, Carbon $start, Carbon $end): float
    {
        return ProductionOrder::where('client_id', $clientId)
            ->where('status', 'completed')
            ->whereBetween('completed_at', [$start, $end])
            ->sum('quantity_produced');
    }

    /**
     * Get variable overhead costs for the period
     */
    private function getVariableOverheadCosts(int $clientId, string $period): float
    {
        // This would come from actual expense accounts
        return ********; // Sample amount
    }

    /**
     * Get fixed overhead costs for the period
     */
    private function getFixedOverheadCosts(int $clientId, string $period): float
    {
        // This would come from actual expense accounts
        return ********; // Sample amount
    }

    /**
     * Get direct labor hours for a production order
     */
    private function getDirectLaborHours(ProductionOrder $productionOrder): float
    {
        return $productionOrder->workOrders->sum(function ($workOrder) {
            return $workOrder->timeEntries->sum('hours_worked');
        });
    }

    /**
     * Get machine hours for a production order
     */
    private function getMachineHours(ProductionOrder $productionOrder): float
    {
        return $productionOrder->workOrders->sum(function ($workOrder) {
            if ($workOrder->actual_start_time && $workOrder->actual_end_time) {
                return $workOrder->actual_start_time->diffInHours($workOrder->actual_end_time);
            }
            return 0;
        });
    }

    /**
     * Get direct labor cost for a production order
     */
    private function getDirectLaborCost(ProductionOrder $productionOrder): float
    {
        return ProductionCost::where('production_order_id', $productionOrder->id)
            ->where('cost_type', 'labor')
            ->where('cost_category', 'direct')
            ->sum('actual_cost');
    }

    /**
     * Get cost center summary for the period
     */
    private function getCostCenterSummary(int $clientId, string $period): array
    {
        $costCenters = CostCenter::where('client_id', $clientId)
            ->where('is_active', true)
            ->get();

        $summary = [];
        foreach ($costCenters as $costCenter) {
            $summary[] = [
                'cost_center_id' => $costCenter->id,
                'cost_center_name' => $costCenter->cost_center_name,
                'cost_center_type' => $costCenter->cost_center_type,
                'budget_amount' => $costCenter->budget_amount,
                'actual_overhead' => $this->getActualOverheadForCostCenter($costCenter->id, $period),
                'allocated_overhead' => $this->getAllocatedOverheadForCostCenter($costCenter->id, $period),
            ];
        }

        return $summary;
    }

    private function getActualOverheadForCostCenter(int $costCenterId, string $period): float
    {
        // This would come from actual expense transactions
        return 0; // Placeholder
    }

    private function getAllocatedOverheadForCostCenter(int $costCenterId, string $period): float
    {
        $periodStart = Carbon::createFromFormat('Y-m', $period)->startOfMonth();
        $periodEnd = Carbon::createFromFormat('Y-m', $period)->endOfMonth();

        return ProductionCost::where('cost_center_id', $costCenterId)
            ->where('cost_type', 'overhead')
            ->whereBetween('cost_date', [$periodStart, $periodEnd])
            ->sum('actual_cost');
    }
}
