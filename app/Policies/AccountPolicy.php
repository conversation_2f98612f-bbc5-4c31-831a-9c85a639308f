<?php

namespace App\Policies;

use App\Models\Account;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class AccountPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view accounts
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Account $account): bool
    {
        // Super admin can view all accounts
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only view accounts from their client
        return $user->client_id === $account->client_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // All authenticated users can create accounts
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Account $account): bool
    {
        // Super admin can update all accounts
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only update accounts from their client
        return $user->client_id === $account->client_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Account $account): bool
    {
        // Super admin can delete all accounts
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only delete accounts from their client
        // But cannot delete accounts that have transactions
        return $user->client_id === $account->client_id &&
            !$account->journalEntries()->exists();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Account $account): bool
    {
        // Super admin can restore all accounts
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only restore accounts from their client
        return $user->client_id === $account->client_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Account $account): bool
    {
        // Only super admin can force delete accounts
        return $user->isSuperAdmin();
    }
}
