<?php

namespace App\Policies;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TransactionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view transactions
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Transaction $transaction): bool
    {
        // Super admin can view all transactions
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only view transactions from their client
        return $user->client_id === $transaction->client_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // All authenticated users can create transactions
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Transaction $transaction): bool
    {
        // Super admin can update all transactions
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only update transactions from their client
        // And only if transaction is not approved
        return $user->client_id === $transaction->client_id &&
            $transaction->status !== 'approved';
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Transaction $transaction): bool
    {
        // Super admin can delete all transactions
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only delete transactions from their client
        // And only if transaction is not approved
        return $user->client_id === $transaction->client_id &&
            $transaction->status !== 'approved';
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Transaction $transaction): bool
    {
        // Super admin can restore all transactions
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only restore transactions from their client
        return $user->client_id === $transaction->client_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Transaction $transaction): bool
    {
        // Only super admin can force delete transactions
        return $user->isSuperAdmin();
    }
}
