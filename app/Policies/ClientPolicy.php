<?php

namespace App\Policies;

use App\Models\Client;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ClientPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Only super admin can view all clients
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Client $client): bool
    {
        // Super admin can view all clients
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only view their own client
        return $user->client_id === $client->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Only super admin can create clients
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Client $client): bool
    {
        // Super admin can update all clients
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Users can only update their own client (basic info only)
        return $user->client_id === $client->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Client $client): bool
    {
        // Only super admin can delete clients
        // And only if client has no transactions
        return $user->isSuperAdmin() &&
            !$client->transactions()->exists();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Client $client): bool
    {
        // Only super admin can restore clients
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Client $client): bool
    {
        // Only super admin can force delete clients
        return $user->isSuperAdmin();
    }
}
