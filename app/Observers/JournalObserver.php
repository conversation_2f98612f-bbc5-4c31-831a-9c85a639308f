<?php

namespace App\Observers;

use App\Models\Journal;
use App\Services\FinancialReportService;
use Illuminate\Support\Facades\Cache;

class JournalObserver
{
    protected $financialReportService;

    public function __construct(FinancialReportService $financialReportService)
    {
        $this->financialReportService = $financialReportService;
    }

    /**
     * Handle the Journal "created" event.
     */
    public function created(Journal $journal): void
    {
        $this->clearCache($journal);
    }

    /**
     * Handle the Journal "updated" event.
     */
    public function updated(Journal $journal): void
    {
        $this->clearCache($journal);
    }

    /**
     * Handle the Journal "deleted" event.
     */
    public function deleted(Journal $journal): void
    {
        $this->clearCache($journal);
    }

    /**
     * Handle the Journal "restored" event.
     */
    public function restored(Journal $journal): void
    {
        $this->clearCache($journal);
    }

    /**
     * Handle the Journal "force deleted" event.
     */
    public function forceDeleted(Journal $journal): void
    {
        $this->clearCache($journal);
    }

    /**
     * Clear relevant caches when journal changes
     */
    private function clearCache(Journal $journal): void
    {
        if ($journal->client_id) {
            $this->financialReportService->clearClientCache($journal->client_id);
        }

        // Clear general cache patterns
        Cache::forget('dashboard_kpi_*');
        Cache::forget('monthly_revenue_*');
    }
}
