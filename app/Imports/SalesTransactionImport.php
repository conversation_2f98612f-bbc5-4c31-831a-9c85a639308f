<?php

namespace App\Imports;

use App\Models\Customer;
use App\Models\Product;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\UnitOfMeasure;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;

class SalesTransactionImport implements ToCollection, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    protected $clientId;
    protected $userId;
    protected $importedCount = 0;
    protected $skippedCount = 0;
    protected $errors = [];

    public function __construct()
    {
        $this->clientId = Auth::user()->client_id;
        $this->userId = Auth::user()->id;
    }

    public function collection(Collection $rows)
    {
        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                $this->processRow($row, $index + 2); // +2 because of header row and 0-based index
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function processRow($row, $rowNumber)
    {
        try {
            // Validate required fields
            if (
                empty(trim($row['tanggal_transaksi'] ?? '')) ||
                empty(trim($row['nama_pelanggan'] ?? '')) ||
                empty(trim($row['kode_produk'] ?? '')) ||
                empty(trim($row['jumlah'] ?? ''))
            ) {
                $this->errors[] = "Baris {$rowNumber}: Data wajib tidak lengkap (tanggal_transaksi, nama_pelanggan, kode_produk, jumlah)";
                $this->skippedCount++;
                return;
            }

            // Parse transaction date
            $transactionDate = $this->parseDate($row['tanggal_transaksi']);
            if (!$transactionDate) {
                $this->errors[] = "Baris {$rowNumber}: Format tanggal tidak valid";
                $this->skippedCount++;
                return;
            }

            // Find or create customer
            $customer = $this->findOrCreateCustomer($row['nama_pelanggan'], $row['email_pelanggan'] ?? null);

            // Find product
            $product = $this->findProduct($row['kode_produk']);
            if (!$product) {
                $this->errors[] = "Baris {$rowNumber}: Produk dengan kode '{$row['kode_produk']}' tidak ditemukan";
                $this->skippedCount++;
                return;
            }

            // Find unit
            $unit = $this->findUnit($row['satuan'] ?? $product->unit->unit_code);
            if (!$unit) {
                $this->errors[] = "Baris {$rowNumber}: Satuan '{$row['satuan']}' tidak ditemukan";
                $this->skippedCount++;
                return;
            }

            // Calculate amounts
            $quantity = (float) ($row['jumlah'] ?? 1);
            $unitPrice = (float) ($row['harga_satuan'] ?? $product->selling_price);
            $lineTotal = $quantity * $unitPrice;
            $discountAmount = (float) ($row['diskon'] ?? 0);

            // Create or find existing transaction
            $transactionNumber = $row['nomor_transaksi'] ?? $this->generateTransactionNumber($transactionDate);
            $transaction = $this->findOrCreateTransaction($transactionNumber, $transactionDate, $customer, $row);

            // Add discount to transaction if not already set
            if ($discountAmount > 0) {
                $currentDiscount = $transaction->discount_amount ?? 0;
                $transaction->update(['discount_amount' => $currentDiscount + $discountAmount]);
            }

            // Create transaction item
            TransactionItem::create([
                'client_id' => $this->clientId,
                'transaction_id' => $transaction->id,
                'product_id' => $product->id,
                'description' => $row['deskripsi'] ?? $product->product_name,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'line_total' => $lineTotal,
                'unit_id' => $unit->id,
                'notes' => $row['catatan'] ?? null,
            ]);

            // Update transaction totals
            $this->updateTransactionTotals($transaction);

            $this->importedCount++;
        } catch (\Exception $e) {
            $this->errors[] = "Baris {$rowNumber}: {$e->getMessage()}";
            $this->skippedCount++;
        }
    }

    protected function findOrCreateCustomer($customerName, $email = null)
    {
        $customer = Customer::where('client_id', $this->clientId)
            ->where('customer_name', $customerName)
            ->first();

        if (!$customer) {
            $customerCode = $this->generateCustomerCode($customerName);

            $customer = Customer::create([
                'client_id' => $this->clientId,
                'customer_code' => $customerCode,
                'customer_name' => $customerName,
                'email' => $email,
                'customer_type' => 'retail',
                'is_active' => true,
            ]);
        }

        return $customer;
    }

    protected function findProduct($productCode)
    {
        return Product::where('client_id', $this->clientId)
            ->where('product_code', $productCode)
            ->where('is_active', true)
            ->first();
    }

    protected function findUnit($unitCode)
    {
        return UnitOfMeasure::where('client_id', $this->clientId)
            ->where('unit_code', $unitCode)
            ->first();
    }

    protected function findOrCreateTransaction($transactionNumber, $transactionDate, $customer, $row)
    {
        $transaction = Transaction::where('client_id', $this->clientId)
            ->where('reference_number', $transactionNumber)
            ->first();

        if (!$transaction) {
            $transaction = Transaction::create([
                'client_id' => $this->clientId,
                'transaction_date' => $transactionDate,
                'type' => 'sales',
                'description' => $row['deskripsi_transaksi'] ?? "Import penjualan - {$customer->customer_name}",
                'customer_id' => $customer->id,
                'payment_method' => $row['metode_pembayaran'] ?? 'cash',
                'payment_status' => $row['status_pembayaran'] ?? 'unpaid',
                'amount' => 0, // Will be calculated later
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'reference_number' => $transactionNumber,
                'status' => $row['status_transaksi'] ?? 'completed',
                'created_by' => $this->userId,
                'notes' => $row['catatan_transaksi'] ?? null,
            ]);
        }

        return $transaction;
    }

    protected function updateTransactionTotals($transaction)
    {
        $items = $transaction->items;
        $lineTotal = $items->sum('line_total');
        $discountAmount = $transaction->discount_amount ?? 0;
        $subtotal = $lineTotal - $discountAmount;
        $taxAmount = $subtotal * 0.11;
        $totalAmount = $subtotal + $taxAmount;

        $transaction->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'amount' => $totalAmount,
        ]);
    }

    protected function parseDate($dateString)
    {
        try {
            // Try different date formats
            $formats = ['Y-m-d', 'd/m/Y', 'd-m-Y', 'm/d/Y', 'Y/m/d'];

            foreach ($formats as $format) {
                $date = Carbon::createFromFormat($format, $dateString);
                if ($date) {
                    return $date;
                }
            }

            // Try Carbon's flexible parsing
            return Carbon::parse($dateString);
        } catch (\Exception) {
            return null;
        }
    }

    protected function generateTransactionNumber($date)
    {
        $prefix = 'TXN-' . $date->format('Ymd') . '-';
        $lastNumber = Transaction::where('client_id', $this->clientId)
            ->where('reference_number', 'like', $prefix . '%')
            ->count();

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    protected function generateCustomerCode($customerName)
    {
        // Generate code from customer name
        $words = explode(' ', $customerName);
        $code = '';

        foreach ($words as $word) {
            if (strlen($word) > 0) {
                $code .= strtoupper(substr($word, 0, 3));
            }
        }

        // Ensure minimum length
        if (strlen($code) < 3) {
            $code = strtoupper(substr($customerName, 0, 3));
        }

        // Check if code exists and add number if needed
        $baseCode = substr($code, 0, 6); // Limit to 6 chars
        $counter = 1;
        $finalCode = $baseCode;

        while (Customer::where('client_id', $this->clientId)
            ->where('customer_code', $finalCode)
            ->exists()
        ) {
            $finalCode = $baseCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $finalCode;
    }

    public function rules(): array
    {
        return [
            'tanggal_transaksi' => 'required',
            'nama_pelanggan' => 'required|string|max:255',
            'kode_produk' => 'required|string|max:50',
            'jumlah' => 'required|numeric|min:0.01',
            'harga_satuan' => 'nullable|numeric|min:0',
            'diskon' => 'nullable|numeric|min:0',
            'email_pelanggan' => 'nullable|email|max:255',
            'metode_pembayaran' => 'nullable|in:cash,transfer,check,credit,debit_card,credit_card',
            'status_pembayaran' => 'nullable|in:unpaid,partial,paid,overpaid',
            'status_transaksi' => 'nullable|in:pending,processed,completed,rejected',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'tanggal_transaksi.required' => 'Tanggal transaksi wajib diisi',
            'nama_pelanggan.required' => 'Nama pelanggan wajib diisi',
            'nama_pelanggan.max' => 'Nama pelanggan maksimal 255 karakter',
            'kode_produk.required' => 'Kode produk wajib diisi',
            'kode_produk.max' => 'Kode produk maksimal 50 karakter',
            'jumlah.required' => 'Jumlah wajib diisi',
            'jumlah.numeric' => 'Jumlah harus berupa angka',
            'jumlah.min' => 'Jumlah minimal 0.01',
            'harga_satuan.numeric' => 'Harga satuan harus berupa angka',
            'harga_satuan.min' => 'Harga satuan tidak boleh negatif',
            'diskon.numeric' => 'Diskon harus berupa angka',
            'diskon.min' => 'Diskon tidak boleh negatif',
            'email_pelanggan.email' => 'Format email tidak valid',
            'email_pelanggan.max' => 'Email maksimal 255 karakter',
            'metode_pembayaran.in' => 'Metode pembayaran tidak valid (cash, transfer, check, credit, debit_card, credit_card)',
            'status_pembayaran.in' => 'Status pembayaran tidak valid (unpaid, partial, paid, overpaid)',
            'status_transaksi.in' => 'Status transaksi tidak valid (pending, processed, completed, rejected)',
        ];
    }

    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
