<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\UnitOfMeasure;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;

class ProductImport implements ToCollection, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    protected $clientId;
    protected $userId;
    protected $importedCount = 0;
    protected $skippedCount = 0;
    protected $updatedCount = 0;
    protected $errors = [];

    public function __construct()
    {
        $user = Auth::user();

        if (!$user) {
            throw new \Exception('User must be authenticated to import products');
        }

        if (!$user->client_id) {
            throw new \Exception('User must be associated with a client to import products');
        }

        $this->clientId = $user->client_id;
        $this->userId = $user->id;
    }

    /**
     * Set client ID manually (useful for testing)
     */
    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
        return $this;
    }

    /**
     * Set user ID manually (useful for testing)
     */
    public function setUserId($userId)
    {
        $this->userId = $userId;
        return $this;
    }

    public function collection(Collection $rows)
    {
        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                $this->processRow($row, $index + 2); // +2 because of header row and 0-based index
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function processRow($row, $rowNumber)
    {
        try {
            // Validate required fields
            if (
                empty(trim($row['kode_produk'] ?? '')) ||
                empty(trim($row['nama_produk'] ?? '')) ||
                empty(trim($row['kategori'] ?? '')) ||
                empty(trim($row['satuan'] ?? ''))
            ) {
                $this->errors[] = "Baris {$rowNumber}: Data wajib tidak lengkap (kode_produk, nama_produk, kategori, satuan)";
                $this->skippedCount++;
                return;
            }

            // Find category
            $category = $this->findOrCreateCategory($row['kategori']);
            if (!$category) {
                $this->errors[] = "Baris {$rowNumber}: Kategori '{$row['kategori']}' tidak dapat dibuat";
                $this->skippedCount++;
                return;
            }

            // Find unit
            $unit = $this->findOrCreateUnit($row['satuan']);
            if (!$unit) {
                $this->errors[] = "Baris {$rowNumber}: Satuan '{$row['satuan']}' tidak dapat dibuat";
                $this->skippedCount++;
                return;
            }

            // Check if product exists
            $existingProduct = Product::where('client_id', $this->clientId)
                ->where('product_code', $row['kode_produk'])
                ->first();

            // Prepare product data
            $productData = $this->prepareProductData($row, $category, $unit);

            if ($existingProduct) {
                // Update existing product
                $existingProduct->update($productData);
                $this->updatedCount++;
            } else {
                // Create new product
                Product::create($productData);
                $this->importedCount++;
            }
        } catch (\Exception $e) {
            $this->errors[] = "Baris {$rowNumber}: {$e->getMessage()}";
            $this->skippedCount++;
        }
    }

    protected function findOrCreateCategory($categoryName)
    {
        $category = ProductCategory::where('client_id', $this->clientId)
            ->where('category_name', $categoryName)
            ->first();

        if (!$category) {
            $categoryCode = $this->generateCategoryCode($categoryName);

            $category = ProductCategory::create([
                'client_id' => $this->clientId,
                'category_code' => $categoryCode,
                'category_name' => $categoryName,
                'is_active' => true,
            ]);
        }

        return $category;
    }

    protected function findOrCreateUnit($unitName)
    {
        // Try to find by unit_code or unit_name
        $unit = UnitOfMeasure::where('client_id', $this->clientId)
            ->where(function ($query) use ($unitName) {
                $query->where('unit_code', $unitName)
                    ->orWhere('unit_name', $unitName);
            })
            ->first();

        if (!$unit) {
            $unit = UnitOfMeasure::create([
                'client_id' => $this->clientId,
                'unit_code' => strtoupper($unitName),
                'unit_name' => $unitName,
                'is_active' => true,
            ]);
        }

        return $unit;
    }

    protected function prepareProductData($row, $category, $unit)
    {
        return [
            'client_id' => $this->clientId,
            'category_id' => $category->id,
            'unit_id' => $unit->id,
            'product_code' => $row['kode_produk'],
            'product_name' => $row['nama_produk'],
            'description' => $row['deskripsi'] ?? null,
            'product_type' => 'finished_good', // Default type
            'barcode' => $row['barcode'] ?? null,
            'sku' => $row['kode_produk'], // Use product code as SKU
            'standard_cost' => $this->parseNumeric($row['harga_pokok'] ?? 0),
            'selling_price' => $this->parseNumeric($row['harga_jual'] ?? 0),
            'purchase_price' => $this->parseNumeric($row['harga_beli'] ?? 0),
            'minimum_stock' => $this->parseNumeric($row['stok_minimum'] ?? 0),
            'maximum_stock' => $this->parseNumeric($row['stok_maksimum'] ?? 0),
            'reorder_point' => $this->parseNumeric($row['titik_reorder'] ?? 0),
            'lead_time_days' => 0, // Default
            'shelf_life_days' => 0, // Default
            'weight' => $this->parseNumeric($row['berat'] ?? 0),
            'dimensions' => $row['dimensi'] ?? null,
            'tax_rate' => 0.11, // Default 11% PPN
            'discount_rate' => 0,
            'commission_rate' => 0,
            'warranty_period' => 0,
            'brand' => $row['merek'] ?? null,
            'model' => null,
            'color' => null,
            'size' => null,
            'material' => null,
            'origin_country' => 'Indonesia',
            'hs_code' => null,
            'image_url' => null,
            'is_active' => true,
            'is_stockable' => true,
            'is_purchasable' => true,
            'is_saleable' => true,
            'is_manufactured' => $this->parseBoolean($row['dapat_dimanufaktur'] ?? false),
            'is_serialized' => false,
            'is_batch_tracked' => false,
            'is_expirable' => false,
        ];
    }



    protected function parseNumeric($value)
    {
        if (is_numeric($value)) {
            return (float) $value;
        }

        // Remove common formatting
        $cleaned = preg_replace('/[^\d.,]/', '', $value);
        $cleaned = str_replace(',', '.', $cleaned);

        return is_numeric($cleaned) ? (float) $cleaned : 0;
    }

    protected function parseBoolean($value)
    {
        if (is_bool($value)) {
            return $value;
        }

        $value = strtolower(trim($value));
        return in_array($value, ['1', 'true', 'ya', 'yes', 'aktif', 'active']);
    }



    protected function generateCategoryCode($categoryName)
    {
        // Generate code from category name
        $words = explode(' ', $categoryName);
        $code = '';

        foreach ($words as $word) {
            if (strlen($word) > 0) {
                $code .= strtoupper(substr($word, 0, 3));
            }
        }

        // Ensure minimum length
        if (strlen($code) < 3) {
            $code = strtoupper(substr($categoryName, 0, 3));
        }

        // Check if code exists and add number if needed
        $baseCode = substr($code, 0, 6); // Limit to 6 chars
        $counter = 1;
        $finalCode = $baseCode;

        while (ProductCategory::where('client_id', $this->clientId)
            ->where('category_code', $finalCode)
            ->exists()
        ) {
            $finalCode = $baseCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $finalCode;
    }

    public function rules(): array
    {
        return [
            'kode_produk' => 'required|string|max:50',
            'nama_produk' => 'required|string|max:255',
            'kategori' => 'required|string|max:100',
            'satuan' => 'required|string|max:20',
            'harga_pokok' => 'nullable|numeric|min:0',
            'harga_jual' => 'nullable|numeric|min:0',
            'harga_beli' => 'nullable|numeric|min:0',
            'stok_minimum' => 'nullable|numeric|min:0',
            'stok_maksimum' => 'nullable|numeric|min:0',
            'titik_reorder' => 'nullable|numeric|min:0',
            'berat' => 'nullable|numeric|min:0',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'kode_produk.required' => 'Kode produk wajib diisi',
            'kode_produk.max' => 'Kode produk maksimal 50 karakter',
            'nama_produk.required' => 'Nama produk wajib diisi',
            'nama_produk.max' => 'Nama produk maksimal 255 karakter',
            'kategori.required' => 'Kategori wajib diisi',
            'satuan.required' => 'Satuan wajib diisi',
            'harga_pokok.numeric' => 'Harga pokok harus berupa angka',
            'harga_jual.numeric' => 'Harga jual harus berupa angka',
            'harga_beli.numeric' => 'Harga beli harus berupa angka',
            'stok_minimum.numeric' => 'Stok minimum harus berupa angka',
            'stok_maksimum.numeric' => 'Stok maksimum harus berupa angka',
            'berat.numeric' => 'Berat harus berupa angka',
        ];
    }

    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    public function getUpdatedCount(): int
    {
        return $this->updatedCount;
    }

    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
