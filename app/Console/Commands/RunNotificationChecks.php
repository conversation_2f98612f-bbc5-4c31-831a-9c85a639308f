<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class RunNotificationChecks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:check {--client= : Check for specific client ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run all notification checks for approval pending, overdue payments, low stock, etc.';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService)
    {
        $this->info('Starting notification checks...');

        $clientId = $this->option('client');

        if ($clientId) {
            $clients = Client::where('id', $clientId)->get();
        } else {
            $clients = Client::all();
        }

        if ($clients->isEmpty()) {
            $this->error('No clients found.');
            return Command::FAILURE;
        }

        $totalResults = [];

        foreach ($clients as $client) {
            $this->info("Checking notifications for client: {$client->name}");

            $results = $notificationService->runAllChecks($client->id);
            $totalResults[$client->id] = $results;

            foreach ($results as $checkType => $result) {
                if (str_contains($result, 'error')) {
                    $this->error("  ✗ {$checkType}: {$result}");
                } else {
                    $this->info("  ✓ {$checkType}: {$result}");
                }
            }
        }

        // Clean up expired notifications
        $this->info('Cleaning up expired notifications...');
        $deletedCount = $notificationService->cleanupExpiredNotifications();
        $this->info("Deleted {$deletedCount} expired notifications.");

        $this->newLine();
        $this->info('Notification checks completed.');

        // Return success if no errors
        $hasErrors = collect($totalResults)->flatten()->contains(fn($result) => str_contains($result, 'error'));
        return $hasErrors ? Command::FAILURE : Command::SUCCESS;
    }
}
