<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateUserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create 
                            {--name= : The name of the user}
                            {--email= : The email of the user}
                            {--password= : The password of the user}
                            {--client= : The client name or ID}
                            {--type=client : The user type (client, super_admin)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new user for the SaaS application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->option('name') ?: $this->ask('What is the user name?');
        $email = $this->option('email') ?: $this->ask('What is the user email?');
        $password = $this->option('password') ?: $this->secret('What is the user password?');
        $userType = $this->option('type');
        
        // Validate input
        $validator = Validator::make([
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'user_type' => $userType,
        ], [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'user_type' => 'required|in:client,super_admin',
        ]);

        if ($validator->fails()) {
            $this->error('Validation failed:');
            foreach ($validator->errors()->all() as $error) {
                $this->error('- ' . $error);
            }
            return 1;
        }

        // Handle client
        $client = null;
        if ($userType === 'client') {
            $clientInput = $this->option('client');
            
            if ($clientInput) {
                // Try to find client by ID or name
                $client = Client::where('id', $clientInput)
                    ->orWhere('name', $clientInput)
                    ->first();
                    
                if (!$client) {
                    $this->error("Client '{$clientInput}' not found.");
                    return 1;
                }
            } else {
                // Show available clients
                $clients = Client::where('is_active', true)->get();
                
                if ($clients->isEmpty()) {
                    $this->error('No active clients found. Please create a client first.');
                    return 1;
                }
                
                $this->info('Available clients:');
                foreach ($clients as $c) {
                    $this->line("- {$c->id}: {$c->name}");
                }
                
                $clientId = $this->ask('Enter client ID');
                $client = Client::find($clientId);
                
                if (!$client) {
                    $this->error("Client with ID '{$clientId}' not found.");
                    return 1;
                }
            }
        } else {
            // For super_admin, use default client
            $client = Client::first();
            if (!$client) {
                $this->error('No client found. Please run seeder first.');
                return 1;
            }
        }

        // Create user
        try {
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'user_type' => $userType,
                'client_id' => $client->id,
                'email_verified_at' => now(),
            ]);

            $this->info('User created successfully!');
            $this->line('');
            $this->line("Name: {$user->name}");
            $this->line("Email: {$user->email}");
            $this->line("Type: {$user->user_type}");
            $this->line("Client: {$client->name}");
            $this->line("Password: {$password}");
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to create user: ' . $e->getMessage());
            return 1;
        }
    }
}
