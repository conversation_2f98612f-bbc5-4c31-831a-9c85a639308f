<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\PayrollIntegrationService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SyncPayrollData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payroll:sync {--client= : Sync specific client only} {--month= : Specific month (YYYY-MM)} {--summary : Show payroll summary}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync payroll data and create automatic journal entries';

    /**
     * Execute the console command.
     */
    public function handle(PayrollIntegrationService $payrollService)
    {
        $clientId = $this->option('client');
        $month = $this->option('month');
        $showSummary = $this->option('summary');

        $this->info('Starting payroll synchronization...');

        // Check payroll connection
        $status = $payrollService->testPayrollConnection();
        if (!$status['connected']) {
            $this->error('Failed to connect to payroll system: ' . $status['error']);
            return Command::FAILURE;
        }

        $this->info('✓ Connected to payroll system: ' . $status['provider']);

        // Set date range
        if ($month) {
            $fromDate = Carbon::createFromFormat('Y-m', $month)->startOfMonth();
            $toDate = Carbon::createFromFormat('Y-m', $month)->endOfMonth();
        } else {
            $fromDate = now()->subMonth()->startOfMonth();
            $toDate = now()->subMonth()->endOfMonth();
        }

        $this->info("Processing payroll for period: {$fromDate->toDateString()} to {$toDate->toDateString()}");

        if ($clientId) {
            $clients = Client::where('id', $clientId)->get();
        } else {
            $clients = Client::all();
        }

        if ($clients->isEmpty()) {
            $this->error('No clients found.');
            return Command::FAILURE;
        }

        $totalProcessed = 0;
        $totalErrors = 0;

        foreach ($clients as $client) {
            $this->info("Processing payroll for client: {$client->name}");

            try {
                // Sync payroll data
                $result = $payrollService->syncPayrollData($client->id, $fromDate, $toDate);

                $this->info("  Payroll sync results:");
                $this->info("    Total payrolls: {$result['total_payrolls']}");
                $this->info("    Processed: {$result['processed']}");
                $this->info("    Errors: {$result['errors']}");

                $totalProcessed += $result['processed'];
                $totalErrors += $result['errors'];

                if (!empty($result['errors_detail'])) {
                    $this->warn("  Errors encountered:");
                    foreach ($result['errors_detail'] as $error) {
                        $this->warn("    - " . ($error['error'] ?? 'Unknown error'));
                    }
                }

                // Show summary if requested
                if ($showSummary) {
                    $summary = $payrollService->getPayrollSummary($client->id, $fromDate, $toDate);

                    $this->info("  Payroll Summary:");
                    $this->info("    Total Gross Pay: " . number_format($summary['total_gross_pay'], 2));
                    $this->info("    Total Net Pay: " . number_format($summary['total_net_pay'], 2));
                    $this->info("    Total Tax Withheld: " . number_format($summary['total_tax_withheld'], 2));
                    $this->info("    Total Social Security: " . number_format($summary['total_social_security'], 2));
                    $this->info("    Total Other Deductions: " . number_format($summary['total_other_deductions'], 2));
                }
            } catch (\Exception $e) {
                $this->error("  Failed to process payroll for client {$client->name}: " . $e->getMessage());
                $totalErrors++;
            }
        }

        $this->newLine();
        $this->info("Payroll synchronization completed!");
        $this->info("Total processed: {$totalProcessed}");
        $this->info("Total errors: {$totalErrors}");

        return $totalErrors > 0 ? Command::FAILURE : Command::SUCCESS;
    }
}
