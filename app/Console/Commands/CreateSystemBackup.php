<?php

namespace App\Console\Commands;

use App\Services\BackupService;
use Illuminate\Console\Command;

class CreateSystemBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:create {--client= : Backup specific client only} {--cleanup : Clean up old backups}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create system backup with database, files, and configuration';

    /**
     * Execute the console command.
     */
    public function handle(BackupService $backupService)
    {
        $clientId = $this->option('client');
        $cleanup = $this->option('cleanup');

        $this->info('Starting system backup...');

        if ($clientId) {
            $this->info("Creating backup for client ID: {$clientId}");
        } else {
            $this->info('Creating full system backup');
        }

        // Create backup
        $result = $backupService->createFullBackup($clientId);

        if ($result['status'] === 'success') {
            $this->info("✓ Backup created successfully!");
            $this->info("  Backup name: {$result['backup_name']}");
            $this->info("  Duration: {$result['duration']} seconds");
            $this->info("  Size: " . $this->formatBytes($result['size']));

            if (isset($result['files']['archive'])) {
                $this->info("  Archive: {$result['files']['archive']}");
            }
        } else {
            $this->error("✗ Backup failed!");
            $this->error("  Error: {$result['error']}");
            return Command::FAILURE;
        }

        // Cleanup old backups if requested
        if ($cleanup) {
            $this->info('Cleaning up old backups...');
            $deletedCount = $backupService->cleanupOldBackups();
            $this->info("✓ Deleted {$deletedCount} old backup files");
        }

        return Command::SUCCESS;
    }

    /**
     * Format bytes to human readable format.
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
