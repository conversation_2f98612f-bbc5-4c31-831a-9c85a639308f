<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\ProductEngineeringSampleSeeder;

class CreateProductEngineeringSampleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product-engineering:create-sample-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create sample data for Product Engineering Analysis dashboard';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating sample data for Product Engineering Analysis...');

        $seeder = new ProductEngineeringSampleSeeder();
        $seeder->setCommand($this);
        $seeder->run();

        $this->info('');
        $this->info('✅ Sample data created successfully!');
        $this->info('');
        $this->info('You can now:');
        $this->info('1. Visit the Product Engineering dashboard');
        $this->info('2. View the 4-quadrant analysis');
        $this->info('3. Analyze product performance by margin vs frequency');
        $this->info('');
        $this->info('Dashboard URL: /admin/client/{tenant}/product-engineering');
    }
}
