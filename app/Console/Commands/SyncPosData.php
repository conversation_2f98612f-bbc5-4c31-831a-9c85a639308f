<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\PosIntegrationService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SyncPosData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:sync {--client= : Sync specific client only} {--days=1 : Number of days to sync} {--products : Sync products to POS}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync sales data from POS system (Olsera) and optionally sync products to POS';

    /**
     * Execute the console command.
     */
    public function handle(PosIntegrationService $posService)
    {
        $clientId = $this->option('client');
        $days = (int) $this->option('days');
        $syncProducts = $this->option('products');

        $this->info('Starting POS synchronization...');

        // Check POS connection
        $status = $posService->getPosStatus();
        if (!$status['connected']) {
            $this->error('Failed to connect to POS system: ' . $status['error']);
            return Command::FAILURE;
        }

        $this->info('✓ Connected to POS system');

        if ($clientId) {
            $clients = Client::where('id', $clientId)->get();
        } else {
            $clients = Client::all();
        }

        if ($clients->isEmpty()) {
            $this->error('No clients found.');
            return Command::FAILURE;
        }

        $totalSynced = 0;
        $totalErrors = 0;

        foreach ($clients as $client) {
            $this->info("Syncing data for client: {$client->name}");

            try {
                // Sync sales data
                $fromDate = now()->subDays($days);
                $toDate = now();

                $result = $posService->syncSalesData($client->id, $fromDate, $toDate);

                $this->info("  Sales sync results:");
                $this->info("    Total sales: {$result['total_sales']}");
                $this->info("    Processed: {$result['processed']}");
                $this->info("    Errors: {$result['errors']}");

                $totalSynced += $result['processed'];
                $totalErrors += $result['errors'];

                if (!empty($result['errors_detail'])) {
                    $this->warn("  Errors encountered:");
                    foreach ($result['errors_detail'] as $error) {
                        $this->warn("    - " . ($error['error'] ?? 'Unknown error'));
                    }
                }

                // Sync products if requested
                if ($syncProducts) {
                    $this->info("  Syncing products to POS...");
                    $productResult = $posService->syncProductsToPos($client->id);

                    $this->info("  Product sync results:");
                    $this->info("    Total products: {$productResult['total_products']}");
                    $this->info("    Synced: {$productResult['synced']}");
                    $this->info("    Errors: {$productResult['errors']}");

                    if (!empty($productResult['errors_detail'])) {
                        $this->warn("  Product sync errors:");
                        foreach ($productResult['errors_detail'] as $error) {
                            $this->warn("    - {$error['product_name']}: {$error['error']}");
                        }
                    }
                }
            } catch (\Exception $e) {
                $this->error("  Failed to sync client {$client->name}: " . $e->getMessage());
                $totalErrors++;
            }
        }

        $this->newLine();
        $this->info("Synchronization completed!");
        $this->info("Total synced: {$totalSynced}");
        $this->info("Total errors: {$totalErrors}");

        return $totalErrors > 0 ? Command::FAILURE : Command::SUCCESS;
    }
}
