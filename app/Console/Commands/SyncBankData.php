<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\BankIntegrationService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SyncBankData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bank:sync {--client= : Sync specific client only} {--days=7 : Number of days to sync} {--balances : Sync account balances only} {--transactions : Sync transactions only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync bank account balances and transactions from bank APIs';

    /**
     * Execute the console command.
     */
    public function handle(BankIntegrationService $bankService)
    {
        $clientId = $this->option('client');
        $days = (int) $this->option('days');
        $syncBalances = $this->option('balances');
        $syncTransactions = $this->option('transactions');

        // If no specific sync type is specified, sync both
        if (!$syncBalances && !$syncTransactions) {
            $syncBalances = true;
            $syncTransactions = true;
        }

        $this->info('Starting bank data synchronization...');

        if ($clientId) {
            $clients = Client::where('id', $clientId)->get();
        } else {
            $clients = Client::all();
        }

        if ($clients->isEmpty()) {
            $this->error('No clients found.');
            return Command::FAILURE;
        }

        $totalErrors = 0;

        foreach ($clients as $client) {
            $this->info("Syncing bank data for client: {$client->name}");

            try {
                // Sync account balances
                if ($syncBalances) {
                    $this->info("  Syncing account balances...");
                    $balanceResult = $bankService->syncAccountBalances($client->id);

                    $this->info("  Balance sync results:");
                    $this->info("    Total accounts: {$balanceResult['total_accounts']}");
                    $this->info("    Synced: {$balanceResult['synced']}");
                    $this->info("    Errors: {$balanceResult['errors']}");

                    if (!empty($balanceResult['errors_detail'])) {
                        $this->warn("  Balance sync errors:");
                        foreach ($balanceResult['errors_detail'] as $error) {
                            $this->warn("    - {$error['account_name']}: {$error['error']}");
                        }
                    }

                    $totalErrors += $balanceResult['errors'];
                }

                // Sync transactions
                if ($syncTransactions) {
                    $this->info("  Syncing transactions...");
                    $fromDate = now()->subDays($days);
                    $toDate = now();

                    $transactionResult = $bankService->importBankTransactions($client->id, $fromDate, $toDate);

                    $this->info("  Transaction sync results:");
                    $this->info("    Total accounts: {$transactionResult['total_accounts']}");
                    $this->info("    Total transactions: {$transactionResult['total_transactions']}");
                    $this->info("    Imported: {$transactionResult['imported']}");
                    $this->info("    Duplicates: {$transactionResult['duplicates']}");
                    $this->info("    Errors: {$transactionResult['errors']}");

                    if (!empty($transactionResult['errors_detail'])) {
                        $this->warn("  Transaction sync errors:");
                        foreach ($transactionResult['errors_detail'] as $error) {
                            $this->warn("    - {$error['account_name']}: {$error['error']}");
                        }
                    }

                    $totalErrors += $transactionResult['errors'];
                }
            } catch (\Exception $e) {
                $this->error("  Failed to sync client {$client->name}: " . $e->getMessage());
                $totalErrors++;
            }
        }

        $this->newLine();
        $this->info("Bank synchronization completed!");
        $this->info("Total errors: {$totalErrors}");

        return $totalErrors > 0 ? Command::FAILURE : Command::SUCCESS;
    }
}
