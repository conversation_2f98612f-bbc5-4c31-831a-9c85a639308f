<?php

namespace App\Console\Commands;

use App\Services\AutomatedJournalService;
use Illuminate\Console\Command;

class ExecuteAutomatedJournals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'journals:execute-automated {--client= : Execute for specific client ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Execute all due automated journal entries';

    /**
     * Execute the console command.
     */
    public function handle(AutomatedJournalService $service)
    {
        $this->info('Starting automated journal execution...');

        $results = $service->executeAllDue();

        $successCount = 0;
        $errorCount = 0;

        foreach ($results as $result) {
            if ($result['success']) {
                $successCount++;
                $this->info("✓ Executed: {$result['entry']->name} - Journal: {$result['journal']->journal_number}");
            } else {
                $errorCount++;
                $this->error("✗ Failed: {$result['entry']->name} - Error: {$result['error']}");
            }
        }

        $this->newLine();
        $this->info("Execution completed:");
        $this->info("- Successful: {$successCount}");
        $this->info("- Failed: {$errorCount}");
        $this->info("- Total: " . count($results));

        return $errorCount === 0 ? Command::SUCCESS : Command::FAILURE;
    }
}
