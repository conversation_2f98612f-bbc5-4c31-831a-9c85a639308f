<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\AuditService;
use Illuminate\Console\Command;

class CleanupAuditLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audit:cleanup {--days=365 : Number of days to keep audit logs} {--client= : Specific client ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old audit logs to maintain database performance';

    /**
     * Execute the console command.
     */
    public function handle(AuditService $auditService)
    {
        $daysToKeep = (int) $this->option('days');
        $clientId = $this->option('client');

        $this->info("Starting audit log cleanup (keeping last {$daysToKeep} days)...");

        if ($clientId) {
            $clients = Client::where('id', $clientId)->get();
        } else {
            $clients = Client::all();
        }

        if ($clients->isEmpty()) {
            $this->error('No clients found.');
            return Command::FAILURE;
        }

        $totalDeleted = 0;

        foreach ($clients as $client) {
            $this->info("Cleaning up audit logs for client: {$client->name}");

            $deleted = $auditService->cleanupOldLogs($client->id, $daysToKeep);
            $totalDeleted += $deleted;

            $this->info("  Deleted {$deleted} old audit logs");
        }

        $this->newLine();
        $this->info("Cleanup completed. Total deleted: {$totalDeleted} audit logs");

        return Command::SUCCESS;
    }
}
