<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\TaxIntegrationService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ProcessTaxReporting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tax:report {--client= : Process specific client only} {--month= : Specific month (YYYY-MM)} {--submit : Submit report to tax authority}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate and optionally submit tax reports to tax authorities';

    /**
     * Execute the console command.
     */
    public function handle(TaxIntegrationService $taxService)
    {
        $clientId = $this->option('client');
        $month = $this->option('month');
        $submitReport = $this->option('submit');

        $this->info('Starting tax report processing...');

        // Check tax system connection
        $status = $taxService->testTaxConnection();
        if (!$status['connected']) {
            $this->warn('Tax system not connected: ' . $status['error']);
            $this->info('Proceeding with local report generation only...');
        } else {
            $this->info('✓ Connected to tax system: ' . $status['provider']);
        }

        // Set date range
        if ($month) {
            $fromDate = Carbon::createFromFormat('Y-m', $month)->startOfMonth();
            $toDate = Carbon::createFromFormat('Y-m', $month)->endOfMonth();
        } else {
            $fromDate = now()->subMonth()->startOfMonth();
            $toDate = now()->subMonth()->endOfMonth();
        }

        $this->info("Processing tax report for period: {$fromDate->toDateString()} to {$toDate->toDateString()}");

        if ($clientId) {
            $clients = Client::where('id', $clientId)->get();
        } else {
            $clients = Client::all();
        }

        if ($clients->isEmpty()) {
            $this->error('No clients found.');
            return Command::FAILURE;
        }

        $totalProcessed = 0;
        $totalErrors = 0;

        foreach ($clients as $client) {
            $this->info("Processing tax report for client: {$client->name}");

            try {
                // Generate tax report
                $report = $taxService->generateTaxReport($client->id, $fromDate, $toDate);

                $this->info("  Tax Report Summary:");
                $this->info("    PPN Output: " . number_format($report['ppn_summary']['ppn_output'], 2));
                $this->info("    PPN Input: " . number_format($report['ppn_summary']['ppn_input'], 2));
                $this->info("    PPN Payable: " . number_format($report['ppn_summary']['ppn_payable'], 2));
                $this->info("    PPh Receivable: " . number_format($report['pph_summary']['pph_receivable'], 2));
                $this->info("    PPh Payable: " . number_format($report['pph_summary']['pph_payable'], 2));
                $this->info("    Total Transactions: " . count($report['transactions']));

                // Submit report if requested and connection is available
                if ($submitReport && $status['connected']) {
                    $this->info("  Submitting tax report...");

                    $submissionResult = $taxService->submitTaxReport($client->id, $report);

                    if ($submissionResult['success']) {
                        $this->info("  ✓ Tax report submitted successfully");
                        $this->info("    Submission ID: {$submissionResult['submission_id']}");
                        $this->info("    Status: {$submissionResult['status']}");
                    } else {
                        $this->error("  ✗ Tax report submission failed: {$submissionResult['error']}");
                        $totalErrors++;
                    }
                } elseif ($submitReport && !$status['connected']) {
                    $this->warn("  Cannot submit report - tax system not connected");
                }

                $totalProcessed++;
            } catch (\Exception $e) {
                $this->error("  Failed to process tax report for client {$client->name}: " . $e->getMessage());
                $totalErrors++;
            }
        }

        $this->newLine();
        $this->info("Tax report processing completed!");
        $this->info("Total processed: {$totalProcessed}");
        $this->info("Total errors: {$totalErrors}");

        return $totalErrors > 0 ? Command::FAILURE : Command::SUCCESS;
    }
}
