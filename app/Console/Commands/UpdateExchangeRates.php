<?php

namespace App\Console\Commands;

use App\Models\Client;
use App\Services\CurrencyService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateExchangeRates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:update-rates {--date= : Specific date (YYYY-MM-DD)} {--revaluation : Perform currency revaluation} {--client= : Process specific client only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update exchange rates and optionally perform currency revaluation';

    /**
     * Execute the console command.
     */
    public function handle(CurrencyService $currencyService)
    {
        $date = $this->option('date');
        $performRevaluation = $this->option('revaluation');
        $clientId = $this->option('client');

        $targetDate = $date ? Carbon::createFromFormat('Y-m-d', $date) : now();

        $this->info('Starting exchange rate update...');
        $this->info("Target date: {$targetDate->toDateString()}");

        try {
            // Update exchange rates
            $this->info('Updating exchange rates from external API...');
            $rateResults = $currencyService->updateExchangeRates($targetDate);

            $this->info("Exchange rate update results:");
            $this->info("  Date: {$rateResults['date']}");
            $this->info("  Updated: {$rateResults['updated']} rates");
            $this->info("  Errors: {$rateResults['errors']}");

            if (!empty($rateResults['errors_detail'])) {
                $this->warn("  Rate update errors:");
                foreach ($rateResults['errors_detail'] as $error) {
                    $this->warn("    - {$error['currency']}: {$error['error']}");
                }
            }

            if (!empty($rateResults['rates'])) {
                $this->info("  Updated rates:");
                foreach ($rateResults['rates'] as $currency => $rates) {
                    $this->info("    {$currency}: to_base={$rates['to_base']}, from_base={$rates['from_base']}");
                }
            }

            // Perform revaluation if requested
            if ($performRevaluation) {
                $this->info('Performing currency revaluation...');

                if ($clientId) {
                    $clients = \App\Models\Client::where('id', $clientId)->get();
                } else {
                    $clients = \App\Models\Client::all();
                }

                if ($clients->isEmpty()) {
                    $this->error('No clients found for revaluation.');
                    return Command::FAILURE;
                }

                $totalRevaluations = 0;
                $totalAmount = 0;

                foreach ($clients as $client) {
                    $this->info("  Processing revaluation for client: {$client->name}");

                    try {
                        $revalResults = $currencyService->performCurrencyRevaluation($client->id, $targetDate);

                        $this->info("    Revaluation results:");
                        $this->info("      Accounts processed: {$revalResults['accounts_processed']}");
                        $this->info("      Total revaluation: " . number_format($revalResults['total_revaluation'], 2));

                        if ($revalResults['journal_id']) {
                            $this->info("      Journal created: {$revalResults['journal_id']}");
                        }

                        if (!empty($revalResults['revaluations'])) {
                            $this->info("      Account details:");
                            foreach ($revalResults['revaluations'] as $reval) {
                                $this->info("        {$reval['account']->name}: " .
                                    number_format($reval['revaluation_amount'], 2) .
                                    " (Rate: {$reval['historical_rate']} -> {$reval['current_rate']})");
                            }
                        }

                        $totalRevaluations += $revalResults['accounts_processed'];
                        $totalAmount += $revalResults['total_revaluation'];
                    } catch (\Exception $e) {
                        $this->error("    Failed to process revaluation for client {$client->name}: " . $e->getMessage());
                    }
                }

                $this->newLine();
                $this->info("Revaluation summary:");
                $this->info("  Total accounts processed: {$totalRevaluations}");
                $this->info("  Total revaluation amount: " . number_format($totalAmount, 2));
            }

            $this->newLine();
            $this->info("Exchange rate update completed successfully!");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Exchange rate update failed: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
